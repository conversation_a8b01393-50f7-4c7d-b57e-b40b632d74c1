package com.cover.common

import com.cover.common.commands.GeneralInsuranceCommand
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.PolicyTermDurationEnum
import com.safeguard.ProductInstallmentRate
import com.safeguard.ProductSubTypeEnum
import com.safeguard.Provider
import com.safeguard.general.GeneralQuote
import com.safeguard.Product
import com.safeguard.User
import grails.transaction.Transactional

@Transactional
class GeneralInsuranceService {

    def commonUtilService
    def leadSgService

    GeneralQuote createGeneralQuote(GeneralInsuranceCommand command) {

        User user = User.findByEmail(command.email);
        if (!user) {
            user = new User()
            user.email = command.email
            user.mobile = command.mobile
            user.name = command.firstName.concat( " ").concat(command.lastName)
            user.country = Country.read(CountryEnum.UAE.id)
            user.password = 'general'
            user.save(failOnError: true)
        }

        GeneralQuote quote = new GeneralQuote()
        quote.name = command.firstName.concat( " ").concat(command.lastName)
        quote.mobile = command.mobile
        quote.user = user
        quote.emiratesIdNumber = command.emiratesId
        quote.provider = command.providerId ? Provider.load(command.providerId) : null
        quote.product = Product.load(command.productId)
        quote.queryString = command.queryString
        quote.utmSource = command.utmSource
        quote.utmMedium = command.utmMedium
        quote.utmCampaign = command.utmCampaign
        quote.gclid = command.gclid
        quote.fbclid = command.fbclid
        quote.save(failOnError: true)

        ProductInstallmentRate installmentRate = ProductInstallmentRate.findByActiveAndProduct(true, quote.product)
        CountryEnum countryEnum = CountryEnum.findCountry(command.country)

        quote.policyReference = commonUtilService.generatePolicyRef(quote, quote.product.provider)
        quote.paymentPlan = installmentRate?.paymentPlan
        quote.productInstallmentRate = installmentRate
        if (quote.product.productSubType.id == ProductSubTypeEnum.BUNDLE.value()) {
            //Bundle product is for Unlimited policy terms
            quote.policyTermDuration = PolicyTermDurationEnum.UNLIMITED
        }
        quote.currency = countryEnum?.currency
        quote.quoteCountry = Country.read(countryEnum?.id)
        quote.lang = command.lang
        quote.policyPrice = installmentRate?.pricePerTerm
        quote.policyPriceVat = commonUtilService.getVATAmount(quote.policyPrice)
        quote.totalPrice = quote.policyPrice ? quote.policyPrice.add(quote.policyPriceVat) : null

        quote.paymentStatus = PaymentStatusEnum.PENDING
        if (quote.product.productSubType.id == ProductSubTypeEnum.BUNDLE.value()) {
            quote.paymentMethod = PaymentMethodEnum.CREDITCARD
        } else {
            quote.paymentMethod = PaymentMethodEnum.COD
        }

        quote.save(failOnError: true)

        leadSgService.createLeadAndComparison(quote)

        return quote
    }
}
