package com.cover.common

import com.cover.api.CarQuoteCommand
import com.cover.api.QuotePaymentV2Command
import com.cover.car.GigRateService
import com.safeguard.*
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteDetail
import com.safeguard.health.HealthQuote
import com.safeguard.health.HealthQuoteDetail
import com.safeguard.home.HomeQuote
import com.safeguard.home.HomeQuoteDetail
import com.safeguard.life.LifeQuote
import com.safeguard.life.LifeQuoteDetail
import grails.transaction.Transactional
import grails.util.Environment
import org.joda.time.DateTime
import org.joda.time.LocalDate
import reactor.spring.context.annotation.Consumer

@Consumer
@Transactional
class CommonQuoteService {

    def commonPolicySgService
    def commonUtilService
    def grailsApplication
    def policySgService
    def paymentService
    def springSecurityService

    /**
     * Confirms policy cancellation for the provided quote. In case of Car Policy if its current CRM Status is
     * {@link CrmStatusEnum#ISSUED} then the new CRM Status is set to {@link CrmStatusEnum#CANCELLATION_DOCS_PENDING},
     * otherwise the new CRM Status is set to {@link CrmStatusEnum#PENDING_CANCEL}.
     * In case of Health and Home policies just an email is sent to an appropriate internal team to process the
     * cancellation.
     *
     * @param cancellationRequest - the policy cancellation request info
     * @param productType - product type(CAR, HEALTH, ...)
     * @param country - country of the application deployment
     * @param lang - language of user preference
     */
    void confirmPolicyCancellation(def cancellationRequest, ProductTypeEnum productType, String country, String lang) {

        def quote = cancellationRequest.quote
        quote.user // just loading the user in order for it to be available in email sending thread

        switch (productType) {
            case ProductTypeEnum.CAR:
                boolean isPolicyIssued = false

                CarQuoteDetail carQuoteDetail = CarQuoteDetail.findByQuote(quote)

                if (carQuoteDetail.crmStatus == CrmStatusEnum.ISSUED) {

                    isPolicyIssued = true
                    policySgService.updateCrmStatus(quote, CrmStatusEnum.CANCELLATION_DOCS_PENDING,
                        "Cancellation Request #: ${Constants.PCR_ID_PREFIX + cancellationRequest.id}")
                    cancellationRequest.statusId = CarPcrStatusEnum.DOCS_PENDING.id

                } else if (quote.paidDate && !(carQuoteDetail.crmStatus in [CrmStatusEnum.PENDING_CANCEL, CrmStatusEnum.CANCELLATION_PROCESSED])) {

                    policySgService.updateCrmStatus(quote, CrmStatusEnum.PENDING_CANCEL,
                        "Cancellation Request #: ${Constants.PCR_ID_PREFIX + cancellationRequest.id}")
                    cancellationRequest.statusId = CarPcrStatusEnum.INTERNAL_REVIEW.id

                } else {
                    throw new RuntimeException("Unable to confirm policy cancellation because the quote is not in a " +
                        "corresponding state. Quote info: quote id = $quote.id, CRM Status = $carQuoteDetail.crmStatus, " +
                        "is paid = ${quote.paidDate ? 'true' : 'false'}")
                }

                cancellationRequest.statusUpdatedDate = DateTime.now()
                cancellationRequest.save(failOnError: true)

                quote.product.provider // just loading the provider info in order for it to be available in email sending thread

                notify AsyncEventConstants.CAR_PCR_EMAIL_REQUEST_CONFIRMED_TO_CUSTOMER, [
                    cancellationRequest: cancellationRequest, productType: productType, isPolicyIssued: isPolicyIssued,
                    country            : country, lang: lang
                ]

                break
            case ProductTypeEnum.HEALTH:
            case ProductTypeEnum.HOME:
            case ProductTypeEnum.LIFE:

                if (Environment.PRODUCTION == Environment.current ||
                    grailsApplication.config.emails.testRecipients.contains(springSecurityService.currentUser.email)) {

                    notify AsyncEventConstants.PCR_EMAIL_REQUEST_RECEIVED, [
                        quote: quote, productType: productType, user: quote.user
                    ]
                    break
                }
        }
    }


    /**
     * Gets quote by provided policy number and product type
     * @param policyNo - policy number
     * @param productType - product type (CAR, HEALTH ...)
     * @return the quote if found, null otherwise
     */
    def getQuote(String policyNo, ProductTypeEnum productType) {
        def quote

        switch (productType) {
            case ProductTypeEnum.CAR:
                quote = CarQuote.findByPolicyNo(policyNo)
                break
            case ProductTypeEnum.HEALTH:
                quote = HealthQuote.findByPolicyNo(policyNo)
                break
            case ProductTypeEnum.HOME:
                quote = HomeQuote.findByPolicyNo(policyNo)
                break
            case ProductTypeEnum.LIFE:
                quote = LifeQuote.findByPolicyNo(policyNo)
                break
        }

        quote
    }

    /**
     * Gets quote by provided id and product type
     * @param id - id of the quote
     * @param productType - product type (CAR, HEALTH ...)
     * @return the quote if found, null otherwise
     */
    def getQuote(Long id, ProductTypeEnum productType) {
        return commonPolicySgService.getQuote(id, productType)
    }

    /**
     * Gets quote details by provided quote and product type. If the quote details does not exist it will be created.
     * @param quote - quote
     * @param productType - product type (CAR, HEALTH ...)
     * @return return existing quote details if it does exist, otherwise a newly created one
     */
    def getQuoteDetail(def quote, ProductTypeEnum productType) {
        def quoteDetail

        switch (productType) {
            case ProductTypeEnum.CAR:
                quoteDetail = CarQuoteDetail.findByQuote(quote)
                break
            case ProductTypeEnum.HOME:
                quoteDetail = HomeQuoteDetail.findOrCreateByHomeQuote(quote)
                break
            case ProductTypeEnum.HEALTH:
                quoteDetail = HealthQuoteDetail.findOrCreateByHealthQuote(quote)
                break
            case ProductTypeEnum.LIFE:
                quoteDetail = LifeQuoteDetail.findOrCreateByLifeQuote(quote)
                break
        }

        quoteDetail
    }

    /**
     * This method will return estimated driving license date based on local driving experience
     *
     * <AUTHOR> Talreja, Syed Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param localDrivingExp [Local driving experience]
     * @return LocalDate [estimated driving license date (Joda)]
     */
    static LocalDate getDrivingLicenseIssueDate(Integer localDrivingExp) {
        DrivingExperienceEnum drivingExperience = DrivingExperienceEnum.findById(localDrivingExp)
        LocalDate issueDate
        if (drivingExperience == DrivingExperienceEnum.ZERO_TO_SIX_MONTHS) {
            issueDate = LocalDate.now().minusMonths(1)
        } else if (drivingExperience == DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS) {
            issueDate = LocalDate.now().minusMonths(6)
        } else {
            issueDate = LocalDate.now().minusYears(drivingExperience.getExperienceInYears())
        }
        return issueDate
    }

    void updatePaymentInformation(def quote, QuotePaymentV2Command quotePaymentV2Command) {
        log.info("commonQuoteService.updatePaymentInformation - quotePaymentV2Command:$quotePaymentV2Command")

        if (quote instanceof CarQuote) {
            quote.policyPrice = quotePaymentV2Command.price.add(quotePaymentV2Command.adminFee ?: 0)
            quote.product = Product.load(GigRateService.PRODUCT_TPL_ID)
            quote.policyReference = commonUtilService.generatePolicyRef(quote, quote.product.provider)
            quote.c4meFee = quotePaymentV2Command.adminFee
            quote.c4meFeeVat = commonUtilService.getVATAmount(quotePaymentV2Command.adminFee)
        } else {
            //TODO: assign product as per the requirement to Travel and Health Quote
            quote.policPrice = quotePaymentV2Command.price
            //quote.product = Product.load(GigRateService.PRODUCT_TPL_ID)
            //quote.policyReference = commonUtilService.generatePolicyRef(quote, product.provider)
            //quote.c4meFee = quotePaymentV2Command.adminFee
            //quote.c4meFeeVat = commonUtilService.getVATAmount(quotePaymentV2Command.adminFee)
        }
        quote.policyPriceVat = commonUtilService.getVATAmount(quote.policyPrice)

        quote.discount = quotePaymentV2Command.discountAmount ?: 0
        quote.discountCode = null

        quote.totalPriceWithoutVat = quote.policyPrice - quote.discount
        quote.totalPrice = quotePaymentV2Command.totalPrice
        quote.authorizedAmount = quote.totalPrice
        quote.capturedAmount = quote.totalPrice

        quote.paymentGateway = PaymentGatewayEnum.CHECKOUT
        quote.merchantRef = quotePaymentV2Command.paymentReference
        quote.paymentMethod = PaymentMethodEnum.CREDITCARD
        quote.paymentStatus = PaymentStatusEnum.DRAFT

        quote.save(failOnError: true, flush:true)

        paymentService.changePaymentStatus(quote, PaymentStatusEnum.PAID)
    }

}
