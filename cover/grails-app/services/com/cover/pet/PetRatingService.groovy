package com.cover.pet


import com.cover.pet.commands.PetRateCommand
import com.cover.pet.commands.RateBenefitsCommand
import com.safeguard.pet.PetQuote
import com.safeguard.pet.PetRating
import com.safeguard.pet.PetProductBenefit
import grails.transaction.Transactional

import java.math.RoundingMode

/**
 * <AUTHOR>
 */
@Transactional
class PetRatingService {

    def grailsApplication
    def checkoutService
    def commonUtilService
    def paymentService
    def crmService
    def utilService
    def additionalChargesService

    @Transactional(readOnly = true)
    List<PetRateCommand> getRatings(PetQuote quote) {
        log.info "Pet.quote.service.getRatings - ${quote}"

        List list = findApplicableRates( quote )
        List <PetRateCommand> petRateCommandList = []
        list.each {  it ->

            PetRateCommand petRateCommand = new PetRateCommand()
            petRateCommand.id = it[0]
            petRateCommand.baseRateYearly = it[1]
            //petRateCommand.baseRateMonthly = ((BigDecimal)it[2]).setScale(0, RoundingMode.CEILING)
            petRateCommand.annualExcess = it[2]
            petRateCommand.savingPercentage =  15
            petRateCommand.productNameEn = it[3]
            petRateCommand.productId = it[4]

            petRateCommand.allRateBenefits = getAllRateBenefits(petRateCommand.productId.toInteger())

            petRateCommandList.add(petRateCommand)
        }

        petRateCommandList
    }

    /*List<RatingsBenefits> getBenefitsList(Long ratingId) {
        PetRating petRating = PetRating.findById(ratingId)
        return RatingsBenefits.findAllByPetRating(petRating)
    }*/

    List<RateBenefitsCommand> getAllRateBenefits(Integer productId) {

        Map params = [productId:productId]

        List benefits = PetProductBenefit.executeQuery("""
            SELECT pb.id as id,
                   pb.name as name,
                   pb.description as description,
                   pb.order as order,
                   rb.value as value

            FROM PetProductBenefit rb
            JOIN PetBenefits pb
            ON pb.id = rb.benefits.id

            where rb.product.id = :productId
            order by pb.order asc

        """, params)

        List<RateBenefitsCommand> productBenefits = []
        benefits.each {
            RateBenefitsCommand command = new RateBenefitsCommand()
            command.id = it[0]
            command.name = it[1]
            command.description = it[2]
            command.order = it[3]
            command.value = it[4]
            productBenefits.add(command)
        }
        //log.info(productBenefits)
        return productBenefits
    }


    List findApplicableRates(PetQuote petQuote) {

        def params = [petAge:petQuote.pet.petAge,
                      petBreedGroupId:petQuote.pet.petBreed.groupId,
        ]
        log.info "Pet.quote.service.findApplicableRates - ${params.toString()}"

        List<PetRateCommand> result = PetRating.executeQuery("""
            SELECT pr.id as id,
            pr.baseRateYearly as baseRateYearly,
            pr.annualExcess as annualExcess,
            p.nameEn as nameEn,
            p.id as productId

            FROM PetRating pr
            JOIN pr.product p


            where pr.petBreedGroupId = :petBreedGroupId
            and pr.petAge = :petAge
            order by pr.baseRateYearly asc

            """, params)

        log.info "result ${result}"
        /*
        List result = PetRating.executeQuery("""

            SELECT pr.id as id,
                   pr.petType as petType,
                   pr.petBreed as petBreed,
                   pr.petAge as petAge,
                   pr.petGender as petGender,
                   pr.baseRateYearly as baseRateYearly,
                   pr.baseRateMonthly as baseRateMonthly,
                   pr.annualExcess as annualExcess,
                   p.nameEn as nameEn,
                   p.id as productId

            FROM PetRating pr
            JOIN Product p
            ON pr.product.id = p.id

            where pr.petBreedGroupId = :petBreedGroupId
            and pr.petAge = :petAge
            order by pr.baseRateYearly asc

        """, params)
*/
        return result
    }

}
