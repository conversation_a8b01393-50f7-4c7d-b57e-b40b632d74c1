package com.cover.pet

import com.cover.pet.commands.UpdatePetCommand

//import com.cover.pet.commands.PetPhotoCommand

import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CrmStatusEnum
import com.safeguard.DocumentUploadService.S3UploadResult
import com.safeguard.PaymentStatusEnum
import com.safeguard.User
import com.safeguard.pet.Pet
import com.safeguard.pet.PetBreed
import com.safeguard.pet.PetQuote
import com.safeguard.pet.PetQuoteDetail
import com.safeguard.pet.PetType
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.joda.time.LocalDateTime
import org.springframework.web.multipart.MultipartFile

class PetService {

    def documentUploadService
    /**
     * To get list of pet type
     *
     * @param country
     * @return
     */
    @Transactional(readOnly = true)
    def getPetTypeList(Country country = null) {

        if (!country) {
            country = Country.read(CountryEnum.UAE.id)
        }

        List<PetType> petType = PetType.findAll();

        return petType
    }

    /**
     * To get pet breed by id
     *
     * @param petBreedId
     * @return
     */
    @Transactional(readOnly = true)
    def getPetBreedById(int petBreedId) {

        return PetBreed.findById(petBreedId)
    }

    /**
     * To get list of pet  breed
     *
     * @param lang
     * @param make
     * @return List of model master
     */
    @Transactional(readOnly = true)
    def getPetBreedList() {

        List<PetBreed> petBreed = PetBreed.findAll().sort{ it.name }

        return petBreed
    }


    def createPet(def params, User user) {
        log.info("petService.createPet - [params:${params}]")

        Pet pet = new Pet()

        pet.petName = params.petName
        pet.petImageUrl = params.petImageUrl
        pet.petGender = params.petGender
        pet.petBreed = PetBreed.read(params.petBreed)
        pet.isMixed = Boolean.parseBoolean(params.isMixed.toString()) ?: 0
        pet.petType = PetType.read(params.petType)
        pet.petAge = Integer.parseInt(params.petAge)
        pet.microchip = params.microchip
        pet.user = user
        pet.dateCreated = LocalDateTime.now()
        pet.save()

        return pet

    }

    def updatePet(UpdatePetCommand cmd) {
        log.info("petService.updatePet - [cmd:${cmd.id}]")

        Pet pet = Pet.read(AESCryption.decrypt(cmd.id))

        if (!pet) {
            return null
        }

        pet.petName = cmd.petName

        if (cmd.petImageUrl?.isEmpty() || cmd.petImageUrl?.equals("null") ) {
            pet.petImageUrl = pet.petImageUrl
        } else {
            pet.petImageUrl = cmd.petImageUrl
        }
        boolean hasActivePolicy = hasActivePolicy (pet)

        if (!hasActivePolicy){
            pet.petGender = cmd.petGender
            pet.petBreed = PetBreed.read(cmd.petBreed)
            pet.petType = PetType.read(cmd.petType)
            pet.petAge = Integer.parseInt(cmd.petAge)
            pet.isMixed = cmd.isMixed ?: 0
            pet.microchip = cmd.microchip
        }

        pet.save()

        return pet
    }

    def removePet(Pet pet) {
        log.info("petService.removePet - [pet:${pet}]")

        boolean hasActivePolicy = hasActivePolicy (pet)

        if (hasActivePolicy){
            return false
        }

        pet.isDeleted = true
        pet.save()

        return true
    }

    def listPetsAndPolicies(User currentUser) {
        def pets = Pet.where { user == currentUser && isDeleted == false }.list()
        def model = [:]
        model.pets = []
        pets.each {
            p ->
                def pet = [:]
                pet.userEmail = p.user.email
                pet.id = AESCryption.encrypt(p.id.toString())
                pet.petName = p.petName
                pet.petType = p.petType.name
                pet.petImageUrl = p.petImageUrl
                pet.petBreed = p.petBreed.name
                pet.petAge = p.petAge
                pet.petGender = p.petGender
                pet.isMixed = p.isMixed
                pet.microchip = p.microchip
                pet.petQuotes = []
                p.petQuotes.sort{a,b-> b.id<=>a.id}
                p.petQuotes.each {
                    pq ->
                        def petQuote = [:]
                        if (pq.paymentStatus == PaymentStatusEnum.PAID || pq.paymentStatus == PaymentStatusEnum.RECEIVED || (pq.paymentStatus == PaymentStatusEnum.ISSUED && pq.daysRemaining > 0)) {
                            petQuote.id = AESCryption.encrypt(pq.id.toString())
                            petQuote.paymentStatus = pq.paymentStatus.name()

                            def petQuoteDetail = PetQuoteDetail.findByQuote(pq)
                            // Adding petQuoteDetail for old quote
                            // Default crmStatus value is draft
                            if (!petQuoteDetail) {
                                petQuoteDetail = new PetQuoteDetail(
                                    crmStatus: CrmStatusEnum.DRAFT,
                                    crmStatusUpdated: LocalDateTime.now(),
                                    quote: pq
                                )
                                petQuoteDetail.save(failOnError: true)
                            }

                            if (petQuoteDetail.crmStatus != CrmStatusEnum.CANCELLATION_PROCESSED) {
                                petQuote.policyStatus = (pq.paymentStatus == PaymentStatusEnum.PAID || pq.paymentStatus == PaymentStatusEnum.RECEIVED)
                                    ? pq.paymentStatus.name().capitalize()
                                    : (pq.daysRemaining > 30 ? 'Active' : 'Expires soon')
                            } else {
                                petQuote.policyStatus = 'Cancelled'
                            }

                            petQuote.daysRemaining = pq.daysRemaining
                            petQuote.expiryDate = pq.expiryDate.toString()
                            petQuote.downloadPolicy = pq.documents?.find {
                                it.documentType.code == "Policy" && !it.isDeleted
                            }?.fullPath
                            petQuote.cancelPolicy = ''
                            pet.petQuotes.push(petQuote)
                        }
                }
                model.pets.push(pet)
        }
        return model
    }

    def savePetPhoto(MultipartFile file, Pet pet) {
        String fileName = file.originalFilename
        InputStream fileInputStream = file.inputStream
        S3UploadResult uploadResult = documentUploadService.uploadToS3(fileInputStream, fileName, pet.id, 'PET_PHOTO')

        if (!uploadResult) {
            return null
        }

        pet.petImageUrl = uploadResult.cfFileUrl
        pet.save()

        return uploadResult
    }

    boolean hasActivePolicy (Pet pet) {

        Map param = [petId:pet.id,
                     paymentStatus:[PaymentStatusEnum.ISSUED, PaymentStatusEnum.PAID, PaymentStatusEnum.RECEIVED],
                     crmStatus:CrmStatusEnum.CANCELLATION_PROCESSED]

        def policies = PetQuote.executeQuery("""
                    SELECT pq,pqd
                    FROM PetQuote pq
                    JOIN PetQuoteDetail pqd
                    ON pq.id = pqd.quote.id
                    where pq.pet.id = :petId
                    and pq.paymentStatus in :paymentStatus
                    and pqd.crmStatus != :crmStatus
                    order by pq.id desc
                """, param)

        return policies.any({  data -> (data[0].daysRemaining > 10) })

    }
}
