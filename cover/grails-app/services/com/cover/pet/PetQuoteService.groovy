package com.cover.pet

import com.cover.pet.commands.PetQuoteCommand
import com.safeguard.*
import com.safeguard.base.Comparison
import com.safeguard.base.Lead
import com.safeguard.pet.Pet
import com.safeguard.pet.PetBreed
import com.safeguard.pet.PetQuote
import com.safeguard.pet.PetQuoteCrmStatus
import com.safeguard.pet.PetQuoteDetail
import com.safeguard.pet.PetRating
import com.safeguard.pet.PetType
import com.safeguard.util.AESCryption
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime

class PetQuoteService {

    def commonUtilService
    def crmService
    def petService
    def leadSgService

    def createQuote(def params) {
        log.info("petQuote.createQuote - [params:${params}]")
        PetQuote petQuote = new PetQuote()


        User user = User.findByEmail(params.email);
        def pet = Pet.findByPetNameAndUserAndPetTypeAndPetBreed(params.petName, user, PetType.read(params.petType), PetBreed.read(params.petBreed))
        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.mobile
            newUser.country = Country.read(CountryEnum.UAE.id)
            newUser.save()
            petQuote.user = newUser
            user = newUser

            pet = petService.createPet(params, petQuote.user)

        } else {
            if (!pet) {
                pet = petService.createPet(params, user)
            } else {
                def hasActivePolicy = petService.hasActivePolicy(pet)

                if (hasActivePolicy) {
                    return false
                }
                if (pet.isDeleted ||
                    pet.petAge != Integer.parseInt(params.petAge) ||
                    pet.microchip != params.microchip ||
                    pet.petGender != params.petGender){
                    if (pet.isDeleted) {
                        pet.isDeleted = false
                    }
                    if (pet.petAge != Integer.parseInt(params.petAge)) {
                        pet.petAge = Integer.parseInt(params.petAge)
                    }
                    if (pet.petGender != params.petGender) {
                        pet.petGender = params.petGender
                    }
                    pet.save()
                }
            }

            user.name = params.name ?: user.name
            user.mobile = params.mobile ?: user.mobile
            petQuote.user = user
        }

        petQuote.pet = pet
        petQuote.quoteCountry = Country.load(CountryEnum.UAE.id)
        petQuote.policyStartDate = new LocalDate(params.policyStartDate)
        petQuote.paymentStatus = PaymentStatusEnum.DRAFT

        if (!user.petLeadType) {
            user.changeLeadType(LeadType.NORMAL, ProductTypeEnum.PET.name().toLowerCase(), 'PetQuoteService.createQuote')
            user.save()
        }

        PetQuoteDetail petQuoteDetail = new PetQuoteDetail(
            crmStatus: CrmStatusEnum.DRAFT,
            crmStatusUpdated: LocalDateTime.now(),
            quote: petQuote
        )
        petQuoteDetail.save(failOnError: true)

        PetQuoteCrmStatus petQuoteCrmStatus = new PetQuoteCrmStatus(
            crmStatus: CrmStatusEnum.DRAFT,
            quote: petQuote
        )
        petQuoteCrmStatus.save(failOnError: true)

        crmService.handleCrmEvents(petQuote.userId, ProductTypeEnum.PET.name().toLowerCase(), petQuote)

        return petQuote

    }

    def saveQuote(PetQuote petQuote) {
        //log.info("saveQuote - [petQuote: ${petQuote.toString()}]")
        petQuote.save(flush: true, failOnError: true)

        leadSgService.createLeadAndComparison(petQuote)
        petQuote
    }


    /**
     * Update pet quote.
     *
     * @param quote
     * @param rate
     * @return
     */
    def updatePetQuote(PetQuote petQuote, PetRating petRating, String paymentPlan) {
        log.info("updatePetQuote - [petQuote: ${petQuote.id}, petRating: ${petRating.id}, paymentPlan:$paymentPlan]")
        Product product = Product.get(petRating.product.id)
        petQuote.product = product

        BigDecimal premiumVAT
        if( paymentPlan.equals("MONTHLY")){
            petQuote.paymentPlan = PaymentPlanEnum.MONTHLY
            petQuote.totalPrice = petRating.baseRateMonthly
            petQuote.policyPrice = petRating.baseRateMonthly
            premiumVAT = commonUtilService.getVATAmount(petRating.baseRateMonthly)
        } else {
            petQuote.paymentPlan = PaymentPlanEnum.ANNUAL
            petQuote.totalPrice = petRating.baseRateYearly
            petQuote.policyPrice = petRating.baseRateYearly
            premiumVAT = commonUtilService.getVATAmount(petRating.baseRateYearly)
        }
        petQuote.annualExcess = petRating.annualExcess
        petQuote.providerBasicYearlyPremium = petRating.baseRateYearly
        petQuote.policyPriceVat = premiumVAT
        petQuote.totalPrice = petQuote.totalPrice.add(premiumVAT)
        petQuote.policyReference = commonUtilService.generatePolicyRef(petQuote, product.provider)

        petQuote.paymentStatus = PaymentStatusEnum.PENDING
        petQuote.save(flush: true, failOnError: true)
    }


    /**
     * Update pet quote details.
     *
     * @param quote
     * @return
     */
    def updatePetQuoteDetails(PetQuoteCommand petQuoteCommand ) {
        log.info("editPetQuoteDetails - [new pet quote data: ${petQuoteCommand.toString()}]")

        PetQuote petQuote = PetQuote.findById( Integer.parseInt(AESCryption.decrypt(petQuoteCommand.cryptedId)) )
        leadSgService.updatePhoneLead(petQuote.user, petQuoteCommand.mobile)
        petQuote.policyStartDate = petQuoteCommand.policyStartDate
        petQuote.emiratesIdNumber = petQuoteCommand.emiratesIdNumber

        petQuote.queryString = petQuoteCommand.queryString != 'null' ? petQuoteCommand.queryString : petQuote.queryString
        petQuote.utmSource = petQuoteCommand.utmSource != 'null' ? petQuoteCommand.utmSource : petQuote.utmSource
        petQuote.utmMedium = petQuoteCommand.utmMedium != 'null' ? petQuoteCommand.utmMedium : petQuote.utmMedium
        petQuote.utmCampaign = petQuoteCommand.utmCampaign != 'null' ? petQuoteCommand.utmCampaign : petQuote.utmCampaign
        petQuote.gclid = petQuoteCommand.gclid != 'null' ? petQuoteCommand.gclid : petQuote.gclid
        petQuote.fbclid = petQuoteCommand.fbclid != 'null' ? petQuoteCommand.fbclid : petQuote.fbclid

        petQuote.save(flush: true, failOnError: true)
    }

    /**
     * Verify that all data is available for payment
     *
     * @param PetQuote
     * @return boolean True if Eligible for Payment else False
     */
    boolean isEligibleForPayment(PetQuote quote) {
        log.debug("petQuoteService.isEligibleForPayment - quote:${quote.id}")
        //TODO: Update eligibility checking below

        /*boolean allDataAvailable = quote.policyPrice && quote.policyPriceVat &&
            quote.totalPrice &&
            (!quote.discount || quote.discount && quote.discountCode)

        log.info("pp:${quote.policyPrice}, ppvat:${quote.policyPriceVat}, " +
            "tp:${quote.totalPrice}, " +
            "discount:${quote.discount}, discountCode:${quote.discountCode?.id}")

        BigDecimal totalPrice = quote.policyPrice.minus(quote.discount ?: 0).plus(quote.policyPriceVat ?: 0)

        if (!allDataAvailable || totalPrice != quote.totalPrice) {
            log.error("travelQuoteService.isEligibleForPayment - quote:${quote.id}, prices not matched. " +
                "Total:${totalPrice}, quote.TotalPrice:${quote.totalPrice}, allDataAvailable:${allDataAvailable}")
            return false
        }*/

        /*if (quote.startDate.isBefore(LocalDate.now())) {
            log.error("travelQuoteService.isEligibleForPayment - Quote's start date has already passed; quote ID: ${quote.id}")
            return false
        }*/

        return true
    }
}
