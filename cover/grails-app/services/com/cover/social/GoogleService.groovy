package com.cover.social

import com.cover.api.UserProfile
import grails.transaction.Transactional
import org.pac4j.core.context.WebContext
import org.pac4j.core.exception.HttpCommunicationException
import org.pac4j.oauth.client.Google2Client
import org.pac4j.oauth.profile.google2.Google2Profile

@Transactional(readOnly = true)
class GoogleService {

    def grailsApplication


    def userInformation(String accessToken, WebContext context) {
        log.info("google.userInformation - entering with [accessToken:${accessToken}]")

        String key = "1005539892830-a4paaol6eik0ja86ecc5dh2nlvcuo264.apps.googleusercontent.com"
        String secret = "OmdMFVYZ_v7SFlaHOSHWC6ue"
        Integer attempts = 2

        Google2Client client = Google2Client.newInstance(key, secret)
        client.callbackUrl = "www.yallacompare.com"

        Google2Profile profile = null

        for (int i = 1; i <= attempts; i) {
            try {
                log.info("Attempt :${i}")

                profile = client.getUserProfile(context, accessToken)
                break

            } catch (HttpCommunicationException httpCommunicationException) {
                log.info("httpCommunicationException error: ${httpCommunicationException.getMessage()}")
                break
            } catch (Exception e) {
                log.info("Exception: ${e.getMessage()}", e)
                break
            }
        }
        log.info("Google2Client:${profile}")

        if (profile) {
            UserProfile userProfile = new UserProfile()
            userProfile.email = profile.email
            userProfile.googleId = profile.id
            userProfile.medium = "google"
            userProfile.name = profile.firstName + " " + profile.getFamilyName()

            return userProfile
        }
        return null
    }
}
