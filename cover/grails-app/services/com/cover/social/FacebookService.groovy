package com.cover.social

import com.cover.api.UserProfile

//import com.github.scribejava.core.exceptions.OAuthConnectionException
import grails.transaction.Transactional
import org.pac4j.core.context.WebContext

import org.pac4j.core.exception.HttpCommunicationException
import org.pac4j.oauth.client.FacebookClient
import org.pac4j.oauth.profile.facebook.FacebookProfile

/**
 * Service class for Facebook related methods. To connect with facebook, and fetch user profile data
 */
@Transactional(readOnly = true)
class FacebookService {

    def grailsApplication

    /**
      * To connect with facebook, and fetch user profile data based on access token.
      *
      * @param accessToken Facebook AccessToken
      * @return UserProfile object
      */
    def userInformation(String accessToken, WebContext context) {
        log.info("facebook.userInformation - entering with [accessToken:${accessToken}]")

        String key = grailsApplication.config.cover.social.oauth.facebook.key
        String secret = grailsApplication.config.cover.social.oauth.facebook.secret
        String callbackUrl = grailsApplication.config.cover.social.oauth.facebook.callbackUrl
        Integer attempts = grailsApplication.config.cover.social.oauth.retry.attempts

        FacebookClient client = FacebookClient.newInstance(key, secret)
        client.callbackUrl = callbackUrl
        client.fields = "id,first_name,last_name,gender,email"

        FacebookProfile profile = null

        for (int i = 1; i <= attempts; i) {
            try {
                log.info("Attempt :${i}")

                profile = client.getUserProfile(context, accessToken)
                break

            } catch (HttpCommunicationException httpCommunicationException) {
                log.info("httpCommunicationException error: ${httpCommunicationException.getMessage()}")
                break
            } catch (Exception e) {
                log.info("Exception: ${e.getMessage()}", e)
                break
            }
        }
        log.info("FacebookProfile:${profile}")

        if (profile) {
            UserProfile userProfile = new UserProfile()
            userProfile.email = profile.email
            userProfile.facebookId = profile.id
            userProfile.medium = "facebook"
            userProfile.name = profile.firstName + " " + profile.getFamilyName()

            return userProfile
        }
        return null
    }
}
