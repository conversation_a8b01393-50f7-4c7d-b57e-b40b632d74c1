package com.cover.travel

import com.safeguard.Product
import com.safeguard.ProductSubType
import com.safeguard.ProductTypeEnum
import com.safeguard.travel.TravelBaseRate
import com.safeguard.travel.TravelBaseRateMultiplier
import com.safeguard.travel.TravelCover
import com.safeguard.travel.TravelProductCover
import grails.transaction.Transactional
import org.grails.datastore.mapping.query.api.BuildableCriteria
import org.joda.time.LocalDate

@Transactional(readOnly = true)
class TravelRatingService {

    List<TravelBaseRate> findApplicableRates(Integer providerId, Integer productId,
                                             Integer memberAge, Integer tripLength,
                                             boolean isOutbound = true ) {
        log.info("travelRating.findApplicableRates - providerId:$providerId, productId:$productId, " +
            "memberAge:$memberAge, tripLength:$tripLength, isOutbound:$isOutbound ")

        BuildableCriteria c = TravelBaseRate.createCriteria()
        List<TravelBaseRate> list = c.list {
            product {
                provider {
                    eq 'id', providerId
                    eq 'active', true
                }
                if (productId) {
                    eq 'id', productId
                }
                eq 'productType.id', ProductTypeEnum.TRAVEL.value()
                eq 'productSubType.id', isOutbound ? ProductSubType.TRAVEL_OUTBOUND : ProductSubType.TRAVEL_INBOUND
                eq 'active', true
            }

            and {
                le 'memberAgeFrom', memberAge
                ge 'memberAgeTo', memberAge
            }

            le 'durationFrom', tripLength
            ge 'durationTo', tripLength

        }

        list
    }

    TravelRateCommand applyCovers(TravelQuoteCommand quoteCommand, TravelRateCommand rateCommand) {

        Product product = Product.read(rateCommand.productId)
        List<TravelProductCover> covers = TravelProductCover.findAllByProduct(product)

        covers.each { TravelProductCover productCover ->
            TravelCover cover = productCover.travelCover

            TravelProductCoverCommand coverCommand = new TravelProductCoverCommand()
            coverCommand.coverId = cover.id
            coverCommand.cover = cover.coverEn
            coverCommand.slug = cover.slug
            coverCommand.value = productCover.value
            coverCommand.parent = cover.parent ? [slug: cover.parent.slug, name: cover.parent.coverEn] : null

            rateCommand.covers.add(coverCommand)
        }

        return rateCommand
    }


    List<TravelBaseRateCommand> toTravelBaseRateCommand(List<TravelBaseRate> memberBaseRateList) {
        List<TravelBaseRateCommand> list = []

        memberBaseRateList.each { TravelBaseRate rate ->
            TravelBaseRateCommand rateCommand = new TravelBaseRateCommand()
            rateCommand.product = rate.product
            rateCommand.productId = rate.productId
            rateCommand.memberAgeFrom = rate.memberAgeFrom
            rateCommand.memberAgeTo = rate.memberAgeTo
            rateCommand.durationFrom = rate.durationFrom
            rateCommand.durationTo = rate.durationTo
            rateCommand.price = rate.price
            rateCommand.multiplier = rate.multiplier

            list.add(rateCommand)
        }

        return list
    }

    /**
     * Get all Travel rating multipliers by provider, product and member age
     *
     * @param providerId
     * @param productId
     * @param memberAge
     * @return
     */
    List<TravelBaseRateMultiplier> getTravelRatingMultipliers(Integer providerId, Integer productId, Integer memberAge) {
        return TravelBaseRateMultiplier.createCriteria().list {
            eq "provider.id", providerId
            or {
                isNull("product")
                eq "product.id", productId
            }
            lte "memberAgeFrom", memberAge
            gte "memberAgeTo", memberAge

            order("product", "desc")
        }
    }

}
