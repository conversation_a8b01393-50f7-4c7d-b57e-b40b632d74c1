package com.cover.travel

import com.safeguard.CountryEnum
import com.safeguard.Provider
import com.safeguard.travel.TravelBaseRate
import com.safeguard.travel.TravelBaseRateMultiplier
import com.safeguard.travel.TravelDestinationCountryZoneEnum
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.LocalDate

@Transactional(readOnly = true)
class TravelNoorRateService {

    def travelRatingService

    public static final Integer PROVIDER_ID = 4

    public static final Integer PRODUCT_INBOUND_I = 2057
    public static final Integer PRODUCT_INBOUND_II = 2058
    public static final Integer PRODUCT_INBOUND_ALREADY_IN_UAE = 2071

    public static final Integer PRODUCT_PROGRAM_1_INDIVIDUAL_WW_EXCL_USA_CANADA_NO_COVID = 2041
    public static final Integer PRODUCT_PROGRAM_1_INDIVIDUAL_WW_NO_COVID = 2042
    public static final Integer PRODUCT_PROGRAM_1_FAMILY_WW_NO_COVID = 2043
    public static final Integer PRODUCT_PROGRAM_1_SCHENGEN_NO_COVID = 2044
    public static final Integer PRODUCT_PROGRAM_2_INDIVIDUAL_WW_EXCL_USA_CANADA_NO_COVID = 2045
    public static final Integer PRODUCT_PROGRAM_2_INDIVIDUAL_WW_NO_COVID = 2046
    public static final Integer PRODUCT_PROGRAM_2_FAMILY_WW_NO_COVID = 2047
    public static final Integer PRODUCT_PROGRAM_2_SCHENGEN_NO_COVID = 2048
    public static final Integer PRODUCT_PROGRAM_1_INDIVIDUAL_WW_EXCL_USA_CANADA_WITH_COVID = 2049
    public static final Integer PRODUCT_PROGRAM_1_INDIVIDUAL_WW_WITH_COVID = 2050
    public static final Integer PRODUCT_PROGRAM_1_FAMILY_WW_WITH_COVID = 2051
    public static final Integer PRODUCT_PROGRAM_1_SCHENGEN_WITH_COVID = 2052
    public static final Integer PRODUCT_PROGRAM_2_INDIVIDUAL_WW_EXCL_USA_CANADA_WITH_COVID = 2053
    public static final Integer PRODUCT_PROGRAM_2_INDIVIDUAL_WW_WITH_COVID = 2054
    public static final Integer PRODUCT_PROGRAM_2_FAMILY_WW_WITH_COVID = 2055
    public static final Integer PRODUCT_PROGRAM_2_SCHENGEN_WITH_COVID = 2056

    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_INDIVIDUAL = 2059
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_FAMILY = 2060
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_GROUP = 2061
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_INDIVIDUAL = 2062
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_FAMILY = 2063
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_GROUP = 2064
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_INDIVIDUAL = 2065
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_FAMILY = 2066
    public static final Integer PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_GROUP = 2067
    public static final Integer PRODUCT_PROGRAM_B_HAJJ_UMRAH_BASIC = 2068
    public static final Integer PRODUCT_PROGRAM_B_HAJJ_UMRAH_PLUS = 2069
    public static final Integer PRODUCT_PROGRAM_B_HAJJ_UMRAH_EXTRA = 2070


    List<TravelRateCommand> getRates(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand> baseRateList = []

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInNearestYear(travellerDOB)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId, memberAge,
                    tripLength, quoteCommand.isOutbound)

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            memberBaseRateCommandList = applyMultiplier(quoteCommand, memberBaseRateCommandList,
                quoteCommand.providerId, memberAge)

            baseRateList.addAll(memberBaseRateCommandList)
        }

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        return rateList
    }

    TravelRateCommand getRate(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand> baseRateList = []

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInNearestYear(travellerDOB)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId, memberAge,
                    tripLength, quoteCommand.isOutbound)

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            memberBaseRateCommandList = applyMultiplier(quoteCommand, memberBaseRateCommandList,
                quoteCommand.providerId, memberAge)

            baseRateList.addAll(memberBaseRateCommandList)
        }

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        return rateList[0]
    }

    boolean checkProductEligibility(TravelQuoteCommand quoteCommand, Integer productId) {
        boolean isEligible = true

        quoteCommand.travelersBirthDates.each {it ->
            Integer ageInDays = Days.daysBetween(it, new LocalDate()).getDays()
            if (ageInDays < 30) {
                isEligible = false
            }
        }

        if (productId in [PRODUCT_PROGRAM_1_SCHENGEN_NO_COVID, PRODUCT_PROGRAM_1_SCHENGEN_WITH_COVID,
                          PRODUCT_PROGRAM_2_SCHENGEN_NO_COVID, PRODUCT_PROGRAM_2_SCHENGEN_WITH_COVID]) {

            //Only applicable if travelling to Schengen Zone
            if (quoteCommand.destinationCountryZoneId != TravelDestinationCountryZoneEnum.SCHENGEN.id) {
                isEligible = false
            }

        } else if (productId in [PRODUCT_PROGRAM_1_INDIVIDUAL_WW_EXCL_USA_CANADA_NO_COVID,
                                 PRODUCT_PROGRAM_2_INDIVIDUAL_WW_EXCL_USA_CANADA_NO_COVID,
                                 PRODUCT_PROGRAM_1_INDIVIDUAL_WW_EXCL_USA_CANADA_WITH_COVID,
                                 PRODUCT_PROGRAM_2_INDIVIDUAL_WW_EXCL_USA_CANADA_WITH_COVID]
        ) {
            //Not Eligible if travelling to USA or Canada
            if (quoteCommand.travellingCountries) {
                quoteCommand.travellingCountries.each { Long countryId ->
                    if (countryId in [CountryEnum.CANADA.id, CountryEnum.USA.id]) {
                        isEligible = false
                    }
                }
            }

        } else if (productId in [PRODUCT_INBOUND_I, PRODUCT_INBOUND_II, PRODUCT_INBOUND_ALREADY_IN_UAE]) {
            // Eligible if travelling to UAE only
            if (quoteCommand.travellingCountries) {
                quoteCommand.travellingCountries.each { Long countryId ->
                    if (countryId != CountryEnum.UAE.id) {
                        isEligible = false
                    }
                }
            }

        } else if (productId in [PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_INDIVIDUAL,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_FAMILY,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_GROUP,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_INDIVIDUAL,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_FAMILY,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_GROUP,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_INDIVIDUAL,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_FAMILY,
                                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_GROUP,
                                PRODUCT_PROGRAM_B_HAJJ_UMRAH_BASIC,
                                PRODUCT_PROGRAM_B_HAJJ_UMRAH_PLUS,
                                PRODUCT_PROGRAM_B_HAJJ_UMRAH_EXTRA]) {
            if (!(quoteCommand.destinationCountryZoneId in TravelDestinationCountryZoneEnum.HAJJ_UMRAH.id)) {
                isEligible = false
            }
        }

        //No family/group product for single traveler
        if (quoteCommand.travelersBirthDates.size() == 1 &&
            productId in [
                PRODUCT_PROGRAM_1_FAMILY_WW_NO_COVID,
                PRODUCT_PROGRAM_2_FAMILY_WW_NO_COVID,
                PRODUCT_PROGRAM_1_FAMILY_WW_WITH_COVID,
                PRODUCT_PROGRAM_2_FAMILY_WW_WITH_COVID,
                PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_FAMILY,
                PRODUCT_PROGRAM_A_HAJJ_UMRAH_BASIC_GROUP,
                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_FAMILY,
                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PLUS_GROUP,
                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_FAMILY,
                PRODUCT_PROGRAM_A_HAJJ_UMRAH_PREMIER_GROUP
        ]) {
            isEligible = false
        }

        log.info("travelNoorRate.checkProductEligibility - productId:$productId, age:$quoteCommand: isEligible?$isEligible")
        return isEligible
    }


    private List<TravelRateCommand> populateRatings(TravelQuoteCommand quoteCommand, List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRateByProduct = groupBaseRateByProduct(baseRateList)

        Integer travellersCount = quoteCommand.travelersBirthDates.size()
        List<TravelRateCommand> travelRateCommandList = []

        groupedBaseRateByProduct.each { Integer productId, List<TravelBaseRateCommand> rateList ->

            TravelRateCommand rateCommand = new TravelRateCommand()

            if (checkProductEligibility(quoteCommand, productId) && rateList.size() == travellersCount) {
                log.info("productId:${productId} .. all good, lets add pricing")
                rateCommand = calculateTravellersPrice(quoteCommand, rateCommand, rateList)
                rateCommand = travelRatingService.applyCovers(quoteCommand, rateCommand)

                travelRateCommandList.add(rateCommand)
            }

        }

        log.info("travelRateCommandList:${travelRateCommandList.size()}")
        return travelRateCommandList
    }

    private Map<Integer, List<TravelBaseRateCommand>> groupBaseRateByProduct(List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRate = [:]

        baseRateList.each { TravelBaseRateCommand baseRate ->
            List<TravelBaseRateCommand> rateList = groupedBaseRate.get(baseRate.productId)

            if (!rateList) {
                rateList = []
            }
            rateList.add(baseRate)
            groupedBaseRate.put(baseRate.productId, rateList)
        }

        return groupedBaseRate
    }


    TravelRateCommand calculateTravellersPrice(TravelQuoteCommand quoteCommand,
                                               TravelRateCommand rateCommand, List<TravelBaseRateCommand> baseRateList) {
        TravelBaseRateCommand firstBaseRate = baseRateList.first()
        rateCommand.productId = firstBaseRate.productId.intValue()
        rateCommand.productName = firstBaseRate.product.name

        Provider provider = firstBaseRate.product.provider
        rateCommand.providerId = provider.id
        rateCommand.provider = provider.name
        rateCommand.providerImage = provider.logo

        baseRateList.each { TravelBaseRateCommand baseRate ->
            TravelRateCommand.MemberRateCommand memberRateCommand = new TravelRateCommand.MemberRateCommand()

            memberRateCommand.price = (baseRate.price * (baseRate.multiplier ?: 1)).setScale(0, BigDecimal.ROUND_UP)

            //Apply AgeRate Multiplier if applicable
            rateCommand.memberRateCommands.add(memberRateCommand)
        }

        rateCommand.totalPrice = rateCommand.memberRateCommands.sum { it.price }

        return rateCommand
    }

    List<TravelBaseRateCommand> applyMultiplier(TravelQuoteCommand quoteCommand, List<TravelBaseRateCommand> baseRates,
                                                Integer providerId, Integer memberAge) {
        List<TravelBaseRateMultiplier> multipliers = TravelBaseRateMultiplier.createCriteria().list {
            eq "provider.id", providerId
            lte "memberAgeFrom", memberAge
            gte "memberAgeTo", memberAge
        }

        if (multipliers.size()) {
            baseRates = baseRates.each {baseRate ->
                TravelBaseRateMultiplier multipler = multipliers.findAll {multiplier ->
                    !multiplier.productId || multiplier.productId == baseRate.productId
                }.sort {!it.productId }[0]

                if (isNonFamilyProduct(baseRate.productId) && quoteCommand.travelersBirthDates.size() == 1 &&
                    memberAge <= 16) {
                    //Non Family with 1 member and age 16 or below -> no discount
                    baseRate.multiplier = 1
                } else {
                    baseRate.multiplier = multipler.value
                }

            }
        }

        return baseRates
    }

    boolean isNonFamilyProduct(Integer productId) {

        if (productId in [ PRODUCT_PROGRAM_1_FAMILY_WW_NO_COVID,
                           PRODUCT_PROGRAM_2_FAMILY_WW_NO_COVID,
                           PRODUCT_PROGRAM_1_FAMILY_WW_WITH_COVID,
                           PRODUCT_PROGRAM_2_FAMILY_WW_WITH_COVID]) {
            //Its a family product
            return false
        }
        return true
    }
}
