package com.cover.travel

import com.cover.api.v2.TravelQuoteV2Command
import com.cover.api.v2.TravelUpdateQuoteV2Command
import com.cover.common.commands.MarketingTrackingCommand
import com.safeguard.ContactTypeEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CreatorEnum
import com.safeguard.Donation
import com.safeguard.DonationTypeEnum
import com.safeguard.ExtraFieldCodeEnum
import com.safeguard.GenderEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.LeadStatusEnum
import com.safeguard.LeadType
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.ProductType
import com.safeguard.ProductTypeEnum
import com.safeguard.QuoteExtraField
import com.safeguard.RequestSourceEnum
import com.safeguard.SubRequestSourceEnum
import com.safeguard.User
import com.safeguard.UserDetails
import com.safeguard.base.Comparison
import com.safeguard.base.Lead
import com.safeguard.car.CarQuoteAddon
import com.safeguard.travel.TravelAgeRangeEnum
import com.safeguard.travel.TravelDestinationCountryZoneEnum
import com.safeguard.travel.TravelDetails
import com.safeguard.travel.TravelQuote
import com.safeguard.travel.TravelQuoteCountry
import com.safeguard.travel.Traveler
import com.safeguard.travel.TravelerDetails
import com.safeguard.travel.TravelersRelationshipTypeEnum
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.transaction.Transactional
import org.apache.tomcat.jni.Local
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter

@Transactional
class TravelQuoteService {

    def commonUtilService
    def grailsApplication
    def paymentService
    def travelDubaiNationalRateService
    def travelMetLifeRateService
    def travelNoorRateService
    def travelQuoteSgService
    def travelUnionRateService
    def leadSgService

    TravelQuote createTravelQuote(TravelQuoteCommand command, String passportNumber = null) {

        RequestSourceEnum requestSource = command.requestSource ?: RequestSourceEnum.WEB
        User user = travelQuoteSgService.getOrBuildUser(command.userName, command.userEmail, command.userMobile)
        UserDetails userDetails = UserDetails.findOrCreateByUser(user)
        if (user.travelLeadStatus != LeadStatusEnum.OPEN && !userDetails.dnd) {
            user.changeLeadStatus(LeadStatusEnum.OPEN, user.travelLeadType, ProductTypeEnum.TRAVEL.name().toLowerCase(), null)
        }
        if (!user.travelLeadType) {
            user.changeLeadType(LeadType.NORMAL, ProductTypeEnum.TRAVEL.name().toLowerCase())
        }
        user.save()

        TravelDestinationCountryZoneEnum destinationZone = TravelDestinationCountryZoneEnum.getById(command.destinationCountryZoneId, false)
        CountryEnum countryEnum = CountryEnum.findCountryByCode(command.country)

        TravelQuote quote = new TravelQuote(
            requestSource:                  requestSource,
            email:                          command.userEmail,
            name:                           command.userName,
            mobile:                         command.userMobile,
            startDate:                      command.startDate,
            endDate:                        command.endDate,
            travelersRelationshipTypeId:    command.travelersRelationshipTypeId,
            sourceCountry:                  command.sourceCountryCode ? Country.findByCode(command.sourceCountryCode) : null,
            destinationCountry:             Country.findById(destinationZone.country.id),
            travelDestinationCountryZone:   destinationZone,
            travelDuration:                 command.tripDuration,
            //seniorTravelersCount: travelDetails.seniorTravelersCount,
            //adultTravelersCount:  travelDetails.adultTravelersCount,
            //childTravelersCount: travelDetails.childTravelersCount,
            apiQuoteId:                     null, // we will update this if we find apiQuoteId in quotation response
            paymentStatus:                  PaymentStatusEnum.DRAFT,
            user:                           user,
            isValid:                        false, // we will update this to true if we find apiQuoteId in quotation response
            quoteCountry:                   Country.read(countryEnum.id),
            currency:                       countryEnum.currency,
            lang:                           command.lang,
            queryString:                    command.marketingTracking?.queryString,
            utmSource:                      command.marketingTracking?.utmSource,
            utmMedium:                      command.marketingTracking?.utmMedium,
            utmCampaign:                    command.marketingTracking?.utmCampaign,
            gclid:                          command.marketingTracking?.gclid,
            fbclid:                         command.marketingTracking?.fbclid
            //agreeToPrivacyPolicy: agreeToPrivacyPolicy
        )
        quote.save(failOnError: true)

        Traveler traveler
        command.travelersBirthDates.each { LocalDate birthDate ->
            traveler = new Traveler([
                quote: quote,
                birthDate: birthDate
            ])
            traveler.save(failOnError: true)
        }

        if (passportNumber && traveler) {
            traveler.identificationNumber = passportNumber
            traveler.save()
        }

        command.travellingCountries.each { Integer countryId ->
            Country country = Country.read(countryId)
            TravelQuoteCountry travelQuoteCountry = new TravelQuoteCountry()
            travelQuoteCountry.country = country
            travelQuoteCountry.quote = quote
            travelQuoteCountry.save(failOnError: true)
        }

        leadSgService.createLeadAndComparison(quote)
        return quote
    }

    @Transactional(readOnly = true)
    TravelQuoteCommand toTravelQuoteCommand(TravelQuoteV2Command travelQuoteV2Command, MarketingTrackingCommand marketingTrackingCommand) {
        TravelQuoteCommand travelQuoteCommand = new TravelQuoteCommand()
        travelQuoteCommand.requestSource = travelQuoteV2Command.source ?: RequestSourceEnum.WEB.name()
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd")
        travelQuoteCommand.startDate = dateTimeFormatter.parseLocalDate(travelQuoteV2Command.startDate)
        travelQuoteCommand.endDate = travelQuoteCommand.startDate.plusDays(travelQuoteV2Command.tripDuration)
        travelQuoteCommand.tripDuration = travelQuoteV2Command.tripDuration
        travelQuoteCommand.userName = travelQuoteV2Command.name
        travelQuoteCommand.userMobile = travelQuoteV2Command.mobile
        travelQuoteCommand.userEmail = travelQuoteV2Command.email
        travelQuoteCommand.country = "uae"

        if (travelQuoteV2Command.coverageType.equalsIgnoreCase("inbound")) {
            travelQuoteCommand.destinationCountryZoneId = TravelDestinationCountryZoneEnum.UAE.id
        } else {
            travelQuoteCommand.destinationCountryZoneId = travelQuoteV2Command.travelDestinationCountryZone.id
        }
        travelQuoteCommand.travelersRelationshipTypeId = TravelersRelationshipTypeEnum.INDIVIDUAL.id
        travelQuoteCommand.travelersBirthDates = [dateTimeFormatter.parseLocalDate(travelQuoteV2Command.dob)]

        travelQuoteCommand.marketingTracking = marketingTrackingCommand

        travelQuoteCommand
    }

    /**
     * Save comin reference for the travel quote
     * @param healthQuote
     * @param cominReference
     */
    void saveCominReference(TravelQuote travelQuote, String cominReference) {
        QuoteExtraField quoteExtraField = new QuoteExtraField()
        quoteExtraField.quoteId = travelQuote.id
        quoteExtraField.insuranceType = InsuranceTypeEnum.TRAVEL
        quoteExtraField.extraFieldCode = ExtraFieldCodeEnum.COMIN_REFERENCE
        quoteExtraField.extraFieldValue = cominReference
        quoteExtraField.save()
    }

    void saveSubSource(TravelQuote travelQuote, SubRequestSourceEnum subRequestSourceEnum) {
        QuoteExtraField quoteExtraField = new QuoteExtraField()
        quoteExtraField.quoteId = travelQuote.id
        quoteExtraField.insuranceType = InsuranceTypeEnum.TRAVEL
        quoteExtraField.extraFieldCode = ExtraFieldCodeEnum.SUB_REQUEST_SOURCE
        quoteExtraField.extraFieldValue = subRequestSourceEnum.name()
        quoteExtraField.save()
    }

    TravelQuote updateTravelQuote(TravelUpdateQuoteV2Command travelUpdateQuoteV2Command) {
        TravelQuote travelQuote = TravelQuote.get(travelUpdateQuoteV2Command.quoteId)
        travelQuote.startDate = travelUpdateQuoteV2Command.startDate
        Traveler traveler = Traveler.findByQuote(travelQuote)
        traveler.birthDate = travelUpdateQuoteV2Command.dob
        traveler.email = travelUpdateQuoteV2Command.email
        traveler.identificationNumber = travelUpdateQuoteV2Command.passportNumber
        traveler.save()

        User user = travelQuoteSgService.getOrBuildUser(travelUpdateQuoteV2Command.name, travelUpdateQuoteV2Command.email, travelUpdateQuoteV2Command.mobile)
        travelQuote.user = user
        if (!user.travelLeadStatus != LeadStatusEnum.OPEN && !user.userDetails.dnd) {
            user.changeLeadStatus(LeadStatusEnum.OPEN, user.travelLeadType, ProductTypeEnum.TRAVEL.name().toLowerCase(), null)
        }
        leadSgService.createLeads(user, ProductTypeEnum.TRAVEL)

        if (travelUpdateQuoteV2Command.normalSportActivitiesAddon) {
            CarQuoteAddon carQuoteAddon = new CarQuoteAddon()
            carQuoteAddon.travelQuote = travelQuote

        }

        travelQuote.save()
    }

    @Transactional(readOnly = true)
    List<TravelRateCommand> getRatings(TravelQuote travelQuote) {
        log.info "travelQuote.getRatings - travelQuoteId:${travelQuote.id}"

        TravelQuoteCommand quoteCommand = toTravelQuoteCommand(travelQuote)
        quoteCommand.productId = null
        quoteCommand.providerId = null

        List travelServices = [travelDubaiNationalRateService,
                               travelMetLifeRateService,
                               travelNoorRateService,
                               travelUnionRateService]

        List<TravelRateCommand> ratings = travelServices.stream()
                                                        .collect{it.getRates(quoteCommand) }
                                                        .flatten()

        ratings.sort { TravelRateCommand a, TravelRateCommand b ->
            // Compare by premium
            a.totalPrice <=> b.totalPrice
        }

        return ratings
    }

    /**
     * Get Travel Rating by provider and product
     * @param quoteCommand
     * @return
     */
    @Transactional(readOnly = true)
    TravelRateCommand getRating(TravelQuoteCommand quoteCommand) {
        log.info("travelQuote.getRating - quoteCommand:${quoteCommand}")

        Product product = Product.get(quoteCommand.productId)
        TravelRateCommand rateCommand = null

        if (product) {
            def rateService = getRatingService(product.providerId)

            if (rateService) {
                quoteCommand.providerId = product.providerId
                rateCommand =  rateService.getRate(quoteCommand)
            }
        }

        return rateCommand
    }

    void updateTravelQuote(TravelQuote travelQuote, def requestParams, TravelRateCommand rateCommand) {

        travelQuote.product = null
        travelQuote.policyPrice = null
        travelQuote.totalPrice = null
        travelQuote.policyPriceVat = null
        travelQuote.discountCode = null
        travelQuote.discount = null

        Product product = Product.read(rateCommand.productId)
        travelQuote.product = product
        travelQuote.currency = "AED"
        travelQuote.policyReference = commonUtilService.generatePolicyRef(travelQuote, product.provider)

        //Set payment status to pending
        paymentService.changePaymentStatus(travelQuote, PaymentStatusEnum.PENDING)

        //adding premium of policy issued
        travelQuote.policyPrice = rateCommand.totalPrice
        travelQuote.policyPriceVat = commonUtilService.getVATAmount(travelQuote.policyPrice)

        //Calculate price again add addon price and minus discount
        //travelQuote.totalPriceWithoutVat = travelQuote.policyPrice.subtract(discountAmount).add(homeQuote.addonPrice)

        travelQuote.totalPrice = travelQuote.policyPrice.add(travelQuote.policyPriceVat)

        travelQuote.save(failOnError: true)
    }

    @Transactional(readOnly = true)
    TravelQuoteCommand toTravelQuoteCommand(TravelQuote travelQuote) {
        TravelQuoteCommand quoteCommand = new TravelQuoteCommand()

        quoteCommand.requestSource = travelQuote.requestSource
        quoteCommand.sourceCountryCode = travelQuote.sourceCountry.code
        quoteCommand.destinationCountryZoneId = TravelDestinationCountryZoneEnum.findByCountry(travelQuote.destinationCountryId).id
        quoteCommand.travelersRelationshipTypeId = 1

        User user = travelQuote.user
        quoteCommand.userEmail = user.email
        quoteCommand.userMobile = user.mobile
        quoteCommand.userName = user.name
        quoteCommand.startDate = travelQuote.startDate
        quoteCommand.endDate = travelQuote.endDate
        travelQuote.getTravelerList().each { Traveler traveller ->
            quoteCommand.travelersBirthDates.add(traveller.birthDate)
        }
        travelQuote.getTravellingCountries().each { TravelQuoteCountry quoteCountry ->
            quoteCommand.travellingCountries.add(quoteCountry.countryId)
        }
        quoteCommand.productId = travelQuote.productId
        if (travelQuote.destinationCountry.id == travelQuote.quoteCountry.id) {
            //Inbound travelling
            quoteCommand.isOutbound = false
        }

        return quoteCommand
    }

    /**
     * Update travel quote and Travellers Details
     * @param quoteId
     * @param travelerList
     * @return
     */
    void updateQuoteAndTravellers(long quoteId, List<TravelDetails> travelerList) {
        log.info("travelQuote.updateTravellers - [travelerList:${travelerList*.id}]")

        TravelQuote quote = TravelQuote.get(quoteId)
        quote.paymentMethod = PaymentMethodEnum.CREDITCARD
        quote.save(failOnError: true)

        travelerList.each { TravelerDetails travelerDetails ->
            Traveler travelerFromDb = Traveler.get(travelerDetails.id)

            travelerFromDb.titleId = travelerDetails.title.id
            travelerFromDb.firstName = travelerDetails.firstName
            travelerFromDb.lastName = travelerDetails.lastName
            travelerFromDb.email = travelerDetails.email
            travelerFromDb.identificationNumber = travelerDetails.identificationNumber
            travelerFromDb.isPolicyHolder = travelerDetails.isPolicyHolder
            log.info("travelerDetails.gender:" + travelerDetails.gender)
            travelerFromDb.genderId = GenderEnum.getById(travelerDetails.gender).id

            travelerFromDb.save(failOnError: true)
        }
    }

    /**
     * Verify that all data is available for payment
     *
     * @param HealthQuote
     * @return
     */
    boolean isEligibleForPayment(TravelQuote quote) {
        log.debug("travelQuoteService.isEligibleForPayment - quote:${quote.id}")
        //TODO: update this one its confirmed that VAT is included or not in Travel Insurance

        boolean allDataAvailable = quote.policyPrice && quote.policyPriceVat &&
            quote.totalPrice &&
            (!quote.discount || quote.discount && quote.discountCode)

        log.info("pp:${quote.policyPrice}, ppvat:${quote.policyPriceVat}, " +
            "tp:${quote.totalPrice}, " +
            "discount:${quote.discount}, discountCode:${quote.discountCode?.id}")

        BigDecimal totalPrice = quote.policyPrice.minus(quote.discount ?: 0).plus(quote.policyPriceVat ?: 0)

        if (!allDataAvailable || totalPrice != quote.totalPrice) {
            log.error("travelQuoteService.isEligibleForPayment - quote:${quote.id}, prices not matched. " +
                "Total:${totalPrice}, quote.TotalPrice:${quote.totalPrice}, allDataAvailable:${allDataAvailable}")
            return false
        }

        if (quote.startDate.isBefore(LocalDate.now())) {
            log.error("travelQuoteService.isEligibleForPayment - Quote's start date has already passed; quote ID: ${quote.id}")
            return false
        }

        return true
    }

    /**
     * Get ratingService by provider
     * @param providerId
     * @return
     */
    private def getRatingService(Long providerId) {

        def service = null
        if (TravelDubaiNationalRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.travelDubaiNationalRateService

        } else if (TravelNoorRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.travelNoorRateService

        } else if (TravelMetLifeRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.travelMetLifeRateService

        } else if (TravelUnionRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.travelUnionRateService

        }

        return service
    }

}
