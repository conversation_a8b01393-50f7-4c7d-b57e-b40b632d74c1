package com.cover.travel

import com.safeguard.CountryEnum
import com.safeguard.Provider
import com.safeguard.travel.TravelBaseRate
import com.safeguard.travel.TravelBaseRateMultiplier
import com.safeguard.travel.TravelDestinationCountryZoneEnum
import grails.transaction.Transactional
import org.joda.time.LocalDate

@Transactional(readOnly = true)
class TravelUnionRateService {

    def travelRatingService

    public static final Integer PROVIDER_ID = 1

    public static final Integer PRODUCT_INBOUND = 2031

    List<TravelRateCommand> getRates(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand> baseRateList = []
        LocalDate purchasingDate = LocalDate.now()

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInYears(travellerDOB, purchasingDate)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId, memberAge,
                    tripLength, quoteCommand.isOutbound)

            TravelBaseRateMultiplier multiplier = TravelBaseRateMultiplier.createCriteria().list {
                eq "provider.id", quoteCommand.providerId
                /*or {
                    eq "product.id", quoteCommand.productId
                    isNull("product")
                }*/
                lte "memberAgeFrom", memberAge
                gte "memberAgeTo", memberAge
            }[0]

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            if (multiplier) {
                memberBaseRateCommandList = memberBaseRateCommandList.each { it.multiplier = multiplier.value }
            }

            baseRateList.addAll(memberBaseRateCommandList)
        }

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        return rateList
    }

    TravelRateCommand getRate(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand> baseRateList = []
        LocalDate purchasingDate = LocalDate.now()

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInYears(travellerDOB, purchasingDate)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId, memberAge,
                    tripLength, quoteCommand.isOutbound)

            TravelBaseRateMultiplier multiplier = TravelBaseRateMultiplier.createCriteria().list {
                eq "provider.id", quoteCommand.providerId
                lte "memberAgeFrom", memberAge
                gte "memberAgeTo", memberAge
            }[0]

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            if (multiplier) {
                memberBaseRateCommandList.each { it.multiplier = multiplier.value }
            }

            baseRateList.addAll(memberBaseRateCommandList)
        }

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        return rateList[0]
    }

    boolean checkProductEligibility(TravelQuoteCommand quoteCommand, Integer productId) {

        boolean isEligible = true

        return isEligible
    }

    private List<TravelRateCommand> populateRatings(TravelQuoteCommand quoteCommand, List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRateByProduct = groupBaseRateByProduct(baseRateList)

        Integer travellersCount = quoteCommand.travelersBirthDates.size()
        List<TravelRateCommand> travelRateCommandList = []

        groupedBaseRateByProduct.each { Integer productId, List<TravelBaseRateCommand> rateList ->

            TravelRateCommand rateCommand = new TravelRateCommand()

            if (checkProductEligibility(quoteCommand, productId) && rateList.size() == travellersCount) {

                rateCommand = calculateTravellersPrice(quoteCommand, rateCommand, rateList)
                rateCommand = travelRatingService.applyCovers(quoteCommand, rateCommand)

                travelRateCommandList.add(rateCommand)
            }

        }

        return travelRateCommandList
    }

    private Map<Integer, List<TravelBaseRateCommand>> groupBaseRateByProduct(List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRate = [:]

        baseRateList.each { TravelBaseRateCommand baseRate ->
            List<TravelBaseRateCommand> rateList = groupedBaseRate.get(baseRate.productId)

            if (!rateList) {
                rateList = []
            }
            rateList.add(baseRate)
            groupedBaseRate.put(baseRate.productId, rateList)
        }

        return groupedBaseRate
    }


    TravelRateCommand calculateTravellersPrice(TravelQuoteCommand quoteCommand,
                                               TravelRateCommand rateCommand, List<TravelBaseRateCommand> baseRateList) {
        TravelBaseRateCommand firstBaseRate = baseRateList.first()
        rateCommand.productId = firstBaseRate.productId.intValue()
        rateCommand.productName = firstBaseRate.product.name

        Provider provider = firstBaseRate.product.provider
        rateCommand.providerId = provider.id
        rateCommand.provider = provider.name
        rateCommand.providerImage = provider.logo

        baseRateList.each { TravelBaseRateCommand baseRate ->
            TravelRateCommand.MemberRateCommand memberRateCommand = new TravelRateCommand.MemberRateCommand()

            memberRateCommand.price = (baseRate.price * (baseRate.multiplier ?: 1)).setScale(0, BigDecimal.ROUND_UP)

            //Apply AgeRate Multiplier if applicable
            rateCommand.memberRateCommands.add(memberRateCommand)
        }

        rateCommand.totalPrice = rateCommand.memberRateCommands.sum { it.price }

        return rateCommand
    }
}
