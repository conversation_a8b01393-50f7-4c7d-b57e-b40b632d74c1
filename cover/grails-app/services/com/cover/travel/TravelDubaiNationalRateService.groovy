package com.cover.travel

import com.safeguard.CountryEnum
import com.safeguard.Provider
import com.safeguard.health.HealthRating
import com.safeguard.travel.TravelBaseRate
import com.safeguard.travel.TravelBaseRateMultiplier
import com.safeguard.travel.TravelDestinationCountryZoneEnum
import com.safeguard.travel.TravelUtils
import grails.transaction.Transactional
import org.joda.time.LocalDate

@Transactional(readOnly = true)
class TravelDubaiNationalRateService {

    def travelRatingService

    public static final Integer PROVIDER_ID = 41

    public static final Integer PRODUCT_SECURE = 2001
    public static final Integer PRODUCT_SAFE = 2002
    public static final Integer PRODUCT_FAMILY = 2003
    public static final Integer PRODUCT_SCHENGEN = 2004
    public static final Integer PRODUCT_OASIS = 2005

    public static final Integer PRODUCT_COVID_SILVER = 2007
    public static final Integer PRODUCT_COVID_GOLD = 2008
    public static final Integer PRODUCT_COVID_PLATINUM = 2009

    List<TravelRateCommand> getRates(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand>  baseRateList = []

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInNearestYear(travellerDOB)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId,
                    memberAge, tripLength, quoteCommand.isOutbound)

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            memberBaseRateCommandList.each { TravelBaseRateCommand baseRateCommand ->

                List<TravelBaseRateMultiplier> multipliers = travelRatingService
                    .getTravelRatingMultipliers(quoteCommand.providerId, baseRateCommand.productId, memberAge)

                if (multipliers.size() > 0) {
                    log.info("dubaiNational - mulitper:${multipliers[0]} for product:${baseRateCommand.productId}")
                    baseRateCommand.multiplier = multipliers[0].value
                }
            }

            baseRateList.addAll(memberBaseRateCommandList)
        }
        //log.info("TravelDnirc.getRates - baseRates:${baseRateList*.id}")

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        return rateList
    }

    TravelRateCommand getRate(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand> baseRateList = []

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInNearestYear(travellerDOB)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId, memberAge,
                    tripLength, quoteCommand.isOutbound)

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            memberBaseRateCommandList.each {TravelBaseRateCommand baseRateCommand ->

                List<TravelBaseRateMultiplier> multipliers = travelRatingService
                    .getTravelRatingMultipliers(quoteCommand.providerId, baseRateCommand.productId, memberAge)

                if (multipliers.size() > 0) {
                    baseRateCommand.multiplier = multipliers[0].value
                }
            }

            baseRateList.addAll(memberBaseRateCommandList)
        }

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        return rateList[0]
    }

    boolean checkEligibility() {

    }

    boolean checkProductEligibility(TravelQuoteCommand quoteCommand, Integer productId) {

        boolean isEligible = true

        if (productId == PRODUCT_SECURE || productId == PRODUCT_COVID_GOLD) {

            if (quoteCommand.travellingCountries) {
                quoteCommand.travellingCountries.each { Long countryId ->
                    if (countryId in TravelUtils.specialCountries*.id) {
                        isEligible = false
                    }
                }
            }

            //Member age shouldn't be more than 70
            /*if (quoteCommand) {
                //Lets handle this from baseRate
            }*/

        } else if (productId == PRODUCT_SCHENGEN) {

            //Only applicable if travelling to Schengen Zone
            if (quoteCommand.destinationCountryZoneId != TravelDestinationCountryZoneEnum.SCHENGEN.id) {
                isEligible = false
            }

        } else if (productId == PRODUCT_OASIS) {

            //Travelling for Hajj/Umrah or GCC/Arab countries
            if (!(quoteCommand.destinationCountryZoneId in [TravelDestinationCountryZoneEnum.GCC_ARAB.id,
                                                            TravelDestinationCountryZoneEnum.HAJJ_UMRAH.id])) {
                isEligible = false
            }

        } else if (productId == PRODUCT_COVID_SILVER) {
            boolean travellingToJapan = quoteCommand.travellingCountries.find { Long travellingCountryId ->
                return travellingCountryId == CountryEnum.JAPAN.id
            }

            // Eligible only if travelling to Europe, MENA AFRICA ASIA (Excl Japan)
            if (travellingToJapan == true || //Travelling to Japan
                !(quoteCommand.destinationCountryZoneId in [TravelDestinationCountryZoneEnum.SCHENGEN.id,
                                                            TravelDestinationCountryZoneEnum.GCC_ARAB.id, //Not Travelling to MENA
                                                            TravelDestinationCountryZoneEnum.HAJJ_UMRAH.id])
            ) {
                isEligible = false
            }
        }

        return isEligible
    }

    private List<TravelRateCommand> populateRatings(TravelQuoteCommand quoteCommand, List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRateByProduct = groupBaseRateByProduct(baseRateList)

        Integer travellersCount = quoteCommand.travelersBirthDates.size()
        List<TravelRateCommand> travelRateCommandList = []

        groupedBaseRateByProduct.each { Integer productId, List<TravelBaseRateCommand> rateList ->
            log.info("productId:${productId}")

            TravelRateCommand rateCommand = new TravelRateCommand()

            if (checkProductEligibility(quoteCommand, productId) && rateList.size() == travellersCount) {

                rateCommand = calculateTravellersPrice(quoteCommand, rateCommand, rateList)
                rateCommand = travelRatingService.applyCovers(quoteCommand, rateCommand)

                travelRateCommandList.add(rateCommand)
                log.info("rateCommand:${rateCommand}")

            }

        }

        return travelRateCommandList
    }

    private Map<Integer, List<TravelBaseRateCommand>> groupBaseRateByProduct(List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRate = [:]

        baseRateList.each { TravelBaseRateCommand baseRate ->
            List<TravelBaseRateCommand> rateList = groupedBaseRate.get(baseRate.productId)

            if (!rateList) {
                rateList = []
            }
            rateList.add(baseRate)
            groupedBaseRate.put(baseRate.productId, rateList)
        }

        return groupedBaseRate
    }


    TravelRateCommand calculateTravellersPrice(TravelQuoteCommand quoteCommand,
                                                 TravelRateCommand rateCommand,
                                               List<TravelBaseRateCommand> baseRateList) {

        TravelBaseRateCommand firstBaseRate = baseRateList.first()
        rateCommand.productId = firstBaseRate.productId.intValue()
        rateCommand.productName = firstBaseRate.product.name

        Provider provider = firstBaseRate.product.provider
        rateCommand.providerId = provider.id
        rateCommand.provider = provider.name
        rateCommand.providerImage = provider.logo

        baseRateList.each { TravelBaseRateCommand baseRate ->
            TravelRateCommand.MemberRateCommand memberRateCommand = new TravelRateCommand.MemberRateCommand()
            //Apply AgeRate Multiplier if applicable
            memberRateCommand.price = (baseRate.price * (baseRate.multiplier ?: 1)).setScale(0, BigDecimal.ROUND_UP)

            rateCommand.memberRateCommands.add(memberRateCommand)
        }

        rateCommand.totalPrice = rateCommand.memberRateCommands.sum { it.price }

        return rateCommand
    }
}
