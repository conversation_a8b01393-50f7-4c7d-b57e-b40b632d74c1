package com.cover.travel

import com.safeguard.CountryEnum
import com.safeguard.Provider
import com.safeguard.travel.TravelBaseRate
import com.safeguard.travel.TravelBaseRateMultiplier
import com.safeguard.travel.TravelDestinationCountryZoneEnum
import com.safeguard.travel.TravelUtils
import grails.transaction.Transactional
import org.joda.time.LocalDate

@Transactional(readOnly = true)
class TravelMetLifeRateService {

    def travelRatingService

    public static final Integer PROVIDER_ID = 60

    public static final Integer PRODUCT_SCHENGEN = 2011
    public static final Integer PRODUCT_TRAVELLER_WORLDWIDE = 2012
    public static final Integer PRODUCT_TRAVELLER_EXCL_USA_CANADA = 2013
    public static final Integer PRODUCT_PEARL_WORLDWIDE = 2014
    public static final Integer PRODUCT_PEARL_EXCL_USA_CANADA = 2015
    public static final Integer PRODUCT_FAMILY = 2016
    public static final Integer PRODUCT_HAJJ_UMRAH_BASIC = 2017
    public static final Integer PRODUCT_HAJJ_UMRAH_PLUS = 2018
    public static final Integer PRODUCT_HAJJ_UMRAH_EXTRA = 2019


    List<TravelRateCommand> getRates(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand> baseRateList = []

        //Temporary, until change the flow to ask for family / individual travelers
        //Since the live MetLife plans are only for individuals, we cannot show for more than 1 member
        if (quoteCommand.travelersBirthDates.size() > 1) {
            return baseRateList
        }

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInNearestYear(travellerDOB)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId, memberAge,
                    tripLength, quoteCommand.isOutbound)

            TravelBaseRateMultiplier multiplier = TravelBaseRateMultiplier.createCriteria().list {
                eq "provider.id", quoteCommand.providerId
                lte "memberAgeFrom", memberAge
                gte "memberAgeTo", memberAge
            }[0]

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            if (multiplier) {
                memberBaseRateCommandList.each { it.multiplier = multiplier.value }
            }

            baseRateList.addAll(memberBaseRateCommandList)
        }

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        /*
        // Pricing is inclusive of VAT, remove VAT amount
        rateList.each { TravelRateCommand rateCommand ->
            rateCommand.totalPrice = (rateCommand.totalPrice / 1.05).setScale(0, BigDecimal.ROUND_UP)
        }*/

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        return rateList
    }

    TravelRateCommand getRate(TravelQuoteCommand quoteCommand) {

        quoteCommand.providerId = PROVIDER_ID

        Integer tripLength = quoteCommand.tripLengthInDays()

        List<TravelBaseRateCommand> baseRateList = []

        quoteCommand.travelersBirthDates.each { LocalDate travellerDOB ->
            Integer memberAge = quoteCommand.ageInNearestYear(travellerDOB)

            List<TravelBaseRate> memberBaseRateList = travelRatingService
                .findApplicableRates(quoteCommand.providerId, quoteCommand.productId, memberAge,
                    tripLength, quoteCommand.isOutbound)

            TravelBaseRateMultiplier multiplier = TravelBaseRateMultiplier.createCriteria().list {
                eq "provider.id", quoteCommand.providerId
                lte "memberAgeFrom", memberAge
                gte "memberAgeTo", memberAge
            }[0]

            List<TravelBaseRateCommand> memberBaseRateCommandList =
                travelRatingService.toTravelBaseRateCommand(memberBaseRateList)

            memberBaseRateCommandList = memberBaseRateCommandList.each { it.multiplier = multiplier.value }

            baseRateList.addAll(memberBaseRateCommandList)
        }

        List<TravelRateCommand> rateList = populateRatings(quoteCommand, baseRateList)

        //Reset whatever is being updated to quoteCommand
        quoteCommand.providerId = null

        /*TravelRateCommand rateCommand = rateList[0]
        rateCommand.totalPrice = (rateCommand.totalPrice / 1.05).setScale(0, BigDecimal.ROUND_UP)*/
        return rateList[0]
    }

    boolean checkEligibility() {

    }

    boolean checkProductEligibility(TravelQuoteCommand quoteCommand, Integer productId) {

        boolean isEligible = true

        if (productId == PRODUCT_SCHENGEN) {

            //Only applicable if travelling to Schengen Zone
            if (quoteCommand.destinationCountryZoneId != TravelDestinationCountryZoneEnum.SCHENGEN.id) {
                isEligible = false
            }

        } else if (productId == PRODUCT_TRAVELLER_WORLDWIDE) {
            //Not Eligible if not travelling to USA or Canada
            /*if (!(CountryEnum.CANADA.id in quoteCommand.travellingCountries ||
                CountryEnum.USA.id in quoteCommand.travellingCountries)) {
                isEligible = false
            }*/

        } else if (productId == PRODUCT_TRAVELLER_EXCL_USA_CANADA) {

            //Not Eligible if travelling to USA or Canada
            if (quoteCommand.travellingCountries) {
                quoteCommand.travellingCountries.each { Long countryId ->
                    if (countryId in [CountryEnum.CANADA.id, CountryEnum.USA.id]) {
                        isEligible = false
                    }
                }
            }

        } else if (productId == PRODUCT_PEARL_WORLDWIDE) {
            //Not Eligible if not travelling to USA or Canada
            /*if (!(CountryEnum.CANADA.id in quoteCommand.travellingCountries ||
                CountryEnum.USA.id in quoteCommand.travellingCountries)) {
                isEligible = false
            }*/

        } else if (productId == PRODUCT_PEARL_EXCL_USA_CANADA) {

            //Not Eligible if travelling to USA or Canada
            if (quoteCommand.travellingCountries) {
                quoteCommand.travellingCountries.each { Long countryId ->
                    if (countryId in [CountryEnum.CANADA.id, CountryEnum.USA.id]) {
                        isEligible = false
                    }
                }
            }


        } else if (productId == PRODUCT_HAJJ_UMRAH_BASIC) {

            if (!(quoteCommand.destinationCountryZoneId in TravelDestinationCountryZoneEnum.HAJJ_UMRAH.id)) {
                isEligible = false
            }

        } else if (productId == PRODUCT_HAJJ_UMRAH_PLUS) {

            if (!(quoteCommand.destinationCountryZoneId in TravelDestinationCountryZoneEnum.HAJJ_UMRAH.id)) {
                isEligible = false
            }

        } else if (productId == PRODUCT_HAJJ_UMRAH_EXTRA) {

            if (!(quoteCommand.destinationCountryZoneId in TravelDestinationCountryZoneEnum.HAJJ_UMRAH.id)) {
                isEligible = false
            }

        }

        return isEligible
    }

    private List<TravelRateCommand> populateRatings(TravelQuoteCommand quoteCommand, List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRateByProduct = groupBaseRateByProduct(baseRateList)

        Integer travellersCount = quoteCommand.travelersBirthDates.size()
        List<TravelRateCommand> travelRateCommandList = []

        groupedBaseRateByProduct.each { Integer productId, List<TravelBaseRateCommand> rateList ->

            TravelRateCommand rateCommand = new TravelRateCommand()

            if (checkProductEligibility(quoteCommand, productId) && rateList.size() == travellersCount) {

                rateCommand = calculateTravellersPrice(quoteCommand, rateCommand, rateList)
                rateCommand = travelRatingService.applyCovers(quoteCommand, rateCommand)

                travelRateCommandList.add(rateCommand)
                log.info("travelMetLife.populateRatings - rateCommand:${rateCommand}")

            }

        }

        return travelRateCommandList
    }

    private Map<Integer, List<TravelBaseRateCommand>> groupBaseRateByProduct(List<TravelBaseRateCommand> baseRateList) {

        Map<Integer, List<TravelBaseRateCommand>> groupedBaseRate = [:]

        baseRateList.each { TravelBaseRateCommand baseRate ->
            List<TravelBaseRateCommand> rateList = groupedBaseRate.get(baseRate.productId)

            if (!rateList) {
                rateList = []
            }

            rateList.add(baseRate)
            groupedBaseRate.put(baseRate.productId, rateList)
        }

        return groupedBaseRate
    }


    TravelRateCommand calculateTravellersPrice(TravelQuoteCommand quoteCommand,
                                               TravelRateCommand rateCommand,
                                               List<TravelBaseRateCommand> baseRateList) {

        TravelBaseRateCommand firstBaseRate = baseRateList.first()
        rateCommand.productId = firstBaseRate.productId.intValue()
        rateCommand.productName = firstBaseRate.product.name

        Provider provider = firstBaseRate.product.provider
        rateCommand.providerId = provider.id
        rateCommand.provider = provider.name
        rateCommand.providerImage = provider.logo

        baseRateList.each { TravelBaseRateCommand baseRate ->
            TravelRateCommand.MemberRateCommand memberRateCommand = new TravelRateCommand.MemberRateCommand()

            memberRateCommand.price = (baseRate.price * (baseRate.multiplier ?: 1)).setScale(0, BigDecimal.ROUND_UP)

            //Apply AgeRate Multiplier if applicable
            rateCommand.memberRateCommands.add(memberRateCommand)
        }

        rateCommand.totalPrice = rateCommand.memberRateCommands.sum { it.price }

        return rateCommand
    }
}
