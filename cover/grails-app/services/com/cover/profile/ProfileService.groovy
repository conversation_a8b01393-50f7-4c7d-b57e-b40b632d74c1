package com.cover.profile

import com.cover.api.AuthenticationCommand
import com.cover.api.CreateProfileCommand
import com.cover.api.ResetPasswordCommand
import com.cover.api.UpdateProfileCommand
import com.cover.api.UserProfile
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.RoleEnum
import com.safeguard.User
import com.safeguard.UserDetails
import com.safeguard.base.Lead
import com.safeguard.security.Role
import com.safeguard.security.UserRole
import grails.plugin.springsecurity.rest.token.AccessToken
import grails.transaction.Transactional
import grails.util.Environment
import org.joda.time.LocalDateTime
import org.pac4j.core.context.WebContext

import java.nio.charset.StandardCharsets
import java.security.MessageDigest

@Transactional
class ProfileService {

    private String _salt = "OIWQEURFNSDFKG@#"

    def facebookService
    def googleService
    def grailsApplication
    def messageSource
    def springSecurityService
    def tokenGenerator
    def tokenStorageService
    def utilService
    def leadSgService

    /**
     * Create/update existing user with facebookId and perform login
     *
     * @param userProfile
     * @return
     */
    def loginSocialUser(UserProfile userProfile, Locale locale, Country country) {
        log.info("profile.loginSocialUser - entering with [userProfile:${userProfile}]")

        User usr = User.findByEmail(userProfile.email)
        UserDetails userDetails

        if (!usr) {
            usr = new User(name: userProfile.name, email: userProfile.email, userDetails: new UserDetails())
            usr.password = springSecurityService.encodePassword(new LocalDateTime().toString(), _salt)
            usr.enabled = true
            usr.mobile = '99999999'
            usr.country = country
            usr.save(failOnError: true)
            userDetails = usr.userDetails
            leadSgService.createLeads(usr, null)
        } else {
            if (!usr.enabled) {
                usr.enabled = true
                usr.save(failOnError: true)
            }
            userDetails = UserDetails.findOrCreateByUser(usr)
        }
        assignCustomerRole(usr)

        if (userProfile.medium == "facebook" && userDetails.facebookId != userProfile.facebookId) {
            userDetails.facebookId = userProfile.facebookId
            userDetails.emailVerified = true
            userDetails.save(failOnError:true)

        } else if (userProfile.medium == "google" && userDetails.googleId != userProfile.googleId) {
            userDetails.googleId = userProfile.googleId
            userDetails.emailVerified = true
            userDetails.save(failOnError:true)
        }

        def loginMap = [:]

        if (usr.isAccountLocked()) {
            loginMap.success = false
            loginMap.message = messageSource.getMessage("user.account.locked", [].toArray(), locale)

        } else {
            //Perform login
            loginMap = performLogin(usr.email)
        }

        return loginMap
    }

    /**
     * Perform login against any user email
     *
     * @param emailAddress
     * @return
     */
    def performLogin (String emailAddress) {
        log.info("profile.performLogin - entering with [emailAddress:${emailAddress}]")

        springSecurityService.reauthenticate(emailAddress)

        def principal = springSecurityService.principal

        AccessToken accessToken = tokenGenerator.generateAccessToken(principal)

        tokenStorageService.storeToken(accessToken.accessToken, springSecurityService.principal )

        def authorities = []
        principal.authorities.each { authority ->
            authorities.add(authority.authority)
        }

        def loginMap = [:]
        loginMap.success = true
        loginMap.username = principal.username
        loginMap.access_token = accessToken.accessToken

        loginMap.roles = authorities

        return loginMap
    }

    /**
     * Get user profile detail from Social media against the token
     * @param command
     * @param context
     * @return
     */
    def getUserProfile (AuthenticationCommand command, WebContext context) {
        UserProfile userProfile

        if (command.medium == 'facebook') {
            userProfile = facebookService.userInformation(command.accessToken, context)
        } else if (command.medium == 'google') {
            userProfile = googleService.userInformation(command.accessToken, context)
        }

        return userProfile
    }

    def create(CreateProfileCommand cmd) {

        User usr = User.findByEmail(cmd.email)

        if (usr) {
            return [
                responseStatus: 409,
                userId        : null,
                message       : "A profile for that email already exists"
            ]
        }

        CountryEnum countryEnum = utilService.convertToCountry(cmd.country)
        Country country = Country.read(countryEnum.id)

        usr = new User(name: cmd.name, email: cmd.email, mobile: cmd.mobile,
            userDetails: new UserDetails(), country: country)

        usr.password = springSecurityService.encodePassword(cmd.password, _salt)

        usr.save()
        leadSgService.createLeads(usr, null)
        assignCustomerRole(usr)

        sendVerificationEmail(usr)

        return [
            responseStatus: 200,
            userId        : usr.id,
            message       : "Success"
        ]
    }

    /**
     * Assigns customer role to user
     * @param user
     */
    private assignCustomerRole(User user) {
        if (!user.hasRole(RoleEnum.ROLE_CUSTOMER)) {
            Role customerRole = Role.findByAuthority(RoleEnum.ROLE_CUSTOMER.name())
            UserRole userRole = new UserRole(user: user, role: customerRole)
            userRole.save()
        }
    }

    def update(User user, UpdateProfileCommand cmd) {

        user.name = cmd.name ?: user.name
        user.email = cmd.email ?: user.email
        user.mobile = cmd.mobile ?: user.mobile
        user.nationality = Country.get(cmd.nationalityId) ?: user.nationality
        user.dob = cmd.dob ?: user.dob

        if (cmd.password) {
            user.password = springSecurityService.encodePassword(cmd.password, _salt)
        }

        UserDetails userDetails = UserDetails.findOrCreateByUser(user)
        userDetails.weeklyNewsletter = cmd.weeklyNewsletter
        userDetails.emailVerified = cmd.emailVerified ?: userDetails.emailVerified
        userDetails.mobileVerified = cmd.mobileVerified ?: userDetails.mobileVerified
        userDetails.save(flush: true)

        user.save(flush: true)

        return user
    }

    def sendVerificationEmail(User user) {
        def host = grailsApplication.config.getProperty("cover.domain")
        def hash = getVerifyEmailHash(user)
        notify AsyncEventConstants.SEND_VERIFICATION_EMAIL, [user: user, link: "$host/v1/profile/verify-email/?email=$user.email&hash=$hash"]
    }

    def verifyEmail(User usr, String hash) {
        if (usr.userDetails?.emailVerified) {
            return "User has already been activated"
        }

        String emailHash = URLEncoder.encode(hash, "UTF-8")
        String userHash = getVerifyEmailHash(usr)
        if (emailHash == userHash) {
            usr.enabled = true

            //make sure we have user details
            usr.userDetails = usr.userDetails ?: new UserDetails()

            usr.userDetails.emailVerified = true
            usr.save()
            return "Congratulations ${usr.name}, your account has been activated"
        } else {
            log.warn("profileService.verifyEmail - Email hash not matching: generated hash: ${userHash} ; Hash from email: ${emailHash}")
        }

        return "There was an error activating this account"
    }

    static def getVerifyEmailHash(User usr) {
        def e = usr.email + usr.id.toString()
        def hash = MessageDigest.getInstance("SHA-256").digest(e.getBytes(StandardCharsets.UTF_8))
        def base64 = Base64.getEncoder().encodeToString(hash)
        base64 = base64.replaceAll("[%/?=]", "")
        return URLEncoder.encode(base64, "UTF-8")
    }

    Boolean sendForgotPasswordEmail(String email) {
        User user = User.findByEmail(email)
        if(!user) {
            return false
        }

        if (user.shouldSendEmail() &&
            (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(email))) {
            String hash = getVerifyEmailHash(user)

            notify AsyncEventConstants.SEND_FORGOT_PASSWORD_EMAIL, [user: user, hash: hash]
            return true
        } else {
            log.info(".sendForgotPasswordEmail - email:$email is not allowed to be sent")
        }

        return false

    }

    def verifyForgotPasswordEmail(String email, String hash) {
        User user = User.findByEmail(email)
        if(!user) {
            return null
        }

        String userHash = getVerifyEmailHash(user)
        String hashEmail = URLEncoder.encode(hash, "UTF-8")
        if(hashEmail == userHash) {
            return user
        } else {
            log.warn("profileService.verifyForgotPasswordEmail - Email hash not matching: generated hash: ${userHash} ; Hash from email: ${hashEmail}")
        }
        return null
    }

    /**
     * Reset password and store last password change date
     * @param resetPasswordCommand
     * @return
     */
    User resetPassword(ResetPasswordCommand resetPasswordCommand) {
        User user = User.findByEmail(resetPasswordCommand.email)
        user.password = springSecurityService.encodePassword(resetPasswordCommand.password)
        user.enabled = true
        user.save(flush: true)
    }

    void expirePassword(Long userId){
        User user = User.findById(userId)
        user.passwordExpired = true
        user.save()
    }
}
