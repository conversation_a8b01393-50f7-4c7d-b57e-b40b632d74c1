package com.cover.gadget

import com.safeguard.AsyncEventConstants
import com.safeguard.User
import com.safeguard.gadget.GadgetQuote

class GadgetQuoteService {
    def saveQuote(def params) {
        GadgetQuote gadgetQuote = new GadgetQuote()
        gadgetQuote.name = params.name
        gadgetQuote.email = params.email
        gadgetQuote.phone = params.phone
        gadgetQuote.gadget_type = params.gadget_type


        User user = User.findByEmail(params.email);

        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.phone
            gadgetQuote.user = newUser
        } else {
            user.name = params.name
            user.mobile = params.phone
            gadgetQuote.user = user

        }


        gadgetQuote.save()

        notify AsyncEventConstants.GADGET_QUOTE_CREATED, [quoteId: gadgetQuote.getId(), gadgetType: gadgetQuote.getGadget_type()]
    }

}
