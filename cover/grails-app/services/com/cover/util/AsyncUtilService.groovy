package com.cover.util

import com.safeguard.AsyncEventConstants
import com.safeguard.CountryEnum
import com.safeguard.User
import com.safeguard.autodialer.CallDispositionLog
import com.safeguard.car.vehicle.MissingTrimsRta
import grails.transaction.Transactional
import reactor.bus.Event
import reactor.spring.context.annotation.Consumer
import reactor.spring.context.annotation.Selector

@Consumer
@Transactional
class AsyncUtilService {

    def commonUtilService

    /**
     * Store rta model id for missing trims
     *
     * @param rtaModelId
     * @param year
     * @return
     */
    @Selector(AsyncEventConstants.STORE_MISSING_TRIMS_FOR_RTA_MODEL)
    def storeMissingTrimsAgainstRtaModel(Event event) {

        MissingTrimsRta missingTrimsRta = new MissingTrimsRta()
        missingTrimsRta.rtaModelId = event.data.rtaModelId
        missingTrimsRta.year = event.data.year

        missingTrimsRta.save()
    }

    /**
     * Update lead against mobile no. in CallDispositionLog
     * @param event
     */
    @Selector(AsyncEventConstants.LEAD_MOBILE_MAPPING)
    void updateLeadInCallDispositionLog(Event event) {
        String phone = event.data.phone
        Long userId = event.data.userId
        User user = userId ? User.read(userId) : null

        if (!user || !phone) {
            return
        }

        CountryEnum countryEnum = CountryEnum.findCountryById(user.countryId.toInteger())
        phone = commonUtilService.extractPhoneNumber(phone, countryEnum)
        List<CallDispositionLog> callDispositionLogs = CallDispositionLog.findAllByPhoneAndLeadIsNull(phone)
        if (callDispositionLogs) {
            callDispositionLogs*.lead = user
            callDispositionLogs*.save()
            log.info("asyncUtilService.updateLeadInCallDispositionLog - Mapped ${user.email} lead with phone ${phone}")
        }
    }
}
