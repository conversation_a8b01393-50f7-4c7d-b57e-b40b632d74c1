package com.cover.util

import com.safeguard.ArabicSalesAssignmentRule
import com.safeguard.CountryEnum
import com.safeguard.CoverPreferenceEnum
import com.safeguard.HealthSalesAssignmentRule
import com.safeguard.ProductTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.assignmentRules.AdibSalesAssignmentRule
import com.safeguard.assignmentRules.ArabicLostSalesAssignmentRule
import com.safeguard.assignmentRules.EmiratiHighValueAssignmentRule
import com.safeguard.assignmentRules.EmiratiSalesAssignmentRule
import com.safeguard.assignmentRules.NbSalesAssignmentRule
import com.safeguard.assignmentRules.PetSalesAssignmentRule
import com.safeguard.assignmentRules.RenewalLostSalesAssignmentRule
import com.safeguard.base.Lead
import com.safeguard.life.LifeLostSalesAssignmentRule
import com.safeguard.HighValueSalesAssignmentRule
import com.safeguard.HomeSalesAssignmentRule
import com.safeguard.LeadStatusEnum
import com.safeguard.LeadType
import com.safeguard.LifeSalesAssignmentRule
import com.safeguard.LostSalesAssignmentRule
import com.safeguard.RenewalSalesAssignmentRule
import com.safeguard.SalesAssignmentRule
import com.safeguard.ThirdPartySalesAssignmentRule
import com.safeguard.User
import com.safeguard.health.HealthLostSalesAssignmentRule
import com.safeguard.health.HealthRenewalSalesAssignmentRule
import com.safeguard.life.LifeRenewalSalesAssignmentRule
import com.safeguard.util.ConfigurationService
import grails.transaction.Transactional
import com.safeguard.UserDetails

@Transactional
class CrmService {

    def grailsApplication
    def renewalService
    def leadSgService
    def configurationService

    @Transactional
    handleCrmEvents(Long userId, String insuranceType, def quote = null) {
        log.info("crmService.handCrmChanges ${userId}")

        User user = User.findById(userId)
        UserDetails userDetails = UserDetails.findOrCreateByUser(user)

        BigDecimal highValueCarLimit = grailsApplication.config.lead.highValue.car.limit
        BigDecimal emiratiHighValueCarLimit = grailsApplication.config.lead.emirati.highValue.car.limit
        String methodName = "crmService.handleCrmChanges"

        def sp
        switch(insuranceType) {
            case 'car':
                sp = 'salesPerson'
                break
            case 'home':
                sp = 'homeSalesPerson'
                break
            case 'health':
                sp = 'healthSalesPerson'
                break
            case 'life':
                sp = 'lifeSalesPerson'
                break
            case 'pet':
                sp = 'petSalesPerson'
                break
            default:
                sp = 'salesPerson'
        }

        if (user[sp]) {

            def sar
            def lar
            def rar
            def aar
            def rlar
            def hvar
            def tpar
            def esar
            def ehvsar
            def wsar

            switch(insuranceType) {
                case 'car':

                    Lead lead = user.phoneLead
                    if (lead.countryId == CountryEnum.UAE.id) {
                        String marketingAgentEmail = configurationService.getValue(ConfigurationService["MARKETING_${user.leadType.name()}_LEADS_AGENT_EMAIL"])
                        User marketingAgent = User.findByEmail(marketingAgentEmail)
                        if (lead.salesPerson == marketingAgent) {
                            break
                        }

                        if (lead.leadType == LeadType.NORMAL && quote.insuredValue >= highValueCarLimit &&
                            (quote.insuredValue < emiratiHighValueCarLimit || lead.nationalityId != CountryEnum.UAE.id) &&
                            HighValueSalesAssignmentRule.count != 0) {
                            hvar = HighValueSalesAssignmentRule.findBySalesPerson(lead.salesPerson)
                            if (!hvar || !lead.salesPerson.enabled) {
                                log.debug("${user.salesPerson.email} is inactive un-assigning lead.")
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                            break
                        }

                        if (quote.requestSource == RequestSourceEnum.ADIB && lead.salesPerson && AdibSalesAssignmentRule.count != 0) {
                            wsar = AdibSalesAssignmentRule.findBySalesPerson(lead.salesPerson)

                            if (!wsar || !lead.salesPerson?.enabled) {
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                        }

                        if (quote.requestSource == RequestSourceEnum.NATIONALBONDS && lead.salesPerson && NbSalesAssignmentRule.count != 0) {
                            wsar = NbSalesAssignmentRule.findBySalesPerson(lead.salesPerson)

                            if (!wsar || !lead.salesPerson?.enabled) {
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                        }

                        if (user.nationalityId == CountryEnum.UAE.id &&
                            quote.insuredValue >= emiratiHighValueCarLimit &&
                            EmiratiHighValueAssignmentRule.count != 0 ) {
                            ehvsar = EmiratiHighValueAssignmentRule.findBySalesPerson(lead.salesPerson)

                            if (!ehvsar || !lead.salesPerson?.enabled) {
                                log.debug("${lead.salesPerson?.email} is inactive un-assigning lead")
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                            break
                        }

                        if (lead.nationalityId == CountryEnum.UAE.id &&
                            lead.leadType in [LeadType.NORMAL, LeadType.ACTIVE_LOST, LeadType.LOST] &&
                            EmiratiSalesAssignmentRule.count !=0) {
                            esar = EmiratiSalesAssignmentRule.findBySalesPerson(lead.salesPerson)

                            if (!esar && lead.salesPerson?.enabled && lead.user.leadStatus == LeadStatusEnum.OPEN) {
                                break
                            }

                            if (!esar || !lead.salesPerson.enabled) {
                                log.debug("${user.salesPerson?.email} is inactive un-assigning lead")
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                            break
                        }

                        if (lead.leadType == LeadType.NORMAL && quote.coverPreference == CoverPreferenceEnum.THIRD_PARTY
                            && ThirdPartySalesAssignmentRule.count != 0) {
                            tpar = ThirdPartySalesAssignmentRule.findBySalesPerson(lead.salesPerson)

                            if (!tpar || !lead.salesPerson.enabled) {
                                log.debug("${user.salesPerson.email} is inactive un-assigning lead.")
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                            break
                        }

                        if (lead.leadType == LeadType.NORMAL && lead.nationality && lead.nationality.isArabic
                            && ArabicSalesAssignmentRule.count != 0) {
                            aar = ArabicSalesAssignmentRule.findBySalesPerson(lead.salesPerson)
                            if (!aar || !lead.salesPerson.enabled) {
                                log.debug("${lead.salesPerson.email} is in active un-assigning lead.")
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                            break
                        }

                        if (lead.leadType == LeadType.LOST && lead.nationality && lead.nationality.isArabic
                            && ArabicLostSalesAssignmentRule.count != 0) {
                            aar = ArabicLostSalesAssignmentRule.findBySalesPerson(lead.salesPerson)
                            if (!aar || !lead.salesPerson.enabled) {
                                log.debug("${user.salesPerson.email} is in active un-assigning lead.")
                                user.assignTo(null, methodName, ProductTypeEnum.CAR)
                            }
                            break
                        }
                    }

                    if (lead.leadType == LeadType.NORMAL || lead.leadType == LeadType.ACTIVE_LOST ) {
                        sar = SalesAssignmentRule.findBySalesPerson(lead.salesPerson)
                    } else if (lead.leadType == LeadType.LOST) {
                        lar = LostSalesAssignmentRule.findBySalesPerson(lead.salesPerson)
                    } else if (lead.leadType == LeadType.RENEWAL_LOST) {
                        rlar = RenewalLostSalesAssignmentRule.findBySalesPerson(lead.salesPerson)
                    } else if (lead.leadType == LeadType.RENEWAL) {
                        rar = RenewalSalesAssignmentRule.findBySalesPerson(lead.salesPerson)
                    }

                    if (!(sar || lar || rar || rlar || wsar) || !lead.salesPerson?.enabled) {
                        log.debug("${user.salesPerson?.email} is in active un-assigning lead.")
                        user.assignTo(null, methodName, ProductTypeEnum.CAR)
                    }

                    lead.save()
                    break
                case 'home':
                    sar = HomeSalesAssignmentRule.findBySalesPerson(user.homeSalesPerson)
                    if (!sar || !user.homeSalesPerson.enabled) {
                        log.debug("${user.homeSalesPerson.email} is in active un-assigning lead.")
                        user.assignTo(null, methodName, ProductTypeEnum.HOME)
                    }
                    break
                case 'health':
                    if (user.healthLeadType == LeadType.NORMAL || user.healthLeadType == LeadType.ACTIVE_LOST) {
                        sar = HealthSalesAssignmentRule.findBySalesPerson(user.healthSalesPerson)
                    } else if (user.healthLeadType == LeadType.LOST) {
                        lar = HealthLostSalesAssignmentRule.findBySalesPerson(user.healthSalesPerson)
                    } else if (user.healthLeadType == LeadType.RENEWAL) {
                        rar = HealthRenewalSalesAssignmentRule.findBySalesPerson(user.healthSalesPerson)
                    }

                    if (!(sar || rar || lar) || !user.healthSalesPerson.enabled) {
                        log.debug("${user.healthSalesPerson.email} is in active un-assigning lead.")
                        user.assignTo(null, methodName, ProductTypeEnum.HEALTH)
                    }
                    break
                case 'life':
                    if (user.lifeLeadType == LeadType.NORMAL || user.lifeLeadType == LeadType.ACTIVE_LOST) {
                        sar = LifeSalesAssignmentRule.findBySalesPerson(user.lifeSalesPerson)
                    } else if (user.lifeLeadType == LeadType.LOST) {
                        lar = LifeLostSalesAssignmentRule.findBySalesPerson(user.lifeSalesPerson)
                    } else if (user.lifeLeadType == LeadType.RENEWAL) {
                        rar = LifeRenewalSalesAssignmentRule.findBySalesPerson(user.lifeSalesPerson)
                    }

                    if (!(sar || rar || lar) || !user.lifeSalesPerson.enabled) {
                        log.debug("${user.lifeSalesPerson.email} is in active un-assigning lead.")
                        user.assignTo(null, methodName, ProductTypeEnum.LIFE)
                    }
                    break
                case 'pet':
                    sar = PetSalesAssignmentRule.findBySalesPerson(user.petSalesPerson)
                    if (user.petSalesPerson && (!sar || !(user.petSalesPerson?.enabled))) {
                        user.assignTo(null, methodName, ProductTypeEnum.PET)
                    }

                    break
                default:
                    sar = SalesAssignmentRule.findBySalesPerson(user.salesPerson)
                    lar = LostSalesAssignmentRule.findBySalesPerson(user.salesPerson)
                    rar = RenewalSalesAssignmentRule.findBySalesPerson(user.salesPerson)
                    if (!(sar || lar || rar) || !user.salesPerson.enabled) {
                        log.debug("${user.salesPerson.email} is in active un-assigning lead.")
                        user.assignTo(null, methodName, ProductTypeEnum.CAR)
                    }
            }
        } else if (insuranceType == 'car' && !user[sp] && user.leadStatus != LeadStatusEnum.OPEN && user.leadType != LeadType.NORMAL){
            if (Boolean.valueOf(configurationService.getValue(ConfigurationService["ENABLE_MARKETING_${user.leadType.name()}_LEADS_ASSIGNMENT"]))) {
                String marketingAgentEmail = configurationService.getValue(ConfigurationService["MARKETING_${user.leadType.name()}_LEADS_AGENT_EMAIL"])
                User marketingAgent = User.findByEmail(marketingAgentEmail)
                user.assignTo(marketingAgent, methodName, ProductTypeEnum.CAR)
            }
        }

        //If a DND lead is creating a quote, then it should not open
        if (userDetails.dnd || user.phoneLead?.isDnd) {
            return null
        }

        user.changeLeadStatus(LeadStatusEnum.OPEN, user[User.leadTypeCol(insuranceType)], insuranceType, user)
        user.save()

    }

}
