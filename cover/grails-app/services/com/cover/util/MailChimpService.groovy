package com.cover.util

import com.safeguard.ProductTypeEnum

import com.safeguard.car.vehicle.Model
import grails.converters.JSON
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import grails.util.Environment
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.springframework.http.HttpStatus

import java.security.MessageDigest
/**
 * Service class encapsulate mail chimp syncing data operations.
 */
@Transactional
class MailChimpService {

    def grailsApplication

    /**
     * Subscribe user with mailchimp for given firstLicenseCountry.
     *
     * @param email
     * @param lead
     * @param productType
     */
    def subscribe(String email, def lead=null, ProductTypeEnum productType) {

        String list = 'd61f32600f'

        if(Environment.current == Environment.PRODUCTION) {
            list = 'c90dd6c6a5'
        }

        mergeMember(email, list, lead, productType)
    }

    /**
     * Sync user with mail chimp using PUT request to create or update an user's data against passed unique list.
     *
     * @param emailAddress - email address of user
     * @param list - list user belong to
     * @param leadData - Map of lead data
     * @param productType - Type of product (Car, home or health)
     */
    def mergeMember(String emailAddress, String list, def quote = null, ProductTypeEnum productType) {
        log.debug(".mergeMember Entering with parameters [emailAddress:$emailAddress, list:$list, quote:$quote]")

        String MAIL_CHIMP_ENDPOINT = grailsApplication.config.mailchimp.endpoint
        String API_KEY = grailsApplication.config.mailchimp.apiKey
        String SUBSCRIBED = 'subscribed'

        def hash = MessageDigest.getInstance('MD5').digest(emailAddress.bytes).encodeHex().toString()
        String memebersEndpoint = "${MAIL_CHIMP_ENDPOINT}/lists/${list}/members/$hash"

        Map memberData = ['email_address': emailAddress, status: SUBSCRIBED]

        if (quote) {
            Map mergeFields = ['merge_fields': [
                'NAME':  quote.name ,
                'TELEPHONE' : quote.mobile,
                'USERID': quote.user.id,
            ]]
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("MM/dd/yyyy")

            switch (productType) {
            //Prepare merge fields json
                case ProductTypeEnum.CAR :
                    Model model = quote.model
                    model.loadTransients("en")
                    mergeFields.merge_fields.putAll([
                                                    'NATIONALIT'    : quote.nationality.nameEn,
                                                    'MAKE'          : model.make.nameEn,
                                                    'MODEL'         : model.getName("en"),
                                                    'YEAR'          : quote.year,
                                                    'NEWCAR'        : Boolean.toString(quote.isBrandNew),
                                                    'FIRSTREG'      : quote.firstRegistrationDate.toString(dateTimeFormatter) ,
                                                    'REGCITY'       : quote.registrationCity ? quote.registrationCity.nameEn : "",
                                                    'INSUREDVAL'    : quote.insuredValue,
                                                    'NONGCC'        : Boolean.toString(quote.isNonGcc),
                                                    'TPLONLY'       : quote.isThirdParty ? Boolean.toString(quote.isThirdParty) : "",
                                                    'AGENCYREP'     : Boolean.toString(quote.isAgencyRepair),
                                                    'EXPIREDPOL'    : Boolean.toString(quote.isExpiredPolicy),
                                                    'LICENSECTY'    : quote.licenseCountry.nameEn,
                                                    'INTDRIVEX'     : quote.internationalExperience.nameEn,
                                                    'LOCDRIVEX'     : quote.localExperience.nameEn,
                                                    'CARPOLDATE'    : quote.policyStartDate.toString(dateTimeFormatter),
                                                    'HASCLAIM'      : quote.hasClaim ? Boolean.toString(quote.hasClaim) : "",
                                                    'LASTYEARCL'    : quote.claimsInTheLastYear ?: 0,
                                                    'DOB'           : quote.dob.toString(dateTimeFormatter),
                                                    'COUNTRY'       : quote.quoteCountry.nameEn
                        ])
                    break
                case ProductTypeEnum.HOME :
                    mergeFields.merge_fields.putAll([
                                                    'HOMCONTVAL'    : quote.homeContent?.value,
                                                    'BELONGINGS'    : quote.homePersonalBelonging?.value,
                                                    'HOMPOLDATE'    : quote.policyStartDate.toString(dateTimeFormatter),
                                                    'BUILDVALUE'    : quote.buildingValue ?: 0,
                                                    'HOMCAT'        : quote.homeInsuranceCategory.nameEn
                                    ])
                    break
                case ProductTypeEnum.HEALTH :
                    mergeFields.merge_fields.putAll([
                                                    'CITY'      : quote.city.nameEn,
                                                    'SALGT4K'   : quote.salaryOver4k == null ? null : Boolean.toString(quote.salaryOver4k)
                                    ])
                    break
            }

            memberData << mergeFields
        }

        RestBuilder rest = new RestBuilder()
        def json = memberData as JSON

        log.debug(".mergeMember Posting json=$json to mailchimp api")

        // The reason for sending PUT http method is:
        // It creates new contact in case it doesn't exist otherwise just update contact
        def resp = rest.put(memebersEndpoint) {
            contentType 'application/json'
            auth('does-not-matter', API_KEY)
            body json
        }

        log.info(".mergeMember Mail chimp returned status:${resp.statusCode} with json:${resp.json}")

        if (resp.statusCode != HttpStatus.OK) {
            log.warn('.mergeMember Unable to sync: ' +
                    "${['email': emailAddress, 'list': list, 'json': json, 'endpoint' : memebersEndpoint]} respone:${resp.json}")
        } else {
            log.debug(".mergeMember Email [$emailAddress] synced successfully with mail chimp")
        }
    }

}
