package com.cover.util

import com.safeguard.ProductTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import grails.transaction.Transactional

@Transactional
class DocumentService {

    def getDocuments(String productType, Long quoteId) {

        def policies = [:]

        switch (productType.toUpperCase()) {
            case ProductTypeEnum.CAR.name():
                policies = CarQuote.get(quoteId).documents
                break
            case ProductTypeEnum.HEALTH.name():
                policies = HealthQuote.get(quoteId).documents
                break
            case ProductTypeEnum.HOME.name():
                policies = HomeQuote.get(quoteId).documents
                break
        }

        return policies
    }

    def uploadDocument(def file) {
        //todo
    }
}
