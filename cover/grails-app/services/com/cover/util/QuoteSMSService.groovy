package com.cover.util

import com.safeguard.AsyncEventConstants
import com.safeguard.CountryEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.car.CarQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.util.AESCryption
import com.safeguard.util.ConfigurationService
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.converters.JSON
import grails.plugins.rest.client.RestBuilder
import grails.plugins.rest.client.RestResponse
import org.grails.plugins.web.taglib.ApplicationTagLib
import grails.transaction.Transactional
import grails.web.mapping.LinkGenerator
import org.springframework.http.HttpStatus
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import reactor.bus.Event
import reactor.spring.context.annotation.Consumer
import reactor.spring.context.annotation.Selector

@Transactional(readOnly = true)
@Consumer
class QuoteSMSService {

    def configurationService
    def grailsApplication
    def messageSource
    def smsService
    LinkGenerator grailsLinkGenerator

    /**
     * Send Car Quote Purchase SMS
     * @param event
     * @return
     */
    @Selector(AsyncEventConstants.CAR_QUOTE_PURCHASED)
    def sendCarQuotePurchaseSMS(Event event) {
        def isActive = configurationService.getValue(ConfigurationService.SMS_CAR_PURCHASE_ENABLED)
        if (isActive && !Boolean.valueOf(isActive)) return

        CarQuote carQuote = CarQuote.get(event.data.quoteId)

        log.info("smsService.sendCarQuotePurchaseSMS - Sending SMS for Purchase of Car Quote ${carQuote.id}")

        if(carQuote.paymentMethod != PaymentMethodEnum.COD) {
            String mobile = carQuote.mobile

            CountryEnum countryEnum = CountryEnum.findCountryById(carQuote.quoteCountry.id)

            Locale locale = new Locale(event.data.lang)

            Boolean isSMSSent = sendCarPurchaseSMS(mobile, carQuote, locale, countryEnum, event.data)

            if (isSMSSent) {
                log.info("quoteSMSService.sendCarQuotePurchaseSMS - Quote Purchase SMS sent to $mobile for Quote ${carQuote.id}")
            } else {
                log.warn("quoteSMSService.sendCarQuotePurchaseSMS - Failed to send SMS to $mobile for Quote ${carQuote.id}")
            }
        } else {
            log.info("quoteSMSService.sendCarQuotePurchaseSMS - SMS will not be sent for Car Quote ${carQuote.id} as payment method is COD")
        }
    }

    /**
     * Send Home Quote Purchase SMS
     * @param event
     * @return
     */
    @Selector(AsyncEventConstants.EMAIL_HOME_QUOTE_PURCHASED)
    def sendHomeQuotePurchaseSMS(Event event) {
        def isActive = configurationService.getValue(ConfigurationService.SMS_HOME_PURCHASE_ENABLED)
        if (isActive && !Boolean.valueOf(isActive)) return

        HomeQuote homeQuote = HomeQuote.get(event.data.quoteId)

        log.info("smsService.sendHomeQuotePurchaseSMS - Sending SMS for Purchase of Car Quote ${homeQuote.id}")

        if(homeQuote.paymentMethod != PaymentMethodEnum.COD) {
            String mobile = homeQuote.mobile

            Locale locale = new Locale(event.data.lang)
            Boolean isSMSSent = sendHomePurchaseSMS(mobile, homeQuote.policyReference, locale, CountryEnum.UAE)

            if (isSMSSent) {
                log.info("quoteSMSService.sendHomeQuotePurchaseSMS - Quote Purchase SMS sent to $mobile for Quote ${homeQuote.id}")
            } else {
                log.warn("quoteSMSService.sendHomeQuotePurchaseSMS - Failed to send SMS to $mobile for Quote ${homeQuote.id}")
            }

        } else {
            log.info("quoteSMSService.sendHomeQuotePurchaseSMS - SMS will not be sent for Home Quote ${homeQuote.id} as payment method is COD")
        }
    }


    /**
     * Send Health Quote Purchase SMS
     * @param event
     * @return
     */
    @Selector(AsyncEventConstants.HEALTH_QUOTE_PURCHASED)
    def sendHealthQuotePurchaseSMS(Event event) {
        def isActive = configurationService.getValue(ConfigurationService.SMS_HEALTH_PURCHASE_ENABLED)
        if (isActive && !Boolean.valueOf(isActive)) return

        HealthQuote healthQuote = HealthQuote.get(event.data.quoteId)

        log.info("smsService.sendHealthQuotePurchaseSMS - Sending SMS for Purchase of Car Quote ${healthQuote.id}")

        if(healthQuote.paymentMethod != PaymentMethodEnum.COD) {
            String mobile = healthQuote.mobile


            String lang = healthQuote.lang ?: "en"
            Locale locale = new Locale(lang)
            Boolean isSMSSent = sendHealthPurchaseSMS(mobile, healthQuote.policyReference, locale, CountryEnum.UAE)

            if (isSMSSent) {
                log.info("quoteSMSService.sendHealthQuotePurchaseSMS - Quote Purchase SMS sent to $mobile for Quote ${healthQuote.id}")
            } else {
                log.warn("quoteSMSService.sendHealthQuotePurchaseSMS - Failed to send SMS to $mobile for Quote ${healthQuote.id}")
            }

        } else {
            log.info("quoteSMSService.sendHealthQuotePurchaseSMS - SMS will not be sent for Health Quote ${healthQuote.id} as payment method is COD")
        }
    }

    /**
     * Send Purchase SMS For Car
     * @param mobile
     * @param carQuote
     * @param locale
     * @param countryEnum
     * @return
     */
    Boolean sendCarPurchaseSMS(String mobile, CarQuote carQuote, Locale locale, CountryEnum countryEnum, def eventData)
    {
        def hash = AESCryption.encrypt(carQuote.id.toString())
        String url

        if(eventData.brand && eventData.serverName){
            url= grailsLinkGenerator.link(controller: 'policy', action: 'uploadPolicyDocs', params: [id: hash])
            String baseUrl = WhiteLabelDomain.findByDomainAndActive(eventData.serverName, true)?.baseUrl
            // replacing insurance as the white label domain will have in its base url
            url = url.replace("insurance","uae/en/car")
            url = baseUrl + url
        } else {
            url= grailsLinkGenerator.link(controller: 'policy', action: 'uploadPolicyDocs', params: [id: hash], absolute: true)
            url = url.replace("insurance","insurance/uae/en/car")
        }
        url = url.replace("uploadPolicyDocs","upload-policy-docs")


        RestBuilder rest = new RestBuilder()
        RestResponse resp = rest.get("https://ycurl.me/api/?key=qHL7skXZeDYZ&url=" + url){
            header("Content-Type", "application/json")
        }
        def respBody = JSON.parse(resp.responseEntity.body)

        List messageParams = [
            WhiteLabelBrandEnum.getWhiteLabelBrandEnumFromRequestSource(carQuote.requestSource).name,
            carQuote.policyReference,
            respBody.short
        ]
        String message = messageSource.getMessage("quote.purchased.sms", messageParams.toArray(), locale)
        Boolean isSMSSent = smsService.sendToQueue(mobile, message, countryEnum, true)
        isSMSSent
    }

    /**
     * Send Purchase SMS For Home
     * @param mobile
     * @param policyReference
     * @param locale
     * @param countryEnum
     * @return
     */
    Boolean sendHomePurchaseSMS(String mobile, String policyReference, Locale locale, CountryEnum countryEnum) {

        List messageParams = [
            policyReference, grailsApplication.config.emails.documents
        ]
        String message = messageSource.getMessage("quote.home.purchased.sms", messageParams.toArray(), locale)

        Boolean isSMSSent = smsService.sendToQueue(mobile, message, countryEnum, true)
        isSMSSent
    }

    /**
     * Send Purchase SMS For Health
     * @param mobile
     * @param policyReference
     * @param locale
     * @param countryEnum
     * @return
     */
    Boolean sendHealthPurchaseSMS(String mobile, String policyReference, Locale locale, CountryEnum countryEnum) {

        List messageParams = [
            policyReference, grailsApplication.config.emails.documents
        ]
        String message = messageSource.getMessage("quote.health.purchased.sms", messageParams.toArray(), locale)

        Boolean isSMSSent = smsService.sendToQueue(mobile, message, countryEnum, true)
        isSMSSent
    }

    /**
     * Send Document Received SMS
     * @param event
     * @return
     */
    @Selector(AsyncEventConstants.POLICY_DOCS_RECEIVED)
    def sendPolicyDocumentsReceivedSMS(Event event) {
        log.info("quoteSMS.sendPolicyDocumentsRecievedSMS - event:${event}")

        CarQuote carQuote = CarQuote.get(event.data.quoteId)

        log.info("smsService.sendPolicyDocumentsReceivedSMS - Sending SMS for Purchase of Car Quote ${carQuote.id}")

        String mobile = carQuote.mobile

        CountryEnum countryEnum = CountryEnum.findCountryById(carQuote.quoteCountry.id)

        Locale locale = new Locale(event.data.lang)

        List messageParams = [
            carQuote.policyReference
        ]
        String message = messageSource.getMessage("quote.document.received.sms", messageParams.toArray(), locale)

        Boolean isSMSSent = smsService.sendToQueue(mobile, message, countryEnum, true)

        if (isSMSSent) {
            log.info("quoteSMSService.sendPolicyDocumentsReceivedSMS - Quote Purchase SMS sent to $mobile for Quote ${carQuote.id}")
        } else {
            log.warn("quoteSMSService.sendPolicyDocumentsReceivedSMS - Failed to send SMS to $mobile for Quote ${carQuote.id}")
        }

    }
}

