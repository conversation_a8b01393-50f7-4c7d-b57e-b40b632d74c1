package com.cover.util

import com.cover.api.ApiResponseCodeEnum
import com.cover.api.response.ErrorResponse
import com.safeguard.*
import com.safeguard.car.CarQuote
import com.safeguard.car.ProductBasicCover
import com.safeguard.car.StaticContent
import com.safeguard.car.translation.ProductBasicCoverTranslation
import com.safeguard.health.HealthProductCover
import com.safeguard.util.AESCryption
import com.safeguard.health.HealthProductCoverTranslation
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeProductCover
import com.safeguard.home.HomeQuote
import com.safeguard.payment.QuoteAdditionalPayment
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.transaction.Transactional
import grails.util.Environment
import grails.web.servlet.mvc.GrailsParameterMap
import org.apache.commons.lang.LocaleUtils
import org.grails.web.util.WebUtils
import org.joda.time.LocalDate
import org.springframework.validation.Errors

import java.time.LocalDateTime
import java.time.ZoneOffset

@Transactional
class UtilService {

    def grailsApplication
    def messageSource
    def sessionService

    /**
     * To get list of cities
     *
     * @param country
     * @param §
     * @return
     */
    @Transactional(readOnly = true)
    def getCities(Country country, boolean isHealth = false) {
        def cityList

        if (isHealth) {
            cityList = City.findAllByCountryAndIsHealth(country, isHealth)
        } else {
            cityList = City.findAllByCountryAndActive(country, true)
        }

        cityList
    }


    def getRenewalQuoteIdByOldQuote(CarQuote quote){
        String systemGeneratedQuoteId
        String insurerGeneratedQuoteId
        if (quote.getPosDaysLeft() == 0){
            return null
        }
        def newRenewal = Renewal.findAllByOldCarQuoteAndCarQuoteIsNotNull(quote)
        if (newRenewal && newRenewal.size() > 0){
            for (Renewal renewal : newRenewal){
                if (!renewal.carQuote.isNotProcessed()){
                    return null
                }
                if (!renewal.isSystemCreated){
                    insurerGeneratedQuoteId = renewal.carQuote.id
                }else {
                    systemGeneratedQuoteId = renewal.carQuote.id
                }
            }
            return insurerGeneratedQuoteId ? AESCryption.encrypt(insurerGeneratedQuoteId) : AESCryption.encrypt(systemGeneratedQuoteId)
        }else {
            return null
        }
    }
    /**
     * Get terms and conditions document link for a policy
     * @param quote
     * @return
     */
    String getTermsDocumentLink(def quote) {
        if (!quote || !quote.product) {
            return null
        }

        if (quote instanceof CarQuote) {
            ProductBasicCover productBasicCover = ProductBasicCover.findByProduct(quote.product, [readOnly: true])
            ProductBasicCoverTranslation productBasicCoverTranslation = ProductBasicCoverTranslation.findByCover(productBasicCover, [readOnly: true])
            return productBasicCoverTranslation?.coverLink
        } else if (quote instanceof HealthQuote) {
            HealthProductCover healthProductCover = HealthProductCover.findByProduct(quote.product, [readOnly: true])
            HealthProductCoverTranslation healthProductCoverTranslation = HealthProductCoverTranslation.findByHealthProductCover(healthProductCover, [readOnly: true])
            return healthProductCoverTranslation?.coverLink
        } else if (quote instanceof HomeQuote) {
            HomeProductCover homeProductCover = HomeProductCover.findByProduct(quote.product, [readOnly: true])
            return homeProductCover?.policyAndTerms
        }

        return null
    }

    /**
     * @return list of all active countries
     */
    @Transactional(readOnly = true)
    def getCountries(String sortBy) {

        //get all active countries
        def list = Country.findAllByActive(true, [sort: sortBy])

        list
    }

    /**
     * @return list of all driving experience values for drop down.
     */
    @Transactional(readOnly = true)
    def getDrivingExperienceList() {
        def list = DrivingExperience.findAllByActive(true, [sort: 'sortIndex'])

        list
    }

    /**
     * @return list of all no claim discount values for drop down.
     */
    @Transactional(readOnly = true)
    def getNoClaimDiscountList() {
        def list = NoClaimDiscount.findAllByActive(true, [sort: 'sortIndex'])

        list
    }

    /**
     * Currently we support only UAE, in future when we add more countries this method should be changed and
     * read firstLicenseCountry from cookie or session.
     *
     * @return
     */
    @Transactional(readOnly = true)
    Country getCountry() {
        def webUtils = WebUtils.retrieveGrailsWebRequest()
        CountryEnum countryEnum = webUtils.session[IConstant.SITE_COUNTRY]
        if (!countryEnum) {
            countryEnum = CountryEnum.UAE
        }
        Integer id = countryEnum.country.getAt("id")
        return Country.get(id)
    }

    @Transactional(readOnly = true)
    def getCountryEnum() {
        def webUtils = WebUtils.retrieveGrailsWebRequest()
        CountryEnum countryEnum = webUtils.session[IConstant.SITE_COUNTRY]
        if (!countryEnum) {
            countryEnum = CountryEnum.UAE
        }
        return countryEnum
    }

    /**
     * Returns the current session lang
     *
     * @return
     */
    @Transactional(readOnly = true)
    def getLanguage() {
        def webUtils = WebUtils.retrieveGrailsWebRequest()

        return webUtils.session.'org.springframework.web.servlet.i18n.SessionLocaleResolver.LOCALE' ?: 'en'
    }

    /**
     * @return fallback CURRENCY
     */
    @Transactional(readOnly = true)
    def getCurrency() {
        //TODO: get it from session when we support multiple currency
        String currency = getCountry().currency

        if (!currency) {
            currency = grailsApplication.config.getProperty("cover.fallback.currency")
        }

        currency
    }

    /**
     * Converts yes/no into true/false
     *
     * @param yesNoStr - string yes or no
     * @return true/false as string.
     */
    @Transactional(readOnly = true)
    def toTrueFalse(String yesNoStr) {

        String toTrueFalse = "false"

        if (yesNoStr.equalsIgnoreCase('yes')) {
            toTrueFalse = "true"
        }

        toTrueFalse
    }

    /**
     * Turn yes/no into boolean true/false
     *
     * @param yesNoStr
     * @return true if passed string is 'yes'
     */
    @Transactional(readOnly = true)
    def toBoolean(String yesNoStr) {

        boolean toTrueFalse = false
        try {
            if (yesNoStr && yesNoStr.equalsIgnoreCase('yes')) {
                toTrueFalse = true
            }
        } catch (Exception e) {
            log.error("Error while parsing string to boolean", e)
        }

        toTrueFalse
    }

    @Transactional(readOnly = true)
    def getClientIp(request) {
        log.debug("utils.getClientIp - X-forwarded-for:${request.getHeader('X-Forwarded-For')}," +
            "remoteAdd:${request.getRemoteAddr()}")

        String allIps = (request.getHeader("X-Forwarded-For")?.trim()) ?: request.getRemoteAddr()?.trim()
        String clientIp = allIps

        if (allIps.contains(",")) {
            clientIp = allIps.split(",")[0].trim()
            if (!isIpv4Address(clientIp)) {
                clientIp = allIps.split(",")[1].trim()
            }
        }

        log.info("utils.getClientIp - clientIp:${clientIp}")

        if (Environment.current == Environment.DEVELOPMENT) {
            log.info("Env is development so setting 127.0.0.1 ip for payfort")
            clientIp = "127.0.0.1"
        }

        return clientIp
    }

    private boolean isIpv4Address(String ipAddress) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ipAddress);
            if (inetAddress instanceof java.net.Inet4Address) {
                return true
            }
        } catch (UnknownHostException e) {
            log.error("util.isIpv4Address - Invalid IP address: " + ipAddress);
        }
        return false
    }

    /**
     * Generate Response errors on the basis of validation errors
     * @param validationErrors list of errors
     * @param locale lang of error
     * @param key enum key
     * @return map of errorResponse list
     */
    public List generateErrorResponse(Errors validationErrors, Locale locale, String key) {
        def errors = []

        validationErrors.each {
            it.getAllErrors().each { fieldError ->
                log.info("fieldError:$fieldError")
                ErrorResponse error = new ErrorResponse()

                log.info("code:${fieldError.code}, argument:${fieldError.arguments[0]}")

                String messageKey = "${key}.${fieldError.arguments[0]}.${fieldError.code}"

                ApiResponseCodeEnum responseCodeEnum = ApiResponseCodeEnum.findKey(messageKey)
                error.errorCode = responseCodeEnum ? responseCodeEnum.code() : null
                error.developerMessage = messageKey
                error.moreInfo = fieldError.arguments[0]
                if (responseCodeEnum) {
                    error.userMessage = messageSource.getMessage(messageKey, [].toArray(), "", locale)
                } else {
                    error.userMessage = messageSource.getMessage("quote.data.${fieldError.code}", [].toArray(), "", locale)
                }

                errors.add(error)
            }
        }

        return errors

    }

    /**
     * Convert provided String into {@link Locale} object
     * in case of invalid/null lang parameter passed by default
     * this method returns locale object with English.
     *
     * @param lang String value of locale
     * @return Locale
     */
    public Locale convertToLocale(String lang) {
        lang = lang?.toLowerCase()
        Locale locale = Locale.ENGLISH

        try {
            if (lang != null && lang in ['en', 'ar']) {
                // org.springframework.util.StringUtil#parseLocaleString(lang) method is not
                // parsing locale properly for example if '123' lang passed it returns Locale
                // with language '123'. Apache commons locale utils.toLocale(lang) has better
                // implementation of converting string into locale object.

                locale = LocaleUtils.toLocale(lang)
            }
        } catch (e) {
            log.warn("Error while extracting $lang from lang param")
        }

        return locale
    }

    /**
     * Convert provided String into proper Country code
     * in case of invalid/null country parameter passed by default
     * this method returns uae as country.
     *
     * @param country String
     * @return CountryEnum
     */
    public CountryEnum convertToCountry(String country) {
        country = country?.toLowerCase()
        CountryEnum countryEnum

        try {
            if (country != null) {
                countryEnum = CountryEnum.findCountry(country)
            }
        } catch (e) {
            log.warn("Error while extracting $country from country param")
        }

        //log.info("util - final countryEnum:${countryEnum}")

        return countryEnum ?: CountryEnum.UAE
    }

    static decodeMerchantRef(String merchantRef) {
        if (!merchantRef || merchantRef.lastIndexOf('-') == -1) return null

        String orderId = merchantRef

        orderId = orderId.substring(orderId.lastIndexOf('-') + 1, orderId.length())

        Long.parseLong(orderId)
    }

    boolean isCollectionOrArray(object) {
        [Collection, Object[]].any { it.isAssignableFrom(object.getClass()) }
    }

    /**
     * Find CountryEnum by Id or Currency
     * @param id
     * @param currency
     * @return
     */
    CountryEnum getCountryByIdOrCurrency(Integer id, String currency) {
        CountryEnum countryEnum

        if (id) {
            countryEnum = CountryEnum.findCountryById(id)
        } else if (currency) {
            countryEnum = CountryEnum.findCountryByCurrency(currency)
        }

        countryEnum
    }

    /**
     * get christmas data (sample to set timed discounts)
     * will send you the info about a event
     *
     * @return shouldShowSlider, christmasDiscountAmount
     */

    List getValentineData() {
        Boolean showValentinePromo = false
        LocalDateTime discountUntilDate = LocalDateTime.now()

        String from = "2019-02-13T19:59:59"
        String to = "2019-02-16T20:00:00"

        if (LocalDateTime.now(ZoneOffset.UTC).isAfter(LocalDateTime.parse(from))
            && LocalDateTime.now(ZoneOffset.UTC).isBefore(LocalDateTime.parse(to))) {
            showValentinePromo = true
            // to convert this in arabian time zone
            discountUntilDate = LocalDateTime.parse(to).plusHours(4)
        } else {
            discountUntilDate = LocalDateTime.now()
        }


        [showValentinePromo, discountUntilDate.toString()]
    }

    @Transactional(readOnly = true)
    Map getStaticContentByCountryAndSlug(Country country, String slug) {
        StaticContent staticContent = StaticContent.findByCountryAndSlug(country, slug)
        Map staticContentMap = [:]
        if (staticContent) {
            staticContent.titleEn = staticContent.title
            staticContentMap.metaDescription = staticContent.metaDescription

            WhiteLabelBrandEnum brandEnum = sessionService.getBrand()
            if (brandEnum && brandEnum == WhiteLabelBrandEnum.ADIB) {
                staticContentMap.content = staticContent.content.replaceAll("/insurance/", "/takaful/")
            } else {
                staticContentMap.content = staticContent.content
            }
        }
        return staticContentMap

    }

    String getRoadsideAssistanceByLang(String rsa, String  lang) {
        if (rsa == null) return null
        return messageSource.getMessage("common.${rsa.toLowerCase()}", null, new Locale(lang))
    }

    static String getQuoteLoggingPrefix(String methodName, Long quoteId, InsuranceProviderEnum provider = null) {
        String log = ".${methodName} - quoteId: ${quoteId}"
        if (provider) {
            log += " - provider: ${provider}"
        }
        return log
    }

}
