package com.cover.util

import com.safeguard.*
import com.safeguard.car.CarQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.life.LifeQuote
import com.safeguard.pet.PetQuote
import com.safeguard.travel.TravelQuote
import grails.transaction.Transactional
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime

@Transactional
class VoucherService {

    def getVouchers() {

        def vouchers
        vouchers = Voucher.createCriteria().list {
            or{
                gt("expireDate", LocalDateTime.now())
                isNull("expireDate")
            }
            eq("isDeleted", false)
        }
        return vouchers
    }


    def saveVoucherEvent(User user, VoucherEventTypeEnum voucherEventType, Long voucherId) {

        Voucher voucher = Voucher.findById(voucherId)
        VoucherEvent voucherEvent = new VoucherEvent()
        voucherEvent.voucher = voucher
        voucherEvent.user = user
        voucherEvent.dateCreated = LocalDateTime.now()
        voucherEvent.eventType = voucherEventType

        return voucherEvent.save()

    }

    /**
     * This method checks if current user has any active and issued policy
     * and return true or false.
     * @param user
     * @return boolean true or false
     */
    Boolean hasActivePolicy(User user) {

        def carQuotes = CarQuote.createCriteria().get {
            projections {
                count('id')
            }
            ge("actualPolicyStartDate", LocalDate.now().minusMonths(12))
            eq("user.id", user.id)
            eq("paymentStatus", PaymentStatusEnum.ISSUED)
        }
        if (carQuotes > 0) {
            return true
        }

        def healthQuotes = HealthQuote.createCriteria().get {
            projections {
                count('id')
            }
            ge("actualPolicyStartDate", LocalDate.now().minusMonths(12))
            eq("user.id", user.id)
            eq("paymentStatus", PaymentStatusEnum.ISSUED)
        }
        if (healthQuotes > 0) {
            return true
        }

        def homeQuotes = HomeQuote.createCriteria().get {
            projections {
                count('id')
            }
            ge("actualPolicyStartDate", LocalDate.now().minusMonths(12))
            eq("user.id", user.id)
            eq("paymentStatus", PaymentStatusEnum.ISSUED)
        }
        if (homeQuotes > 0) {
            return true
        }
        def travelQuotes = TravelQuote.createCriteria().get {
            projections {
                count('id')
            }
            ge("endDate", LocalDate.now())
            eq("user.id", user.id)
            eq("paymentStatus", PaymentStatusEnum.ISSUED)
        }
        if (travelQuotes > 0) {
            return true
        }

        def lifeQuotes = LifeQuote.createCriteria().get {
            projections {
                count('id')
            }
            ge("actualPolicyStartDate", LocalDate.now().minusMonths(12))
            eq("user.id", user.id)
            eq("paymentStatus", PaymentStatusEnum.ISSUED)
        }
        if (lifeQuotes > 0) {
            return true
        }
        def petQuotes = PetQuote.createCriteria().get {
            projections {
                count('id')
            }
            ge("actualPolicyStartDate", LocalDate.now().minusMonths(12))
            eq("user.id", user.id)
            eq("paymentStatus", PaymentStatusEnum.ISSUED)
        }
        if (petQuotes > 0) {
            return true
        }
        def paLeads = PaLead.createCriteria().get {
            projections {
                count('id')
            }
            ge("actualPolicyStartDate", LocalDate.now().minusMonths(12))
            eq("user.id", user.id)
            eq("paymentStatus", PaymentStatusEnum.ISSUED)
        }
        if (paLeads > 0) {
            return true
        }

        return false
    }

}
