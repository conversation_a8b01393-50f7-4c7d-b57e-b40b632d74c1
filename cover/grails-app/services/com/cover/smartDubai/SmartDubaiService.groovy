package com.cover.smartDubai

import com.cover.api.smartdubai.SmartDubaiCancelRequestCommand
import com.cover.api.smartdubai.SmartDubaiConfirmRequestCommand
import com.cover.api.smartdubai.SmartDubaiInitiateRequestCommand
import com.safeguard.smartDubai.SmartDubaiHashAlgorithm
import grails.transaction.Transactional
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject

@Transactional(readOnly = true)
class SmartDubaiService {

    def grailsApplication

    /**
     * Validate hash by encrypting the parameters and comparing with the provided hash
     * @param properties
     * @return
     */
    def validateHash(def properties, String hash) {
        log.info("smartDubai.validateHash - properties:${properties}, hash:$hash")

        SmartDubaiHashAlgorithm hashAlgorithm = new SmartDubaiHashAlgorithm()

        Map<String, String> data = new HashMap()
        properties.each { property ->
            data[property.name] = property.value
        }

        String smartDubaiSecretKey = grailsApplication.config.smartDubai.secretKey
        log.info("smartDubai.validateHash - actual key:${smartDubaiSecretKey}")

        String generatedHash = hashAlgorithm.generateHMAC(data, smartDubaiSecretKey, true)
        log.info("smartDubai.validateHash - data:${data}, generatedHash:${generatedHash}")

        if (generatedHash == hash) {
            return true
        }

        return false
    }

    /**
     * Get initiate request command
     * @param propertiesList
     * @return
     */
    SmartDubaiInitiateRequestCommand getInitiateRequest(def propertiesList, def requestJSON) {
        Map<String, String> propertiesMap = new HashMap<>()

        propertiesList.each { property ->
            propertiesMap[property.name] = property.value
        }

        SmartDubaiInitiateRequestCommand initiateRequest = new SmartDubaiInitiateRequestCommand()
        initiateRequest.with {
            quoteId = propertiesMap['quoteId']
            timestamp = propertiesMap['timestamp']
            customerEmail  = propertiesMap['customer-email']
            transactionAmount = propertiesMap['transaction-amount']
        }
        initiateRequest.requestJSON = requestJSON

        initiateRequest
    }

    /**
     * Get Confirm Request command
     * @param propertiesList
     * @return
     */
    SmartDubaiConfirmRequestCommand getConfirmRequest(JSONArray propertiesList, JSONObject requestJSON) {
        Map<String, String> propertiesMap = new HashMap<>()

        propertiesList.each { property ->
            propertiesMap[property.name] = property.value
        }

        SmartDubaiConfirmRequestCommand confirmRequest = new SmartDubaiConfirmRequestCommand()

        confirmRequest.with {
            merchantReference = propertiesMap['epay-sptrn']
            amount = propertiesMap['amount']
            customerEmail = propertiesMap['customer-email']
            timestamp = propertiesMap['timestamp']
            epayDegTrn = propertiesMap['epay-DEGTRN']
            epayTimestamp = propertiesMap['epay-timestamp']
            epayServiceProviderCode = propertiesMap['epay-spcode']
            epayServiceCode = propertiesMap['epay-servcode']
            epayPaymentMethod = propertiesMap['epay-paymentMethod']
            approvalCode = propertiesMap['approvalCode']
            epayAmount = propertiesMap['epay-amount']
        }
        confirmRequest.requestJSON = requestJSON

        confirmRequest
    }

    /**
     * Get Cancel Request command
     * @param propertiesList
     * @return
     */
    SmartDubaiCancelRequestCommand getCancelRequest(JSONArray propertiesList, JSONObject requestJSON) {
        Map<String, String> propertiesMap = new HashMap<>()

        propertiesList.each { property ->
            propertiesMap[property.name] = property.value
        }

        SmartDubaiCancelRequestCommand cancelRequest = new SmartDubaiCancelRequestCommand()

        cancelRequest.with {
            statusCode = propertiesMap['status-code']
            statusDescription = propertiesMap['status-description']
            merchantReference = propertiesMap['epay-sptrn']
            customerEmail = propertiesMap['customer-email']
            timestamp = propertiesMap['timestamp']
            epayDegTrn = propertiesMap['epay-degtrn']
            epayTimestamp = propertiesMap['epay-timestamp']
            epayServiceProviderCode = propertiesMap['epay-spcode']
            epayServiceCode = propertiesMap['epay-servcode']
            epayAmount = propertiesMap['epay-amount']
        }
        cancelRequest.requestJSON = requestJSON

        cancelRequest
    }
}
