package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.cover.health.commands.HealthRateMemberCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.health.HealthRating
import com.safeguard.health.HealthRelationship
import grails.transaction.Transactional
import org.joda.time.LocalDate
import org.joda.time.Period

/**
 * Health rating service for Watania.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthDamanRateService {

    public static final Integer PROVIDER_ID = 11
    public static final Integer EBP_PRODUCT_ID = 47

    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)
        boolean isSalaryBelow4k = healthRatingService.isMemberSalaryBelow4k(quoteCommand)
        if (isEligible) {

            for (member in quoteCommand.members) {

                //Integer memberAge = member.age + 1
                Integer memberAge = getMemberAge(member)
                List<HealthRating> ratingList =
                    healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, null,
                        memberAge, member.gender, quoteCommand.cityId,
                        member.relationship, isSalaryBelow4k)

                if (!ratingList) {
                    isEligible = false
                    break
                }
                int count = 0
                for (HealthRating rating in ratingList) {

                    // for the same product / deductible combination update the premium
                    // and add it to member list instead of creating new rateCommand.
                    // For 2nd and more iteration same product are repeated so we add them
                    // into member details
                    if (index > 0) {
                        HealthRateCommand rateCommand = rateCommandList[count]
                        rateCommand.premium = rateCommand.premium + rating.premium
                        rateCommand.members.add(healthRatingService.getMember(rating, member))

                        count++
                        continue
                    }

                    HealthRateCommand rateCommand = new HealthRateCommand()
                    // add covers for the plan
                    rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                    List memberList = []
                    rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                    memberList.add(healthRatingService.getMember(rating, member))
                    rateCommand.members = memberList
                    rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                    rateCommandList.add(rateCommand)
                }
                index++
            }
        }

        if (!isEligible) {
            rateCommandList = []
        }

        rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommandList = healthRatingService.applyVAT(rateCommandList)
        }

        rateCommandList
    }

     /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if(!member.dob){
            return (member.age + 1)
        }
        else {
            Period period = new Period(member.dob,(new LocalDate()))
            return  (period.getYears() + 1)
        }
    }


    boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        boolean isEligible = false

        boolean hasNonChildMember = false
        boolean sponsorBelowUnderAge = false

        List<HealthMemberCommand> members = quoteCommand.members
        members.each { HealthMemberCommand memberCommand ->
            if (memberCommand.relationship.id != HealthRelationshipEnum.CHILD.value()) {
                hasNonChildMember = true
            }

            if (memberCommand.relationship.id == HealthRelationshipEnum.SPONSOR.value()
                && getMemberAge(memberCommand) < 18)  {
                sponsorBelowUnderAge = true
            }
        }

        if (hasNonChildMember) {
            isEligible = true
        }

        if (sponsorBelowUnderAge) {
            isEligible = false
        }

        return isEligible
    }

}
