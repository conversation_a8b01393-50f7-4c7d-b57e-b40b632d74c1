package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.GenderEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.health.HealthGender
import com.safeguard.health.HealthRating
import com.safeguard.health.HealthRelationship
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.LocalDate
import org.joda.time.Period

/**
 * Health rating service for Orient.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthOrientRateService {

    public static final Integer PROVIDER_ID = 18

    public static final Integer DMED_PRODUCT_ID = 84
    public static final Integer EBP_PRODUCT_ID = 85
    public static final Integer IMED_PRODUCT_ID = 86
    public static final Integer NEMED_PRODUCT_ID = 98
    public static final Integer NEMED_ENHANCED_PRODUCT_ID = 99
    public static final Integer NEMED_LITE_PRODUCT_ID = 5180

    public static final Integer FAMILY_CARE_PLAN_GOLD_ID = 1044
    public static final Integer FAMILY_CARE_PLAN_SILVER_PREMIUM_ID = 1045
    public static final Integer FAMILY_CARE_PLAN_SILVER_CLASSIC_ID = 1046
    public static final Integer FAMILY_CARE_PLAN_GREEN_ID = 1047
    public static final Integer FAMILY_CARE_PLAN_EMERALD_ID = 5183
    public static final Integer FAMILY_CARE_PLAN_PEARL_ID = 5182
    public static final Integer FAMILY_CARE_PLAN_SILK_ROAD_ID = 1048

    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)
        boolean isSalaryBelow4k = healthRatingService.isMemberSalaryBelow4k(quoteCommand)
        Boolean hasSponsor = quoteCommand.members.any { it.relationship.id.toInteger() == HealthRelationshipEnum.SPONSOR.value() }
        Boolean hasFamilyMember = quoteCommand.members.any { it.relationship.id.toInteger() in [HealthRelationshipEnum.CHILD.value(), HealthRelationshipEnum.PARENT.value(), HealthRelationshipEnum.SPOUSE.value()] }
        Boolean hasNonChildMember = quoteCommand.members.any { it.relationship.id.toInteger() != HealthRelationshipEnum.CHILD.value() }
        Boolean hasParentMember = quoteCommand.members.any { it.relationship.id.toInteger() == HealthRelationshipEnum.PARENT.value() }
        Boolean hasFemaleMember = quoteCommand.members.any { it.gender.id.toInteger() != GenderEnum.MALE.id }

        if (isEligible) {

            for (member in quoteCommand.members) {

                //Integer memberAge = member.age + 1
                Integer memberAge = getMemberAge(member)
                List<HealthRating> ratingList =
                    healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                        memberAge, member.gender, quoteCommand.cityId,
                        member.relationship, isSalaryBelow4k, hasNonChildMember, null, quoteCommand.viewEbp)

                if (!ratingList) {
                    isEligible = false
                    break
                }
                int count = 0
                for (HealthRating rating in ratingList) {

                   /* if (hasParentMember && rating.product.id in [NEMED_PRODUCT_ID, NEMED_ENHANCED_PRODUCT_ID]) {
                        continue
                    }*/

                    if (hasSponsor && rating.product.id in [DMED_PRODUCT_ID, NEMED_ENHANCED_PRODUCT_ID]) {
                        continue
                    }

                    if (hasFamilyMember && rating.product.id == IMED_PRODUCT_ID) {
                        continue
                    }

                    // for the same product / deductible combination update the premium
                    // and add it to member list instead of creating new rateCommand.
                    // For 2nd and more iteration same product are repeated so we add them
                    // into member details
                    if (index > 0) {
                        HealthRateCommand rateCommand = rateCommandList[count]
                        rateCommand.premium = rateCommand.premium + rating.premium
                        rateCommand.members.add(healthRatingService.getMember(rating, member))

                        count++
                        continue
                    }


                    HealthRateCommand rateCommand = new HealthRateCommand()
                    // add covers for the plan
                    rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                    rateCommand = applyCovers(member, rateCommand, rating, hasFemaleMember)

                    List memberList = []
                    rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                    memberList.add(healthRatingService.getMember(rating, member))
                    rateCommand.members = memberList
                    rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                    rateCommandList.add(rateCommand)
                }
                index++
            }
        }

        if (!isEligible) {
            rateCommandList = []
        }

        rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)

        if (rateCommandList.size() > 0) {
            rateCommandList = healthRatingService.applyC4meFee(rateCommandList)
        }

        rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommandList = healthRatingService.applyVAT(rateCommandList)
        }

        rateCommandList
    }

    private boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        boolean isEligible = true
        boolean hasEmployeeMoreThan4KMember = false
        boolean hasEmployeeLessThan4KMember = false
        boolean hasNonEmployeeMember = false
        boolean hasFamilyMember = false
        boolean hasNonFamilyMember = false

        quoteCommand.members.each { HealthMemberCommand member ->
            if(member.relationship.id == HealthRelationshipEnum.EMPLOYEE.value()) {
                if (member.salaryOver4k) {
                    hasEmployeeMoreThan4KMember = true
                } else {
                    hasEmployeeLessThan4KMember = true
                }
            } else {
                hasNonEmployeeMember = true
            }

            /*if (member.relationship.id == HealthRelationshipEnum.EMPLOYEE.value() ||
                member.relationship.id == HealthRelationshipEnum.SPONSOR.value() ||
                member.relationship.id == HealthRelationshipEnum.DOMESTIC_WORKER.value()) {
                hasNonFamilyMember = true
            } else {
                hasFamilyMember = true
            }*/

            if (member.relationship.id == HealthRelationshipEnum.DOMESTIC_WORKER.value() && !quoteCommand.viewEbp) {
                isEligible = false
            }
        }

        if (hasEmployeeMoreThan4KMember && hasEmployeeLessThan4KMember) {
            isEligible = false
        } else if ((hasEmployeeMoreThan4KMember || hasEmployeeLessThan4KMember) && hasNonEmployeeMember) {
            isEligible = false
        }

        /*if (hasNonFamilyMember && hasFamilyMember) {
            isEligible = false
        }*/

        isEligible
    }
    /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if(!member.dob){
            return (member.age + 1)
        }
        else {
            Integer days = Days.daysBetween(member.dob, new LocalDate()).getDays()
            Integer age = (days / 365.25).setScale(0, BigDecimal.ROUND_HALF_UP).toInteger()

            return age
        }
    }

    HealthRateCommand applyCovers(HealthMemberCommand member, HealthRateCommand rateCommand, HealthRating rating, Boolean hasFemaleMember) {


        if ((rateCommand.productId in [DMED_PRODUCT_ID, NEMED_PRODUCT_ID] && member.relationship.id.intValue() == HealthRelationshipEnum.SPOUSE.value()
            && getMemberAge(member) > 45) || !hasFemaleMember){
            rateCommand.maternity = null
        }

        return rateCommand
    }
}
