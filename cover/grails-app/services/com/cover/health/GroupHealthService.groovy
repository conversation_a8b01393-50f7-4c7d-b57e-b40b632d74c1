package com.cover.health

import com.cover.health.commands.HealthRateCommand
import com.cover.health.groupHealth.commands.GroupHealthHomeCommand
import com.cover.health.groupHealth.commands.GroupHealthQuoteCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.LeadType
import com.safeguard.Product
import com.safeguard.ProductTypeEnum
import com.safeguard.User
import com.safeguard.health.GroupHealthQuoteDetail
import com.safeguard.health.HealthQuote
import com.safeguard.health.HealthRating
import grails.transaction.Transactional

@Transactional
class GroupHealthService {

    def healthRatingService
    def healthEmailService
    def crmService

    @Transactional(readOnly = true)
    List<HealthRateCommand> quotes() {

        List<Product> products  = Product.findAllByIsGroupHealthAndActive(true, true)
        List<HealthRating> healthRatingList = []

        if (products) {
            healthRatingList = HealthRating.findAllByProductInList(products, [sort: "premium"])
        }

        List<HealthRateCommand> healthRateCommandList = []

        healthRatingList.each { HealthRating healthRating ->
            HealthRateCommand healthRateCommand = new HealthRateCommand()
            healthRatingService.applyCovers(healthRateCommand, healthRating)
            healthRateCommand.premium = healthRating.premium
            healthRateCommandList += healthRateCommand
        }

        healthRateCommandList
    }

    HealthQuote createHealthQuote(GroupHealthQuoteCommand groupHealthQuoteCommand) {

        User user = User.findOrCreateByEmail(groupHealthQuoteCommand.email)

        user.name = groupHealthQuoteCommand.name
        user.mobile = groupHealthQuoteCommand.phone

        user.save()

        if (!user.healthLeadType) {
            user.changeLeadType(LeadType.NORMAL, ProductTypeEnum.HEALTH.name().toLowerCase())
            user.save()
        }

        Integer countryId = CountryEnum.findCountryByCode(groupHealthQuoteCommand.country).id

        HealthQuote healthQuote = new HealthQuote()
        healthQuote.user = user
        healthQuote.email = groupHealthQuoteCommand.email
        healthQuote.mobile = groupHealthQuoteCommand.phone
        healthQuote.applicationType = HealthApplicationTypeEnum.EMPLOYEES
        healthQuote.companyName = groupHealthQuoteCommand.companyName
        healthQuote.numberOfEmployees = groupHealthQuoteCommand.numberOfEmployees
        healthQuote.city = groupHealthQuoteCommand.city
        healthQuote.quoteCountry = Country.read(countryId)
        healthQuote.type = user.healthLeadType

        healthQuote.queryString = groupHealthQuoteCommand.marketingTracking.queryString
        healthQuote.utmSource = groupHealthQuoteCommand.marketingTracking.utmSource
        healthQuote.utmMedium = groupHealthQuoteCommand.marketingTracking.utmMedium
        healthQuote.utmCampaign = groupHealthQuoteCommand.marketingTracking.utmCampaign
        healthQuote.gclid = groupHealthQuoteCommand.marketingTracking.gclid
        healthQuote.fbclid= groupHealthQuoteCommand.marketingTracking.fbclid

        healthQuote.save(flush: true, failOnError: true)

        GroupHealthQuoteDetail groupHealthQuoteDetail = new GroupHealthQuoteDetail()
        groupHealthQuoteDetail.healthQuote = healthQuote
        groupHealthQuoteDetail.user = user
        groupHealthQuoteDetail.numberOfFemaleEmployees = groupHealthQuoteCommand.numberOfFemaleEmployees
        groupHealthQuoteDetail.numberOfMaleEmployees = groupHealthQuoteCommand.numberOfMaleEmployees
        groupHealthQuoteDetail.youngestEmployeeAge = groupHealthQuoteCommand.youngestEmployeeAge
        groupHealthQuoteDetail.oldestEmployeeAge = groupHealthQuoteCommand.oldestEmployeeAge
        groupHealthQuoteDetail.save()

        crmService.handleCrmEvents(user.id, ProductTypeEnum.HEALTH.name().toLowerCase())
        notify AsyncEventConstants.HEALTH_QUOTE_CREATED, [quoteId: healthQuote.id, country: groupHealthQuoteCommand.country]
        notify AsyncEventConstants.HEALTH_EMPLOYEES_QUOTE_CREATED, [quoteId: healthQuote.id, numberOfEmployees: healthQuote.numberOfEmployees, companyName: healthQuote.companyName]
        healthQuote
    }
}
