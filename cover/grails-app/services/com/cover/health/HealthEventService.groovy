package com.cover.health

import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.AsyncEventConstants
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.health.HealthQuote
import grails.transaction.Transactional
import reactor.bus.Event
import reactor.spring.context.annotation.Consumer
import reactor.spring.context.annotation.Selector

/**
 * Handle Health insurance related events.
 * <AUTHOR>
 */
@Consumer
@Transactional(readOnly = true)
class HealthEventService {

    def grailsApplication
    def healthQuoteService
    def healthEmailService
    def groupHealthService

    @Selector(AsyncEventConstants.HEALTH_QUOTE_CREATED)
    void handleQuoteCreated(Event event) {

        // sleep for 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)
        log.info "Handling health quote created event ${event.data}"
        Long quoteId = event.data.quoteId

        HealthQuote healthQuote = HealthQuote.get(quoteId)
        HealthQuoteCommand quoteCommand = healthQuoteService.toHealthQuoteCommand(healthQuote)

        List ratings

        if (healthQuote.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
            ratings = groupHealthService.quotes()
        } else {
            ratings = healthQuoteService.getRatings(quoteCommand)
            healthQuoteService.saveAllQuotes((List<HealthRateCommand>)ratings,quoteId)
        }

        healthEmailService.sendHealthQuoteEmail(ratings, healthQuote, event.data.country.toString())

    }

    @Selector(AsyncEventConstants.HEALTH_QUOTE_PURCHASED)
    void handleQuotePurchased(Event event) {

        // sleep for 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)
        log.info "Handling health quote purchased event ${event.data}"
        Long quoteId = event.data.quoteId

        HealthQuote healthQuote = HealthQuote.get(quoteId)

        healthEmailService.sendHealthQuotePurcahsedEmail(healthQuote)

    }
}
