package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.health.HealthRating
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.LocalDate
import org.joda.time.Period

@Transactional
class HealthAdamjeeRateService {

    public static final Integer PROVIDER_ID = 12

    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)
        Boolean hasSponsor = quoteCommand.members.any { it.relationship.id.toInteger() == HealthRelationshipEnum.SPONSOR.value() }
        Boolean hasFamilyMember = quoteCommand.members.any { it.relationship.id.toInteger() in [HealthRelationshipEnum.CHILD.value(), HealthRelationshipEnum.PARENT.value(), HealthRelationshipEnum.SPOUSE.value()] }

        for (member in quoteCommand.members) {

            //Integer memberAge = member.age +
            Integer memberAge = getMemberAge(member)
            log.info("adamjeeRate.getRates - memberAge:${memberAge}")

            List<HealthRating> ratingList =
                healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                    memberAge, member.gender, quoteCommand.cityId, member.relationship, null, null, quoteCommand.networkId)
            log.info("adamjeeRate.getRates - ratingList:${ratingList.size()}")

            if (!ratingList) {
                isEligible = false
                break
            }
            int count = 0
            for (HealthRating rating in ratingList) {

                // for the same product / deductible combination update the premium
                // and add it to member list instead of creating new rateCommand.
                // For 2nd and more iteration same product are repeated so we add them
                // into member details
                if (index > 0) {
                    HealthRateCommand rateCommand = rateCommandList[count]
                    rateCommand.premium = rateCommand.premium + rating.premium
                    rateCommand.members.add(healthRatingService.getMember(rating, member))

                    count++
                    continue
                }

                HealthRateCommand rateCommand = new HealthRateCommand()
                // add covers for the plan
                rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                List memberList = []
                rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                memberList.add(healthRatingService.getMember(rating, member))
                rateCommand.members = memberList
                rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                rateCommandList.add(rateCommand)
            }
            index++
        }

        if (!isEligible) {
            rateCommandList = []
        }

        rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommandList = healthRatingService.applyVAT(rateCommandList)
        }

        rateCommandList
    }
    /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if (!member.dob) {
            return (member.age + 1)
        }
        else {
            Integer days = Days.daysBetween(member.dob, new LocalDate()).getDays()
            Integer age = (days / 365.25).setScale(0, BigDecimal.ROUND_HALF_UP).toInteger()

            return  age
        }
    }

    boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        Boolean isEligible = true
        // Child(ren) member(s) only can not be insured
        Boolean hasNonChildMember = false

        List<HealthMemberCommand> members = quoteCommand.members
        members.each { HealthMemberCommand memberCommand ->

            if (memberCommand.relationship.id != HealthRelationshipEnum.CHILD.value()) {
                hasNonChildMember = true
            }

            if (memberCommand.relationship.id == HealthRelationshipEnum.SPONSOR.value()
                && getMemberAge(memberCommand) < 18)  {
                //Sponsor can not be less than 18
                isEligible = false
            }

            if (memberCommand.relationship.id == HealthRelationshipEnum.SPOUSE.value()
                && getMemberAge(memberCommand) < 18)  {
                //Spouse can not be less than 18
                isEligible = false
            }
        }

        if (quoteCommand.source != RequestSourceEnum.WEB.toString() || !hasNonChildMember || !quoteCommand.salaryOver4k) {
            isEligible = false
        }

        return isEligible
    }
}
