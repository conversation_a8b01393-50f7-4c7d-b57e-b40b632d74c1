package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.health.HealthRating
import com.safeguard.health.HealthRelationship
import grails.transaction.Transactional
import org.joda.time.LocalDate
import org.joda.time.Period

/**
 * Health rating service for Union.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthUnionRateService {

    public static final Integer PROVIDER_ID = 1

    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)

        if (isEligible) {
            for (member in quoteCommand.members) {

                //Integer memberAge = member.age + 1
                Integer memberAge = getMemberAge(member)
                List<HealthRating> ratingList =
                    healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                        memberAge, member.gender, quoteCommand.cityId, member.relationship)

                if (!ratingList) {
                    isEligible = false
                    break
                }
                int count = 0
                for (HealthRating rating in ratingList) {

                    // for the same product / deductible combination update the premium
                    // and add it to member list instead of creating new rateCommand.
                    // For 2nd and more iteration same product are repeated so we add them
                    // into member details
                    if (index > 0) {
                        HealthRateCommand rateCommand = rateCommandList[count]
                        rateCommand.premium = rateCommand.premium + rating.premium
                        rateCommand.members.add(healthRatingService.getMember(rating, member))

                        count++
                        continue
                    }

                    HealthRateCommand rateCommand = new HealthRateCommand()
                    // add covers for the plan
                    rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                    List memberList = []
                    rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                    memberList.add(healthRatingService.getMember(rating, member))
                    rateCommand.members = memberList
                    rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                    rateCommandList.add(rateCommand)
                }
                index++
            }
        }

        if (!isEligible) {
            rateCommandList = []
        }

        rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommandList = healthRatingService.applyVAT(rateCommandList)
        }

        rateCommandList
    }

    private boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        Integer employeeRelationshipId = HealthRelationshipEnum.EMPLOYEE.value()
        Integer workerRelationshipId = HealthRelationshipEnum.DOMESTIC_WORKER.value()

        boolean isEligible = true
        boolean hasEmployeeMember = false
        boolean hasNonEmployeeMember = false
        boolean salaryBelow4k = true

        quoteCommand.members.each { HealthMemberCommand member ->
            if(!hasEmployeeMember &&
                (member.relationship.id == employeeRelationshipId || member.relationship.id == workerRelationshipId)) {

                hasEmployeeMember = true
            }
            if ((member.relationship.id == employeeRelationshipId || member.relationship.id == workerRelationshipId) &&
                salaryBelow4k && member.salaryOver4k) {

                salaryBelow4k = false
            }
        }

        quoteCommand.members.each { HealthMemberCommand member ->
            if(!hasNonEmployeeMember &&
                (member.relationship.id != employeeRelationshipId && member.relationship.id != workerRelationshipId)) {

                hasNonEmployeeMember = true
            }
        }

        if (hasEmployeeMember && !salaryBelow4k) {
            isEligible = false
        }

        if (hasEmployeeMember && hasNonEmployeeMember) {
            isEligible = false
        }

        isEligible
    }
    /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if(!member.dob){
            return (member.age + 1)
        }
        else {
            Period period = new Period(member.dob,(new LocalDate()))
            return  (period.getYears() + 1)
        }
    }

}
