package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.cover.health.commands.HealthRateMemberCommand
import com.safeguard.Constants
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.ProductEnum
import com.safeguard.health.HealthAnswer
import com.safeguard.health.HealthMember
import com.safeguard.health.HealthQuestion
import com.safeguard.health.HealthQuote
import com.safeguard.health.HealthRating
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.LocalDate

@Transactional(readOnly = true)
class HealthTakafulEmaratService {

    public static final Integer PROVIDER_ID = 35

    public static final Integer NEXTCARE_RHODIUM_ID = 326
    public static final Integer NEXTCARE_PLATINUM_ID = 327
    public static final Integer NEXTCARE_GOLD_PLUS_ID = 5167
    public static final Integer NEXTCARE_GOLD_ID = 328
    public static final Integer NEXTCARE_IRIDIUM_ID = 329
    public static final Integer NEXTCARE_SILVER_ID = 330

    public static final Integer NEXTCARE_DEDUCTIBLE_ZERO = 72
    public static final Integer NEXTCARE_DEDUCTIBLE_TEN = 354
    public static final Integer NEXTCARE_DEDUCTIBLE_TWENTY = 346

    public static final Integer ECARE_BLUE_PLAN_1 = 5113
    public static final Integer ECARE_BLUE_PLAN_2 = 5114
    public static final Integer ECARE_BLUE_PLAN_3 = 5115
    public static final Integer ECARE_BLUE_PLAN_4 = 5116


    final Integer PREGNANCY_LOADING = 11000


    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        Map rateMap = [:]
        int index = 0
        boolean isEligibleForEBP = quoteCommand.viewEbp
        boolean isEligibleForNonEBP = checkEligibilityForNonEBP(quoteCommand)
        boolean isEligibleForAgeGroupAgainstRelation = checkEligibilityForAgeGroupAgainstRelationship(quoteCommand)
        Boolean hasSelfOrSpouseMember = quoteCommand.members.any {
            it.relationship.id.toInteger() == HealthRelationshipEnum.SPOUSE.value() ||
                it.relationship.id.toInteger() == HealthRelationshipEnum.SPONSOR.value() }

        if (isEligibleForAgeGroupAgainstRelation) {
            if (isEligibleForEBP || isEligibleForNonEBP) {
                for (member in quoteCommand.members) {

                    Integer memberAge = getMemberAge(member)
                    List<HealthRating> ratingList

                    if (quoteCommand.isAnyMemberPregnantOnly && isEligibleForEBP) {
                        ratingList = healthRatingService.findApplicableRates(PROVIDER_ID, ProductEnum.TAKAFUL_EMARAT_DHA_BASIC_PLAN.id, quoteCommand.deductibleId,
                            memberAge, member.gender, quoteCommand.cityId, member.relationship, !quoteCommand.salaryOver4k)
                    } else if (isEligibleForEBP) {
                        HealthQuote healthQuote = HealthQuote.read(quoteCommand.id)
                        Boolean noAnswers = healthQuote.isLost || healthQuote.isRenewal

                        if (noAnswers) {
                            ratingList = healthRatingService.findApplicableRates(PROVIDER_ID, ProductEnum.TAKAFUL_EMARAT_MEDICAL_TAKAFUL_PLAN.id, quoteCommand.deductibleId,
                                memberAge, member.gender, quoteCommand.cityId, member.relationship, !quoteCommand.salaryOver4k)
                        } else {
                            ratingList = healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                                memberAge, member.gender, quoteCommand.cityId, member.relationship, !quoteCommand.salaryOver4k, null, null, true)
                        }
                    } else if (isEligibleForNonEBP) {
                        ratingList = healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                            memberAge, member.gender, quoteCommand.cityId, member.relationship, !quoteCommand.salaryOver4k, null, null, false)
                    }

                    if (!ratingList) {
                        isEligibleForEBP = false
                        break
                    }
                    int count = 0

                    for (HealthRating rating in ratingList) {

                        if (index > 0) {
                            HealthRateCommand rateCommand = rateCommandList[count]
                            BigDecimal memberPremium = getBasePremium(hasSelfOrSpouseMember, memberAge, rating) //rating.premium
                            rateCommand.premium = rateCommand.premium + memberPremium
                            HealthRateMemberCommand healthRateMemberCommand = healthRatingService.getMember(rating, member)
                            healthRateMemberCommand.premium = memberPremium
                            rateCommand.members.add(healthRateMemberCommand)

                            if (rating.productId.toInteger() == ProductEnum.TAKAFUL_EMARAT_DHA_BASIC_PLAN.id && memberAge <= 6) {
                                rateCommand.premium += 750
                                healthRateMemberCommand.premium += 750
                            }

                            count++
                            continue
                        }

                        HealthRateCommand rateCommand = new HealthRateCommand()
                        rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                        List memberList = []
                        BigDecimal memberPremium = getBasePremium(hasSelfOrSpouseMember, memberAge, rating) //rating.premium
                        rateCommand.premium = (rateCommand.premium ?: 0) + memberPremium
                        HealthRateMemberCommand healthRateMemberCommand = healthRatingService.getMember(rating, member)
                        healthRateMemberCommand.premium = memberPremium
                        memberList.add(healthRateMemberCommand)
                        rateCommand.members = memberList
                        rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                        rateCommandList.add(rateCommand)

                        if (rating.productId.toInteger() == ProductEnum.TAKAFUL_EMARAT_DHA_BASIC_PLAN.id && memberAge <= 6) {
                            rateCommand.premium += 750
                            healthRateMemberCommand.premium += 750
                        }
                        rateMap.put(rateCommand.productId + '' + rateCommand.deductibleId + '' + rateCommand.networkId, rateCommand)
                    }

                    index++
                }

                if (!isEligibleForEBP && !isEligibleForNonEBP) {
                    rateCommandList = []
                }

                rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)
                rateCommandList = healthRatingService.applyC4meFee(rateCommandList)
                rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

                Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
                if (pricesInclusiveVat) {
                    rateCommandList = healthRatingService.applyVAT(rateCommandList)
                }
            }
        }
        List<HealthRateCommand> newMemberList = []

        rateMap.each { key, HealthRateCommand rateCommand ->
            if (rateCommand.members.size() == quoteCommand.members.size()) {
                newMemberList.add(rateCommand)
            }
        }

       // rateCommandList
        newMemberList
    }


    Integer getMemberAge(HealthMemberCommand member) {
        if(!member.dob){
            return (member.age + 1)
        }
        else {

            Integer days = Days.daysBetween(member.dob, new LocalDate()).getDays()
            Integer age = (days / 365.25).setScale(0, BigDecimal.ROUND_HALF_UP).toInteger()

            log.info("healthTakafulEmaratService.getMemberAge - dob:${member.dob} days:${days}, age:${age}")

            return  age
        }
    }

    private boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        boolean isEligible = true

        /*if (quoteCommand.insuranceForYourself || quoteCommand.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
            isEligible = false
        } else if (quoteCommand.applicationType == HealthApplicationTypeEnum.WORKER || quoteCommand.applicationType == HealthApplicationTypeEnum.FAMILY) {
            for (HealthMemberCommand memberCommand : quoteCommand.members) {
                if (memberCommand.salaryOver4k) {
                    isEligible = false
                    break
                }
            }

        }*/

        isEligible
    }

    private Boolean checkEligibilityForNonEBP(HealthQuoteCommand healthQuoteCommand) {

        boolean isEligible = true

        /*if (healthQuoteCommand.applicationType in [HealthApplicationTypeEnum.WORKER, HealthApplicationTypeEnum.EMPLOYEES]) {
            isEligible = false
        }*/

        isEligible
    }

    private Boolean checkEligibilityForAgeGroupAgainstRelationship(HealthQuoteCommand healthQuoteCommand){
        boolean isEligible = true

        if (healthQuoteCommand.applicationType == HealthApplicationTypeEnum.FAMILY){

            for (HealthMemberCommand memberCommand : healthQuoteCommand.members) {
                int id = memberCommand.relationship.getId()
                if (id == HealthRelationshipEnum.CHILD.value() && getMemberAge(memberCommand) > 18 ) {
                    isEligible = false
                    break
                }
                if (id == HealthRelationshipEnum.SPOUSE.value() && getMemberAge(memberCommand) < 18) {
                    isEligible = false
                    break
                }
                if (id == HealthRelationshipEnum.PARENT.value() && getMemberAge(memberCommand) < 45) {
                    isEligible = false
                    break
                }
                if (id == HealthRelationshipEnum.SPONSOR.value() && getMemberAge(memberCommand) < 18) {
                    isEligible = false
                    break
                }
            }
        }

        isEligible

    }

    private Boolean isPregnancyAnsweredTrue(Long memberId) {
        HealthQuestion pregnancyQuestion = HealthQuestion.read(Constants.HEALTH_PREGNANCE_QUESTION_ID)
        HealthAnswer healthAnswer = HealthAnswer.findByMemberAndHealthQuestion(HealthMember.load(memberId), pregnancyQuestion, [readOnly: true])
        healthAnswer?.value
    }

    private BigDecimal getBasePremium(boolean hasSelfOrSpouseMember, Integer memberAge, HealthRating rate) {
        if (hasSelfOrSpouseMember) return rate.premium
        if (memberAge > 6) return rate.premium
        if (!(rate.productId in [NEXTCARE_RHODIUM_ID, NEXTCARE_PLATINUM_ID, NEXTCARE_GOLD_PLUS_ID,
                                 NEXTCARE_GOLD_ID, NEXTCARE_IRIDIUM_ID, NEXTCARE_SILVER_ID,
                                 ECARE_BLUE_PLAN_1, ECARE_BLUE_PLAN_2, ECARE_BLUE_PLAN_3, ECARE_BLUE_PLAN_4])) {
            return rate.premium
        }

        if (rate.productId == NEXTCARE_RHODIUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_ZERO) {
            if (memberAge <= 1) { return 18278.70 }
            else if (memberAge <= 5) {return 12274.25 }
            else if (memberAge == 6) {return 9535.20 }
        }
        else if (rate.productId == NEXTCARE_RHODIUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TEN) {
            if (memberAge <= 1) { return 16048.60 }
            else if (memberAge <= 5) {return 10774.95 }
            else if (memberAge == 6) {return 8370.85 }
        }
        else if (rate.productId == NEXTCARE_RHODIUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TWENTY) {
            if (memberAge <= 1) { return 15307.65 }
            else if (memberAge <= 5) {return 10279.05 }
            else if (memberAge == 6) {return 7985.15 }
        }

        else if (rate.productId == NEXTCARE_PLATINUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_ZERO) {
            if (memberAge <= 1) { return 17033.15 }
            else if (memberAge <= 5) {return 11437.60 }
            else if (memberAge == 6) {return 8884.15 }
        }
        else if (rate.productId == NEXTCARE_PLATINUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TEN) {
            if (memberAge <= 1) { return 14826.25 }
            else if (memberAge <= 5) {return 9955.70 }
            else if (memberAge == 6) {return 7732.85 }
        }
        else if (rate.productId == NEXTCARE_PLATINUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TWENTY) {
            if (memberAge <= 1) { return 14028.75 }
            else if (memberAge <= 5) {return 9420.65 }
            else if (memberAge == 6) {return 7318.15 }
        }

        else if (rate.productId == NEXTCARE_GOLD_PLUS_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_ZERO) {
            if (memberAge <= 1) { return 14330.35 }
            else if (memberAge <= 5) {return 9622.20 }
            else if (memberAge == 6) {return 7474.75 }
        }
        else if (rate.productId == NEXTCARE_GOLD_PLUS_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TEN) {
            if (memberAge <= 1) { return 12472.90 }
            else if (memberAge <= 5) {return 8375.20 }
            else if (memberAge == 6) {return 6506.15 }
        }
        else if (rate.productId == NEXTCARE_GOLD_PLUS_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TWENTY) {
            if (memberAge <= 1) { return 11801.55 }
            else if (memberAge <= 5) {return 7925.70 }
            else if (memberAge == 6) {return 6156.70 }
        }

        else if (rate.productId == NEXTCARE_GOLD_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_ZERO) {
            if (memberAge <= 1) { return 11163.55 }
            else if (memberAge <= 5) {return 7328.30 }
            else if (memberAge == 6) {return 5701.40 }
        }
        else if (rate.productId == NEXTCARE_GOLD_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TEN) {
            if (memberAge <= 1) { return 9643.95 }
            else if (memberAge <= 5) {return 6329.25 }
            else if (memberAge == 6) {return 4924.20 }
        }
        else if (rate.productId == NEXTCARE_GOLD_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TWENTY) {
            if (memberAge <= 1) { return 9042.20 }
            else if (memberAge <= 5) {return 5934.85 }
            else if (memberAge == 6) {return 4616.80 }
        }

        else if (rate.productId == NEXTCARE_IRIDIUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_ZERO) {
            if (memberAge <= 1) { return 9578.70 }
            else if (memberAge <= 5) {return 6395.95 }
            else if (memberAge == 6) {return 4974.95 }
        }
        else if (rate.productId == NEXTCARE_IRIDIUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TEN) {
            if (memberAge <= 1) { return 8204.10 }
            else if (memberAge <= 5) {return 5479.55 }
            else if (memberAge == 6) {return 4261.55 }
        }
        else if (rate.productId == NEXTCARE_IRIDIUM_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TWENTY) {
            if (memberAge <= 1) { return 7628.45 }
            else if (memberAge <= 5) {return 5095.30 }
            else if (memberAge == 6) {return 3962.85 }
        }

        else if (rate.productId == NEXTCARE_SILVER_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_ZERO) {
            if (memberAge <= 1) { return 3929.50 }
            else if (memberAge <= 5) {return 2652.05 }
            else if (memberAge == 6) {return 2063.35 }
        }
        else if (rate.productId == NEXTCARE_SILVER_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TEN) {
            if (memberAge <= 1) { return 3339.35 }
            else if (memberAge <= 5) {return 2253.30 }
            else if (memberAge == 6) {return 1753.05 }
        }
        else if (rate.productId == NEXTCARE_SILVER_ID && rate.deductibleId == NEXTCARE_DEDUCTIBLE_TWENTY) {
            if (memberAge <= 1) { return 3078.35 }
            else if (memberAge <= 5) {return 2076.40 }
            else if (memberAge == 6) {return 1615.30 }
        }

        else if (rate.productId == ECARE_BLUE_PLAN_1) {
            if (memberAge <= 1) { return 2649.15 }
            else if (memberAge <= 6) {return 1471.75 }
        }
        else if (rate.productId == ECARE_BLUE_PLAN_2) {
            if (memberAge <= 1) { return 2847.80 }
            else if (memberAge <= 6) {return 1583.40 }
        }
        else if (rate.productId == ECARE_BLUE_PLAN_3) {
            if (memberAge <= 1) { return 2962.35 }
            else if (memberAge <= 6) {return 1645.75 }
        }
        else if (rate.productId == ECARE_BLUE_PLAN_4) {
            if (memberAge <= 1) { return 3311.80 }
            else if (memberAge <= 6) {return 1840.05 }
        }

    }

}
