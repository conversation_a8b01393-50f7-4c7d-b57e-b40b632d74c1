package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.CityEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.health.HealthRating
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.Duration
import org.joda.time.LocalDate
import org.joda.time.Period

/**
 * Health rating service for Cigna Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthCignaRateService {

    public static final Integer PROVIDER_ID = 50

    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)
        Boolean hasSponsor = quoteCommand.members.any { it.relationship.id.toInteger() == HealthRelationshipEnum.SPONSOR.value() }
        Boolean hasFamilyMember = quoteCommand.members.any { it.relationship.id.toInteger() in [HealthRelationshipEnum.CHILD.value(), HealthRelationshipEnum.PARENT.value(), HealthRelationshipEnum.SPOUSE.value()] }

        for (member in quoteCommand.members) {

            //Integer memberAge = member.age +
            Integer memberAge = getMemberAge(member)
            log.info("cignaRate.getRates - memberAge:${memberAge}")

            boolean salaryBelow4k = quoteCommand.applicationType == HealthApplicationTypeEnum.WORKER
                ? !member.salaryOver4k : !quoteCommand.salaryOver4k

            List<HealthRating> ratingList =
                healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                    memberAge, member.gender, quoteCommand.cityId, member.relationship, salaryBelow4k,
                    null, quoteCommand.networkId, quoteCommand.viewEbp)
            log.info("cignaRate.getRates - ratingList:${ratingList.size()}")

            if (!ratingList) {
                isEligible = false
                break
            }
            int count = 0
            for (HealthRating rating in ratingList) {

                // for the same product / deductible combination update the premium
                // and add it to member list instead of creating new rateCommand.
                // For 2nd and more iteration same product are repeated so we add them
                // into member details
                if (index > 0) {
                    HealthRateCommand rateCommand = rateCommandList[count]
                    rateCommand.premium = rateCommand.premium + rating.premium
                    rateCommand.members.add(healthRatingService.getMember(rating, member))

                    count++
                    continue
                }

                HealthRateCommand rateCommand = new HealthRateCommand()
                // add covers for the plan
                rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                List memberList = []
                rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                memberList.add(healthRatingService.getMember(rating, member))
                rateCommand.members = memberList
                rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                rateCommandList.add(rateCommand)
            }
            index++
        }

        if (!isEligible) {
            rateCommandList = []
        }

        rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommandList = healthRatingService.applyVAT(rateCommandList)
        }

        rateCommandList
    }
    /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if (!member.dob) {
            return (member.age)
        }
        else {

            Period p = new Period(member.dob, new LocalDate())
            Integer age = p.getYears()

            return  age
        }
    }

    boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        boolean isEligible = true

        // Child(ren) member(s) only can not be insured
//        boolean hasNonChildMember = false

        List<HealthMemberCommand> members = quoteCommand.members
        members.each { HealthMemberCommand memberCommand ->
//            if (memberCommand.relationship.id != HealthRelationshipEnum.CHILD.value()) {
//                hasNonChildMember = true
//            }

            if (memberCommand.relationship.id == HealthRelationshipEnum.SPONSOR.value()
                && getMemberAge(memberCommand) < 18)  {
                //Sponsor can not be less than 18
                isEligible = false
            }

            if (memberCommand.relationship.id == HealthRelationshipEnum.SPOUSE.value()
                && getMemberAge(memberCommand) < 18)  {
                //Spouse can not be less than 18
                isEligible = false
            }

            //Not eligible for Cuba, Iran, North Korea, Sudan, Syria
            if (!memberCommand.nationality || memberCommand.nationality.id in [41, 74, 116, 146, 151] ) {
                isEligible = false
            }

        }

//        if (!hasNonChildMember || quoteCommand.source != RequestSourceEnum.WEB.toString()) {
        if (quoteCommand.source != RequestSourceEnum.WEB.toString()) {
            isEligible = false
        }

        //If Dubai Visa Holder, salary cannot be 4k Or below
        /*if (quoteCommand.cityId == CityEnum.DUBAI.value() && !quoteCommand.salaryOver4k) {
            isEligible = false
        }*/

        /*if (restrictedCountry()) {
            isEligible = false
        }*/

        return isEligible
    }

    /*private boolean isRestrictedNationality() {
        Cuba, Iran, North Korea, Sudan, Syria.
    }*/
}
