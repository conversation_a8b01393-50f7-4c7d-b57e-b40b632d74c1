package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.CityEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.health.HealthRating
import com.safeguard.health.HealthRelationship
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.Duration
import org.joda.time.LocalDate
import org.joda.time.Period

import java.text.DateFormat
import java.text.SimpleDateFormat

/**
 * Health rating service for Dubai National Insurance and Reinsurance.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthDubaiNationalRateService {

    public static final Integer PROVIDER_ID = 41

    public static final Integer MEDNET_GOLD_ID = 1051
    public static final Integer MEDNET_SILVER_PREMIUM_ID = 1052
    public static final Integer MEDNET_SILVER_CLASSIC_ID = 1053
    public static final Integer MEDNET_GREEN_ID = 1054
    public static final Integer MEDNET_SILK_ROAD_ID = 1055


    public static final Integer NEXTCARE_PLAN_PLATINUM_PLUS_ID = 1058
    public static final Integer NEXTCARE_PLAN_PLATINUM_ID = 1059
    public static final Integer NEXTCARE_PLAN_DIAMOND_ID = 1060
    public static final Integer NEXTCARE_PLAN_GOLD_ID = 1061
    public static final Integer NEXTCARE_PLAN_SILVER_ID = 1062
    public static final Integer NEXTCARE_PLAN_ESSENTIAL_ID = 1063


    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)
        Boolean hasSponsor = quoteCommand.members.any { it.relationship.id.toInteger() == HealthRelationshipEnum.SPONSOR.value() }
        Boolean hasFamilyMember = quoteCommand.members.any { it.relationship.id.toInteger() in [HealthRelationshipEnum.CHILD.value(), HealthRelationshipEnum.PARENT.value(), HealthRelationshipEnum.SPOUSE.value()] }

        for (member in quoteCommand.members) {

            //Integer memberAge = member.age +
            Integer memberAge = getMemberAge(member)
            log.info("healthDubaiNationalRate.getRates - memberAge:${memberAge}")

            List<HealthRating> ratingList =
                healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                    memberAge, member.gender, quoteCommand.cityId, member.relationship, !quoteCommand.salaryOver4k)
            log.info("healthDubaiNationalRate.getRates - ratingList:${ratingList.size()}")

            if (!ratingList) {
                isEligible = false
                break
            }
            int count = 0
            for (HealthRating rating in ratingList) {

                //If MEDNET product and no family member, then product isnt allowed
                /*if (!hasFamilyMember && rating.productId in [MEDNET_GOLD_ID, MEDNET_GREEN_ID, MEDNET_SILK_ROAD_ID,
                                                             MEDNET_SILVER_CLASSIC_ID, MEDNET_SILVER_PREMIUM_ID]) {
                    //loop through other products, leave this one
                    continue
                }*/

                // for the same product / deductible combination update the premium
                // and add it to member list instead of creating new rateCommand.
                // For 2nd and more iteration same product are repeated so we add them
                // into member details
                if (index > 0) {
                    HealthRateCommand rateCommand = rateCommandList[count]
                    rateCommand.premium = rateCommand.premium + rating.premium
                    rateCommand.members.add(healthRatingService.getMember(rating, member))

                    count++
                    continue
                }

                HealthRateCommand rateCommand = new HealthRateCommand()
                // add covers for the plan
                rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                List memberList = []
                rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                memberList.add(healthRatingService.getMember(rating, member))
                rateCommand.members = memberList
                rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                rateCommandList.add(rateCommand)
            }
            index++
        }

        if (!isEligible) {
            rateCommandList = []
        }

        rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommandList = healthRatingService.applyVAT(rateCommandList)
        }

        rateCommandList
    }
    /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if(!member.dob){
            return (member.age + 1)
        }
        else {

            Period p = new Period(member.dob, new LocalDate())
            Integer age = p.getYears()
            //If 6th month started then next year is started
            if (p.getMonths() >= 6) {
                age = age + 1
            }

            //log.info("healthDubaiNationalRate.getMemberAge - dob:${member.dob}, months:${p.getMonths()}, days:${p.getDays()}, age:${age}")

            return  age
        }
    }

    boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        boolean isEligible = false

        // Child(ren) member(s) only can not be insured
        boolean hasNonChildMember = false
        boolean sponsorBelowUnderAge = false

        List<HealthMemberCommand> members = quoteCommand.members
        members.each { HealthMemberCommand memberCommand ->
            if (memberCommand.relationship.id != HealthRelationshipEnum.CHILD.value()) {
                hasNonChildMember = true
            }

            if (memberCommand.relationship.id == HealthRelationshipEnum.SPONSOR.value()
                && getMemberAge(memberCommand) < 18)  {
                sponsorBelowUnderAge = true
            }

        }

        if (hasNonChildMember) {
            isEligible = true
        }

        if (sponsorBelowUnderAge) {
            isEligible = false
        }

        //If Dubai Visa Holder, salary cannot be 4k Or below
        if (quoteCommand.cityId == CityEnum.DUBAI.value() && !quoteCommand.salaryOver4k) {
            isEligible = false
        }

        return isEligible
    }
}
