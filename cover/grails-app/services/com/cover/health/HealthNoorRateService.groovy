package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.GenderEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.health.HealthMember
import com.safeguard.health.HealthRating
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.LocalDate
import org.joda.time.Period

/**
 * Health rating service for Watania.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthNoorRateService {

    public static final Integer PROVIDER_ID = 4

    public static final Integer PRODUCT_DEPENDENT_BASIC_1_ID = 56     //Option 4, 1 & 8
    public static final Integer PRODUCT_DEPENDENT_BASIC_2_ID = 57     //Option 5 & 2
    public static final Integer PRODUCT_DEPENDENT_BASIC_3_ID = 58     //Option 6 & 3
    public static final Integer PRODUCT_EBP_DEPENDENTS_BASIC_ID = 55  //Option 9, 10

    public static final Integer PRODUCT_EBP_DOMESTIC_WORKER_ID = 94   //Option 7


    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        Map rateMap = [:]
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)
        boolean isSalaryBelow4k = isSalaryBelow4k(quoteCommand)
        Boolean hasNonChildFemaleMember = quoteCommand.members.any {
            it.gender.id.toInteger() != GenderEnum.MALE.id && it.relationship.id.toInteger() in [HealthRelationshipEnum.SPONSOR.value(), HealthRelationshipEnum.SPOUSE.value()] && getMemberAge(it) < 65
        }

        if (hasNonChildFemaleMember) {
            log.info("healthNoorRateService.getRates - hasNonChildFemaleMember : true for quote: ${quoteCommand.id}")
        } else {
            HealthMemberCommand healthMemberCommand = quoteCommand.members.first()
            log.info("healthNoorRateService.getRates - hasNonChildFemaleMember : false for quote: ${quoteCommand.id}, gender: ${healthMemberCommand.gender.nameEn}, relationship: ${healthMemberCommand.relationship}, age: ${getMemberAge(healthMemberCommand)}")
        }

        if (isEligible) {
            for (member in quoteCommand.members) {

                //Integer memberAge = member.age + 1
                Integer memberAge = getMemberAge(member)
                List<HealthRating> ratingList =
                    healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, null,
                        memberAge, member.gender, quoteCommand.cityId, member.relationship, isSalaryBelow4k, null,
                        quoteCommand.networkId, quoteCommand.viewEbp)

                int count = 0
                for (HealthRating rating in ratingList) {

                    //Allow LSB Employee dependent product when employee/sponsor below 4k, otherwise not allowed
                    /*if (rating.productId == PRODUCT_EBP_DEPENDENTS_BASIC_ID && quoteCommand.salaryOver4k) {
                        continue
                    }*/

                    //Allow Non LSB Employee dependent product when employee/sponsor above 4k, otherwise not allowed
                    /*if (rating.productId in [PRODUCT_DEPENDENT_BASIC_1_ID,
                                             PRODUCT_DEPENDENT_BASIC_2_ID,
                                             PRODUCT_DEPENDENT_BASIC_3_ID] && !quoteCommand.salaryOver4k) {
                        continue
                    }*/

                    // for the same product / deductible combination update the premium
                    // and add it to member list instead of creating new rateCommand.
                    // For 2nd and more iteration same product are repeated so we add them
                    // into member details
                    if (index > 0) {
                        HealthRateCommand rateCommand = rateCommandList[count]
                        if (!rateCommand) {
                            break
                        }
                        rateCommand.premium = rateCommand.premium + rating.premium
                        rateCommand.members.add(healthRatingService.getMember(rating, member))
                        count++
                        continue
                    }

                    HealthRateCommand rateCommand = new HealthRateCommand()
                    // add covers for the plan
                    rateCommand = healthRatingService.applyCovers(rateCommand, rating)
                    applyCovers(rateCommand, hasNonChildFemaleMember)

                    List memberList = []
                    rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                    memberList.add(healthRatingService.getMember(rating, member))
                    rateCommand.members = memberList
                    rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                    rateCommandList.add(rateCommand)
                    rateMap.put(rateCommand.productId+''+rateCommand.deductibleId + ''+rateCommand.networkId, rateCommand)
                }
                index++
            }
        }

        List<HealthRateCommand> newMemberList = []

        rateMap.each { key, HealthRateCommand rateCommand ->
            if (rateCommand.members.size() == quoteCommand.members.size()) {
                newMemberList.add(rateCommand)
            }
        }

        if (!isEligible) {
            newMemberList = []
        }
        newMemberList = healthRatingService.storeOriginalPremiumBeforeC4meFee(newMemberList)

        newMemberList = healthRatingService.applyC4meFee(newMemberList)

        newMemberList = healthRatingService.applyExtraDiscount(newMemberList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            newMemberList = healthRatingService.applyVAT(newMemberList)
        }

        newMemberList
    }

    private boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        boolean isEligible = true
       /* if (!quoteCommand.salaryOver4k) {
            isEligible = false
        }*/
        isEligible
    }

    /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if (!member.dob) {
            return (member.age + 1)
        } else {
            Integer days = Days.daysBetween(member.dob, new LocalDate()).getDays()
            Integer age = (days / 365.25).setScale(0, BigDecimal.ROUND_HALF_UP).toInteger()
            return  age
        }
    }

    boolean isSalaryBelow4k(HealthQuoteCommand quoteCommand) {

        boolean isBelow4k = true
        if (quoteCommand.applicationType == HealthApplicationTypeEnum.WORKER) {
            quoteCommand.members.each { HealthMemberCommand memberCommand ->
                if (isBelow4k && memberCommand.salaryOver4k) {
                    isBelow4k = false
                }
            }

        } else {
            if (quoteCommand.salaryOver4k) {
                isBelow4k = false
            }
        }
        isBelow4k
    }

    private void applyCovers(HealthRateCommand rateCommand, Boolean hasNonChildFemaleMember) {
        if (rateCommand.productId in [PRODUCT_DEPENDENT_BASIC_1_ID, PRODUCT_DEPENDENT_BASIC_2_ID, PRODUCT_DEPENDENT_BASIC_3_ID, PRODUCT_EBP_DEPENDENTS_BASIC_ID] && !hasNonChildFemaleMember) {
            rateCommand.maternity = null
        }

        if (rateCommand.productId in [PRODUCT_DEPENDENT_BASIC_2_ID, PRODUCT_DEPENDENT_BASIC_3_ID] && hasNonChildFemaleMember) {
            rateCommand.coverage = "${rateCommand.coverage} (and extended to ISC for married female only)"
        }
    }
}
