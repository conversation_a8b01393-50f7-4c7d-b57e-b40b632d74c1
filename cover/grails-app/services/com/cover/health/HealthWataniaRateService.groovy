package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.cover.health.commands.HealthRateMemberCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.health.HealthRating
import grails.transaction.Transactional
import org.joda.time.Days
import org.joda.time.LocalDate
import org.joda.time.Period

/**
 * Health rating service for Watania.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthWataniaRateService {

    public static final Integer PROVIDER_ID = 6

    def grailsApplication
    def healthRatingService

    List<HealthRateCommand> getRates(HealthQuoteCommand quoteCommand) {

        // fetch all ratings
        List<HealthRateCommand> rateCommandList = []
        int index = 0
        boolean isEligible = checkEligibility(quoteCommand)

        for (member in quoteCommand.members) {

            //Integer memberAge = member.age + 1
            Integer memberAge = getMemberAge(member)
            List<HealthRating> ratingList =
                healthRatingService.findApplicableRates(PROVIDER_ID, quoteCommand.productId, quoteCommand.deductibleId,
                    memberAge, member.gender, quoteCommand.cityId, null)

            if (!ratingList) {
                isEligible = false
                break
            }
            int count = 0
            for (HealthRating rating in ratingList) {

                // for the same product / deductible combination update the premium
                // and add it to member list instead of creating new rateCommand.
                // For 2nd and more iteration same product are repeated so we add them
                // into member details
                if (index > 0) {
                    HealthRateCommand rateCommand = rateCommandList[count]
                    rateCommand.premium = rateCommand.premium + rating.premium
                    rateCommand.members.add(healthRatingService.getMember(rating, member))

                    count++
                    continue
                }

                HealthRateCommand rateCommand = new HealthRateCommand()
                // add covers for the plan
                rateCommand = healthRatingService.applyCovers(rateCommand, rating)

                List memberList = []
                rateCommand.premium = (rateCommand.premium ?: 0) + rating.premium
                memberList.add(healthRatingService.getMember(rating, member))
                rateCommand.members = memberList
                rateCommand.isEbp = (rating.product.type?.id == CoverageTypeEnum.HEALTH_EBP.value())
                rateCommandList.add(rateCommand)
            }
            index++
        }

        if (!isEligible) {
            rateCommandList = []
        }

        rateCommandList = healthRatingService.storeOriginalPremiumBeforeC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyC4meFee(rateCommandList)

        rateCommandList = healthRatingService.applyExtraDiscount(rateCommandList, quoteCommand)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommandList = healthRatingService.applyVAT(rateCommandList)
        }

        rateCommandList
    }

    /**
     * Apply loading
     *
     * @param rateCommandList
     * @param quoteCommand
     * @return
     */
    List<HealthRateCommand> applyLoading(List<HealthRateCommand> rateCommandList, HealthQuoteCommand quoteCommand) {
        /*
            if spouse alone, child alone or spouse+child alone, apply 30% loading
         */
        boolean hasStandaloneDependent = true

        List<HealthMemberCommand> members = quoteCommand.members
        members.each { HealthMemberCommand memberCommand ->
            if (memberCommand.relationship.id == HealthRelationshipEnum.SPONSOR.value() ||
                memberCommand.relationship.id == HealthRelationshipEnum.EMPLOYEE.value() ||
                memberCommand.relationship.id == HealthRelationshipEnum.DOMESTIC_WORKER.value() ||
                memberCommand.relationship.id == HealthRelationshipEnum.PARENT.value()) {
                hasStandaloneDependent = false
            }
        }

        if (hasStandaloneDependent) {
            log.debug("healthWatania.applyLoading - quoteCommand:${quoteCommand} - contains standalone members")
            rateCommandList.each { HealthRateCommand rateCommand ->
                rateCommand.premium = rateCommand.premium.add(rateCommand.premium * 0.30)
                rateCommand.members.each { HealthRateMemberCommand healthRateMemberCommand ->
                    healthRateMemberCommand.premium =
                        healthRateMemberCommand.premium.add(healthRateMemberCommand.premium * 0.30)
                }
            }
        }

        rateCommandList
    }

    boolean checkEligibility(HealthQuoteCommand quoteCommand) {

        boolean isEligible = false

        boolean hasNonChildMember = false

        //Any domestic worker below 4k salary
        boolean hasDomesticWorkerBelow4kSalary = false

        List<HealthMemberCommand> members = quoteCommand.members
        members.each { HealthMemberCommand memberCommand ->
            if (memberCommand.relationship.id != HealthRelationshipEnum.CHILD.value()) {
                hasNonChildMember = true
            }
            if (memberCommand.relationship.id == HealthRelationshipEnum.DOMESTIC_WORKER.value()
                && !memberCommand.salaryOver4k) {
                hasDomesticWorkerBelow4kSalary = true
            }
        }

        if (hasNonChildMember) {
            isEligible = true
        }

        if (hasDomesticWorkerBelow4kSalary) {
            isEligible = false
        }

        return isEligible
    }
    /**
     * @param HealthMemberCommand
     * @return Member Age
     */
    Integer getMemberAge(HealthMemberCommand member) {
        if(!member.dob){
            return (member.age + 1)
        }
        else {
            Integer days = Days.daysBetween(member.dob, LocalDate.now()).days
            BigDecimal years = days / 365

            return years.setScale(0, BigDecimal.ROUND_HALF_UP)
        }
    }
}
