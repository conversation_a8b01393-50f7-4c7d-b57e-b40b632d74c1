package com.cover.health

import com.cover.api.v2.HealthMemberV2Command
import com.cover.api.v2.HealthQuoteV2Command
import com.cover.api.v2.HealthUpdateMemberV2Command
import com.cover.api.v2.HealthUpdateQuoteV2Command
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.health.commands.*
import com.safeguard.AdditionalCharges
import com.safeguard.AddonCodeEnum
import com.safeguard.AddonTypeEnum
import com.safeguard.City
import com.safeguard.CityEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.ExtraFieldCodeEnum
import com.safeguard.GenderEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.HealthApplicationTypeV2Enum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.LeadStatusReasonEnum
import com.safeguard.LeadType
import com.safeguard.MaritalStatusEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentPlanEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.ProductType
import com.safeguard.QuoteExtraField
import com.safeguard.RequestSourceEnum
import com.safeguard.SubRequestSourceEnum
import com.safeguard.User
import com.safeguard.UserDetails
import com.safeguard.base.Comparison
import com.safeguard.car.Addon
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.translation.AddonTranslation
import com.safeguard.health.HealthAnswer
import com.safeguard.health.HealthGender
import com.safeguard.health.HealthMember
import com.safeguard.health.HealthQuestion
import com.safeguard.health.HealthQuote
import com.safeguard.health.HealthQuoteResult
import com.safeguard.health.HealthRelationship
import com.safeguard.health.ResidentType
import com.safeguard.payment.RecurringPayment
import grails.gorm.DetachedCriteria
import grails.transaction.Transactional
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.json.JSONObject

import java.math.RoundingMode

/**
 * <AUTHOR>
 */
@Transactional
class HealthQuoteService {

    def grailsApplication
    def healthRatingService
    def healthWataniaRateService
    def healthDamanRateService
    def healthNoorRateService
    def healthOrientRateService
    def healthSalamaRateService
    def healthUnionRateService
    def healthTakafulEmaratService
    def healthDubaiNationalRateService
    def healthCignaRateService
    def healthAdamjeeRateService
    def checkoutService
    def commonUtilService
    def paymentService
    def crmService
    def utilService
    def additionalChargesService
    def leadSgService
    def healthPolicySgService

    @Transactional(readOnly = true)
    List<HealthRateCommand> getRatings(HealthQuoteCommand quoteCommand) {
        // force fully setting to null to fetch all ratings
        quoteCommand.productId = null
        quoteCommand.deductibleId = null
        quoteCommand.networkId = null
        log.info "health.quote.service.getRatings - ${quoteCommand}"

        getAllRatings(quoteCommand)
    }

    private List<HealthRateCommand> getAllRatings(HealthQuoteCommand quoteCommand) {


        List<HealthRateCommand> rateList = []

        rateList.addAll(healthAdamjeeRateService.getRates(quoteCommand))
        rateList.addAll(healthWataniaRateService.getRates(quoteCommand))
        rateList.addAll(healthDamanRateService.getRates(quoteCommand))
        rateList.addAll(healthNoorRateService.getRates(quoteCommand))
        rateList.addAll(healthSalamaRateService.getRates(quoteCommand))
        rateList.addAll(healthUnionRateService.getRates(quoteCommand))
        try {
            rateList.addAll(healthOrientRateService.getRates(quoteCommand))
        } catch (NullPointerException npe) {
            log.error("Exception while getting Orient rate. QuoteCommand:${quoteCommand} ", npe)
        }
        rateList.addAll(healthDubaiNationalRateService.getRates(quoteCommand))
        rateList.addAll(healthCignaRateService.getRates(quoteCommand))
        rateList.addAll(healthTakafulEmaratService.getRates(quoteCommand))

        List<HealthRateCommand> healthRateCommandsAfterAdditionCharges = additionalChargesService.addAdditionalChargesToPremiums(rateList, ProductType.findByName('HEALTH'))

        healthRateCommandsAfterAdditionCharges
    }

    @Transactional(readOnly = true)
    List<HealthRateCommand> getRatings(HealthQuote quote) {
        log.info "health.quote.service.getRatings quote - ${quote.id}"

        HealthQuoteCommand healthQuoteCommand = toHealthQuoteCommand(quote)

        getRatings(healthQuoteCommand)
    }

    @Transactional(readOnly = true)
    HealthRateCommand getRating(HealthQuoteCommand quoteCommand) {
        log.info "health.quote.service.getRating - ${quoteCommand}"

        List<HealthRateCommand> rateCommandList = getAllRatings(quoteCommand)

        rateCommandList.first()
    }

    HealthQuote createHealthQuote(HealthQuoteCommand healthQuoteCommand) {
        log.info(".createHealthQuote Entering with healthDetailsCommand: ${healthQuoteCommand}")

        User user = User.findByEmail(healthQuoteCommand.email)
        Country country = Country.get(healthQuoteCommand.countryEnum.country.id)

        if (!user) {
            user = new User(email: healthQuoteCommand.email, password: 'health', healthLeadType: LeadType.NORMAL)

            //Setting sales person so that lead doesn't get open.
            User salesPerson = User.read(99999999)
            if (salesPerson) {
                user.healthSalesPerson = salesPerson
            }
        }
        user.password = user.password ?: 'health'

        if (!user.enabled) {
            user.name = healthQuoteCommand.name
            user.country = country
        }

        if (!user.userDetails || !user.userDetails.mobileVerified) {
            user.mobile = healthQuoteCommand.phone
        }

        if (!user.healthLeadType) {
            user.healthLeadType = LeadType.NORMAL
        }
        user.country = Country.get(healthQuoteCommand.countryEnum.id)
        user.save(flush: true)

        //Stopping the re open of lead with wrong number and wrong email
        UserDetails userDetails = UserDetails.findByUser(user)
        if(userDetails && userDetails.healthStatusReason) {
            if(userDetails.healthStatusReason.id.intValue() != LeadStatusReasonEnum.WRONG_EMAIL.value()
                && userDetails.healthStatusReason.id.intValue() != LeadStatusReasonEnum.WRONG_NUMBER.value()) {
                crmService.handleCrmEvents(user.id, 'health')
            }
        } else {
            crmService.handleCrmEvents(user.id, 'health')
        }

        RequestSourceEnum requestSourceEnum = RequestSourceEnum.findRequestSource(healthQuoteCommand.source ?: "") ?: RequestSourceEnum.WEB
        HealthQuote healthQuote = new HealthQuote()

        healthQuote.with {
            city = City.read(healthQuoteCommand.cityId)
            name = healthQuoteCommand.name
            mobile = healthQuoteCommand.phone
            email = healthQuoteCommand.email
            salaryOver4k = healthQuoteCommand.salaryOver4k
            salaryOver15k = healthQuoteCommand.salaryOver15k
            healthQuote.user = user
            queryString = healthQuoteCommand.marketingTracking?.queryString
            utmSource = healthQuoteCommand.marketingTracking?.utmSource
            utmMedium = healthQuoteCommand.marketingTracking?.utmMedium
            utmCampaign = healthQuoteCommand.marketingTracking?.utmCampaign
            gclid = healthQuoteCommand.marketingTracking?.gclid
            fbclid= healthQuoteCommand.marketingTracking?.fbclid
            applicationType = healthQuoteCommand.applicationType
            companyName = healthQuoteCommand.companyName
            numberOfEmployees = healthQuoteCommand.numberOfEmployees
            insuranceForYourself = healthQuoteCommand.insuranceForYourself
            quoteCountry = country
            requestSource = requestSourceEnum
            isEbp = healthQuoteCommand.viewEbp
            communicationOptIn = healthQuoteCommand.communicationOptIn
            policyStartDate = new LocalDate(healthQuoteCommand.policyStartDate)
        }

        if (user.healthLeadType) {
            healthQuote.type = user.healthLeadType
        } else {
            healthQuote.type = LeadType.NORMAL
        }

        if (healthQuoteCommand.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
            healthQuote.paymentMethod = PaymentMethodEnum.COD
        }

        healthQuoteCommand.members?.each { HealthMemberCommand healthMemberCommand ->
            HealthMember healthMember = new HealthMember(
                salaryOver4k: (healthMemberCommand.salaryOver4k == null) ? healthQuote.salaryOver4k : healthMemberCommand.salaryOver4k,
                dob: healthMemberCommand.dob,
                age: healthMemberCommand.age,
                gender: healthMemberCommand.gender,
                nationality: healthMemberCommand.nationality,
                relationship: healthMemberCommand.relationship,
                quote: healthQuote,
                fullname: healthMemberCommand.name,
                residentType: healthMemberCommand.residentType
            )

            if (!healthMember.relationship && healthMember.residentType) {
                ResidentType residentType = healthMember.residentType
                HealthRelationshipEnum healthRelationshipEnum
                switch (residentType.id) {
                    case ResidentType.SELF_SPONSORED:
                    case ResidentType.INVESTOR_OR_PARTNER:
                    case ResidentType.EMPLOYEE_ABOVE_4K:
                    case ResidentType.EMPLOYEE_BELOW_4K:
                        healthRelationshipEnum = HealthRelationshipEnum.SPONSOR
                        if (residentType.id == ResidentType.EMPLOYEE_BELOW_4K) {
                            healthMember.salaryOver4k = false
                            if (healthQuote.salaryOver4k == null) {
                                healthQuote.salaryOver4k = false
                            }
                        } else {
                            healthMember.salaryOver4k = true
                        }
                        break
                    case ResidentType.DEPENDENT_SPOUSE:
                        healthRelationshipEnum = HealthRelationshipEnum.SPOUSE
                        break
                    case ResidentType.DEPENDENT_CHILD:
                        healthRelationshipEnum = HealthRelationshipEnum.CHILD
                        break
                    case ResidentType.DEPENDENT_PARENT:
                        healthRelationshipEnum = HealthRelationshipEnum.PARENT
                        break
                    case ResidentType.DOMESTIC_WORKER:
                        healthRelationshipEnum = HealthRelationshipEnum.DOMESTIC_WORKER
                        break
                }
                healthMember.relationship = HealthRelationship.load(healthRelationshipEnum.value())
            } else if (healthQuote.applicationType == HealthApplicationTypeEnum.WORKER) {
                healthMember.residentType = ResidentType.load(ResidentType.DOMESTIC_WORKER)
                healthMember.relationship = HealthRelationship.load(HealthRelationshipEnum.DOMESTIC_WORKER.value())
                healthMember.salaryOver4k = healthMemberCommand.salaryOver4k
            }

            if (!healthMember.validate()) {
                log.info("createHealthQuote healthMember.errors : ${healthMember.errors}")
            }

            healthQuote.addToHealthMembers(healthMember)
        }

        if (healthQuote.salaryOver4k == null) {
            healthQuote.salaryOver4k = true
        }

        paymentService.changePaymentStatus(healthQuote, PaymentStatusEnum.DRAFT)

        if (healthQuoteCommand.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
            paymentService.changePaymentStatus(healthQuote, PaymentStatusEnum.PENDING)
        }

        if (!healthQuote.validate()) {
            log.info("createHealthQuote healthQuote.errors : ${healthQuote.errors}")
        }

        healthQuote.save(flush: true, failOnError: true)
        healthQuote.policyReference = commonUtilService.generatePolicyRef(healthQuote, null)
        healthQuote.save(flush: true, failOnError: true)

        Comparison comparison = leadSgService.createLeadAndComparison(healthQuote)
        log.info(".createHealthQuote Created healthQuote ${healthQuote.id} with total ${healthQuote.healthMembers?.size()} members")

        healthQuote
    }

    HealthQuoteCommand toHealthQuoteCommand(HealthQuoteV2Command healthQuoteV2Command, MarketingTrackingCommand marketingTrackingCommand) {
        HealthQuoteCommand healthQuoteCommand = new HealthQuoteCommand()
        healthQuoteCommand.name = healthQuoteV2Command.name
        healthQuoteCommand.email = healthQuoteV2Command.email
        healthQuoteCommand.phone = healthQuoteV2Command.phone
        healthQuoteCommand.salaryOver4k = healthQuoteV2Command.salaryOver4k
        healthQuoteCommand.salaryOver15k = healthQuoteV2Command.salaryOver15k

        healthQuoteCommand.cityId = CityEnum.findByName(healthQuoteV2Command.visaType).value()

        HealthApplicationTypeEnum applicationTypeEnum

        if (healthQuoteV2Command.applicationType == HealthApplicationTypeV2Enum.MYSELF) {
            applicationTypeEnum = HealthApplicationTypeEnum.YOURSELF
        } else {
            applicationTypeEnum = HealthApplicationTypeEnum.FAMILY
        }

        healthQuoteCommand.applicationType = applicationTypeEnum
        healthQuoteCommand.countryEnum = CountryEnum.UAE
        healthQuoteCommand.viewEbp = true
        healthQuoteCommand.policyStartDate = new Date().plus(1)
        healthQuoteCommand.source = healthQuoteV2Command.source ?: RequestSourceEnum.WEB.name()
        healthQuoteCommand.communicationOptIn = false

        healthQuoteCommand.marketingTracking = marketingTrackingCommand

        healthQuoteCommand
    }

    /**
     * Save sub request source for the health quote
     * @param healthQuote
     * @param subRequestSourceEnum
     */
    void saveSubSource(HealthQuote healthQuote, SubRequestSourceEnum subRequestSourceEnum) {
        QuoteExtraField quoteExtraField = new QuoteExtraField()
        quoteExtraField.quoteId = healthQuote.id
        quoteExtraField.insuranceType = InsuranceTypeEnum.HEALTH
        quoteExtraField.extraFieldCode = ExtraFieldCodeEnum.SUB_REQUEST_SOURCE
        quoteExtraField.extraFieldValue = subRequestSourceEnum.name()
        quoteExtraField.save()
    }

    /**
     * Save comin reference for the health quote
     * @param healthQuote
     * @param cominReference
     */
    void saveCominReference(HealthQuote healthQuote, String cominReference) {
        QuoteExtraField quoteExtraField = new QuoteExtraField()
        quoteExtraField.quoteId = healthQuote.id
        quoteExtraField.insuranceType = InsuranceTypeEnum.HEALTH
        quoteExtraField.extraFieldCode = ExtraFieldCodeEnum.COMIN_REFERENCE
        quoteExtraField.extraFieldValue = cominReference
        quoteExtraField.save()
    }

    /**
     * Update health quote with details in healthUpdateQuoteV2Command instance
     * @param healthUpdateQuoteV2Command
     * @return
     */
    HealthQuote updateHealthQuote(HealthQuote healthQuote, HealthUpdateQuoteV2Command healthUpdateQuoteV2Command) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd")
        healthQuote.policyStartDate = dateTimeFormatter.parseLocalDate(healthUpdateQuoteV2Command.policyStartDate)
        healthQuote.user = healthPolicySgService.updateHealthQuoteUser(healthQuote, healthUpdateQuoteV2Command.name, healthUpdateQuoteV2Command.email, healthUpdateQuoteV2Command.phone, "healthQuoteService.updateHealthQuote")
        healthQuote.email = healthUpdateQuoteV2Command.email
        healthQuote.name = healthUpdateQuoteV2Command.name
        healthQuote.mobile = healthUpdateQuoteV2Command.phone
        String productSlug = healthUpdateQuoteV2Command.product
        Product product = Product.findBySlug(productSlug)
        healthQuote.product = product
        healthQuote.save()

        List healthQuestionSlugs = [
            'havePreexistingMedicalCondition',
            'chronic',
            'medicalObservation',
            'physicalProblem',
            'otherMedicalQuestion',
            'criticalIllness',
            'isPregnant',
            'fPrematureBabies',
            'fTreatmentPregnancy',
            'fUltrasoundReport',
            'fOtherDiseases'
        ]

        healthUpdateQuoteV2Command.members.each { Object memberObject ->
            HealthUpdateMemberV2Command healthUpdateMemberV2Command = new HealthUpdateMemberV2Command(memberObject)
            if (!healthUpdateMemberV2Command.validate()) {
                throw new Exception("Enter valid details of all members")
            }

            HealthMember healthMember = new HealthMember(
                salaryOver4k: healthQuote.salaryOver4k,
                dob: dateTimeFormatter.parseLocalDate(healthUpdateMemberV2Command.dob),
                relationship: HealthRelationship.load(healthUpdateMemberV2Command.relationship.value()),
                quote: healthQuote,
                fullname: healthUpdateMemberV2Command.name
            )

            GenderEnum gender = healthUpdateMemberV2Command.gender
            MaritalStatusEnum maritalStatusEnum = healthUpdateMemberV2Command.maritalStatus
            if (gender == GenderEnum.MALE) {
                healthMember.gender = HealthGender.load(GenderEnum.MALE.id)
            } else if (maritalStatusEnum == MaritalStatusEnum.MARRIED) {
                healthMember.gender = HealthGender.load(2)
            } else {
                healthMember.gender = HealthGender.load(3)
            }

            healthMember.save()

            healthQuestionSlugs.each { String slug ->
                Boolean answer = healthUpdateMemberV2Command[slug]
                if (answer != null) {
                    HealthQuestion healthQuestion = HealthQuestion.findBySlug(slug)
                    HealthAnswer healthAnswer = new HealthAnswer()
                    healthAnswer.healthQuestion = healthQuestion
                    healthAnswer.questionEn = healthQuestion.nameEn
                    healthAnswer.questionAr = healthQuestion.nameAr
                    healthAnswer.value = answer
                    healthAnswer.member = healthMember
                    if (slug != "havePreexistingMedicalCondition") {
                        healthAnswer.moreInfo = healthUpdateMemberV2Command[slug + "MoreInfo"]
                    }
                    healthAnswer.save()
                }
            }

            if (healthUpdateMemberV2Command.lastMenstrualDate) {
                HealthQuestion menstrualQuestion = HealthQuestion.findBySlug("lastMenstrualDate")
                HealthAnswer healthAnswer = new HealthAnswer()
                healthAnswer.healthQuestion = menstrualQuestion
                healthAnswer.questionEn = menstrualQuestion.nameEn
                healthAnswer.questionAr = menstrualQuestion.nameAr
                healthAnswer.member = healthMember
                healthAnswer.value = true
                LocalDate lastMenstrualDate = dateTimeFormatter.parseLocalDate(healthUpdateMemberV2Command.lastMenstrualDate)
                healthAnswer.moreInfo = lastMenstrualDate.toString("yyyy-MM-dd")
                healthAnswer.save()
            }
        }

        healthQuote
    }

    /**
     * Save all health quotes
     * @param healthratings
     * @param quoteId
     */
    def saveAllQuotes(List<HealthRateCommand> healthratings, Long quoteId) {
        log.info("saving health qoutes ${quoteId}")

        healthratings.eachWithIndex { healthrating, idx ->
            def healthqouteresult = new HealthQuoteResult()
            healthqouteresult.productId = healthrating.productId
            healthqouteresult.productName = healthrating.productNameEn
            healthqouteresult.providerName = healthrating.providerNameEn
            healthqouteresult.c4meFee = healthrating.c4meFee
            healthqouteresult.deductibleId = healthrating.deductibleId
            healthqouteresult.premium = healthrating.premium
            healthqouteresult.quoteId = quoteId
            healthqouteresult.deductables = healthrating.deductible
            healthqouteresult.coverage = healthrating.coverage
            healthqouteresult.maternity = healthrating.maternity
            healthqouteresult.networks = healthrating.networks
            healthqouteresult.pharmaceuticals = healthrating.pharmaceuticals
            healthqouteresult.save(failOnError: true)
        }
    }

    List<HealthQuestion> getHealthQuestions() {
        HealthQuestion.findAllByActive(true, [sort: "sortOrder"])
    }

    HealthQuoteCommand toHealthQuoteCommand(HealthQuote quote) {
        HealthQuoteCommand command = new HealthQuoteCommand()

        command.with {
            id = quote.id
            cityId = quote.city.id
            email = quote.email
            name = quote.name
            phone = quote.mobile
            productId = quote.productId
            deductibleId = quote.deductibleId
            networkId = quote.networkId
            salaryOver4k = quote.salaryOver4k
            additionalCharges = quote.additionalCharges
            applicationType = quote.applicationType
            companyName = quote.companyName
            numberOfEmployees = quote.numberOfEmployees
            insuranceForYourself = quote.insuranceForYourself
            countryEnum = utilService.getCountryByIdOrCurrency(quote.quoteCountryId, quote.currency)
            viewEbp = quote.isEbp
            source = quote.requestSource.toString()
            isAnyMemberPregnantOnly = quote.isAnyMemberPregnantOnly()
            policyStartDate = quote.policyStartDate?.toDate()
        }

        List<HealthMemberCommand> memberCommandList = []
        quote.healthMembers.each { healthMember ->

            HealthMemberCommand memberCommand = new HealthMemberCommand()
            memberCommand.with {
                memberId = healthMember.id
                age = healthMember.age
                gender = healthMember.gender
                nationality = healthMember.nationality
                dob = healthMember.dob
                salaryOver4k = healthMember.salaryOver4k
                additionalCharges = healthMember.additionalCharges
                relationship = healthMember.relationship
            }

            memberCommandList.add(memberCommand)

        }

        command.members = memberCommandList

        command
    }

    def updateMembers(HealthDeclarationCommand healthDeclarationCommand) {

        healthDeclarationCommand.members.each { HealthMemberCommand command ->
            HealthMember member = HealthMember.get(command.memberId)
            HealthAnswer.executeUpdate("delete from HealthAnswer a where a.member.id = :memberId ",
                [memberId: command.memberId])
            command.answers.each {
                HealthAnswer healthAnswer = new HealthAnswer()
                HealthQuestion healthQuestion = HealthQuestion.get(it.questionId)

                healthAnswer.value = it.value
                healthAnswer.questionEn = healthQuestion.nameEn
                healthAnswer.questionAr = healthQuestion.nameAr
                healthAnswer.healthQuestion = healthQuestion
                healthAnswer.member = member
                member.addToAnswers(healthAnswer)
            }

            member.save()
        }
    }

    @Transactional(readOnly = true)
    List getQuestions(Long healthQuoteId) {
        HealthQuote healthQuote = HealthQuote.findById(healthQuoteId)

        /*Format of questions list
        List questions = [
            [id: 1, name: 'test', gender: 'male', questions: ["test"]]
        ]*/
        List questions = []

        healthQuote.healthMembers.each { HealthMember member ->
            def mem = [id: member.id, age: member.age, gender: member.gender.name, questions: []]
            def memberQuestions = []

            List<HealthAnswer> answers = HealthAnswer.findAllByMember(member, [sort: "healthQuestion.sortOrder", readOnly: true])

            if (answers) {
                answers.each { HealthAnswer answer ->
                    String text = answer.healthQuestion.name
                    boolean val = answer.value
                    Integer id = answer.healthQuestion.id
                    memberQuestions << [id: id, text: text, value: val]
                }
            } else {
                def allQuestions = getHealthQuestions()
                allQuestions.each { HealthQuestion healthQuestion ->
                    memberQuestions << [id: healthQuestion.id, text: healthQuestion.name]
                }
            }

            mem.questions = memberQuestions

            questions << mem
        }

        questions
    }

    def updateHealthQuoteRating(HealthCheckoutCommand healthCheckoutCommand, def params,
                                PaymentStatusEnum status = PaymentStatusEnum.DRAFT, Boolean applyVAT = false) {
        log.info("heathQuote.updateHealthQuoteRating - entering with [command:$healthCheckoutCommand, status:$status] ")

        HealthQuote healthQuote = healthCheckoutCommand.healthQuote

        healthQuote.deductible = healthCheckoutCommand.deductible
        healthQuote.network = healthCheckoutCommand.network
        healthQuote.product = healthCheckoutCommand.product
        HealthRateCommand rating = getRating(toHealthQuoteCommand(healthQuote))

        //BigDecimal c4meFees = 0
        healthQuote.healthMembers.each { HealthMember member ->
            HealthRateMemberCommand memberCommand = rating.members.find { it.memberId == member.id }
            member.premium = memberCommand.premium
            member.totalPremium = memberCommand.totalPremium

            member.additionalCharges = memberCommand.additionalCharges
            // member.c4meFee = getC4meFees() //C4me Fee already added while getting rating
            // c4meFees = c4meFees.add(memberCommand.c4meFee)
        }

        List<AdditionalCharges> charges = additionalChargesService.getAdditionalCharges(rating, ProductType.findByName('HEALTH'), healthQuote.city.id)
//        healthQuote.healthQuoteAdditionalCharges.removeAll(healthQuote)
        if (charges && charges.size() > 0 && healthQuote.healthQuoteAdditionalCharges.size() != 0) {
            healthQuote.healthQuoteAdditionalCharges*.delete()
            healthQuote.healthQuoteAdditionalCharges.clear()
        }
        charges.each { charge ->
            healthQuote.addHealthQuoteAdditionalCharges(healthQuote, charge)
        }

        Integer personalAccident247 = healthCheckoutCommand.personalAccident247

        BigDecimal addonPrice = 0.0

        List quoteAddons = []

        if (personalAccident247) {
            AddonTranslation personalAccident247Addon = AddonTranslation.findById(personalAccident247)

            //TODO: We need to make first month free for Monthly option
            BigDecimal pa247AddonPrice = personalAccident247Addon.paymentPlan == PaymentPlanEnum.MONTHLY ? 0 :personalAccident247Addon.price
            quoteAddons.add(new CarQuoteAddon(healthQuote: healthQuote, addonTranslation: personalAccident247Addon,
                price: pa247AddonPrice,
                code: personalAccident247Addon.code))
        }

        def annualMultiTravel = params.annualMultiTravel
        def smileSaverDentalCard = params.smileSaverDentalCard

        if (annualMultiTravel) {
            def annualMultiTravelAddon = AddonTranslation.findById(annualMultiTravel)

            quoteAddons.add(new CarQuoteAddon(healthQuote: healthQuote, addonTranslation: annualMultiTravelAddon,
                price: annualMultiTravelAddon.price, code: annualMultiTravelAddon.code))
        }

        if (smileSaverDentalCard) {
            def smileSaverDentalCardAddon = AddonTranslation.findById(smileSaverDentalCard)

            quoteAddons.add(new CarQuoteAddon(healthQuote: healthQuote, addonTranslation: smileSaverDentalCardAddon,
                price: smileSaverDentalCardAddon.price, code: smileSaverDentalCardAddon.code))
        }

        log.info("quoteAddons:${quoteAddons}")
        clearAddons(healthQuote)

        //Save all the addons
        quoteAddons*.save()

        //Get price of addons
        quoteAddons.collect { addonPrice += it.price}
        log.info("addonPrice:${addonPrice}")
        //Set addon price
        healthQuote.addonPrice = addonPrice
        healthQuote.addonVat = commonUtilService.getVATAmount(healthQuote.addonPrice)

        healthQuote.policyPrice = rating.originalPremium
        healthQuote.c4meFee = rating.originalC4meFee
        healthQuote.additionalCharges = rating.additionalCharges
        healthQuote.additionalChargesVAT = rating.additionalChargesVAT

        paymentService.changePaymentStatus(healthQuote, status)

        BigDecimal discountAmount = 0
        def discount, discountCodeObj

        healthQuote.discountCode = null

        if (healthCheckoutCommand.discountCode) {
            try {
                CountryEnum countryEnum = CountryEnum.findCountryByIsoAlpha2Code(healthQuote.quoteCountry.code)

                (discount, discountCodeObj) = checkoutService.getDiscount(healthCheckoutCommand.discountCode,
                    rating.getTotalPremiumWithoutVAT(), ProductType.HEALTH, healthQuote.productId, countryEnum.code, healthQuote.id)

                discountAmount = BigDecimal.valueOf(discount)
                healthQuote.discountCode = discountCodeObj

            } catch (Exception codeExp) {
                log.error("heathQuote.updateHealthQuoteRating - Error while getting discount", codeExp)
            }

        } else {

            CountryEnum countryEnum = CountryEnum.findCountryById(healthQuote.quoteCountry.id)

            (discount, discountCodeObj) = healthRatingService.getDiscount(rating.getTotalPremiumWithoutVAT(), healthQuote, healthQuote.product, rating.providerId, rating.originalPremium)

            healthQuote.discountCode = discountCodeObj

            if (discount) {
                discountAmount = BigDecimal.valueOf(discount)
                healthQuote.discountCode = discountCodeObj
            } else {
                healthQuote.discountCode = null
                discountAmount = 0d
            }
        }

        rating = healthRatingService.applyDiscount(rating, discount, discountCodeObj)

        if (applyVAT && !Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))) {
            rating = healthRatingService.applyVAT(rating)
        }
        healthQuote.policyPriceVat = rating.premiumVAT


        if (!healthQuote.paymentMethod) {
            healthQuote.paymentMethod = PaymentMethodEnum.CREDITCARD
        }
        healthQuote.discount = discountAmount

        healthQuote.c4meFeeVat = rating.c4meFeeVAT

        BigDecimal totalVAT = healthQuote.getTotalVAT()

        //TODO: DHA AED 19 added to premium, in a new field.
        healthQuote.totalPriceWithoutVat = healthQuote.policyPrice
            .add(healthQuote.c4meFee ?: 0)
            .subtract(healthQuote.discount)
            .add(healthQuote.additionalCharges ?: 0)
            .add(healthQuote.addonPrice)

        healthQuote.totalPrice = healthQuote.totalPriceWithoutVat.add(totalVAT)

        healthQuote.policyReference = commonUtilService.generatePolicyRef(healthQuote, healthCheckoutCommand.product.provider)
        healthQuote.save()

        [healthQuote, rating]
    }

    /*BigDecimal getC4meFees() {

        BigDecimal c4meFee = 50

        c4meFee
    }*/

    /*BigDecimal calculateC4meFeeVAT(HealthQuote healthQuote, BigDecimal discount) {

        def c4meFeeVAT = 0

        if (discount) {
            if(healthQuote.c4meFee) {
                def c4meFee = discount > healthQuote.c4meFee ? 0 : (healthQuote.c4meFee - discount)
                c4meFeeVAT = commonUtilService.getVATAmount(c4meFee)
            }
        } else {
            c4meFeeVAT = commonUtilService.getVATAmount(healthQuote.c4meFee)
        }

        return c4meFeeVAT
    }*/

    /**
     * Verify that all data is available for payment
     *
     * @param HealthQuote
     * @return
     */
    boolean isEligibleForPayment(HealthQuote quote) {
        log.debug("healthQuote.isEligibleForPayment - quote:${quote.id}")

        boolean allDataAvailable = quote.policyPrice && quote.policyPriceVat &&
            quote.totalPrice && quote.totalPriceWithoutVat &&
            //(!quote.c4meFee || quote.c4meFee && (quote.discount >= quote.c4meFee ? true : quote.c4meFeeVat)) &&
            (!quote.discount || quote.discount && quote.discountCode)

        log.info("pp:${quote.policyPrice}, ppvat:${quote.policyPriceVat}, " +
            "c4me:${quote.c4meFee}, c4meVat:${quote.c4meFeeVat}," +
            "tp:${quote.totalPrice}, tpwovat:${quote.totalPriceWithoutVat}, " +
            "discount:${quote.discount}, discountCode:${quote.discountCode?.id}")

        BigDecimal totalPrice = quote.totalPriceWithoutVat.add(quote.getTotalVAT())

        log.info("allDataAvailable:${allDataAvailable}, totalPrice:$totalPrice, quote.totalPrice:${quote.totalPrice}")
        if (!allDataAvailable || totalPrice != quote.totalPrice) {
            log.error("healthQuote.isEligibleForPayment - quote:${quote.id}, prices not matched. " +
                "Total:${totalPrice}, quote.TotalPrice:${quote.totalPrice}, allDataAvailable:${allDataAvailable}")
            return false
        }

        return true
    }

    /**
     * Get AddOns based on product
     *
     * @param rateCommand
     * @param model
     * @return
     */
    def getAddOns(HealthRateCommand rateCommand, def selectedAddons = [:], HealthQuote quote) {

        Product product = Product.get(rateCommand.productId)
        String countryCode = product.provider.country.code

        CountryEnum country = CountryEnum.findCountryByDfp(countryCode)

        Set checkboxList = []
        Set dropdownList = []

        DetachedCriteria criteria = Addon.where {
            active == true && showForHealth == true && (product == null || product == product)
        }
        List addons = criteria.list(sort:"sortOrder")

        addons.each { Addon addon ->

            if (addon.type == AddonTypeEnum.CHECKBOX) {
                AddonTranslation translation = AddonTranslation.findByAddon(addon)

                if (!isAddonApplicable(rateCommand, translation.code, quote)) {
                    return
                }

                double price = translation.price

                if (translation.code == AddonCodeEnum.PA_247.code &&
                    translation.paymentPlan == PaymentPlanEnum.MONTHLY) {
                    //TODO: free first month, for now making it 0
                    price = 0
                }

                if (translation.code == AddonCodeEnum.HEALTH_MOTHER_BABY_CARE.code) {

                    List spouseMembers = rateCommand.members.findAll {
                        it.relationship.toUpperCase() == HealthRelationshipEnum.SPOUSE.toString() && it.gender == 'Female (Married)'
                    }

                    if (spouseMembers.size()) {
                        boolean isAddonValid = true
                        int spouseCount = 0

                        spouseMembers.each { HealthRateMemberCommand member ->
                            Integer memberAge = member.age

                            if (memberAge >= 18 && memberAge <= 44) {
                                spouseCount++
                            }
                            if (memberAge < 18 || memberAge > 44) {
                                isAddonValid = false
                            }
                        }

                        if (!isAddonValid) {
                            return
                        }

                        price = spouseCount * price

                    } else {
                        return
                    }
                }

                def selected = false

                if (translation.isDefault) {
                    selected = true
                }

                if (selectedAddons[translation.code]) {
                    selected = true
                }

                checkboxList.add([label:translation.label, value:price, valueText: translation.priceText, description:translation.description,
                                  name:translation.code, selected:selected, id:translation.id])

            } else if (addon.type == AddonTypeEnum.SELECT) {
                List<AddonTranslation> translations = AddonTranslation.findAllByAddon(addon)
                def options = []
                def selected = null

                translations.each { option ->
                    if (!isAddonApplicable(rateCommand, option.code, quote)) {
                        return
                    }

                    def value = selectedAddons[option.code]

                    if (option.isDefault) {
                        selected = option.id
                    }

                    if(option.price == value) {
                        selected = option.id
                    }

                    def price = option.price

                    if (option.code == AddonCodeEnum.PA_247.code &&
                        option.paymentPlan == PaymentPlanEnum.MONTHLY) {
                        //TODO: free first month, for now making it 0
                        price = 0
                    }

                    options.add([option:option.value, value:price, id:option.id])
                }

                Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))

                if (country == CountryEnum.UAE && pricesInclusiveVat) {
                    options = applyVAT(options)
                }

                dropdownList.add([label:translations.first().label, description:translations.first().description,
                                  options:options, name:translations.first().code,  value:selected])
            }
        }

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))

        if (country == CountryEnum.UAE && pricesInclusiveVat) {
            checkboxList = applyVAT(checkboxList)
        }

        log.info("checkboxList:${checkboxList?.size()}, dropDownList:${dropdownList.size()}")

        [checkboxList:checkboxList, dropdownList:dropdownList]

    }

    /**
     * Check if a specific Addon is applicable
     * @param rateCommand
     * @param addonCode
     * @return
     */
    private boolean isAddonApplicable(HealthRateCommand rateCommand, String addonCode, HealthQuote quote) {
        log.info("healthQuote.isAddonApplicable - addonCode:${addonCode}")

        boolean isApplicable = true
        if (addonCode in  [ AddonCodeEnum.PA_247.code, AddonCodeEnum.ANNUAL_MULTI_TRAVEL.code ]) {
            List eligibleMembers =  rateCommand.members.findAll { HealthRateMemberCommand member ->
                log.info("member.age:${member.age}")
                isApplicable =  member.age >= 18 && member.age < 65
            }
            if (eligibleMembers.size() == 0) {
                log.info("Member not applicable")
                //PA_247 Addon isn't applicable to any member
                isApplicable = false
            }
        }

        if (addonCode == AddonCodeEnum.PA_247.code){
            def recurringPayment = RecurringPayment.findByCustomerEmail(quote.email)
            recurringPayment.each {
                if (it.active && !it.endDate && it.paLead){
                    isApplicable = false
                }
            }
        }


        return isApplicable
    }

    def applyVAT(def items) {
        items.each { item ->
            item.originalValue = item.value
            item.value = commonUtilService.addVAT(item.value)
            item.value = new BigDecimal(item.value).setScale(2, RoundingMode.CEILING)
        }

        items
    }

    def clearAddons(HealthQuote quote) {
        CarQuoteAddon.executeUpdate("delete CarQuoteAddon a where a.healthQuote.id = :id", [id:quote.id])
    }

}
