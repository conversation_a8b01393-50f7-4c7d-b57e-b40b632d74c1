package com.cover.health

import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.cover.health.commands.HealthRateMemberCommand
import com.safeguard.CityEnum
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.Product
import com.safeguard.ProductEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.Provider
import com.safeguard.car.CoverageType
import com.safeguard.car.DiscountCode
import com.safeguard.health.HealthProductCover
import com.safeguard.health.HealthQuote
import com.safeguard.health.HealthRating
import com.safeguard.health.HealthRelationship
import grails.transaction.Transactional
import org.joda.time.LocalDate

/**
 * Health ratings calculation parent service.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class HealthRatingService {

    def commonUtilService
    def grailsApplication
    def discountService
    def healthQuoteService

    HealthRateCommand applyCovers(HealthRateCommand rateCommand, HealthRating rating) {

        Product product = rating.product
        Provider provider = product.provider

        rateCommand.productId = product.id
        rateCommand.productName = product.name
        rateCommand.productNameEn = product.nameEn
        rateCommand.providerId = provider.id
        rateCommand.providerLogo = provider.logo
        rateCommand.providerName = provider.name
        rateCommand.providerNameEn = provider.nameEn
        rating.deductible.loadTransients('en')
        rateCommand.deductible = rating.deductible?.name
        rateCommand.deductibleId = rating.deductibleId
        rateCommand.networkId = rating.networkId
        rateCommand.city = rating.city

        HealthProductCover cover = HealthProductCover.findByProduct(product)
        cover.loadTransients('en', rating.network, rating.deductible.name)
        rateCommand.maxAnnualCoverage = cover.maxAnnualCoverage
        rateCommand.coverage = cover.coverage
        rateCommand.routineDental = cover.routineDental
        rateCommand.maternity = cover.maternity
        rateCommand.networks = cover.networks
        rateCommand.networkLink = cover.networkLink
        if (rating.network) {
            rateCommand.networks = rating.network.name
        }
        rateCommand.policyWordingsLink = cover.policyWordingsLink
        if (rating.deductible.coverLink) {
            rateCommand.coverLink = rating.deductible.coverLink
        } else {
            rateCommand.coverLink = cover.coverLink
        }
        if (rating.deductible.pharmaceuticals) {
            rateCommand.pharmaceuticals = rating.deductible.pharmaceuticals
        } else {
            rateCommand.pharmaceuticals = cover.pharmaceuticals
        }
        rateCommand
    }

    HealthRateMemberCommand getMember(HealthRating rating, HealthMemberCommand member) {
        HealthRateMemberCommand rateMember = new HealthRateMemberCommand()
        rateMember.memberId = member.memberId
        rateMember.age = getRatingService(rating.product.providerId).getMemberAge(member)
        rateMember.gender = rating.gender.name
        rateMember.premium = rating.premium
        rateMember.salaryOver4k = member.salaryOver4k
        rateMember.relationship = member.relationship.name
        //rateMember.c4meFee = 50 //No need to apply here. Applied by each rating service

        rateMember
    }

    List<HealthRating> findApplicableRates(providerId, productId, deductibleId, memberAge, memberGender, cityId,
                                           HealthRelationship memberRelationship, Boolean salaryBelow4k = null,
                                           Boolean hasNonChildMember = null, Integer networkId = null, Boolean ebpOnly = null) {

        def c = HealthRating.createCriteria()
        def list = c.list {
            product {
                provider {
                    eq 'id', providerId
                    eq 'active', true
                }
                if (productId) {
                    eq 'id', productId
                }

                if (providerId == HealthDamanRateService.PROVIDER_ID) {
                    if ((memberRelationship.id == HealthRelationshipEnum.EMPLOYEE.value() ||
                        memberRelationship.id == HealthRelationshipEnum.DOMESTIC_WORKER.value())
                        && salaryBelow4k) {
                        eq 'id', HealthDamanRateService.EBP_PRODUCT_ID
                    } else {
                        ne 'id', HealthDamanRateService.EBP_PRODUCT_ID
                    }
                }

                if (providerId == HealthOrientRateService.PROVIDER_ID && !productId) {
                    List fcpList = []

                    if (hasNonChildMember) {
                        fcpList = [
                            HealthOrientRateService.FAMILY_CARE_PLAN_GOLD_ID,
                            HealthOrientRateService.FAMILY_CARE_PLAN_GREEN_ID,
                            HealthOrientRateService.FAMILY_CARE_PLAN_SILK_ROAD_ID,
                            HealthOrientRateService.FAMILY_CARE_PLAN_SILVER_CLASSIC_ID,
                            HealthOrientRateService.FAMILY_CARE_PLAN_SILVER_PREMIUM_ID,
                            HealthOrientRateService.FAMILY_CARE_PLAN_PEARL_ID,
                            HealthOrientRateService.FAMILY_CARE_PLAN_EMERALD_ID
                        ]
                    }
                    if (cityId == CityEnum.DUBAI.value()) {

                        if (memberRelationship.id == HealthRelationshipEnum.EMPLOYEE.value() ||
                            memberRelationship.id == HealthRelationshipEnum.DOMESTIC_WORKER.value() ||
                            memberRelationship.id == HealthRelationshipEnum.SPONSOR.value()) {

                            List productList = []
                            if (memberRelationship.id == HealthRelationshipEnum.SPONSOR.value() && !ebpOnly) {
                                productList = fcpList
                            }

                            if (ebpOnly) {
                                if (salaryBelow4k) {
                                    productList.push(HealthOrientRateService.EBP_PRODUCT_ID)
                                } else {
                                    productList.push(HealthOrientRateService.IMED_PRODUCT_ID)
                                }
                            }

                            inList 'id', productList
                        } else if (!ebpOnly && fcpList) {
                            inList('id', fcpList)
                        } else if (!hasNonChildMember) {
                            not {
                                'in'('id', [
                                    HealthOrientRateService.FAMILY_CARE_PLAN_GOLD_ID,
                                    HealthOrientRateService.FAMILY_CARE_PLAN_GREEN_ID,
                                    HealthOrientRateService.FAMILY_CARE_PLAN_SILK_ROAD_ID,
                                    HealthOrientRateService.FAMILY_CARE_PLAN_SILVER_CLASSIC_ID,
                                    HealthOrientRateService.FAMILY_CARE_PLAN_SILVER_PREMIUM_ID,
                                    HealthOrientRateService.FAMILY_CARE_PLAN_PEARL_ID,
                                    HealthOrientRateService.FAMILY_CARE_PLAN_EMERALD_ID
                                ])
                            }
                        }
                    } else {
                        List productList = [
                            HealthOrientRateService.NEMED_PRODUCT_ID,
                            HealthOrientRateService.NEMED_ENHANCED_PRODUCT_ID,
                            HealthOrientRateService.NEMED_LITE_PRODUCT_ID
                        ]

                        if (!(memberRelationship.id in [HealthRelationshipEnum.EMPLOYEE.value(), HealthRelationshipEnum.DOMESTIC_WORKER.value()])
                            && hasNonChildMember) {
                            productList += fcpList
                        }

                        inList 'id', productList
                    }
                }

                if (providerId == HealthNoorRateService.PROVIDER_ID) {
                    /*if (memberRelationship.id == HealthRelationshipEnum.DOMESTIC_WORKER.value()) {
                        eq 'id', HealthNoorRateService.PRODUCT_EBP_DOMESTIC_WORKER_ID
                    } else {
                        ne 'id', HealthNoorRateService.PRODUCT_EBP_DOMESTIC_WORKER_ID
                    }*/
                }

                eq 'active', true
                or {
                    isNull("isGroupHealth")
                    eq("isGroupHealth", false)
                }

                if (ebpOnly != null) {
                    eq("type", (ebpOnly ? CoverageType.load(CoverageTypeEnum.HEALTH_EBP.value()) : CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value())))
                }
            }

            or {
                isNull("salaryOver4k")
                eq("salaryOver4k", !salaryBelow4k)
            }

            if (deductibleId) {
                eq 'deductible.id', deductibleId
            }

            if (networkId) {
                eq 'network.id', networkId
            }

            if (cityId) {
                eq 'city.id', cityId
            }

            if (memberRelationship) {
                eq 'relationship', memberRelationship
            }

            le 'ageFrom', memberAge
            ge 'ageTo', memberAge

            if (memberGender) {
                eq 'gender', memberGender
            }

            order('product', 'asc')
            order('deductible', 'asc')
            order('network', 'asc')
        }

        list
    }

    /**
     * Apply VAT on health ratings, on its premium and c4me fee
     * @param rateCommandList
     * @return
     */
    def applyVAT(List rateCommandList) {
        rateCommandList.each { HealthRateCommand rateCommand ->
            rateCommand = applyVAT(rateCommand)
        }
    }

    def applyVAT(HealthRateCommand rateCommand) {
        //Premium VAT is always on the original premium and doesnt change
        rateCommand.premiumVAT = commonUtilService.getVATAmount(rateCommand.originalPremium)
        rateCommand.c4meFeeVAT = commonUtilService.getVATAmount(rateCommand.c4meFee)

        rateCommand.originalC4meFeeVAT = commonUtilService.getVATAmount(rateCommand.originalC4meFee)

        //Now Premium VAT is added to the Premium
        rateCommand.premium = rateCommand.premium.plus(rateCommand.premiumVAT)

        return rateCommand
    }

    def applyC4meFee(List rateCommandList) {
        rateCommandList.each { HealthRateCommand rateCommand ->
                rateCommand.applyC4meFee()
        }
    }

    List applyExtraDiscount(List rateCommandList, HealthQuoteCommand healthQuoteCommand) {
        rateCommandList.each { HealthRateCommand rateCommand ->
            applyExtraDiscount(rateCommand, healthQuoteCommand)
        }
    }

    boolean isMemberSalaryBelow4k(HealthQuoteCommand quoteCommand) {

        boolean isBelow4k = true
        quoteCommand.members.each { HealthMemberCommand memberCommand ->
            if (isBelow4k && memberCommand.salaryOver4k) {
                isBelow4k = false
            }
        }
        isBelow4k
    }

    /**
     * Apply Discount on the Health Rate Command
     * @param rateCommand
     * @return
     */
    HealthRateCommand applyExtraDiscount(HealthRateCommand rateCommand, HealthQuoteCommand healthQuoteCommand) {

        HealthQuote healthQuote = HealthQuote.read(healthQuoteCommand.id)
        // Get the Global discount
        def (discount, discountCodeObj) = getDiscount(rateCommand.totalPremium, healthQuote, Product.read(rateCommand.productId), rateCommand.providerId, rateCommand.originalPremium)

        //Apply discount onto the rate command
        rateCommand = applyDiscount(rateCommand, discount, discountCodeObj)

        return rateCommand
    }

    /**
     * Get the global discount or provider/product level discount on health products
     * @param amount
     * @return
     */
    def getDiscount(BigDecimal amount, HealthQuote healthQuote, Product product, Long providerId = null, BigDecimal premium = null) {

        BigDecimal discount
        DiscountCode discountCodeObj

        CountryEnum countryEnum = CountryEnum.findCountryById(healthQuote.quoteCountryId.toInteger())
        discountCodeObj = discountService.getApplicableDiscountCode(
            countryEnum,
            ProductTypeEnum.HEALTH.value(),
            healthQuote.user.healthLeadType,
            providerId as Integer,
            product.id as Integer,
            premium,
            null,
            healthQuote.user.healthSalesPerson,
            product.type,
            healthQuote.user.nationality
        )

//        //Is global discount enabled
//        if (Boolean.parseBoolean(grailsApplication.config.getProperty("cover.premium.health.discount.${countryEnum.code}.enabled"))) {
//
//            discountCodeId = Integer.parseInt(grailsApplication.config.getProperty("cover.premium.health.discount.${countryEnum.code}.id"))
//
//        } else if (grailsApplication.config.cover.premium.health.discount["${countryEnum.code}"].product &&
//            grailsApplication.config.cover.premium.health.discount["${countryEnum.code}"].product[product.id]) {
//
//            discountCodeId = grailsApplication.config.cover.premium.health.discount["${countryEnum.code}"].product[product.id].id
//
//        } else if (grailsApplication.config.cover.premium.health.discount["${countryEnum.code}"][product.providerId]) {
//
//            discountCodeId = grailsApplication.config.cover.premium.health.discount["${countryEnum.code}"]["${product.providerId}"].id
//
//        }


        //Use the discount code if available

        if (discountCodeObj) {
            discount = discountCodeObj.discount

            if (discountCodeObj.hasPercentDiscount) {
                discount = (discountCodeObj.discount * amount) / 100
            }

            discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        [discount, discountCodeObj]
    }

    /**
     * Apply the available discount on Rate Command
     *
     * @param rateCommand
     * @param discount
     * @param discountCodeObj
     * @return
     */
    HealthRateCommand applyDiscount(HealthRateCommand rateCommand, BigDecimal discount, DiscountCode discountCodeObj) {

//        Product product = Product.read(rateCommand.productId)
//        if (grailsApplication.config.cover.premium.health.discount."${CountryEnum.UAE.code}".product &&
//            grailsApplication.config.cover.premium.health.discount."${CountryEnum.UAE.code}".product[product.id]) {
//            Integer discountId = grailsApplication.config.cover.premium.health.discount."${CountryEnum.UAE.code}".product[product.id].id
//            discountCodeObj = DiscountCode.get(discountId)
//
//        } else if (grailsApplication.config.cover.premium.health.discount."${CountryEnum.UAE.code}"[product.providerId]) {
//            Integer discountId = grailsApplication.config.cover.premium.health.discount."${CountryEnum.UAE.code}"[product.providerId].id
//            discountCodeObj = DiscountCode.get(discountId)
//        }
        if (discountCodeObj) {
            if (discountCodeObj.hasPercentDiscount) {
                rateCommand.discountPercent = discountCodeObj.discount
            }
            rateCommand.discountAmount = discount

            if (rateCommand.originalC4meFee) {
                BigDecimal newC4meFee = discount > rateCommand.originalC4meFee ? 0 : (rateCommand.originalC4meFee - discount)
                discount = discount > rateCommand.originalC4meFee ? discount - rateCommand.originalC4meFee : 0
                rateCommand.c4meFee = newC4meFee
            }

            rateCommand.premium = rateCommand.originalPremium.add(rateCommand.c4meFee ?: 0).subtract(discount ?: 0)
        }

        return rateCommand
    }

    /**
     *
     * @param rateCommand
     * @return
     */
    List<HealthRateCommand> storeOriginalPremiumBeforeC4meFee(List<HealthRateCommand> rateCommandList) {
        rateCommandList.each { HealthRateCommand rateCommand ->
            rateCommand.originalPremium = rateCommand.premium
        }

        rateCommandList
    }


    private getRatingService(Integer providerId) {

        def service = null
        if (HealthUnionRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthUnionRateService

        } else if (HealthDamanRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthDamanRateService

        } else if (HealthNoorRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthNoorRateService

        } else if (HealthOrientRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthOrientRateService

        } else if (HealthSalamaRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthSalamaRateService

        } else if (HealthWataniaRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthWataniaRateService

        } else if (HealthDubaiNationalRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthDubaiNationalRateService

        } else if (HealthTakafulEmaratService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthTakafulEmaratService

        } else if (HealthCignaRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthCignaRateService

        } else if (HealthAdamjeeRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.healthAdamjeeRateService
        }

        return service
    }

}
