package com.cover.health

import com.cover.health.commands.HealthRateCommand
import com.safeguard.DonationTypeEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.Donation
import com.safeguard.car.CarQuoteAddon

import com.safeguard.health.HealthQuote
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import grails.util.Environment

@Transactional(readOnly = true)
class HealthEmailService {

    def mailService
    def grailsApplication
    def mailChimpService
    def messageSource
    def emailCheckerService

    def sendHealthQuoteEmail(List<HealthRateCommand> ratings, HealthQuote healthQuote, String country) {

        if (healthQuote.user.shouldSendEmail()) {
            if (ratings) {
                log.info "Sending home quote created email for ${healthQuote.id}"
                String subjectText = healthQuote.lang ==
                    'ar' ? 'Your recent health insurance quote' : 'Your recent health insurance quote'

                //If current env is not production
                if (Environment.current != Environment.PRODUCTION) {
                    subjectText = "[${Environment.current}] ${subjectText}"
                }

                String encId = AESCryption.encrypt(healthQuote.id.toString())

                HealthRateCommand rateCommand = ratings.sort { it.premium }.first()

                if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(healthQuote.email) ) {
                    String emailTemplate = "/health/emails/quoteCreated"
                    if (healthQuote.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
                        emailTemplate = "/health/emails/groupQuoteCreated"
                    }

                    if (emailCheckerService.isEmailVerified(healthQuote.email)) {
                        mailService.sendMail {
                            to healthQuote.email
                            replyTo grailsApplication.config.grails.mail.default.replyTo
                            headers "X-MC-Tags": "health, quote",
                                "X-MC-Important": "true"
                            subject subjectText
                            html view: emailTemplate, model: [
                                quote    : healthQuote,
                                rate     : rateCommand,
                                encId    : encId,
                                locale   : healthQuote.lang,
                                country  : country,
                                baseUrl  : grailsApplication.config.getProperty('yallacompare.baseURL'),
                                dir      : healthQuote.lang == 'ar' ? 'rtl' : 'ltr',
                                imageCode: "compareit4meLogo.url"
                            ]
                        }

                        mailChimpService.subscribe(healthQuote.email, healthQuote, ProductTypeEnum.HEALTH)
                    }
                } else {
                    log.warn "Health Quote email won't be sent to ${healthQuote.email} on non production env"
                }
            } else {
                log.warn "Health Quote email won't be sent as no ratings for quoteId ${healthQuote.id}"
            }
        } else {
            log.warn "Health Quote email won't be sent to ${healthQuote.email} as blacklisted"
        }

    }

    def sendHealthQuotePurcahsedEmail(HealthQuote healthQuote) {

        if (healthQuote.user.shouldSendEmail()) {
            log.info "Sending health quote purchased email for health quote ${healthQuote.id}"

            String lang = healthQuote.lang ?: 'en'

            String subjectText = (lang == 'en') ? 'Health Insurance - Acknowledgement of Policy Application' : 'التأمين المنزلي- معرفة كيفية تطبيق سياسة التأمين'

            Donation donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(healthQuote.id, ProductTypeEnum.HEALTH.value(), DonationTypeEnum.CHARITY)

            BigDecimal insuranceSubTotal = healthQuote.policyPrice.add(healthQuote.policyPriceVat ?: 0).add(healthQuote.additionalCharges ?: 0).add(healthQuote.additionalChargesVAT ?: 0)

            CarQuoteAddon[] addons = CarQuoteAddon.findAllByHealthQuoteAndIsDeleted(healthQuote, false)

            List addonList = []

            addons.collect {
                def addon = [:]
                if ('ar'.equalsIgnoreCase(lang)) {
                    addon.label = it.addonTranslation.displayAr
                } else {
                    addon.label = it.addonTranslation.labelEn
                }
                addon.code = it.code
                addon.price = it.price
                addonList.add(addon)
            }

            //If current env is not production
            if (Environment.current != Environment.PRODUCTION) subjectText = "[${Environment.current}] ${subjectText}"

            if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(healthQuote.email)) {
                String htmlView = "/health/emails/quotePurchased"

                if (healthQuote.isCod()) {
                    htmlView = "/health/emails/codQuotePurchased"
                }

                if (healthQuote.paymentGateway in [PaymentGatewayEnum.CHECKOUT, PaymentGatewayEnum.TAP_PAYMENT]) {
                    htmlView = "/health/emails/quotePurchased_capturedPayment"
                    subjectText = messageSource.getMessage("email.purchase.subject.captured", [].toArray(), new Locale(lang))
                }

                if (emailCheckerService.isEmailVerified(healthQuote.email)) {
                    mailService.sendMail {
                        to healthQuote.email
                        headers "X-MC-Tags": "health, order",
                            "X-MC-BccAddress": "<EMAIL>"
                        replyTo grailsApplication.config.grails.mail.default.replyTo
                        subject subjectText
                        html view: htmlView, model: [
                            quote    : healthQuote,
                            addons   : addonList,
                            subTotal : insuranceSubTotal,
                            donation : donation ? donation.amount : 0,
                            locale   : lang,
                            baseUrl  : grailsApplication.config.getProperty('yallacompare.baseURL'),
                            dir      : lang == 'ar' ? 'rtl' : 'ltr',
                            imageCode: "compareit4meLogo.url"

                        ]
                    }
                }
            } else {
                log.warn "Email won't be sent to ${healthQuote.email} on non production env"
            }
        } else {
            log.warn "Health Quote purchase email won't be sent to ${healthQuote.email} as blacklisted"
        }
    }
}
