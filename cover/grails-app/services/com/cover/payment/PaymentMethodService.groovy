package com.cover.payment

import com.c4m.payfort.util.PayfortCommandEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CurrencyEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentPlanEnum
import com.safeguard.Product
import com.safeguard.ProductTypeEnum
import com.safeguard.Provider
import com.safeguard.User
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteDetail
import com.safeguard.checkoutPsp.CheckoutPspCommand
import com.safeguard.checkoutPsp.CheckoutPspPaymentTypeEnum
import com.safeguard.health.HealthQuote
import com.safeguard.payment.PaymentMetadata
import com.safeguard.payment.ProviderPaymentMethod
import com.safeguard.pet.PetQuote
import com.safeguard.util.AESCryption
import grails.transaction.Transactional


@Transactional
class PaymentMethodService {

    def checkoutService
    def checkoutPspService
    def grailsApplication
    def messageSource
    def payfortService
    def paymentService
    def utilService

    def getCreditCardPaymentParameters(PaymentGatewayEnum paymentGateway, def quote, User user,
                                       String countryCode, String clientIp, String returnUrl,
                                       String checkoutSuccessUrl, String checkoutFailureUrl, String checkoutCancelUrl,
                                       BigDecimal totalPrice) {

        String quoteUserVersion = quote ? quote.version.toString() : user.version.toString()

        def creditCardParams = [:]
        def installmentParams
        boolean showMerchantPage = true

        if (paymentGateway == PaymentGatewayEnum.PAYFORT) {

            if (countryCode == CountryEnum.UAE.code) {
                log.info("apiV1.checkout.paynow - after adding domain, returnUrl:${returnUrl}")
                Provider provider = quote.product.provider
                String providerCode
                if (provider.id == InsuranceProviderEnum.TAKAFUL_EMIRATE.id) {
                    providerCode = provider.code
                }

                (creditCardParams, installmentParams) =
                    getMerchantPageParams(quote ? quote.encodedrMerchantRef() : user.encodeMerchantRef(),
                        quote ? quote.currency : CurrencyEnum.AED.toString(), totalPrice ? totalPrice : quote.totalPrice,
                        "${returnUrl}?p_quote_v=${quoteUserVersion}", countryCode, providerCode)

            } else if (quote) {
                //Redirection
                showMerchantPage = false

                (creditCardParams, installmentParams) = getRedirectionParams(quote,
                    "${returnUrl}", clientIp)
            }

        } else if (paymentGateway == PaymentGatewayEnum.CHECKOUT) {
            def (pspHostedPageLink, merchantRef) = generateCheckoutPspHostedPageLink(quote, user, clientIp,
                checkoutSuccessUrl, checkoutFailureUrl, checkoutCancelUrl, totalPrice)

            creditCardParams.merchant_reference = merchantRef
            creditCardParams.pspHostedPaymentLink = pspHostedPageLink

        } else if (paymentGateway == PaymentGatewayEnum.TAP_PAYMENT) {
            //no need as of now
        }

        return [creditCardParams, installmentParams, showMerchantPage]
    }

    def generateCheckoutPspHostedPageLink(def quote, User user, String clientIp,
                                          String checkoutSuccessUrl, String checkoutFailureUrl, String checkoutCancelUrl,
                                          BigDecimal totalPrice) {
        String encQuoteUserId = AESCryption.encrypt(quote ? quote.id.toString() : user.id.toString())

        CheckoutPspCommand pspCommand = buildCheckoutHostedPaymentRequest(quote, user, encQuoteUserId, clientIp, totalPrice)
        pspCommand.successUrl = checkoutSuccessUrl
        pspCommand.failureUrl = checkoutFailureUrl
        pspCommand.cancelUrl = checkoutCancelUrl

        String pspHostedPageLink = checkoutPspService.getCheckoutHostedPageLink(pspCommand)

        return [pspHostedPageLink, pspCommand.reference]
    }

    /**
     * Merchant Page parameters
     * @param merchantRef
     * @param currency
     * @param totalPrice
     * @return Map with params and action url for form.
     */
    @Transactional(readOnly = true)
    def getMerchantPageParams(String merchantRef, String currency, BigDecimal totalPrice, String returnUrl,
                              String countryCode, String providerCode) {
        String merchantIdentifier = paymentService.getActiveMerchantId(countryCode, providerCode)
        String accessCode = paymentService.getPayfortAccessCode(merchantIdentifier, countryCode, providerCode)

        def params = [
            service_command    : grailsApplication.config.getProperty("payfort.serviceCommand"),
            merchant_identifier: merchantIdentifier,
            access_code        : accessCode,
            merchant_reference : merchantRef,
            language           : utilService.getLanguage(),
            return_url         : returnUrl

        ]

        String sha = payfortService.createHash(params, paymentService.getPayfortRequestPhrase(merchantIdentifier, countryCode, providerCode))
        params.signature = sha

        //Key value map for installment request
        Map installmentParams = [:]
        installmentParams = installmentParams << params
        installmentParams.currency = currency
        installmentParams.amount = totalPrice.multiply(100).intValue()
        installmentParams.customer_country_code = "${countryCode.toUpperCase()}" ////TODO: get it from database
        installmentParams.installments = "STANDALONE"

        String installmentSha = payfortService.createHash(installmentParams, paymentService.getPayfortRequestPhrase(merchantIdentifier, countryCode, providerCode))
        installmentParams.signature = installmentSha

        [params, installmentParams]
    }

    /**
     * Redirection parameters
     * @param merchantRef
     * @param currency
     * @param totalPrice
     * @return Map with params and action url for form.
     */
    @Transactional(readOnly = true)
    def getRedirectionParams(def quote, String returnUrl, String customerIp) {

        String merchantRef = quote.encodedrMerchantRef()
        String currency = quote.currency
        BigDecimal totalPrice = quote.totalPrice
        Country country = quote.quoteCountry
        String countryCode = CountryEnum.findCountryByDfp(country.code).code
        log.info("checkout.getRedirectionParams - countryCode:${countryCode}")

        Provider provider = quote.product?.provider
        String providerCode
        if (provider.id == InsuranceProviderEnum.TAKAFUL_EMIRATE.id) {
            providerCode = provider.code
        }
        String merchantIdentifier = paymentService.getActiveMerchantId(countryCode, providerCode)
        String accessCode = paymentService.getPayfortAccessCode(merchantIdentifier, countryCode, providerCode)

        def params = [
            command            : PayfortCommandEnum.AUTHORIZATION,
            merchant_identifier: merchantIdentifier,
            access_code        : accessCode,
            merchant_reference : merchantRef,
            amount             : totalPrice.multiply(100).intValue(),
            currency           : currency,
            language           : utilService.getLanguage(),
            //order_description: paymentService.getOrderDescription(quote),
            customer_email     : quote.email,
            customer_name      : quote.name.take(40),
            //customer_ip: customerIp,
            return_url         : returnUrl
        ]

        String sha = payfortService.createHash(params, paymentService.getPayfortRequestPhrase(merchantIdentifier, countryCode, providerCode))
        params.signature = sha

        //Key value map for installment request
        Map installmentParams = [:]
        installmentParams = installmentParams << params
        installmentParams.installments = "STANDALONE"

        String installmentSha = payfortService.createHash(installmentParams, paymentService.getPayfortRequestPhrase(merchantIdentifier, countryCode, providerCode))
        installmentParams.signature = installmentSha

        [params, installmentParams]
    }

    //TODO: Move this to CheckoutPspService
    /**
     * Builds up Checkout Hosted payment request
     *
     * @param quote
     * @param user
     * @param encQuoteUserId
     * @param clientIp
     * @return
     */
    CheckoutPspCommand buildCheckoutHostedPaymentRequest(def quote, User user, String encQuoteUserId,
                                                         String clientIp, BigDecimal totalPrice) {
        log.info("")
        Locale locale = utilService.getLanguage()
        CheckoutPspCommand pspCommand = new CheckoutPspCommand()

        pspCommand.encryptedQuoteId = encQuoteUserId
        pspCommand.productType = quote ? quote.productType : ProductTypeEnum.userPaymentCard
        pspCommand.amount = totalPrice ? totalPrice : quote.totalPrice
        pspCommand.currency = quote ? quote.currency.toString() : CurrencyEnum.AED.toString()
        pspCommand.paymentType = CheckoutPspPaymentTypeEnum.REGULAR
        if (quote instanceof CarQuote || quote instanceof HealthQuote) {
            if (quote.hasPAAddon()) {
                pspCommand.paymentType = CheckoutPspPaymentTypeEnum.RECURRING
            }
        } else if ((quote == null && user != null) || (quote instanceof PetQuote && quote.paymentPlan == PaymentPlanEnum.MONTHLY)) {
            pspCommand.paymentType = CheckoutPspPaymentTypeEnum.RECURRING
        }
        pspCommand.paymentIp = clientIp
        pspCommand.reference = quote ? quote.encodedrMerchantRef() : user.encodeMerchantRef()
        pspCommand.description = quote ? paymentService.getOrderDescription(quote, 120) : "Payment Card Verification"
        //String processingChannelId
       /* List<CheckoutPspCommand.CheckoutPspProduct> products = new ArrayList<>(10)
        CheckoutPspCommand.CheckoutPspProduct mainProduct = new CheckoutPspCommand.CheckoutPspProduct()
        mainProduct.name = pspCommand.description
        mainProduct.quantity =  1
        mainProduct.price = totalPrice ? totalPrice : (quote.policyPrice - (quote.discount ?: 0))
        products.add(mainProduct)*/

        /*Donation donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(quote.id, ProductTypeEnum.findByName(insuranceType).value(), DonationTypeEnum.CHARITY)
        if (donation && donation.getAmount() != null && donation.getAmount() > 0) {
            CheckoutPspCommand.CheckoutPspProduct donationProduct = new CheckoutPspCommand.CheckoutPspProduct()
            donationProduct.name = donation.donationType.toString()
            donationProduct.quantity =  1
            donationProduct.price = donation.getAmount()
            products.add(donationProduct)
            pspCommand.amount = pspCommand.amount + donationProduct.price
        }*/

        String addonIds = ""
        if (quote) {
            List addons = checkoutService.getUAEOrderSummary(quote).addonList
            addons.each {
                //[label: it.addonTranslation.display, price: it.price, code: it.addonTranslation.code]
                /*CheckoutPspCommand.CheckoutPspProduct addonProduct = new CheckoutPspCommand.CheckoutPspProduct()
                addonProduct.name = it.label
                addonProduct.quantity = 1
                addonProduct.price = it.price
                addonProduct.code = it.code
                products.add(addonProduct)*/

                addonIds = addonIds ? addonIds + "," + it.addonTranslationId : it.addonTranslationId
            }

            /*if (quote instanceof HealthQuote) {
                CheckoutPspCommand.CheckoutPspProduct basmaFeeProduct = new CheckoutPspCommand.CheckoutPspProduct()
                basmaFeeProduct.name = messageSource.getMessage("orderSummary.bashma", null, locale)
                basmaFeeProduct.quantity = 1
                basmaFeeProduct.price = quote.additionalCharges
                products.add(basmaFeeProduct)

                CheckoutPspCommand.CheckoutPspProduct adminFeeProduct = new CheckoutPspCommand.CheckoutPspProduct()
                adminFeeProduct.name = messageSource.getMessage("healthInsurance.policyFee", null, locale)
                adminFeeProduct.quantity = 1
                adminFeeProduct.price = quote.c4meFee
                products.add(adminFeeProduct)
            }

            if (quote.getTotalVAT() != null ) {
                CheckoutPspCommand.CheckoutPspProduct vatProduct = new CheckoutPspCommand.CheckoutPspProduct()
                vatProduct.name = "Total VAT"
                vatProduct.quantity = 1
                vatProduct.price = quote.getTotalVAT()
                products.add(vatProduct)
            }*/
        }

        pspCommand.products = []//products

        if (quote) {
            PaymentMetadata metadata = new PaymentMetadata()
            metadata.p_total_price = quote.totalPrice
            metadata.p_discount_id = quote.discountCode?.id
            metadata.p_discount = quote.discount
            metadata.p_product_id = quote.product.id
            if (quote.hasProperty('addonPrice')) {
                metadata.p_addon_price = quote.addonPrice
                metadata.p_addon_vat = quote.addonVat
            }
            metadata.p_policy_price = quote.policyPrice
            if (quote.hasProperty('c4meFee')) {
                metadata.p_c4me_fee = quote.c4meFee
                metadata.p_c4me_vat = quote.c4meFeeVat
            }
            metadata.p_pprice_vat = quote.policyPriceVat
            metadata.p_addon_id = addonIds
            if (quote instanceof CarQuote) {
                metadata.p_excess = quote.excess
                CarQuoteDetail carQuoteDetail = CarQuoteDetail.findByQuote(quote)
                metadata.p_sales_person_id = carQuoteDetail && carQuoteDetail.paymentLinkSalesPerson ? carQuoteDetail.paymentLinkSalesPerson.id : null
                metadata.p_act_sum_ins = quote.actualInsuredValue
            }
            pspCommand.metadata = metadata
        }

        CheckoutPspCommand.CheckoutPspBillingDescriptor descriptor = new CheckoutPspCommand.CheckoutPspBillingDescriptor()
        descriptor.name = quote ? quote.name : user.name
        descriptor.city = "Dubai"
        pspCommand.checkoutPspBillingDescriptor = descriptor

        pspCommand.is3DSEnabled = true
        pspCommand.doCapture = quote ? true : false

        CheckoutPspCommand.CheckoutPspCompleteAddress billingAddress = new CheckoutPspCommand.CheckoutPspCompleteAddress()
        billingAddress.address = new CheckoutPspCommand.CheckoutPspAddress()
        pspCommand.billingAddress = billingAddress
        pspCommand.shippingAddress = pspCommand.billingAddress

        CheckoutPspCommand.CheckoutPspCustomer customer = new CheckoutPspCommand.CheckoutPspCustomer()
        customer.name = quote ? quote.name : user.name
        customer.email = quote ? quote.email : user.email
        pspCommand.customer = customer

        return pspCommand
    }

}
