package com.cover.payment

import com.aciworldwide.commerce.gateway.plugins.e24PaymentPipe
import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.api.smartdubai.SmartDubaiCancelRequestCommand
import com.cover.api.smartdubai.SmartDubaiConfirmRequestCommand
import com.cover.api.smartdubai.SmartDubaiInitiateRequestCommand
import com.cover.checkout.DiscountCodeException
import com.safeguard.*
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.CarQuoteDetail
import com.safeguard.car.DiscountCode
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.payment.ProviderPaymentQuoteMapping
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.transaction.Transactional
import org.apache.commons.lang3.RandomStringUtils
import org.joda.time.LocalDateTime
import org.springframework.http.HttpStatus
import org.springframework.util.Assert

import java.math.RoundingMode

@Transactional
class CheckoutService {

    def commonQuoteService
    def commonUtilService
    def commonPolicySgService
    def grailsApplication
    def messageSource
    def payfortService
    def paymentService
    def quoteService
    def utilService
    def policySgService
    def sessionService
    def discountService

    def lifeUtilService

    def changePaymentStatus(def quoteId, PaymentStatusEnum newStatus, String merchantRef = null) {

        CarQuote quote = CarQuote.findById(quoteId)

        log.info("Changing CarQuote[${quoteId}] status from ${quote.paymentStatus} to-> ${newStatus}")

        paymentService.changePaymentStatus(quote, newStatus)

        if (merchantRef) {
            quote.merchantRef = merchantRef
        }

        quote.save(flush: true)
    }


    @Transactional(readOnly = true)
    def getDiscount(String code, double amount, Integer productTypeId, Long productId = null, String country = null,
                    Long quoteId = null) throws DiscountCodeException {
        if (!code || !amount) {
            throw new IllegalArgumentException("'code', 'amount' parameters can't be null")
        }

        WhiteLabelBrandEnum brandEnum = sessionService.getBrand()
        WhiteLabelBrand brand
        //For YC, discount code to get will be for no brand.
        //Temporary fix
        if (brandEnum && brandEnum != WhiteLabelBrandEnum.YALLACOMPARE) {
            brand = WhiteLabelBrand.read(brandEnum.id)
        }

        ProductType productType = ProductType.read(productTypeId)

        if (productType?.id == ProductTypeEnum.CAR.value() && !productId) {
            throw new IllegalArgumentException("parameter 'productId' is required when parameter 'productTypeId' = $ProductType.CAR")
        }

        List<DiscountCode> discountCodeList = DiscountCode.createCriteria().list {
            eq("code", code)

            if (brand) {
                eq("brand", brand)
            } else {
                isNull("brand")
            }

            if (productType) {
                or {
                    eq("productType", productType)
                    isNull("productType")
                }
            } else {
                isNull("productType")
            }
        }

        DiscountCode discountCodeObj
        if (discountCodeList) {
            discountCodeObj = discountCodeList[0]
        }

        if (!discountCodeObj || !discountCodeObj.isStarted() ||
            (!country && discountCodeObj.country.id != utilService.country.id) ||
            (country && discountCodeObj.country.id != CountryEnum.findCountry(country).id)) {
            throw new DiscountCodeException("discount.code.invalid", code)
        }

        if (discountCodeObj.isExpired()) {
            throw new DiscountCodeException("discount.code.expired", code)
        }

        if (discountCodeObj.isUsed()) {
            throw new DiscountCodeException("discount.code.used", code)
        }

        /*if (productType.id == ProductTypeEnum.CAR.value() &&
            Product.findById(productId as Integer).typeId != CoverageTypeEnum.COMPREHENSIVE.value()) {
            throw new DiscountCodeException("discount.invalid.coveragetype", code)
        }*/

        if (discountCodeObj.minAmount > amount) {
            List args = [discountCodeObj.country.currency, discountCodeObj.minAmount]
            throw new DiscountCodeException("discount.code.minimum.amount", code, args)
        }

        BigDecimal discount = discountCodeObj.discount
        if (discountCodeObj.hasPercentDiscount) {
            discount = (discountCodeObj.discount * amount) / 100
        }

        if (productTypeId == ProductTypeEnum.CAR.value()) {
            Product product = Product.read(productId)
            BigDecimal c4meFee = 30
            if (quoteId) {
                CarQuote quote = CarQuote.read(quoteId)
                c4meFee = quote.c4meFee
            }
            discount = discountService.applyDiscountForTPL(discountCodeObj, product, new BigDecimal(amount - ((c4meFee?.doubleValue()) ?: 0)), c4meFee, discount)
        }

        if (country == CountryEnum.KWT.code) {
            discount = discount.setScale(2, BigDecimal.ROUND_HALF_UP)
        } else {
            discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        log.debug("Returning discount ${discount} for amount ${amount}")

        [discount, discountCodeObj]
    }

    boolean isSecured(Map params) {
        boolean ret = false

        try {
            String signature = params.signature

            String merchantRef = params.merchant_reference.toString()
            String[] merchantRefParts = merchantRef.split("-")

            if (merchantRefParts.length == 4) {
                merchantRef = "${merchantRefParts[0]}-${merchantRefParts[1]}-${merchantRefParts[2]}"
            }

            String countryCode
            def quote
            if (merchantRef.startsWith("MT")) {
                countryCode =  CountryEnum.UAE.code
            } else {
                quote = commonUtilService.getQuoteFromMerchantRef(merchantRef)
                CountryEnum countryEnum = commonUtilService.getCountryByQuote(quote)
                countryCode = countryEnum ? countryEnum.code : CountryEnum.UAE.code
            }

            String providerCode
            Provider provider = quote.product?.provider
            if (quote instanceof HealthQuote && provider.id == InsuranceProviderEnum.TAKAFUL_EMIRATE.id) {
                providerCode = provider.code
            }

            String merchantIdentifier = params.merchant_identifier
            String generatedSig = payfortService.createHash(params, paymentService.getPayfortResponsePhrase(merchantIdentifier, countryCode, providerCode))

            if (signature.equalsIgnoreCase(generatedSig)) {
                log.debug("Walla? Security signature is matched!")
                ret = true
            }
        } catch (Exception exp) {
            log.error("Error while checking hash security", exp)
        }

        return ret
    }

    /**
     * Is the request parameters secured? verify by signature
     * @param params
     * @return
     */
    boolean isCyberSourceRequestSecured(Map params) {
        boolean ret = false

        try {
            String signature = params.signature

            String generatedSig = paymentService.getCyberSourceSignature(params)

            if (signature.equalsIgnoreCase(generatedSig)) {
                log.debug("Walla? Security signature is matched!")
                ret = true
            }
        } catch (Exception exp) {
            log.error("Error while checking hash security", exp)
        }

        return ret
    }

    /**
     * For processing offline payments
     *
     * @param quote
     * @param paymentMethodEnum
     */
    def offlinePayment(def quote, PaymentMethodEnum paymentMethodEnum) {
        quote.paymentMethod = paymentMethodEnum
        quote.merchantRef = quote.encodedrMerchantRef()
        quote.save(flush: true)

        if (quote instanceof CarQuote || quote instanceof HealthQuote) {
            //COD isnt accepted as payment method for PA247 addon. Mark as deleted.
            CarQuoteAddon pa247Addon = null
            if (quote instanceof CarQuote) {
                pa247Addon = CarQuoteAddon.findByCarQuoteAndCode(quote, AddonCodeEnum.PA_247.code)
            } else if (quote instanceof HealthQuote) {
                pa247Addon = CarQuoteAddon.findByHealthQuoteAndCode(quote, AddonCodeEnum.PA_247.code)
            }

            if (pa247Addon) {
                pa247Addon.isDeleted = true
                pa247Addon.save(failOnError: true, flush: true)
            }
        }
    }

    /**
     * Process payfort feedback.
     * Verify signature and if not processed then processes only for SUCCESSFUL authorization.
     * Store response from payfort against the quote id
     *
     * @param handler
     * @return
     */
    def processPayfortFeedback(String handler, def params) {
        log.info(".processPayfortFeedback - merchant_reference:${params.merchant_reference}")

        def customParams = getCustomParams(params)

        // check if the response received from payfort is for Car, Health, or Home Insurance
        String merchantReference = params.merchant_reference.toString()
        def quote = commonUtilService.getQuoteFromMerchantRef(merchantReference)

        ProviderPaymentQuoteMapping providerPaymentQuoteMapping = ProviderPaymentQuoteMapping.findByProviderPaymentReference(merchantReference)

        if (!quote) {
            log.debug("checkout.${handler} - Bad request - params:$params")
            return HttpStatus.BAD_REQUEST
        } else {
            log.info("quote found by merchant ref ")
        }

        if ((providerPaymentQuoteMapping && quote) || isSecured(params)) {

            paymentService.savePaymentResponse(quote, params)

            if (quote.isNotProcessed()) {
                if (PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status)) {

                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)

                    log.debug("checkout.${handler} - processor quoteId: ${quote.id}")

                } else {

                    log.error "checkout.${handler} #### ERROR #### ${params} for ${quote.id} #### ERROR ####"
                }
            } else {
                log.warn("checkout.${handler} - quote is already Processed -> ${quote?.id}")

                if (PayfortStatusEnum.CAPTURE_SUCCESS.toString().equals(params.status) || PayfortStatusEnum.REFUND_SUCCESS.toString().equals(params.status)) {
                    log.debug("checkout.${handler} - status:${params.status}, quoteId:${quote.id}, updating payment details")

                    PayfortStatusEnum payfortStatus = PayfortStatusEnum.findByStatus(params.status)

                    String orderDescription = (String) params.order_description
                    if (orderDescription) {
                        Optional<String> paymentInfoUuidOpt = Arrays.asList(orderDescription.split("##"))
                            .stream()
                            .filter({ String token ->
                                return token.contains("payment_info_uuid:")
                            }).map({ String token ->
                                return token.split("payment_info_uuid:")[1]?.trim()
                            }).findFirst()

                        if (paymentInfoUuidOpt.isPresent()) {
                            // Processing is needed only if the Quote Payment Info record exists(record will be there
                            // only in case if Payfort request returns UNCERTAIN_TRANSACTION response, otherwise it will be
                            // deleted immediately after Payfort request)
                            if (QuotePaymentInfo.findByUuid(paymentInfoUuidOpt.get())) {
                                processQuotePaymentInfoRecord(paymentInfoUuidOpt.get(), payfortStatus)
                            }
                        } else {
                            log.warn("'payment_info_uuid' is missing in 'order_description'(value = '$orderDescription') param. That UUID is required to check" +
                                "if there is a Quote Payment Info record waiting to be processed.")
                        }
                    } else {
                        log.warn("'order_description' is missing in params. Most probably Payfort request has been made directly through Payfort website.")
                    }
                }

                //Update Card details if required
                if (params.token_name != null &&
                    (PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status) ||
                        PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status))) {
                    paymentService.updatePaymentCardToken(merchantReference, params.token_name)
                }

            }

        } else {
            log.error("checkout.${handler} **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED **** params:$params")
        }

        return HttpStatus.OK
    }

    /**
     * Get custom parameters from params returned from payfort
     *
     * @param params
     * @return
     */
    def getCustomParams(def params) {
        def customParams = [:]

        customParams.p_addon_id = params.list('p_addon_id')
        customParams.p_product_id = params.p_product_id
        customParams.p_discount_id = params.p_discount_id
        customParams.p_discount = params.p_discount
        customParams.p_excess = params.p_excess
        customParams.p_total_price = params.p_total_price
        customParams.p_policy_price = params.p_policy_price
        customParams.p_c4me_fee = params.p_c4me_fee
        customParams.p_addon_price = params.p_addon_price
        customParams.p_quote_v = params.p_quote_v
        customParams.p_pprice_vat = params.p_pprice_vat
        customParams.p_c4me_vat = params.p_c4me_vat
        customParams.p_addon_vat = params.p_addon_vat
        customParams.p_act_sum_ins = params.p_act_sum_ins

        return customParams
    }

    def getUAEOrderSummary(def quote) {

        def addons
        def donation

        if (quote instanceof HomeQuote) {
            addons = CarQuoteAddon.findAllByHomeQuoteAndIsDeleted(quote, false)
            donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(quote.id, ProductTypeEnum.HOME.value(), DonationTypeEnum.CHARITY)
        } else if (quote instanceof HealthQuote) {
            addons = CarQuoteAddon.findAllByHealthQuoteAndIsDeleted(quote, false)
            donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(quote.id, ProductTypeEnum.HEALTH.value(), DonationTypeEnum.CHARITY)
        } else if (quote instanceof CarQuote) {
            addons = CarQuoteAddon.findAllByCarQuoteAndIsDeleted(quote, false)
            donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(quote.id, ProductTypeEnum.CAR.value(), DonationTypeEnum.CHARITY)
        } else {
            addons = null
        }

        Provider provider = quote.product?.provider

        List addonList = []
        List carQuoteAddonList = []

        def bulletServiceAddon
        def homeInsuranceAddon
        def lifeInsuranceAddon

        BigDecimal bulletServiceAddonVat
        BigDecimal homeInsuranceAddonVat

        addons.collect {
            def addonItem = [label: it.addonTranslation.display, price: it.price, code: it.addonTranslation.code,
                             addonTranslationId: it.addonTranslation.id]

            // life Insurance addon on car insurance checkout
            if (addonItem.code == 'lifeInsuranceAddon') {
                try {
                    Long rateId = it.code.split('-')[1] as Long
                    if (rateId) {
                        // i used static country as the name of this method is for UAE
                        addonItem.label = lifeUtilService.getRateLabelByRateId(rateId, CountryEnum.findCountryByCurrency(quote.currency))
                    }
                }
                catch (Exception e) {
                    log.error "Error fetching life rate in add on $e"
                }

            }

            if (it.addonTranslation.code == AddonCodeEnum.DYNAMIC_ADDON.code) {
                addonItem.label = it.addonDescription
            }

            addonList.add(addonItem)

            if (quote instanceof CarQuote) {
                if (addonItem.code == 'bulletService') {
                    bulletServiceAddon = addonItem
                } else if (addonItem.code == 'homeInsurance') {
                    homeInsuranceAddon = addonItem
                } else if ( addonItem.code == 'lifeInsuranceAddon' ) {
                    lifeInsuranceAddon = addonItem
                } else {
                    carQuoteAddonList.add(addonItem)
                }
            }
        }

        BigDecimal insuranceSubTotalVat = 0
        BigDecimal insuranceSubTotal = 0

        if (quote instanceof CarQuote) {

            if (bulletServiceAddon) {
                bulletServiceAddonVat = commonUtilService.getVATAmount(bulletServiceAddon.price)
            }
            if (homeInsuranceAddon) {
                homeInsuranceAddonVat = commonUtilService.getVATAmount(homeInsuranceAddon.price)
            }

            log.info("checkoutTagLib.orderSummary - quoteId:${quote.id}, policyPriceVat:${quote.policyPriceVat}")

            //Total vat excluding non yc addons
            insuranceSubTotalVat = quote.policyPriceVat ? quote.policyPriceVat
                .plus(quote.addonVat ?: 0)
                .minus(quote.ycAddonVat ?: 0)
                .plus(quote.additionalChargesVAT ?: 0) : 0

            log.info("checkoutTagLib.orderSummary - quoteId:${quote.id}, insuranceSubTotalVat:${insuranceSubTotalVat}")

            //Total price excluding non yc addons
            insuranceSubTotal = quote.policyPrice
                .minus(quote.discount ?: 0)
                .plus(insuranceSubTotalVat)
                .plus(quote.addonPrice ?: 0)
                .minus(quote.ycAddonPrice ?: 0)
                .plus(quote.additionalCharges ?: 0)

            log.info("checkoutTagLib.orderSummary - quoteId:${quote.id}, insuranceSubTotal:${insuranceSubTotal}")

            insuranceSubTotalVat = insuranceSubTotalVat.minus(quote.c4meFeeVat ?: 0)

            log.info("checkoutTagLib.orderSummary - quoteId:${quote.id}, insuranceSubTotalVat:${insuranceSubTotalVat}")

        } else if (quote instanceof HealthQuote) {
            insuranceSubTotal = quote.policyPrice.add(quote.policyPriceVat ?: 0).add(quote.additionalCharges ?: 0).add(quote.additionalChargesVAT ?: 0)
        }

        return [quote                : quote, provider: provider, addonList: addonList, bulletServiceAddon: bulletServiceAddon,
                homeInsuranceAddon   : homeInsuranceAddon,
                lifeInsuranceAddon   : lifeInsuranceAddon,
                donation             : donation ? donation.amount : 0,
                bulletServiceAddonVat: bulletServiceAddonVat, homeInsuranceAddonVat: homeInsuranceAddonVat,
                subTotal             : insuranceSubTotal,
                subTotalVat          : insuranceSubTotalVat, carQuoteAddonList: carQuoteAddonList]

    }

    /**
     * Get simple order summary
     *
     * @param quote
     * @return
     */
    def getSimpleOrderSummary(CarQuote quote) {

        def addons = CarQuoteAddon.findAllByCarQuoteAndIsDeleted(quote, false)

        Provider provider = quote.product.provider

        List addonList = []

        addons.collect {
            def addonItem = [label: it.addonTranslation.display, price: it.price, code: it.addonTranslation.code]
            addonList.add(addonItem)
        }

        //Total vat other than non car-insurance vat
        log.info("checkoutTagLib.orderSummary - quoteId:${quote.id}")

        //Total Price other than price and vat for non car-insurance
        BigDecimal insuranceSubTotal = quote.policyPrice
            .minus(quote.discount ?: 0)
            .plus(quote.addonPrice ?: 0)

        return [quote: quote, provider: provider, addonList: addonList, subTotal: insuranceSubTotal]
    }

    /**
     * Processes Quote Payment Info record and updates the corresponding quote with that record information.
     * @param quote Car, Health, Home or Life quote
     * @param payfortStatus payfort status
     */
    void processQuotePaymentInfoRecord(String uuid, PayfortStatusEnum payfortStatus) {
        log.info("com.cover.payment.CheckoutService#processQuotePaymentInfoRecord - uuid: $uuid, payfortStatus: ${payfortStatus.toString()}")

        Assert.hasText(uuid, "'uuid' should not be null/empty")

        QuotePaymentInfo paymentInfo = QuotePaymentInfo.findByUuid(uuid)
        Assert.notNull(paymentInfo, "Could not find any Quote Payment Info record by provided 'uuid'(value = '$uuid')")

        Assert.isTrue(payfortStatus in [PayfortStatusEnum.CAPTURE_SUCCESS, PayfortStatusEnum.REFUND_SUCCESS], "'payfortStatus' " +
            "should be equal to one from the following list [${PayfortStatusEnum.CAPTURE_SUCCESS.toString()}, " +
            "${PayfortStatusEnum.REFUND_SUCCESS.toString()}]'. Received value of 'payfortStatus' is '${payfortStatus.toString()}'")

        def quote
        String quoteType
        boolean isCarQuote = false

        if (paymentInfo.carQuote) {
            quote = paymentInfo.carQuote
            quoteType = "Car"
            isCarQuote = true
        } else if (paymentInfo.healthQuote) {
            quote = paymentInfo.healthQuote
            quoteType = "Health"
        } else if (paymentInfo.homeQuote) {
            quote = paymentInfo.homeQuote
            quoteType = "Home"
        } else if (paymentInfo.lifeQuote) {
            quote = paymentInfo.lifeQuote
            quoteType = "Life"
        } else {
            throw new RuntimeException("Quote Payment Info(uuid = $uuid) does not have a reference to any of the following: " +
                "Car Quote, Home Quote, Health Quote, Life Quote")
        }

        log.info("Updating $quoteType quote(id = $quote.id) with details in Quote Payment Info record: ${paymentInfo}")

        quote.capturedAmount = paymentInfo.capturedAmount

        PaymentStatusEnum newPaymentStatus

        if (payfortStatus == PayfortStatusEnum.CAPTURE_SUCCESS) {
            newPaymentStatus = PaymentStatusEnum.ISSUED

            quote.policyNo = paymentInfo.policyNo
            quote.policyActualPrice = paymentInfo.policyActualPrice
            quote.creditNote = paymentInfo.creditNote
            quote.actualPolicyStartDate = paymentInfo.actualPolicyStartDate

            if (isCarQuote) {
                quote.chassisNumber = paymentInfo.chassisNumber
                quote.actualInsuredValue = paymentInfo.actualInsuredValue

                policySgService.updateCrmStatus((CarQuote) quote, CrmStatusEnum.ISSUED, null, paymentInfo.currentUser)

                if (paymentInfo.lastYearProvider) {
                    CarQuoteDetail carQuoteDetail = CarQuoteDetail.findByQuote((CarQuote) quote)
                    carQuoteDetail.lastYearProvider = paymentInfo.lastYearProvider
                    carQuoteDetail.save(failOnError: true)
                }
            }

        } else if (payfortStatus == PayfortStatusEnum.REFUND_SUCCESS) {
            newPaymentStatus = (paymentInfo.capturedAmount > 0) ? PaymentStatusEnum.ISSUED : PaymentStatusEnum.REFUND
        } else {
            throw new RuntimeException("Payfort Status(value = '${payfortStatus.toString()}') is not supported")
        }

        paymentService.changePaymentStatus(quote, newPaymentStatus, null, paymentInfo.remarks,
            paymentInfo.capturedAmount, paymentInfo.currentUser)

        quote.save(failOnError: true)

        paymentInfo.dateProcessed = LocalDateTime.now()
        paymentInfo.save(failOnError: true)
    }

    /**
     * Creates a discount code for user in case if he/she doesn't have an active discount code for the specified country
     * and product type(Car, Health ... ) at the moment and returns the generated discount code. If the discount code
     * matching the aforementioned criteria already exists then the existing discount code is returned.
     *
     * @param productTypeId - an identifier for product type (Car, Health ...)
     * @param country - the country where the quote has been purchased by the user
     * @param user - user who has purchased the quote
     * @param quoteId - id of the quote the user has bought. The discount code is granted to the user after purchasing a quote
     * @return the existing discount code if one exists, otherwise a newly generated discount code.
     */
    String getOrCreateDiscountCode(int productTypeId, Country country, User user, long quoteId) {
        DiscountCode currentDiscountCode = DiscountCode.where {
            owningUser {
                id == user.id
            }
            productType {
                id == productTypeId
            }
            active == true
        }.get()

        if (currentDiscountCode) {
            return currentDiscountCode.code
        } else {
            int numberOfChars = grailsApplication.config.getProperty('autoGeneratedDiscountCode.numberOfChars', Integer, 6)

            def chars = ('A'..'Z') + ('0'..'9').toList()
            chars.removeAll('I', 'O', '1', '0')
            String discountCodeStr = RandomStringUtils.random(numberOfChars, chars as char[])

            DiscountCode discountCode = new DiscountCode(
                active: true,
                code: discountCodeStr,
                owningUser: user,
                discount: grailsApplication.config.getProperty("autoGeneratedDiscountCode.${CountryEnum.findCountryById(country.id).code}.discountPercentage"),
                hasPercentDiscount: true,
                name: 'referral',
                startDate: LocalDateTime.now(),
                productType: ProductType.read(productTypeId),
                country: country
            )

            if (productTypeId == ProductTypeEnum.CAR.value()) {
                CarQuote quote = CarQuote.read(quoteId)
                if (quote && quote.requestSource != RequestSourceEnum.WEB
                    && quote.requestSource != RequestSourceEnum.MOBILEAPP
                    && quote.requestSource != RequestSourceEnum.ZOHO) {
                    int brandId = WhiteLabelBrandEnum.getWhiteLabelBrandEnumFromRequestSource(quote.requestSource).id
                    discountCode.brand = WhiteLabelBrand.read(brandId)
                }
            }

            discountCode.save(failOnError: true)

            discountCode.code
        }
    }

    /**
     * Get Applicable installment plans
     * @param country
     * @param lang
     * @param totalAmount
     * @param bin
     * @param currency
     * @return
     */
    def getInstallmentPlans(String country, String lang, BigDecimal totalAmount, String bin, String currency) {
        log.info("checkout.getInstallmentPlans - country:$country, lang:$lang, amount:$totalAmount, bin:$bin, currency:$currency")

        PayfortResponse payfortResponse = payfortService.getInstallmentPlans(country, lang)
        Locale locale = new Locale(lang)
        List plans = []

        String countryCodeOther = "are"

        Map issuingBank = payfortResponse.installmentIssuerDetail.find { Map issuerDetail ->
            Map binDetail = issuerDetail.bins.find {
                it.bin == bin && it.country_code.toLowerCase() == countryCodeOther && it.currency_code.toLowerCase() == currency.toLowerCase()
            }
            if (binDetail) return true
        }
        log.info("issuingBank?.plan_details:${issuingBank?.plan_details}")
        List applicablePlans = issuingBank?.plan_details.findAll {
            it.minimum_amount / 100 <= totalAmount &&
                it.maximum_amount / 100 >= totalAmount &&
                it.currency_code.toLowerCase() == currency.toLowerCase()
        }

        if (applicablePlans && issuingBank?.formula) {

            String formula = issuingBank?.formula //Has to be this: (amount +(amount * effective rate/100))/period

            applicablePlans.each { Map planDetail ->
                log.info("checkout.getInstallmentPlans - planDetail:$planDetail")

                BigDecimal feesAmount = planDetail.fees_amount / 100
                Integer noOfInstallments = planDetail.number_of_installment
                BigDecimal feeDisplayAmount = (new BigDecimal(planDetail.fee_display_value) / 100).setScale(2, RoundingMode.HALF_UP)

                switch (planDetail.fees_type) {
                    case "Percentage":

                        BigDecimal rate = feesAmount
                        log.info("checkout.getInstallmentPlans - rate:$rate, noOfInstallments:$noOfInstallments")

                        String expression = formula.replaceAll("amount", "$totalAmount")
                        expression = expression.replaceAll("effective rate", "$rate")
                        expression = expression.replaceAll("period", "$noOfInstallments")
                        log.info("checkout.getInstallmentPlans - expression:$expression")

                        BigDecimal amountPerMonth =
                            Eval.me(expression).toString().toBigDecimal().setScale(2, RoundingMode.HALF_UP)
                        log.info("checkout.getInstallmentPlans - amountPerMonth:$amountPerMonth")

                        Map plan = [
                            planCode          : planDetail.plan_code,
                            planType          : planDetail.fees_type,
                            interestRate      : feeDisplayAmount,
                            amountPerMonth    : amountPerMonth,
                            installmentsNo    : noOfInstallments,
                            currency          : currency,
                            installmentsText  : messageSource
                                .getMessage("cover.payment.installment.hosted.installmentsText", [noOfInstallments].toArray(), locale),
                            amountPerMonthText: messageSource
                                .getMessage("cover.payment.installment.hosted.amountPerMonthText",
                                [commonUtilService.formatNumber(amountPerMonth), currency].toArray(), locale),
                            interestRateText  : messageSource
                                .getMessage("cover.payment.installment.hosted.interestRateText", [feeDisplayAmount].toArray(), locale)
                        ]

                        plans.add(plan)

                        break
                    case "Fixed":
                        //(fees_amount + amount) / number of installments.
                        BigDecimal amountPerMonth =
                            ((feesAmount + totalAmount) / noOfInstallments).setScale(2, RoundingMode.HALF_UP)

                        Map plan = [
                            planCode          : planDetail.plan_code,
                            planType          : planDetail.fees_type,
                            interestRate      : feeDisplayAmount,
                            amountPerMonth    : amountPerMonth,
                            installmentsNo    : noOfInstallments,
                            currency          : currency,
                            installmentsText  : messageSource
                                .getMessage("cover.payment.installment.hosted.installmentsText", [noOfInstallments].toArray(), locale),
                            amountPerMonthText: messageSource
                                .getMessage("cover.payment.installment.hosted.amountPerMonthText",
                                [commonUtilService.formatNumber(amountPerMonth), currency].toArray(), locale),
                            interestRateText  : "${feeDisplayAmount} $currency",
                        ]

                        plans.add(plan)
                        break
                    default:
                        break
                }
            }
        }

        plans = plans.sort { a, b -> a.installmentsNo <=> b.installmentsNo }

        Map issuer = [
            issuerCode           : issuingBank?.issuer_code,
            issuerName           : lang == 'ar' ? issuingBank?.issuer_name_ar : issuingBank?.issuer_name_en,
            termsLink            : lang == 'ar' ? issuingBank?.terms_and_condition_ar : issuingBank?.terms_and_condition_en,
            logo                 : lang == 'ar' ? issuingBank?.issuer_logo_ar : issuingBank?.issuer_logo_en,
            processingFeesMessage: lang == 'ar' ? issuingBank?.processing_fees_message_ar : issuingBank?.processing_fees_message_en,
            plans                : plans
        ]

        issuer.termsLinkMessage =
            messageSource.getMessage("cover.payment.installment.hosted.agreement", [issuer.termsLink].toArray(), locale)

        return issuer
    }

    /**
     * Update installment plan against the code in QuoteDetail
     *
     * @param quote
     * @param quoteDetail
     * @param issuerCode
     * @param planCode
     */
    void updateInstallmentPlan(def quote, def quoteDetail, String issuerCode, String planCode,
                               String numberOfInstallments) {
        log.info("checkout.updateInstallmentPlan - entering with [quote:${quote.id}, issuerCode:$issuerCode, " +
            "planCode:$planCode, noOfInstallments:$numberOfInstallments]")

        quoteDetail.installmentIssuerCode = issuerCode == "" ? null : issuerCode
        quoteDetail.installmentPlanCode = planCode == "" ? null : planCode
        quoteDetail.save(failOnError: true)
    }

    /**
     * Clear Quote with payment information
     */
    void clearQuoteWithFromPaymentDetail(def quote, ProductTypeEnum productTypeEnum) {
        log.info("checkout.clearQuoteOnInvalidPayment - entering with [quote:${quote.id}]")

        def quoteDetail = commonQuoteService.getQuoteDetail(quote, productTypeEnum)

        //Clear installment payment information
        if (quoteDetail) {
            updateInstallmentPlan(quote, quoteDetail, null, null, null)
        }


    }

    /**
     * KNet Page parameters
     * @param merchantRef
     * @param currency
     * @param totalPrice
     * @return Map with params and action url for form.
     */
    def getKnetPaymentPage(CarQuote quote, String quoteVersion, String countryCode) {

        String merchantRef = quote.encodedrMerchantRef()

        def params = [
            action      : grailsApplication.config.getProperty("knet.paymentPage.action"),
            alias       : grailsApplication.config.getProperty("knet.paymentPage.alias"),
            currency    : "414", //KD
            language    : quote.lang == "ar" ? "ARA" : "ENG", //ENG for English, ARA for Arabic
            resourcePath: grailsApplication.config.getProperty("knet.paymentPage.resourcePath"),
            responseURL : grailsApplication.config.getProperty("knet.paymentPage.responseURL"),
            errorURL    : grailsApplication.config.getProperty("knet.paymentPage.errorURL"),
            totalAmount : quote.totalPrice,
            trackId     : merchantRef,
            "usd1"      : quoteVersion
        ]
        log.info("params:$params")
        // set properties
        def pipe = new e24PaymentPipe()
        pipe.setDebug(true)
        pipe.setAction(params.action)
        pipe.setCurrency(params.currency)
        pipe.setLanguage(params.language)
        pipe.setResponseURL(params.responseURL)
        pipe.setErrorURL(params.errorURL)
        pipe.setAmt(params.totalAmount.toString())
        pipe.setResourcePath(params.resourcePath)
        pipe.setAlias(params.alias)
        pipe.setTrackId(params.trackId)
        log.info("pipe.getDebugMsg():${pipe.getDebugMsg()}")

        log.info("error:${pipe.getErrorMsg()}")


        // send the Payment Initialization message
        if (pipe.performPaymentInitialization() != pipe.SUCCESS) {
            System.out.println("Error sending Payment Initialization Request: ");
            System.out.println(pipe.getDebugMsg());
            //response.sendRedirect( response.encodeRedirectURL("error.jsp") );
            //TODO: return error
            return
        }

        //get results
        String payID = pipe.getPaymentId()
        String payURL = pipe.getPaymentPage()

        //Store payment ID
        quote.paymentMethod = PaymentMethodEnum.KNET
        quote.knetPaymentId = payID
        quote.save(failOnError: true)

        log.info("quote.knetPaymentId:${quote.knetPaymentId}")

        return payURL + "?PaymentID=" + payID
    }

    /**
     * Add Payment Transaction against Quote
     * @param quote
     * @return
     */
    PaymentTransaction initializePaymentTransaction(def quote, SmartDubaiInitiateRequestCommand initiateRequest) {
        log.info("checkout.initializePaymentTransaction - quote:${quote.id}, initiateRequest:${initiateRequest}")

        PaymentTransaction paymentTransaction

        if (quote instanceof CarQuote) {
            paymentTransaction = new PaymentTransaction()
            paymentTransaction.merchantReference = quote.encodedrMerchantRef()
            paymentTransaction.carQuote = quote
            paymentTransaction.paymentStatus = quote.paymentStatus
            paymentTransaction.statusUpdated = LocalDateTime.now()
            paymentTransaction.customerEmail = initiateRequest.customerEmail
            paymentTransaction.initiateTimestamp = initiateRequest.timestamp
            paymentTransaction.amount = initiateRequest.transactionAmount
            paymentTransaction.requestString = initiateRequest.requestJSON
            paymentTransaction.save(failOnError: true)
        }

        paymentTransaction
    }

    /**
     * Confirm the Payment. Change status to paid and store the payment transaction details
     *
     * @param quote
     * @param confirmRequest
     * @return
     */
    PaymentTransaction confirmPaymentTransaction(def quote, SmartDubaiConfirmRequestCommand confirmRequest) {
        log.info("checkout.confirmPaymentTransaction - quote:${quote.id}, confirmRequest:${confirmRequest}")

        //Mark it as PAID
        //Update Confirm Payment Transaction
        Map<String, String> parameters = new HashMap<>()
        parameters.merchant_reference = confirmRequest.merchantReference

        paymentService.paid(parameters, [paymentGatewayEnum: PaymentGatewayEnum.SMARTDUBAI])

        log.info("checkout.confirmPaymentTransaction - finding Payment Transaction:${confirmRequest.merchantReference}")
        PaymentTransaction paymentTransaction =
            PaymentTransaction.findByMerchantReference(confirmRequest.merchantReference)

        paymentTransaction.statusUpdated = LocalDateTime.now()
        paymentTransaction.paymentStatus = PaymentStatusEnum.PAID
        paymentTransaction.confirmTimestamp = confirmRequest.timestamp
        paymentTransaction.epayDegTrn = confirmRequest.epayDegTrn
        paymentTransaction.epayTimestamp = confirmRequest.epayTimestamp
        paymentTransaction.epayServiceProviderCode = confirmRequest.epayServiceProviderCode
        paymentTransaction.epayServiceCode = confirmRequest.epayServiceCode
        paymentTransaction.amount = confirmRequest.amount
        paymentTransaction.epayAmount = confirmRequest.epayAmount
        paymentTransaction.approvalCode = confirmRequest.approvalCode
        paymentTransaction.requestString = paymentTransaction.requestString.concat(confirmRequest.requestJSON.toString())

        paymentTransaction.save(failOnError: true)

        paymentTransaction
    }

    /**
     * Cancel the Payment. Change status to cancel and store the payment transaction details
     *
     * @param quote
     * @param cancelRequest
     * @return
     */
    PaymentTransaction cancelPaymentTransaction(def quote, SmartDubaiCancelRequestCommand cancelRequest) {
        log.info("checkout.cancelPaymentTransaction - quote:${quote.id}, cancelRequestCommand:${cancelRequest}")

        //Mark it as Cancel
        //Update Confirm Payment Transaction
        Map<String, String> parameters = new HashMap<>()
        parameters.merchant_reference = cancelRequest.merchantReference

        log.info("checkout.cancelPaymentTransaction - finding Payment Transaction:${cancelRequest.merchantReference}")
        PaymentTransaction paymentTransaction =
            PaymentTransaction.findByMerchantReference(cancelRequest.merchantReference)

        paymentTransaction.statusUpdated = LocalDateTime.now()
        paymentTransaction.paymentStatus = PaymentStatusEnum.CANCEL
        paymentTransaction.cancelStatusCode = cancelRequest.statusCode
        paymentTransaction.cancelStatusDescription = cancelRequest.statusDescription
        paymentTransaction.cancelTimestamp = cancelRequest.timestamp
        paymentTransaction.epayDegTrn = cancelRequest.epayDegTrn
        paymentTransaction.epayTimestamp = cancelRequest.epayTimestamp
        paymentTransaction.epayServiceProviderCode = cancelRequest.epayServiceProviderCode
        paymentTransaction.epayServiceCode = cancelRequest.epayServiceCode
        paymentTransaction.epayAmount = cancelRequest.epayAmount
        paymentTransaction.requestString = paymentTransaction.requestString.concat(cancelRequest.requestJSON.toString())

        paymentTransaction.save(failOnError: true)

        paymentTransaction
    }

    /**
     * GetCyberSource CreditCard payment  parameters
     *
     * @param quote
     * @param quoteVersion
     * @param countryCode
     */
    def getCyberSourcePaymentParams(CarQuote quote, String quoteVersion, String countryCode, String customerIp,
                                    String sessionId) {
        log.info("checkout.getCyberSourcePaymentParams - quote:${quote.id}, quoteVersion:$quoteVersion, " +
            "countryCode:$countryCode, customerIp:${customerIp}")

        def params = [
            access_key             : grailsApplication.config.getProperty("cyberSource.paymentPage.accessKey"),
            profile_id             : grailsApplication.config.getProperty("cyberSource.paymentPage.profileId"),
            transaction_uuid       : UUID.randomUUID(),
            unsigned_field_names   : "",
            signed_field_names     : "device_fingerprint_id,customer_ip_address,bill_to_address_line1,bill_to_address_city,bill_to_address_country,access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,bill_to_surname,bill_to_email,bill_to_forename",
            signed_date_time       : commonUtilService.getUTCDateTime(),
            transaction_type       : "sale",
            reference_number       : quote.encodedrMerchantRef(),
            amount                 : quote.totalPrice.intValue(),
            currency               : quote.currency,
            bill_to_address_line1  : "-",
            bill_to_address_city   : "Sharq",

            bill_to_address_country: CountryEnum.KWT.dfp,
            locale                 : quote.lang,
            bill_to_email          : quote.email,
            bill_to_surname        : quote.name,
            bill_to_forename       : quote.name,
            customer_ip_address    : "${customerIp}",
            device_fingerprint_id  : "${sessionId}"
        ]

        String signature = paymentService.getCyberSourceSignature(params)
        params.action_url = grailsApplication.config.getProperty("cyberSource.paymentPage.action")
        params.signature = signature

        return [cyberSource: params]
    }

    /**
     * Process CyberSource response
     * Verify signature and if not processed then processes only for SUCCESSFUL authorization.
     * Store response from payfort against the quote id
     *
     * @param handler
     * @return
     */
    def processCyberSourceResponse(String handler, def params) {
        def customParams = getCustomParams(params)

        boolean hashMatched = isSecured(params)

        String merchantReference = params.merchant_reference.toString()
        def quote = commonUtilService.getQuoteFromMerchantRef(merchantReference)

        if (!quote) {
            log.debug("checkout.${handler} - Bad request - params:$params")
            return HttpStatus.BAD_REQUEST
        }

        if (hashMatched) {
            paymentService.savePaymentResponse(quote, params)

            if (quote && quote.isNotProcessed()) {


                if (PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status)) {

                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)

                    log.debug("checkout.${handler} - processor quoteId: ${quote.id}")

                } else {

                    log.error "checkout.${handler} #### ERROR #### ${params} for ${quote.id} #### ERROR ####"
                }
            } else {
                log.warn("checkout.${handler} - quote is already Processed -> ${quote?.id}")

                if (quote.paidDate &&
                    (quote.paymentStatus != PaymentStatusEnum.ISSUED
                        && PayfortStatusEnum.CAPTURE_SUCCESS.toString().equals(params.status))
                ){
                    log.debug("checkout.${handler} - status:${params.status}, quoteId:${quote.id}, updating payment details")

                    updateQuoteWithPaymentDetail(quote) // TODO: fix this. updateQuoteWithPaymentDetail() -> processQuotePaymentInfoRecord()
                }
            }

        } else {
            log.error("checkout.${handler} **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED **** params:$params")
        }

        return HttpStatus.OK
    }

}
