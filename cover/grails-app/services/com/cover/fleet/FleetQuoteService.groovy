package com.cover.fleet

import com.safeguard.AsyncEventConstants
import com.safeguard.User
import com.safeguard.fleet.FleetQuote
import grails.transaction.Transactional

@Transactional
class FleetQuoteService {

    def saveQuote(def params) {
        FleetQuote fleetQuote = new FleetQuote()
        fleetQuote.name = params.name
        fleetQuote.email = params.email
        fleetQuote.phone = params.phone
        fleetQuote.fleetSize = params.fleet_size
        fleetQuote.companyName = params.company_name

        User user = User.findByEmail(params.email);

        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.phone
            fleetQuote.user = newUser
        } else {
            user.name = params.name
            user.mobile = params.phone
            fleetQuote.user = user

        }
        fleetQuote.save()

        notify AsyncEventConstants.FLEET_QUOTE_CREATED, [quoteId: fleetQuote.getId(), companyName: fleetQuote.getCompanyName(), fleetSize: fleetQuote.getFleetSize()]
    }
}
