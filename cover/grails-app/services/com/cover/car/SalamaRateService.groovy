package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.CarValuationDto
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.apache.commons.lang3.tuple.Pair
import org.codehaus.groovy.runtime.InvokerHelper
import org.joda.time.LocalDate
/**
 * Calculate premium for Salama Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class SalamaRateService {

    def commonUtilService
    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 9
    public static final Integer PRODUCT_STANDARD = 34
    public static final Integer PRODUCT_HIGH_VALUE = 83
    public static final Integer PRODUCT_TPL = 35

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.noOfCyl = model.noOfCyl
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).salamaCategory
            QuoteCommand clonedQuoteCommand = new QuoteCommand()
            clonedQuoteCommand = customChecks(quoteCommand, clonedQuoteCommand)

            //salama ratings depend on noOfCyl so its mandatory
            if (model.noOfCyl) {
               // Integer noClaimYears = quoteCommand.customerAge > 29 ? ratingService.getNoClaimYears(quoteCommand) : null
                //Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(clonedQuoteCommand)
                Integer applicableDiscountsYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
                Integer noClaimYears = applicableDiscountsYears //noClaimYears ?: 0
                List<ProductBaseRate> applicableRates = ratingService.getProductBaseRate(clonedQuoteCommand, noClaimYears, isOffline)
                log.info("salama.getRates - applicableRates:${applicableRates.id}")
                if (applicableRates) {
                    for (rate in applicableRates) {
                        if (checkProductEligibility(clonedQuoteCommand, rate)) {
                            RateCommand rateCommand = populateRatings(clonedQuoteCommand, rate)
                            rateCommand.requiredSelfDeclarationNumber = noClaimYears
                            rateList.add(rateCommand)
                        }
                    }
                }
            }
        } else {
            log.info("Salama comprehensive is not eligible ")
        }

        rateList
    }

    QuoteCommand customChecks(QuoteCommand quoteCommand, QuoteCommand clonedQuoteCommand) {

        Integer modelMasterId = Model.read(quoteCommand.modelId).modelMasterId

        InvokerHelper.setProperties(clonedQuoteCommand, quoteCommand.properties)
        //Get Sedan rate for Hyundai Veloster
        if (modelMasterId == 207) {
            clonedQuoteCommand.vechileTypeId = VehicleTypeEnum.SEDAN.value
        }

        CarValuationDto providerValuation = commonUtilService.getProviderValuation(InsuranceProviderEnum.SALAMA,
            !quoteCommand.isNonGccSpec,  quoteCommand.carValuation)

        BigDecimal newInsuredValue = quoteCommand.insuredValue
        if (newInsuredValue > providerValuation.valuationMax) {
            newInsuredValue = providerValuation.valuationMax
        } else if (newInsuredValue < providerValuation.valuationMin) {
            newInsuredValue = providerValuation.valuationMin
        }

        clonedQuoteCommand.carValuation = providerValuation
        log.info(".customChecks - providerValuation:${providerValuation}")
        clonedQuoteCommand.insuredValue = newInsuredValue

        clonedQuoteCommand
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.noOfCyl = model.noOfCyl
        quoteCommand.carCategory = null
        quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).salamaCategory

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            QuoteCommand clonedQuoteCommand = new QuoteCommand()
            clonedQuoteCommand = customChecks(quoteCommand, clonedQuoteCommand)
//            Integer noClaimYears = quoteCommand.customerAge > 29 ? ratingService.getNoClaimYears(quoteCommand) : null
            Integer applicableDiscountsYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
            Integer noClaimYears = applicableDiscountsYears //noClaimYears ?: 0

            List<ProductBaseRate> applicableRates = ratingService.getProductBaseRate(clonedQuoteCommand, noClaimYears, isOffline)
            if (applicableRates) {

                ProductBaseRate rate = applicableRates.first()
                if (checkProductEligibility(clonedQuoteCommand, rate)) {
                    rateCommand = populateRatings(clonedQuoteCommand, rate)
                    rateCommand.requiredSelfDeclarationNumber = noClaimYears
                }
            }
        }

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        quoteCommand.carCategory = null
        quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).salamaCategoryTpl

        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)
        log.info("getTplRate - checkEligibility: ${checkEligibility}")

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId, model.noOfCyl, quoteCommand.customerAge,
                        isOffline, null, true, quoteCommand.requestSource, quoteCommand.nationalityCategory)
            log.info("getTplRate - applicableRatesSize: ${applicableRates?.size()}")
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                log.info("getTplRate - rateId: ${rate.id}, rateBasePremium: ${rate.basePremium}")
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand = applyTplBasePremium(rateCommand, quoteCommand, rate)
        rateCommand.premium = rateCommand.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        log.info("after premium - base:${rateCommand.basePremium}, min:${rateCommand.minPremium}, prem:${rateCommand.premium}")

        rateCommand = applyDiscounts(quoteCommand, rateCommand)

        rateCommand = checkMinimumPremium(rateCommand)
        log.info("after min premium - base:${rateCommand.basePremium}, min:${rateCommand.minPremium}, prem:${rateCommand.premium}")

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)

        rateCommand = applyAdditionalFees(rateCommand)
        log.info("after additional fee - base:${rateCommand.basePremium}, min:${rateCommand.minPremium}, prem:${rateCommand.premium}")

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
        log.info("after c4me - base:${rateCommand.basePremium}, min:${rateCommand.minPremium}, prem:${rateCommand.premium}")

        //TODO: Remove this check after 31 July 2018, So discount could be applied to agency repair as well
        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)
        log.info("quoteCommand.insuredValue:${quoteCommand.insuredValue}")

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand.productName = product.name
        rateCommand.carAge = quoteCommand.carAge

        rateCommand = applyBaseRate(rateCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        if (rateCommand.premium <= 0) {
            rateCommand.premium = rateCommand.minPremium
        }
        rateCommand.basePremium = rateCommand.premium

        log.info("base premium: ${rateCommand.basePremium}, min premium:${rateCommand.minPremium}")
        rateCommand

    }

    private RateCommand applyBaseRate(RateCommand rateCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        rateCommand.baseRate = baseRate
        return rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate productBaseRate) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            if (quoteCommand.isBrandNew && quoteCommand.carAge <= 1) {
                isAgency = true
            }
//            if (quoteCommand.carAge == 2 && !quoteCommand.hasClaim && quoteCommand.isOldAgency
//                && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
//                isAgency = true
//            } else if (quoteCommand.carAge == 3 && !quoteCommand.hasClaim && quoteCommand.isOldAgency
//                && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()) {
//                isAgency = true
//            } else if (productBaseRate.productId == PRODUCT_HIGH_VALUE
//                && quoteCommand.carAge == 4 && !quoteCommand.hasClaim && quoteCommand.isOldAgency
//                && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
//                isAgency = true
//            }
        }

        isAgency
    }

    RateCommand applyAdditionalFees(RateCommand rateCommand) {

        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)
        int carAge = quoteCommand.carAge
        log.info("isEligible:$isEligible")
        log.info("quoteCommand.carAgeByManufactureYear:${quoteCommand.carAgeByManufactureYear}")
        log.info("quoteCommand.carAgeInMonthsByRegistrationDate:${quoteCommand.carAgeInMonthsByRegistrationDate}")

        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            if (quoteCommand.isThirdParty) {
                isEligible = false
            }

            if (quoteCommand.carAgeByManufactureYear > 16 ||
                quoteCommand.carAgeInMonthsByRegistrationDate >= 169) {
                isEligible = false
            }

        } else {
            if (carAge > 21) {
                isEligible = false
            }
        }

        Model model = Model.read(quoteCommand.modelId)
        if (!(model.vehicleTypeId.intValue() in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value])) {
            //Any vehicle other than Sedan and SUV are not allowed
            isEligible = false
        }

        // Salama to show for 27 & Below only for the time
        /*if (productTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.customerAge > 27) {
            isEligible = false
        }*/

        isEligible
    }

    BigDecimal calculateBasicPremiumRateForInstantIssueApi(QuoteCommand quoteCommand) {
        quoteCommand.providerId = PROVIDER_ID
        BigDecimal basicPremiumRate = null

        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.noOfCyl = model.noOfCyl
        quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).salamaCategory

        if (quoteCommand.productId == ProductEnum.SALAMA_THIRD_PARTY.id) {

            List<ProductTplRate> applicableRates = ratingService.findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                model.noOfCyl, quoteCommand.customerAge, false, null, true, quoteCommand.requestSource)

            if (applicableRates.isEmpty()) {
                throw new RuntimeException("Product Tpl Rate is not found by the following criteria: provider id: $PROVIDER_ID, " +
                    "vehicle type id: $model.vehicleTypeId, noOfCyl: $model.noOfCyl, customer age: $quoteCommand.customerAge, " +
                    "isOffline: false")
            }

            ProductTplRate productTplRate = applicableRates.first()
            basicPremiumRate = productTplRate.basePremium

        } else {

            QuoteCommand clonedQuoteCommand = new QuoteCommand()
            clonedQuoteCommand = customChecks(quoteCommand, clonedQuoteCommand)

//            Integer noClaimYears = quoteCommand.customerAge > 29 ? ratingService.getNoClaimYears(quoteCommand) : null
            Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(clonedQuoteCommand)
            noClaimYears = noClaimYears ?: 0
            List<ProductBaseRate> applicableRates =
                ratingService.findApplicableRates(PROVIDER_ID, clonedQuoteCommand.makeId, clonedQuoteCommand.vechileTypeId, clonedQuoteCommand.insuredValue,
                    clonedQuoteCommand.customerAge, clonedQuoteCommand.carAge, clonedQuoteCommand.productId, clonedQuoteCommand.carCategory,
                    clonedQuoteCommand.noOfCyl, false, clonedQuoteCommand.requestSource,
                    clonedQuoteCommand.registrationCityId, clonedQuoteCommand.nationalityCategory, noClaimYears)

            if (applicableRates.isEmpty()) {
                throw new RuntimeException("Product Base Rate is not found by the following criteria: provider id: $PROVIDER_ID, " +
                    "vehicle type id: $clonedQuoteCommand.vechileTypeId, insured value: $clonedQuoteCommand.insuredValue, " +
                    "customer age: $clonedQuoteCommand.customerAge, car age: $clonedQuoteCommand.carAge, product id: $clonedQuoteCommand.productId," +
                    " car category: $clonedQuoteCommand.carCategory, noOfCyl: $model.noOfCyl, isOffline: false")
            }

            ProductBaseRate productBaseRate = applicableRates.first()

            RateCommand rateCommand = calculatePremium(clonedQuoteCommand, productBaseRate)
            rateCommand = applyDiscounts(clonedQuoteCommand, rateCommand)

            rateCommand = checkMinimumPremium(rateCommand)

            if (rateCommand.premium == rateCommand.minPremium) {
                basicPremiumRate = rateCommand.premium

            } else {
                boolean isAgency = checkAgency(clonedQuoteCommand, productBaseRate)
                BigDecimal baseRate = rateCommand.baseRate

                basicPremiumRate = baseRate

                if (productBaseRate.productId.intValue() == PRODUCT_HIGH_VALUE) {
                    // NCD Discount
                    if (clonedQuoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                        // 10% discount
                        basicPremiumRate = baseRate * new BigDecimal("0.9")
                    } else if (clonedQuoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()) {
                        // 15% discount
                        basicPremiumRate = baseRate * new BigDecimal("0.8")
                    }
                }

                int customerAge = clonedQuoteCommand.customerAge

                if (customerAge >= 65) {
                    basicPremiumRate += new BigDecimal(baseRate * 0.1) // 10% addition
                } /*else if (customerAge >= 28 && customerAge < 30) {
                    basicPremiumRate += new BigDecimal(baseRate * 0.15) // 15% addition
                } else if (customerAge >= 26 && customerAge <= 27) {
                    basicPremiumRate += new BigDecimal(baseRate * 0.25) // 25% addition
                } else if (customerAge >= 24 && customerAge <= 25) {
                    basicPremiumRate += new BigDecimal(baseRate * 0.4) // 40% addition
                }*/

                /*if (isAgency && rateCommand.productId == PRODUCT_HIGH_VALUE) {
                    if (clonedQuoteCommand.carAge == 2) {
                        basicPremiumRate += new BigDecimal(baseRate * 0.15) // 15% addition
                    } else if (clonedQuoteCommand.carAge == 3) {
                        basicPremiumRate += new BigDecimal(baseRate * 0.2) // 20% addition
                    } else if (clonedQuoteCommand.carAge == 4) {
                        basicPremiumRate += new BigDecimal(baseRate * 0.4) // 40% addition
                    }
                }*/

            }

        }

        basicPremiumRate
    }

    boolean checkProductEligibility(QuoteCommand quoteCommand, ProductBaseRate rate) {
        log.info("salama.checkProductEligibility - rate:${rate.productId}")

        if (quoteCommand.manufactureYear <= 2010 && rate.productId == PRODUCT_STANDARD) {
            log.info("salama.checkProductEligibility - rate:${rate.productId} is not eligible")
            return false
        }

        if (rate.productId != PRODUCT_HIGH_VALUE ||
            (rate.productId == PRODUCT_HIGH_VALUE
                && quoteCommand.registrationCityId in [CityEnum.DUBAI.value(), CityEnum.ABU_DHABI.value(), CityEnum.AL_AIN.value()]
                && quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.ONE_TO_TWO.id)) {
            log.info("salama.checkProductEligibility - rate:${rate.productId} is eligible")

            return true
        }

        log.info("salama.checkProductEligibility - rate:${rate.productId} is not eligible")

        return false
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        if (rate.productId == PRODUCT_HIGH_VALUE) {

            // NCD Discount
            if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                // 10% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
            } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()) {
                // 20% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
            }
        }

        /*if (rate.productId == PRODUCT_STANDARD
            && !quoteCommand.isPolicyExpired
            && ratingService.isEligibleForSelfDecDiscount(quoteCommand)
            && quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
            rate.requiredSelfDeclarationNumber = noClaimYears
        }*/

        rate
    }

    private RateCommand applyTplBasePremium(RateCommand rateCommand, QuoteCommand quoteCommand,
                                            ProductTplRate productTplRate) {

        BigDecimal basePremium = productTplRate.basePremium
        Integer noOfCyl = Model.read(quoteCommand.modelId).noOfCyl

        Integer noClaimYearsByNCD = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(quoteCommand)
        if (noClaimYearsByNCD == 0 && quoteCommand.noClaimsDiscountId && isNewDriverWithInternationalLicense(quoteCommand)) {
            noClaimYearsByNCD = 1
        }

        Integer noClaimYearsBySelfDec = 0
        if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) && !quoteCommand.isPolicyExpired) {
            noClaimYearsBySelfDec = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
            if (noClaimYearsBySelfDec == 0 && isNewDriverWithInternationalLicense(quoteCommand)) {
                noClaimYearsBySelfDec = 1
            }
        }
        Integer noClaimYears = noClaimYearsByNCD ? noClaimYearsByNCD : noClaimYearsBySelfDec
        log.info(".applyTplBasePremium - noClaimYears:${noClaimYears}")

        if (productTplRate.productId == PRODUCT_TPL && !quoteCommand.isPolicyExpired && noClaimYears != null) {

            if (quoteCommand.customerAge >= 30 && quoteCommand.customerAge <= 99) {
                if (quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value] &&
                    quoteCommand.nationalityCategory == "NG1") {

                    if (noClaimYears == 1) {
                        if (noOfCyl == 4) {
                            basePremium = 750
                        } else if (noOfCyl == 6) {
                            basePremium = 855
                        } else if (noOfCyl == 8) {
                            basePremium = 855
                        } else if (noOfCyl > 8) {
                            basePremium = 1300
                        }

                    } else if (noClaimYears >= 2) {
                        if (noOfCyl == 4) {
                            basePremium = 750
                        } else if (noOfCyl == 6) {
                            basePremium = 850
                        } else if (noOfCyl == 8) {
                            basePremium = 850
                        } else if (noOfCyl > 8) {
                            basePremium = 1300
                        }

                    }

                } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                    quoteCommand.nationalityCategory == "NG1") {

                    if (noClaimYears == 1) {
                        if (noOfCyl == 4) {
                            basePremium = 900
                        } else if (noOfCyl == 6) {
                            basePremium = 990
                        } else if (noOfCyl == 8) {
                            basePremium = 990
                        } else if (noOfCyl > 8) {
                            basePremium = 1170
                        }

                    } else if (noClaimYears >= 2) {
                        if (noOfCyl == 4) {
                            basePremium = 800
                        } else if (noOfCyl == 6) {
                            basePremium = 880
                        } else if (noOfCyl == 8) {
                            basePremium = 880
                        } else if (noOfCyl > 8) {
                            basePremium = 1040
                        }
                    }

                } else if (quoteCommand.vechileTypeId in  [VehicleTypeEnum.SEDAN.value] &&
                    quoteCommand.nationalityCategory == "NG2") {

                    if (noClaimYears == 1) {
                        if (noOfCyl == 4) {
                            basePremium = 1158
                        } else if (noOfCyl == 6) {
                            basePremium = 1263
                        } else if (noOfCyl == 8) {
                            basePremium = 1263
                        } else if (noOfCyl > 8) {
                            basePremium = 2100
                        }

                    } else if (noClaimYears >= 2) {
                        if (noOfCyl == 4) {
                            basePremium = 1158
                        } else if (noOfCyl == 6) {
                            basePremium = 1263
                        } else if (noOfCyl == 8) {
                            basePremium = 1263
                        } else if (noOfCyl > 8) {
                            basePremium = 2100
                        }
                    }

                } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                    quoteCommand.nationalityCategory == "NG2") {

                    if (noClaimYears == 1) {
                        if (noOfCyl == 4) {
                            basePremium = 1579
                        } else if (noOfCyl == 6) {
                            basePremium = 1789
                        } else if (noOfCyl == 8) {
                            basePremium = 1789
                        } else if (noOfCyl > 8) {
                            basePremium = 2150
                        }

                    } else if (noClaimYears >= 2) {
                        if (noOfCyl == 4) {
                            basePremium = 1579
                        } else if (noOfCyl == 6) {
                            basePremium = 1789
                        } else if (noOfCyl == 8) {
                            basePremium = 1789
                        } else if (noOfCyl > 8) {
                            basePremium = 2150
                        }
                    }

                }
            } else if (quoteCommand.customerAge >= 18 && quoteCommand.customerAge <= 29) {
                 if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                    quoteCommand.nationalityCategory == "NG1") {

                    if (noClaimYears == 1) {
                        if (noOfCyl == 4) {
                            basePremium = 1053
                        } else if (noOfCyl == 6) {
                            basePremium = 1137
                        } else if (noOfCyl == 8) {
                            basePremium = 1232
                        } else if (noOfCyl > 8) {
                            basePremium = 1232
                        }

                    } else if (noClaimYears >= 2) {
                        if (noOfCyl == 4) {
                            basePremium = 1053
                        } else if (noOfCyl == 6) {
                            basePremium = 1011
                        } else if (noOfCyl == 8) {
                            basePremium = 1095
                        } else if (noOfCyl > 8) {
                            basePremium = 1095
                        }
                    }
                }
            }

            if (!noClaimYearsByNCD) {
                rateCommand.requiredSelfDeclarationNumber = noClaimYears
            }

        }

        rateCommand.basePremium = basePremium

        return rateCommand
    }

    RateCommand checkMinimumPremium(RateCommand rate) {
        rate.actualBasePremium = rate.premium

        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium

            rate.productDiscountAmount = null
            rate.productDiscountPercent = null
        }

        rate
    }

    /**
     * Check if customer has less local driving experience but has at least 1 year international driving experience
     * @param quoteCommand
     * @return
     */
    private boolean isNewDriverWithInternationalLicense(QuoteCommand quoteCommand) {
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id &&
            quoteCommand.internationalDrivingExperienceId >= DrivingExperienceEnum.ONE_TO_TWO.id) {
            return true
        }

        return false
    }
}
