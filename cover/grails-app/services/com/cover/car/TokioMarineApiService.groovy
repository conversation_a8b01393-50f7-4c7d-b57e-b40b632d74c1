package com.cover.car

import com.cover.car.commands.RateCommand
import com.cover.car.tokio.TokioQuoteCommand
import com.cover.car.tokio.TokioRateCommand
import com.cover.common.CommonQuoteService
import com.safeguard.*
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONElement
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod

import javax.annotation.PostConstruct

/**
 * This service handles TOKIO APIs
 *
 * <AUTHOR> Abdullah
 * @version 1.0
 * @since 2023-10-06
 */
@Transactional
class TokioMarineApiService extends ProviderRatingService {

    def grailsApplication
    def providerApiService
    def providerTokenService
    def ratingService

    private static final Integer SUCCESS_RESPONSE_STATUS = 1
    private static final Integer UNAUTHORIZED_RESPONSE_STATUS = 401
    private static final String POL_DEPT_CODE = "1010"
    private static final String POL_DIVN_CODE = "100"
    private static final String POL_ASSR_TYPE = "101"
    private static final String POL_LOB_CODE = "10"
    private static final String POL_PREM_CURR_CODE = "101"
    private static final String POL_SCH_CODE = "101"
    private static final String POL_SI_CURR_CODE = "101"
    private static final String VEH_USAGE = "1001"

    private static final String LOGIN_API = "/Api/Auth/Login"
    private static final String CREATE_QUOTE_API = "/Api/Motor/CreateQuote"
    private static final String SAVE_QUOTE_WITH_PLAN_API = "/Api/Motor/SaveQuoteWithPlan"

    private String host
    private String username
    private String password
    private String loginMode
    private String polPartyCode

    @PostConstruct
    private void init() {
        host = grailsApplication.config.getProperty("tokio.auth.host")
        username = grailsApplication.config.getProperty("tokio.auth.username")
        password = grailsApplication.config.getProperty("tokio.auth.password")
        loginMode = grailsApplication.config.getProperty("tokio.auth.loginMode")
        polPartyCode = grailsApplication.config.getProperty("tokio.auth.polPartyCode")
    }


    /**
     * TOKIO API 1: Api/Auth/Login
     * This method call login API to fetch authentication token
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @return List<TokioRateCommand> [return rates as TokioRateCommand]
     */
    private String callTokenApi(Long quoteId) {
        String authToken = null
        String url = host + LOGIN_API
        Map<String, String> request = buildLoginRequest()
        List<Map<String, String>> headers = [["provider": "TMI"]]
        headers.add(getEncryptedQuoteIdHeader(quoteId))
        log.info("TokioMarineApiService.callTokenApi - url: ${url}, headers: ${headers}, request: ${request}")

        JSONElement resp
        try {
            resp = providerApiService.callApi(url, headers, request, HttpMethod.POST)
            log.info("TokioMarineApiService.callTokenApi - url: ${url}, response: ${resp}")

            if (resp?.Status == SUCCESS_RESPONSE_STATUS && resp?.Data?.trim()) {
                String token = resp?.Data
                providerTokenService.saveProviderToken(ExternalDataSourceEnum.TOKIO, token)
                log.info("TokioMarineApiService.callTokenApi - Saved token successfully")

                authToken = providerTokenService.getToken(ExternalDataSourceEnum.TOKIO)
            } else {
                log.error("TokioMarineApiService.callTokenApi - quoteId: ${quoteId}, failedResponse: ${resp}")
            }
        } catch (Exception e) {
            log.error("TokioMarineApiService.callTokenApi - quoteId: ${quoteId}, exception: ${e.printStackTrace()}")
        }

        return authToken
    }


    /**
     * TOKIO API 2: Api/Motor/CreateQuote
     * This method call create quote API to fetch rates based on tokio criteria
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param tokioQuoteCommand [TOKIO Criteria of fetching quotes]
     * @return List<TokioRateCommand> [return rates as TokioRateCommand]
     */
    List<TokioRateCommand> callCreateQuoteAPI(TokioQuoteCommand tokioQuoteCommand) {
        List<TokioRateCommand> tokioRateCommands = []

        // We have used ExternalDataSourceEnum.TOKIO only for authentication tokens; for mapping, we have used ExternalDataSourceEnum.EDATA
        String authenticationToken = providerTokenService.getToken(ExternalDataSourceEnum.TOKIO) ?: callTokenApi(tokioQuoteCommand.quoteId)
        log.info("TokioMarineApiService.callCreateQuoteAPI - authenticationToken: ${authenticationToken}")
        if (!authenticationToken) {
            return tokioRateCommands
        }

        String url = host + CREATE_QUOTE_API
        Map request = buildCreateQuoteRequest(tokioQuoteCommand)
        List<Map<String, String>> headers = getAllHeaders(authenticationToken, tokioQuoteCommand.quoteId)
        log.info("TokioMarineApiService.callCreateQuoteAPI - url: ${url}, headers: ${headers}, request: ${request}")

        JSONElement resp
        try {
            resp = providerApiService.callApi(url, headers, request, HttpMethod.POST)
            log.info("TokioMarineApiService.callCreateQuoteAPI - url: ${url}, response: ${resp}")

            if (resp?.status == UNAUTHORIZED_RESPONSE_STATUS) {
                authenticationToken = callTokenApi(tokioQuoteCommand.quoteId)
                log.info("TokioMarineApiService.callCreateQuoteAPI - new authentication token: ${authenticationToken}")

                headers = getAllHeaders(authenticationToken, tokioQuoteCommand.quoteId)
                log.info("TokioMarineApiService.callCreateQuoteAPI - again calling - url: ${url}, headers: ${headers}, request: ${request}")

                resp = providerApiService.callApi(url, headers, request, HttpMethod.POST)
                log.info("TokioMarineApiService.callCreateQuoteAPI - again received - url: ${url}, response: ${resp}")
            }

            if (resp?.Status == SUCCESS_RESPONSE_STATUS && resp?.Data?.PlanDetails) {
                // The response will return Comprehensive and TPL plans
                JSONArray plans = resp?.Data?.PlanDetails
                log.info("TokioMarineApiService.callCreateQuoteAPI - TotalPlans: ${plans.length()}")
                for (plan in plans) {
                    plan["providerReference"] = resp?.Data?.ReferenceNo
                    log.info("TokioMarineApiService.callCreateQuoteAPI - planName: ${plan?.Name}, planCode: ${plan?.Code}, " +
                        "planPremiumExVat: ${plan?.PremiumExVat}, plan: ${plan}")

                    CarQuoteCover carQuoteCover = savePlanAsCarQuoteCover(resp?.Data?.SchemeCode as String, plan as JSONElement, tokioQuoteCommand)
                    log.info("TokioMarineApiService.callCreateQuoteAPI - carQuoteCoverId: ${carQuoteCover.id}")

                    TokioRateCommand tokioRateCommand = TokioRateCommand.generateTokioRateCommand(carQuoteCover)

                    // this TOKIO API validate the CreateQuoteRequest
                    //if the trim is not valid, the api will throw error like
                    //Ex: "This trim is not belongs to this vehicle, Available Trims are ELITE,EX,EX-L,LX,TOURING"
                    String providerQuoteNumber = callSaveQuoteWithPlanAPI([], tokioRateCommand, tokioQuoteCommand)
                    if (!providerQuoteNumber.isEmpty()) {
                        tokioRateCommands.add(tokioRateCommand)
                    }
                }
            } else {
                log.error("TokioMarineApiService.callCreateQuoteAPI - quoteId: ${tokioQuoteCommand.quoteId}, failedResponse: ${resp}")
            }
        } catch (Exception e) {
            log.error("TokioMarineApiService.callCreateQuoteAPI - quoteId: ${tokioQuoteCommand.quoteId}, exception: ${e.printStackTrace()}")
        }

        return tokioRateCommands
    }

    /**
     * TOKIO API 3: Api/Motor/SaveQuoteWithPlan
     * This method call save quote with plan API to save covers/addons against a quote
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param selectedDynamicAddons [selected addons]
     * @param tokioRateCommand [plan details]
     * @param tokioQuoteCommand [TOKIO Criteria of fetching quotes]
     * @return void
     */
    String callSaveQuoteWithPlanAPI(List selectedDynamicAddons, TokioRateCommand tokioRateCommand,
                                    TokioQuoteCommand tokioQuoteCommand) {

        String providerQuoteNumber = null

        // We have used ExternalDataSourceEnum.TOKIO only for authentication tokens; for mapping, we have used ExternalDataSourceEnum.EDATA
        String authenticationToken = providerTokenService.getToken(ExternalDataSourceEnum.TOKIO) ?: callTokenApi(tokioQuoteCommand.quoteId)
        log.info("TokioMarineApiService.callSaveQuoteWithPlanAPI - authenticationToken: ${authenticationToken}")
        if (!authenticationToken) {
            return providerQuoteNumber
        }

        String url = host + SAVE_QUOTE_WITH_PLAN_API
        Map request = buildSaveQuoteWithPlanRequest(selectedDynamicAddons, tokioRateCommand)
        List<Map<String, String>> headers = getAllHeaders(authenticationToken, tokioQuoteCommand.quoteId)
        log.info("TokioMarineApiService.callSaveQuoteWithPlanAPI - url: ${url}, headers: ${headers}, request: ${request}")

        JSONElement resp
        try {
            resp = providerApiService.callApi(url, headers, request, HttpMethod.POST)
            log.info("TokioMarineApiService.callSaveQuoteWithPlanAPI - url: ${url}, response: ${resp}")

            if (resp?.status == UNAUTHORIZED_RESPONSE_STATUS) {
                authenticationToken = callTokenApi(tokioQuoteCommand.quoteId)
                log.info("TokioMarineApiService.callSaveQuoteWithPlanAPI - new authentication token: ${authenticationToken}")

                headers = getAllHeaders(authenticationToken, tokioQuoteCommand.quoteId)
                log.info("TokioMarineApiService.callSaveQuoteWithPlanAPI - again calling - url: ${url}, headers: ${headers}, request: ${request}")

                resp = providerApiService.callApi(url, headers, request, HttpMethod.POST)
                log.info("TokioMarineApiService.callSaveQuoteWithPlanAPI - again received - url: ${url}, response: ${resp}")
            }

            if (resp?.Status == SUCCESS_RESPONSE_STATUS && resp?.Data?.trim()) {
                // The response will return Comprehensive and TPL plans
                providerQuoteNumber = resp?.Data
                log.info("TokioMarineApiService.callSaveQuoteWithPlanAPI - providerQuoteNumber: ${providerQuoteNumber}")

                updateProviderQuoteNumber(providerQuoteNumber, tokioQuoteCommand, tokioRateCommand)

            } else {
                log.error("TokioMarineApiService.callSaveQuoteWithPlanAPI - quoteId: ${tokioQuoteCommand.quoteId}, failedResponse: ${resp}")
            }
        } catch (Exception e) {
            log.error("TokioMarineApiService.callSaveQuoteWithPlanAPI - quoteId: ${tokioQuoteCommand.quoteId}, exception: ${e.printStackTrace()}")
        }

        return providerQuoteNumber
    }


    /**
     * This method build login request
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @return Map<String, String> [return login request map]
     */
    private Map<String, String> buildLoginRequest() {
        Map<String, String> request = new HashMap<>()
        request.put('username', username)
        request.put('password', password)
        request.put('loginMode', loginMode)

        return request
    }


    /**
     * This method build create quote request
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param tokioQuoteCommand [TOKIO Criteria of fetching quotes]
     * @return Map<String, String> [return create quote request map]
     */
    private Map buildCreateQuoteRequest(TokioQuoteCommand tokioQuoteCommand) {
        Integer ncdYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(tokioQuoteCommand)

        Map request = new HashMap()
        request.put("PolPartyCode", polPartyCode)
        request.put("PolDeptCode", POL_DEPT_CODE)
        request.put("PolDivnCode", POL_DIVN_CODE)
        request.put("PolAssrType", POL_ASSR_TYPE)
        request.put("PolLobCode", POL_LOB_CODE)
        request.put("PolPremCurrCode", POL_PREM_CURR_CODE)
        request.put("PolSchCode", POL_SCH_CODE)
        request.put("PolSiCurrCode", POL_SI_CURR_CODE)
        request.put("PolAssrDob", tokioQuoteCommand.dob.toString("MM/dd/yyyy"))
        request.put("PolAssrName", tokioQuoteCommand.getName())
        request.put("VehNoSeats", tokioQuoteCommand.vehNoSeats)
        request.put("VehNoSeatsDrvr", tokioQuoteCommand.vehNoSeatsDrvr)
        request.put("VehNoSeatsPass", tokioQuoteCommand.vehNoSeatsPass)
        request.put("VehMake", tokioQuoteCommand.vehicleMakeCode)
        request.put("VehModel", tokioQuoteCommand.vehicleModelCode)
        request.put("VehBodyType", tokioQuoteCommand.vehicleBodyTypeCode)
        request.put("VehMfgYear", tokioQuoteCommand.manufactureYear)
        request.put("VehBrandNewYn", tokioQuoteCommand.isBrandNew ? "Y" : "N")
        request.put("VehRegnDt", tokioQuoteCommand.firstRegistrationDate.toString("MM/dd/yyyy"))
        request.put("VehUsage", VEH_USAGE)
        request.put("VehNoCylinder", String.format("%02d", tokioQuoteCommand.noOfCyl))
        request.put("VehFcValue", tokioQuoteCommand.insuredValue)
        request.put("VehCc", tokioQuoteCommand.vehCc)
        request.put("VehDriverDob", tokioQuoteCommand.dob.toString("MM/dd/yyyy"))
        request.put("VehNcdYears", ncdYears > 3 ? 3 : ncdYears)
        request.put("VehPrevInsType", tokioQuoteCommand.isThirdParty ? "TPL" : "COMP")
        request.put("VehRegion", tokioQuoteCommand.isNonGccSpec ? "Non-GCC" : "GCC")
        request.put("VehTrim", tokioQuoteCommand.vehTrim)
        request.put("VehModifiedYn", tokioQuoteCommand.isNonGccSpec ? "Y" : "N")
        request.put("PolAssrVehLicIssDt", CommonQuoteService.getDrivingLicenseIssueDate(tokioQuoteCommand.localDrivingExperienceId).toString("MM/dd/yyyy"))
        request.put("PolPrevRenewalNote", "Y")
        request.put("VehPlateSource", tokioQuoteCommand.vehPlateSourceCode)
        request.put("PolAssrNation", tokioQuoteCommand.nationalityCode)

        return request
    }


    /**
     * This method build save quote with plan request
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param selectedDynamicAddons [selected addons]
     * @param tokioRateCommand [plan details]
     * @return Map [return save quote with plan request map]
     */
    private Map buildSaveQuoteWithPlanRequest(List selectedDynamicAddons, TokioRateCommand tokioRateCommand) {
        List allCovers = []
        allCovers.addAll(tokioRateCommand.covers)
        allCovers.addAll(tokioRateCommand.discountCovers)
        allCovers.addAll(tokioRateCommand.loadingCovers)
        allCovers.addAll(tokioRateCommand.excessCovers)
        allCovers.addAll(tokioRateCommand.optionalCovers.findAll { it.premium == 0 })

        List coversWithoutAddons = allCovers.
            collect {
                [
                    Code       : it.code,
                    CoverPremFc: it.premium,
                    CoverPremLc: it.premium
                ]
            }
        log.info("TokioMarineApiService.buildSaveQuoteWithPlanRequest - coversWithoutAddonsSize: " +
            "${coversWithoutAddons.size()}, coversWithoutAddons: ${coversWithoutAddons.toString()}")

        List addons = selectedDynamicAddons
            .collect {
                [
                    Code       : it.providerCode,
                    CoverPremFc: it.price,
                    CoverPremLc: it.price
                ]
            }
        log.info("TokioMarineApiService.buildSaveQuoteWithPlanRequest - addonsSize: " +
            "${addons.size()}, addons: ${addons.toString()}")

        List coversWithAddons = coversWithoutAddons + addons
        log.info("TokioMarineApiService.buildSaveQuoteWithPlanRequest - coversWithAddonsSize: " +
            "${coversWithAddons.size()}, coversWithAddons: ${coversWithAddons.toString()}")

        Map request = new HashMap()
        request.put("ReferenceNo", tokioRateCommand.referenceNo)
        request.put("SchemeCode", tokioRateCommand.schemeCode)
        request.put("ProductCode", tokioRateCommand.productCode)
        request.put("SelectedCovers", coversWithAddons)

        return request
    }


    /**
     * This method convert TokioRateCommand and TokioQuoteCommand to RateCommand
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param tokioRateCommand [plan details]
     * @param tokioQuoteCommand [TOKIO Criteria of fetching quotes]
     * @return RateCommand [return rate as rate command]
     */
    RateCommand toRateCommand(TokioRateCommand tokioRateCommand, TokioQuoteCommand tokioQuoteCommand) {
        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = tokioQuoteCommand.insuredValue
        Integer productId = TokioMarineRateV2Service.getProductId(tokioRateCommand.productCode)
        if (!productId) {
            log.error("TokioMarineApiService.toRateCommand - new productCode: ${tokioRateCommand.productCode} is found " +
                "against quoteId: ${tokioQuoteCommand.quoteId}")
            return null
        }

        rateCommand.productId = productId
        rateCommand.providerId = TokioMarineRateV2Service.PROVIDER_ID
        rateCommand.basePremium = tokioRateCommand.netPremium
        rateCommand.premium = rateCommand.basePremium
        rateCommand.minPremium = rateCommand.premium
        rateCommand.currency = tokioQuoteCommand.currency
        rateCommand.excess = tokioRateCommand.excess
        rateCommand.agencyRepair = TokioMarineRateV2Service.checkAgency(tokioRateCommand.productCode)

        // map static covers from product_basic_cover_translation, product_road_side_assistance and product_special_feature from DB
        rateCommand = ratingService.applyCovers(tokioQuoteCommand, rateCommand)

        // map dynamic addons for checkout page
        rateCommand.dynamicAddons = tokioRateCommand.optionalCovers.
            findAll { it.premium > 0 }.
            collect {
                [
                    label       : it.description,
                    price       : new BigDecimal(it.premium),
                    providerCode: it.code,
                    code        : AddonCodeEnum.DYNAMIC_ADDON.code,
                    description : it.description
                ]
            }

        // map dynamic covers from API
        List allCovers = []
        allCovers.addAll(tokioRateCommand.covers)
        allCovers.addAll(tokioRateCommand.discountCovers)
        allCovers.addAll(tokioRateCommand.loadingCovers)
        allCovers.addAll(tokioRateCommand.optionalCovers)
        allCovers.addAll(tokioRateCommand.excessCovers)

        TokioRateCommand.TokioRateCoverCommand RSA_SILVER = allCovers.find {
            it.code == CarCoversEnum.RSA_SILVER.tokioCode
        } as TokioRateCommand.TokioRateCoverCommand
        rateCommand.breakdownCover = RSA_SILVER ? (RSA_SILVER.coverFlag == "OC" && RSA_SILVER.premium > 0 ?
            RSA_SILVER.premium : CarCoversEnum.findByTokioCode(RSA_SILVER.code).shortName) : "none"

        TokioRateCommand.TokioRateCoverCommand pax = allCovers.find {
            it.code == CarCoversEnum.PAB_PASSENGER.tokioCode
        } as TokioRateCommand.TokioRateCoverCommand
        rateCommand.personalAccidentPax = pax ? (pax.coverFlag == "OC" && pax.premium > 0 ? pax.premium : "yes") : "no"

        TokioRateCommand.TokioRateCoverCommand natural_calamity = allCovers.find {
            it.code == CarCoversEnum.NATURAL_CALAMITY.tokioCode
        } as TokioRateCommand.TokioRateCoverCommand
        rateCommand.naturalCalamity = natural_calamity ? (natural_calamity.coverFlag == "OC" &&
            natural_calamity.premium > 0 ? natural_calamity.premium : "yes") : "no"

        TokioRateCommand.TokioRateCoverCommand pad = allCovers.find {
            it.code == CarCoversEnum.PAB_DRIVER.tokioCode
        } as TokioRateCommand.TokioRateCoverCommand
        rateCommand.paCover = pad ? (pad.coverFlag == "OC" && pad.premium > 0 ? pad.premium : "yes") : "no"

        TokioRateCommand.TokioRateCoverCommand rac = allCovers.find {
            it.code == CarCoversEnum.REPLACE_CAR.tokioCode
        } as TokioRateCommand.TokioRateCoverCommand
        rateCommand.replacementCar = rac ? (rac.coverFlag == "OC" && rac.premium > 0 ? rac.premium : "yes") : "no"

        TokioRateCommand.TokioRateCoverCommand windScreenCover = allCovers.find {
            it.code == CarCoversEnum.WIND_SCREEN.tokioCode
        } as TokioRateCommand.TokioRateCoverCommand
        rateCommand.windScreenCover = windScreenCover ? (windScreenCover.coverFlag == "OC" &&
            windScreenCover.premium > 0 ? windScreenCover.premium : "yes") : "no"

        return rateCommand
    }


    /**
     * Get all headers to add in API request
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param authenticationToken [authentication token]
     * @param carQuoteId [Long]
     * @return List<Map<String, String>> [all headers]
     */
    static List<Map<String, String>> getAllHeaders(String authenticationToken, Long carQuoteId) {
        List<Map<String, String>> headers = [["provider": "TMI"]]
        headers.add(getBearerTokenHeader(authenticationToken))
        headers.add(getEncryptedQuoteIdHeader(carQuoteId))
        return headers
    }


    /**
     * Get authorization header to add in API request
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param authenticationToken [authentication token]
     * @return Map<String, String> [authorization header]
     */
    static Map<String, String> getBearerTokenHeader(String token) {
        [
            Authorization: "Bearer ${token}"
        ]
    }


    /**
     * Get encrypted quote id header to add in API request
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param carQuoteId [Long]
     * @return Map<String, String> [encrypted quote id header]
     */
    static Map<String, String> getEncryptedQuoteIdHeader(Long carQuoteId) {
        String encQuoteId = AESCryption.encrypt(carQuoteId + "")
        [
            encQuoteId: encQuoteId
        ]
    }


    /**
     * This method will save tokio plan in CarQuoteCover table
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param referenceNo [tokio reference number]
     * @param schemeCode [tokio scheme code]
     * @param plan [tokio plan details from API]
     * @param tokioQuoteCommand [TOKIO Criteria of fetching quotes]
     * @return CarQuoteCover [plan details]
     */
    private static CarQuoteCover savePlanAsCarQuoteCover(String schemeCode, JSONElement plan, TokioQuoteCommand tokioQuoteCommand) {
        CarQuoteCover carQuoteCover = new CarQuoteCover()
        carQuoteCover.quote = CarQuote.load(tokioQuoteCommand.quoteId)
        carQuoteCover.provider = Provider.load(tokioQuoteCommand.providerId)
        carQuoteCover.covers = plan
        carQuoteCover.expiryDate = LocalDateTime.now().withTime(23, 59, 59, 999)
        carQuoteCover.providerQuoteNo = null
        carQuoteCover.schemeCode = schemeCode
        carQuoteCover.insuredValue = (Integer) tokioQuoteCommand.insuredValue
        carQuoteCover.netPremium = new BigDecimal(plan.PremiumExVat as String)
        carQuoteCover.coverageType = CoverageType.load(plan.Code == TokioMarineRateV2Service.TPL_PRODUCT_CODE ?
            CoverageTypeEnum.THIRD_PARTY.value() : CoverageTypeEnum.COMPREHENSIVE.value())
        carQuoteCover.repairType = plan.Code == TokioMarineRateV2Service.COMPREHENSIVE_AGENCY_PRODUCT_CODE ? RepairTypeEnum.AGENCY :
            plan.Code == TokioMarineRateV2Service.COMPREHENSIVE_GARAGE_PRODUCT_CODE ? RepairTypeEnum.GARAGE : null

        carQuoteCover.save(failOnError: true)

        return carQuoteCover
    }


    /**
     * This method will update provider quote number in CarQuoteCover table
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param quoteNumber [tokio quote number]
     * @param tokioQuoteCommand [TOKIO Criteria of fetching quotes]
     * @param tokioRateCommand [plan details]
     */
    private static void updateProviderQuoteNumber(String quoteNumber, TokioQuoteCommand tokioQuoteCommand,
                                                  TokioRateCommand tokioRateCommand) {
        CarQuote quote = CarQuote.load(tokioQuoteCommand.quoteId)
        Integer productId = tokioQuoteCommand.productId ?: TokioMarineRateV2Service.getProductId(tokioRateCommand.productCode)
        CoverageType coverageType = CoverageType.load(productId == TokioMarineRateV2Service.TPL_PRODUCT_ID ?
            CoverageTypeEnum.THIRD_PARTY.value() : CoverageTypeEnum.COMPREHENSIVE.value())
        RepairTypeEnum repairType = productId == TokioMarineRateV2Service.COMPREHENSIVE_AGENCY_PRODUCT_ID ?
            RepairTypeEnum.AGENCY : productId == TokioMarineRateV2Service.COMPREHENSIVE_GARAGE_PRODUCT_ID ?
            RepairTypeEnum.GARAGE : null

        CarQuoteCover carQuoteCover = CarQuoteCover
            .findByIsDeletedAndQuoteAndProviderAndCoverageTypeAndRepairTypeAndExpiryDateGreaterThan(
                false, quote, Provider.load(TokioMarineRateV2Service.PROVIDER_ID), coverageType,
                repairType, LocalDateTime.now())

        carQuoteCover.setProviderQuoteNo(quoteNumber)
        carQuoteCover.save(failOnError: true)
    }

}


