package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CityEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Ratings calculation for Gateway Insurance.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class GatewayRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 5
    public static final Integer PRODUCT_PREMIUM = 37

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            quoteCommand.carCategory = null
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ProductTplRate.
                    findApplicableRate(PROVIDER_ID, model.vehicleTypeId, model.noOfCyl, quoteCommand.customerAge,
                        isOffline).list()

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        // if car age is 1 then no need to apply agency rate
        rateCommand.premium = rateCommand.agencyRepair ?
            ratingService.calculate(applicableRate.baseRateAgency, quoteCommand.insuredValue) :
            ratingService.calculate(applicableRate.baseRateGarage, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        int carAge = quoteCommand.carAge
        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (carAge == 1) {
                // if car age is 1 then agency for all products
                isAgency = true

            } else if (carAge == 2 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value() &&
                quoteCommand.isOldAgency) {
                // if car age is 2 and at-least 1 years NCD
                isAgency = true

            } else if (carAge == 3 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value() &&
                applicableRate.productId == PRODUCT_PREMIUM &&
                quoteCommand.isOldAgency) {
                isAgency = true
            }
        }

        isAgency
    }

    RateCommand applyAdditionalFees(RateCommand rateCommand, QuoteCommand quoteCommand) {

        if (rateCommand.coverageTypeId == CoverageTypeEnum.THIRD_PARTY.value()) {
            // additional fee 50 AED ambulance fee
            rateCommand.premium = rateCommand.premium.add(50)
        } else {
            // additional fee 50 AED ambulance fee, 50 AED policy fee, 50 AED break down cover
            rateCommand.premium = rateCommand.premium.add(50).add(50).add(50)
        }

        // if registration city not dubai then additional 30 aed for "Emirates vehicle gate"
        if (quoteCommand.registrationCityId != CityEnum.DUBAI.value()) {
            rateCommand.premium = rateCommand.premium.add(30)
        }

        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        if (productTypeEnum == CoverageTypeEnum.THIRD_PARTY) {

            if (quoteCommand.customerAge > 25) {
                isEligible = false
            }

        } else {
            /*
                not eligible if
                - current policy is third party
            */
            if (quoteCommand.isThirdParty) {
                isEligible = false
            }

            // either uae or international experience should be 1 year
            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId() &&
                quoteCommand.internationalDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                isEligible = false
            }
        }

        isEligible
    }
}
