package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.BodyTypeEnum
import com.safeguard.CarMakeEnum
import com.safeguard.City
import com.safeguard.CityEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

@Transactional(readOnly = true)
class AiawRateService {

    def commonUtilService
    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 24
    public static final Integer PRODUCT_FIRST_CLASS = 5196
    public static final Integer PRODUCT_GARAGE = 5197

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("getRates- quoteCommand - ${quoteCommand} - isOffline - ${isOffline}")
        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        quoteCommand.carCategory = null
        boolean checkEligibility = checkEligibility(quoteCommand)
        log.info("getRates - checkEligibility:${checkEligibility}")
        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.nationalityCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            log.info("getRates - applicableRates:${applicableRates}")

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    if (!rateCommand.isAgencyRepair() && quoteCommand.carAge <= 1 && rateCommand.productId == PRODUCT_FIRST_CLASS) {
                        //No Non Agency allowed for 1st year car
                    } else {
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("getRate - ${quoteCommand} - isOffline - ${isOffline}")
        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        quoteCommand.carCategory = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.nationalityCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            log.info("Rates - ${applicableRates.size()}")
            if (applicableRates) {
                rateCommand = populateRatings(quoteCommand, applicableRates.first())
            }

            if (!rateCommand.isAgencyRepair() && quoteCommand.carAge <= 1 && rateCommand.productId == PRODUCT_FIRST_CLASS) {
                //No Non Agency allowed for 1st year car
               return null
            }

        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)

        //rateCommand = applyDiscounts(quoteCommand, rateCommand)
        //rateCommand = applyLoadings(quoteCommand, rateCommand)
        log.info("populateRatings - rate.premium after loading:${rateCommand.premium}")

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, false)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        Model model = Model.get(quoteCommand.modelId)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = checkAgency(product, quoteCommand)
        rateCommand.productId = product.id

        BigDecimal baseRate = calculateBaseRate(quoteCommand, applicableRate, rateCommand.agencyRepair)
        rateCommand.baseRate = baseRate

        BigDecimal premiumBeforeDiscount = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.premium = premiumBeforeDiscount
        rateCommand.premium = applyPremiumDiscount(quoteCommand, premiumBeforeDiscount)
        rateCommand.premium = rateCommand.premium.setScale(0, BigDecimal.ROUND_HALF_UP) //Rounding off

        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        if (model.bodyType == BodyTypeEnum.HATCHBACK) {
            rateCommand.minPremium = rateCommand.agencyRepair ? 2640 : (applicableRate.productId == PRODUCT_FIRST_CLASS ? 1650 : 1540)
        }
        rateCommand.basePremium = rateCommand.premium
        log.info("rateCommand.minPremium:${rateCommand.minPremium}")
        rateCommand.minPremium = applyMinPremiumDiscount(quoteCommand, rateCommand.minPremium)
        log.info("after discount rateCommand.minPremium:${rateCommand.minPremium}")

        if (rateCommand.premium < rateCommand.minPremium) {
            rateCommand.premium = rateCommand.minPremium
        }

        rateCommand = applyLoading(quoteCommand, rateCommand)
        rateCommand
    }

    private BigDecimal calculateBaseRate(QuoteCommand quoteCommand, ProductBaseRate applicableRate, boolean isAgencyRepair) {

        Model model = Model.get(quoteCommand.modelId)

        City city = City.get(quoteCommand.registrationCityId)
        Country country = Country.get(quoteCommand.nationalityId)

        Float ageFactor = getAgeFactor(quoteCommand.customerAge)
        Float licenseFactor = getDrivingLicenseFactor(quoteCommand.localDrivingExperienceId)
        Float nationalityFactor = country.aiawFactor
        Float cityFactor = getEmirateFactor(quoteCommand.registrationCityId)
        Float makeFactor = model.make.aiawFactor
        Float vehicleBodyFactor = getBodyTypeFactor(quoteCommand.vechileTypeId)
        Float modelYearFactor = getModelYearFactor(quoteCommand.manufactureYear)
        Float repairConditionFactor = getRepairConditionFactor(applicableRate.productId, isAgencyRepair)
        Float ownerShipFactor = 1.00 //individual, 1.05 for Company
        Float sumInsuredRangeFactor = getSumInsuredRangeFactor(quoteCommand.insuredValue.toInteger())
        log.info("isAgencyRepair:$isAgencyRepair, ageFactor:$ageFactor, licenseFactor:$licenseFactor, nationalityFactor:$nationalityFactor, " +
            "cityFactor:$cityFactor, makeFactor:$makeFactor, vehicleBodyFactor:$vehicleBodyFactor, " +
            "modelYearFactor:$modelYearFactor, repairConditionFactor:$repairConditionFactor, " +
            "ownerShipFactor:$ownerShipFactor, sumInsuredRangeFactor:$sumInsuredRangeFactor")

        BigDecimal baseRate =  3.4056 * ageFactor * licenseFactor * nationalityFactor * cityFactor *
            makeFactor * vehicleBodyFactor * modelYearFactor * repairConditionFactor * ownerShipFactor *
            sumInsuredRangeFactor
        baseRate = baseRate.setScale(5, BigDecimal.ROUND_HALF_UP)
        log.info("New baseRate: $baseRate")

        BigDecimal minBaseRate =  isAgencyRepair ? applicableRate.baseRateAgency.floatValue() : applicableRate.baseRateGarage.floatValue()
        if (model.bodyType == BodyTypeEnum.HATCHBACK) {
            minBaseRate = isAgencyRepair ? 1.60 : (applicableRate.productId == PRODUCT_FIRST_CLASS ? 1.50 : 1.40)
        }
        log.info("minBaseRate: $minBaseRate")

        BigDecimal maxBaseRate = getMaxBaseRate(quoteCommand.vechileTypeId)
        log.info("maxBaseRate: $maxBaseRate")

        baseRate = baseRate < minBaseRate ? minBaseRate : baseRate
        baseRate = baseRate > maxBaseRate ? maxBaseRate : baseRate
        baseRate = baseRate.setScale(5, BigDecimal.ROUND_HALF_UP)
        log.info("final baseRate: $baseRate")

        baseRate
    }

    private BigDecimal applyMinPremiumDiscount(QuoteCommand quoteCommand, BigDecimal minimumPremium) {
        Model model = Model.load(quoteCommand.modelId)
        //No Discount on chinese make
        if (model.make.countryId == CountryEnum.CHINA.id) return minimumPremium

        return minimumPremium.subtract(minimumPremium * 0.05) //5% discount now on min premium
    }

    private BigDecimal applyPremiumDiscount(QuoteCommand quoteCommand, BigDecimal premium) {
        Model model = Model.load(quoteCommand.modelId)
        //No Discount on chinese make
        if (model.make.countryId == CountryEnum.CHINA.id) return premium

        return premium.subtract(premium * 0.10) //10% discount now on premium
    }

    private RateCommand applyLoading(QuoteCommand quoteCommand, RateCommand rateCommand) {
        Model model = Model.load(quoteCommand.modelId)

        if (model.make.countryId == CountryEnum.CHINA.id) {
            if (rateCommand.premium <= rateCommand.minPremium) {
                //10% Loading on min premium
                rateCommand.minPremium = rateCommand.minPremium.add(rateCommand.minPremium * 0.10)
                rateCommand.premium = rateCommand.minPremium
            } else {
                //20% Loading on  premium
                rateCommand.premium = rateCommand.premium.add(rateCommand.premium * 0.20)
            }
        }
        return rateCommand
    }

    private boolean checkAgency(Product product, QuoteCommand quoteCommand) {

        boolean isAgency = false

        int carAge = quoteCommand.carAge

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (!quoteCommand.isThirdParty) {

                if (quoteCommand.isBrandNew || carAge <= 1) {
                    isAgency = true
                }
/*                if (carAge == 1 && !quoteCommand.hasClaim) {
                    isAgency = true
                }*/
                if (carAge == 2 && !quoteCommand.hasClaim && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
                    isAgency = true
                }

            }
        }

        isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)

        if (quoteCommand.isNonGccSpec) {
            return false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.isThirdParty) {
            return false
        }

        /*if (quoteCommand.customerAge < 25 || quoteCommand.hasClaim) {
            return false
        }*/

        Country nationality = Country.read(quoteCommand.nationalityId)
        if (nationality.isArabic) {
            return false
        }

        Make make = Make.load(quoteCommand.makeId)
        if (make.countryId.longValue() == CountryEnum.CHINA.id) {
            return false
        }
        if (!(make.id in [123, //ALFA ROMEO
                            124, //ASTON MARTIN
                            125, //AUDI
                            126, //BENTLEY
                            127, //BMW
                            128, //CADILLAC
                            129, //CHEVROLET
                            130, //CHRYSLER
                            131, //DAIHATSU
                            132, //DODGE
                            133, //FERRARI
                            134, //FORD
                            135, //GMC
                            136, //HONDA
                            137, //HUMMER
                            138, //HYUNDAI
                            139, //INFINITI
                            141, //JAGUAR
                            142, //JEEP
                            143, //KIA
                            144, //LAMBORGHINI
                            145, //LANDROVER
                            146, //LEXUS
                            147, //LINCOLN
                            148, //LOTUS
                            149, //MASERATI
                            151, //MAZDA
                            152, //MERCEDES
                            154, //MINI
                            155, //MITSUBISHI
                            156, //NISSAN
                            157, //OPEL
                            158, //PEUGEOT
                            160, //PORSCHE
                            161, //RANGE ROVER
                            162, //RENAULT
                            163, //ROLLS ROYCE
                            164, //SAAB
                            165, //SEAT
                            166, //SKODA
                            168, //SUBARU
                            169, //SUZUKI
                            170, //TOYOTA
                            171, //VOLKSWAGEN
                            172, //VOLVO
                            173, //MCLAREN
                            174, //CITROEN
                            175, //FIAT
                            180 //TESLA
                            ])) {
            isEligible = false
        }

        // not eligible if uae driving experience is less then 1 Year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }

        isEligible
    }

    private RateCommand applyBaseRate(RateCommand rateCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        rateCommand.baseRate = baseRate
        return rateCommand
    }

    private float getEmirateFactor(Integer registrationCityId) {
        switch (registrationCityId) {
            case CityEnum.ABU_DHABI.value():
                return 1.05
                break
            case CityEnum.AL_AIN.value():
                return 1.05
                break
            case CityEnum.AJMAN.value():
                return 1.10
                break
            case CityEnum.UAQ.value():
                return 1.05
                break
            case CityEnum.DUBAI.value():
                return 1.00
                break
            case CityEnum.FUJ.value():
                return 0.95
                break
            case CityEnum.RAK.value():
                return 0.95
                break
            case CityEnum.SHARJAH.value():
                return 1.05
                break
        }

        return 1.00
    }

    private float getAgeFactor(int age) {
        if (age == 25) {
            return 1.5
        } else if (age == 26) {
            return 1.4
        } else if (age in [27, 28, 29]) {
            return 1.3
        } else if (age in [30, 31, 32, 33, 34]) {
            return 1.2
        } else if (age in [35, 36, 37, 38, 39] ||
                    age in [65, 66, 67, 68, 69, 70]) {
            return 1.1
        } else if (age in [40, 41, 42, 43, 44] ||
                    age in [50, 51, 52, 54, 54]) {
            return 1.00
        } else if (age in [45, 46, 47, 48, 49] ||
                    age in [55, 56, 57, 59, 60, 61, 62, 63, 64]) {
            return 0.95
        }
        return 1.00
    }

    private float getModelYearFactor(int year) {
        if (year >= 2024) {
            return 1.10
        } else if (year == 2023) {
            return 1.15
        } else if (year in [2020, 2021, 2022]) {
            return 1.10
        } else if (year == 2019) {
            return 1.00
        } else if (year == 2018) {
            return 1.125
        } else if (year == 2017) {
            return 0.95
        } else if (year == 2016) {
            return 1.15
        } else if (year == 2015) {
            return 1.15
        }  else if (year <= 2014) {
            return 1.00
        }

        return 1.00
    }

    private float getRepairConditionFactor(Integer productId, boolean isAgency) {
        if (productId == PRODUCT_FIRST_CLASS && !isAgency) {
            return 0.9
        } else if (productId == PRODUCT_GARAGE && !isAgency) {
            return 0.8
        } else if (isAgency) {
            return 1.1
        }
        return 0
    }

    private float getDrivingLicenseFactor(Integer drivingExperience) {
        if (drivingExperience.intValue() == DrivingExperienceEnum.ZERO_TO_SIX_MONTHS.id) {
            return 1.50
        } else if (drivingExperience.intValue() == DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.id) {
            return 1.25
        }
        return 1.00
    }

    private float getSumInsuredRangeFactor(Integer insuredValue) {
        if (insuredValue <= 50000) {
            return 1.4
        } else if (insuredValue > 50000 && insuredValue <= 100000) {
            return 1.2
        } else if (insuredValue > 100001 && insuredValue <= 150000) {
            return 1.0
        } else if (insuredValue > 150001 && insuredValue <= 200000) {
            return 0.95
        } else if (insuredValue > 200000 && insuredValue <= 275000) {
            return 0.95
        } else if (insuredValue > 275000 && insuredValue <= 400000) {
            return 0.85
        } else if (insuredValue > 400000) {
            return 0.70
        }
        return 0
    }

    private float getBodyTypeFactor(Integer vehicleTypeId) {
        if (vehicleTypeId.intValue() == VehicleTypeEnum.SEDAN.value) {
            return 1.1
        } else if (vehicleTypeId.intValue() == VehicleTypeEnum.FOURx4.value) {
            return 0.9
        }
        return 0
    }

    private float getMaxBaseRate(Integer vehicleTypeId) {
        if (vehicleTypeId.intValue() == VehicleTypeEnum.SEDAN.value) {
            return 5.00
        } else if (vehicleTypeId.intValue() == VehicleTypeEnum.FOURx4.value) {
            return 6.00
        }
        return 6.00
    }
}
