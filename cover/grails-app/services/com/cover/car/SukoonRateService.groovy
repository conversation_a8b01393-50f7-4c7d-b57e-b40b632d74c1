package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.*
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Ratings calculation for Sukoon Insurance.
 * old name was OMAN
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class SukoonRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 2
    public static final Integer SILVER_ID = 5
    public static final Integer GOLD_ID = 6
    public static final Integer PRODUCT_TPL_ID = 5075

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("Sukoon getRates- quoteCommand - ${quoteCommand} - isOffline - ${isOffline}")
        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)
        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = null
            Country country = Country.get(quoteCommand.nationalityId)

            // Not showing comprehensive product (id: 6, name GOLD) if hasClaim=false
            boolean hasClaim = quoteCommand.hasClaim
            log.info(".getRates - hasClaim ${hasClaim}, productId  ${quoteCommand.productId}")

            if (model && country.omanFactor && !hasClaim) {
                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

                if (applicableRates) {
                    for (rate in applicableRates) {
                        RateCommand rateCommand = populateRatings(quoteCommand, rate)
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("sukoongetRate - ${quoteCommand} - isOffline - ${isOffline}")
        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = null
            quoteCommand.nationalityCategory = null
            Country country = Country.get(quoteCommand.nationalityId)

            // Not showing comprehensive product (id: 6, name GOLD) if hasClaim=false and isNationalityEligibleForComp=false
            boolean hasClaim = quoteCommand.hasClaim
            log.info(".getRates - " +
                "hasClaim ${hasClaim}, productId  ${quoteCommand.productId}")

            if (model && country.omanFactor && !hasClaim) {
                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, true)
                log.info("Rates - ${applicableRates.size()}")
                if (applicableRates) {
                    rateCommand = populateRatings(quoteCommand, applicableRates.first())
                }
            }
        }

        rateCommand
    }

    RateCommand updateMinimumPremium(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (rateCommand.productId == GOLD_ID && !rateCommand.agencyRepair &&
            !((quoteCommand.nationalityId as long) in [CountryEnum.PAKISTAN.id, CountryEnum.BANGLADESH.id,])) {
            if (isTransferableNationality(quoteCommand)) {
                if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                    rateCommand.minPremium = 1700
                } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                    rateCommand.minPremium = 1800
                }
            } else if (!isMENationality(quoteCommand) || (quoteCommand.nationalityId as long) in [CountryEnum.LBN.id]) {
                if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                    rateCommand.minPremium = 1900
                } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                    rateCommand.minPremium = 2000
                }
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        //rateCommand = applyAdditionalFees(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = updateReplacementCarCover(quoteCommand, rateCommand)
        rateCommand = updateOffRoadCover(rateCommand, rate)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        Provider provider = Provider.get(PROVIDER_ID)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        //BigDecimal baseRate = calculateBaseRate(quoteCommand, provider, product)

        rateCommand.agencyRepair = checkAgency(product, quoteCommand)
        rateCommand.productId = product.id

        rateCommand = applyBaseRate(rateCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand = updateMinimumPremium(quoteCommand, rateCommand)
        rateCommand.basePremium = rateCommand.premium

        if (rateCommand.premium < rateCommand.minPremium) {
            rateCommand.premium = rateCommand.minPremium
        }
        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("sukoonTplRate - ${quoteCommand} - isOffline - ${isOffline}")
        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)
            for (ProductTplRate tplRate : applicableRates) {
                log.info(tplRate.productId.toString() + ":" + tplRate.basePremium.toString() + ":" + tplRate.noOfCyl.toString() + ":" + tplRate.vehicleTypeId.toString())
            }

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand = applyTplBasePremium(rateCommand, quoteCommand, rate)
        rateCommand.premium = rateCommand.basePremium
        rateCommand.providerId = PROVIDER_ID
        rateCommand.productId = rate.productId

        rateCommand = applyTplLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        // rateCommand = applyAdditionalFees(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand applyTplLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {
        // TODO: add TPL loading

        rateCommand
    }

    private boolean checkAgency(Product product, QuoteCommand quoteCommand) {

        boolean isAgency = false

//        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
//            if (GOLD_ID == product.id && quoteCommand.carAge <= 2) {
//                isAgency = true
//            } else if (SILVER_ID == product.id && quoteCommand.carAge == 0) {
//                isAgency = true
//            }
//        }

        isAgency
    }

    RateCommand applyAdditionalFees(QuoteCommand quoteCommand, RateCommand rateCommand) {

        Model model = Model.get(quoteCommand.modelId)

        // PA Driver 120
        // PA Pax 30 * number of pax
        // Roadside assistance silver 40 Gold 70
        // Ambulance fee
        BigDecimal roadSide = BigDecimal.valueOf(40) // for silver
        if (rateCommand.productId == GOLD_ID) {
            roadSide = BigDecimal.valueOf(70)
        }
        rateCommand.premium = rateCommand.premium.add(120).add((model.numberOfSeats - 1) * 30).add(roadSide).add(50)

        // Flat AED 300 added as requested by Oman
        rateCommand.premium = rateCommand.premium.add(300)

        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)
        log.info("isEligible:${isEligible}")
        /*
            not eligible if
            - current policy is third party
        */
//        if (quoteCommand.isThirdParty) {
//            isEligible = false
//        }

        if (quoteCommand.customerAge < 23) {
            return false
        }

//        if (quoteCommand.lastClaimPeriod != ClaimPeriodEnum.NEVER) {
//            return false
//        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.carAge > 14) {
            return false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.isThirdParty) {
            return false
        }

        // not eligible if uae driving experience is less then 6 month
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId()) {
            isEligible = false
        }
        log.info("quoteCommand.customerAge:${quoteCommand.customerAge}, isEligible:$isEligible")
        //Restricting Sukoon to 27, 28, 29 only for the time
        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY &&
            (quoteCommand.customerAge < 27/* || quoteCommand.customerAge >= 30*/) ) {
            isEligible = false
        }

        Country nationality = Country.read(quoteCommand.nationalityId)
        if (nationality.isArabic && nationality.id.intValue() != CountryEnum.LBN.id.intValue()) {
            isEligible = false
        }

        log.info("isEligible:${isEligible}")
        isEligible
    }

    boolean isTransferableNationality(QuoteCommand quoteCommand) {

        if ((quoteCommand.nationalityId as long) in [CountryEnum.AUSTRALIA.id, CountryEnum.AUSTRIA.id, CountryEnum.BELGIUM.id, CountryEnum.CANADA.id,
                                                     CountryEnum.CHINA.id, CountryEnum.DENMARK.id, CountryEnum.FRANCE.id, CountryEnum.FINLAND.id, CountryEnum.GERMANY.id, CountryEnum.GREECE.id,
                                                     CountryEnum.HONG_KONG.id, CountryEnum.IRELAND.id, CountryEnum.ITALY.id, CountryEnum.JAPAN.id, CountryEnum.SOUTH_KOREA.id, CountryEnum.USA.id,
                                                     CountryEnum.LATVIA.id, CountryEnum.LITHUANIA.id, CountryEnum.LUXEMBOURG.id, CountryEnum.NORWAY.id, CountryEnum.POLAND.id, CountryEnum.PORTUGAL.id,
                                                     CountryEnum.ROMANIA.id, CountryEnum.SERBIA.id, CountryEnum.SINGAPORE.id, CountryEnum.SLOVAKIA.id, CountryEnum.SOUTH_AFRICA.id, CountryEnum.SPAIN.id,
                                                     CountryEnum.SWEDEN.id, CountryEnum.SWITZERLAND.id, CountryEnum.TURKEY.id, CountryEnum.UK.id, CountryEnum.NETHERLANDS.id, CountryEnum.PHILIPPINES.id]) {
            return true
        }

        return false

    }

    boolean isMENationality(QuoteCommand quoteCommand) {

        if ((quoteCommand.nationalityId as long) in [CountryEnum.ALGERIA.id, CountryEnum.BAHRAIN.id, CountryEnum.EGYPT.id, CountryEnum.IRAQ.id,
                                                     CountryEnum.IRAN.id, CountryEnum.ISRAEL.id, CountryEnum.JOR.id, CountryEnum.KWT.id, CountryEnum.LBN.id, CountryEnum.LIBYA.id,
                                                     CountryEnum.MOROCCO.id, CountryEnum.OMAN.id, CountryEnum.PALESTINE.id, CountryEnum.QATAR.id, CountryEnum.KSA.id, CountryEnum.SYRIA.id,
                                                     CountryEnum.TUNISIA.id, CountryEnum.YEMEN.id, CountryEnum.UAE.id]) {
            return true
        }

        return false
    }

    RateCommand applyTplBasePremium(RateCommand rateCommand, QuoteCommand quoteCommand, ProductTplRate productTplRate) {

        BigDecimal basePremium = productTplRate.basePremium
        Integer noOfCyl = Model.findById(quoteCommand.modelId).noOfCyl

        if (quoteCommand.customerAge >= 30) {

            // for nationality = [Transfer, India, PHILIPPINES]
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.INDIA.id, CountryEnum.PHILIPPINES.id] ||
                    isTransferableNationality(quoteCommand))) {
                if (noOfCyl <= 4) {
                    basePremium = 620
                } else if (noOfCyl <= 6) {
                    basePremium = 700
                } else if (noOfCyl <= 8) {
                    basePremium = 780
                } else if (noOfCyl > 8) {
                    basePremium = 1060
                }
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.INDIA.id, CountryEnum.PHILIPPINES.id] ||
                    isTransferableNationality(quoteCommand))) {
                if (noOfCyl <= 4) {
                    basePremium = 790
                } else if (noOfCyl <= 6) {
                    basePremium = 830
                } else if (noOfCyl <= 8) {
                    basePremium = 870
                } else if (noOfCyl > 8) {
                    basePremium = 950
                }
            }

            // for nationality = [UAE, GCC, LEBANON]
            else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.UAE.id, CountryEnum.BAHRAIN.id, CountryEnum.KWT.id,
                                                          CountryEnum.OMAN.id, CountryEnum.QATAR.id, CountryEnum.KSA.id,
                                                          CountryEnum.LBN.id])) {
                if (noOfCyl <= 4) {
                    basePremium = 630
                } else if (noOfCyl <= 6) {
                    basePremium = 715
                } else if (noOfCyl <= 8) {
                    basePremium = 800
                } else if (noOfCyl > 8) {
                    basePremium = 1095
                }
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.UAE.id, CountryEnum.BAHRAIN.id, CountryEnum.KWT.id,
                                                          CountryEnum.OMAN.id, CountryEnum.QATAR.id, CountryEnum.KSA.id,
                                                          CountryEnum.LBN.id])) {
                if (noOfCyl <= 4) {
                    basePremium = 850
                } else if (noOfCyl <= 6) {
                    basePremium = 895
                } else if (noOfCyl <= 8) {
                    basePremium = 935
                } else if (noOfCyl > 8) {
                    basePremium = 1020
                }
            }
        }

        rateCommand.basePremium = basePremium

        return rateCommand
    }

    private RateCommand applyBaseRate(RateCommand rateCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        rateCommand.baseRate = baseRate
        return rateCommand
    }

    private boolean isNationalityEligibleForDiscount(QuoteCommand quoteCommand) {
        Long nationalityId = quoteCommand.nationalityId
        return (isTransferableNationality(quoteCommand) || (nationalityId in [CountryEnum.INDIA.id, CountryEnum.PHILIPPINES.id, CountryEnum.LBN.id]))
    }

    // Update off road cover for sedan
    private static RateCommand updateOffRoadCover(RateCommand rateCommand, ProductBaseRate rate){
        // set 'no' to offRoadCover for sedan
        if ((int)rate.vehicleTypeId == VehicleTypeEnum.SEDAN.getValue()) {
            rateCommand.offRoadDesertRecovery = 'no'
        }
        return rateCommand
    }

    /**
     * Update rent a car cover to 'yes' for nationalities that are transferrable, non ME or Lebanese
     * @param quoteCommand
     * @param rateCommand
     * @return
     */
    private RateCommand updateReplacementCarCover(QuoteCommand quoteCommand, RateCommand rateCommand) {
//        Long nationalityId = quoteCommand.nationalityId as Long
//        if (rateCommand.productId == GOLD_ID && (isTransferableNationality(quoteCommand) ||
//            nationalityId in [CountryEnum.INDIA.id, CountryEnum.PHILIPPINES.id, CountryEnum.LBN.id])) {
//            rateCommand.replacementCar = "yes"
//        }

        rateCommand
    }
}
