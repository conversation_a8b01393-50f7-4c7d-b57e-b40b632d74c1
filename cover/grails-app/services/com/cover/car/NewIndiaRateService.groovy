package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.ClaimPeriodEnum
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import grails.transaction.Transactional
import org.joda.time.LocalDate

@Transactional
class NewIndiaRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 51
    public static final Integer COMPREHENSIVE_PRODUCT_ID = 1071


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("newIndiaRate.getRates - entering with quoteCommand:${quoteCommand}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)
        log.info("newIndiaRate.getRates - carAge:${quoteCommand.carAge}")

        if (checkEligibility) {

            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("insuranceHouseRate.getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                log.info("newIndiaRate.getRates - applicableRates:${applicableRates.size()}")

                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("insuranceHouseRate.getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.carCategory = null
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        log.info("newIndiaRate.populateRatings - rate:${rate.id}, agency:${rate.baseRateAgency}, garage:${rate.baseRateGarage}")

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)

        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        log.info("newIndiaRate.populateRatings - after min premium, rateCommand:${rateCommand.premium}")

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        rateCommand.baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand = getMinimumPremium(rateCommand, quoteCommand, applicableRate)
        rateCommand.basePremium = rateCommand.premium

        log.info("newIndiaRate.calculatePremium - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        int carAge = quoteCommand.carAge

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if ((quoteCommand.isBrandNew || carAge <= 1) && !quoteCommand.hasClaim) {
                // if car age is <= 1 then agency for all products
                isAgency = true

            } else if (carAge == 2 && quoteCommand.isOldAgency && !quoteCommand.hasClaim &&
                quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {

                // if car age is 2 and at-least 1 years NCD
                isAgency = true
            }

            //No agency if agency is blacklisted for the car or if it has claim
            if (ratingService.agencyBlacklisted(quoteCommand) || quoteCommand.hasClaim) {
                isAgency = false
            }
        }

        return isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (quoteCommand.isPolicyExpired || quoteCommand.isThirdParty ||
            quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id) {
            isEligible = false
        }

        if (quoteCommand.isNonGccSpec && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            isEligible = false
        }

        //no eligible from 13th year (2007 and below)
        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            (LocalDate.now().getYear() - quoteCommand.manufactureYear) > 11) {
            isEligible = false
        }

        //No Quote for Non Indian National
        if(quoteCommand.nationalityId != CountryEnum.INDIA.id) {
            isEligible = false
        }

        isEligible
    }

    RateCommand getMinimumPremium(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        //For non agency repair with NCD or self dec, minimum premium is discounted
        if (!rateCommand.agencyRepair && !quoteCommand.hasClaim &&
            (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value() ||
                    (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
                        quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS))) {

            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                minPremium = 1040

            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                minPremium = 1600

            }

            if (!quoteCommand.noClaimsDiscountId) {
                //min premium is reduced because of self dec
                rateCommand.requiredSelfDeclarationNumber = 1
            }
        }

        rateCommand.minPremium = minPremium

        rateCommand
    }

}
