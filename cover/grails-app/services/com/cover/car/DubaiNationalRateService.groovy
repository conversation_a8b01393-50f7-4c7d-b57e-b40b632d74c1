package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

@Transactional
class DubaiNationalRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 41
    public static final Integer PREMIUM_PRODUCT_ID = 1039 //Old Name Silver
    public static final Integer PREMIUM_PLUS_PRODUCT_ID = 1041 //Old name Platinum
    public static final Integer TPL_PRODUCT_ID = 1042
    List<Integer> agencyMakeIds = [127, 145, 141, 142, 135, 149]

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("dubaiNational.getRates - entering with quoteCommand:${quoteCommand}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)
        log.info("dubaiNational.getRates - carAge:${quoteCommand.carAge}")

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.dnircCategory
            quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).dnircCategory

            Integer noClaimYears = ratingService.getNoClaimYears(quoteCommand)
            List<ProductBaseRate> applicableRates = ratingService
                .getBaseRateMinPremium(quoteCommand, isOffline, noClaimYears)

            if (applicableRates) {

                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)

        quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).dnircCategory

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.dnircCategory

            Integer noClaimYears = ratingService.getNoClaimYears(quoteCommand)
            List<ProductBaseRate> applicableRates = ratingService
                .getBaseRateMinPremium(quoteCommand, isOffline, noClaimYears)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        log.info("dubaiNational.populateRatings - rate:${rate.id}, agency:${rate.baseRateAgency}, garage:${rate.baseRateGarage}")

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)

        rateCommand = checkMinimumPremium(rateCommand, quoteCommand)

        log.info("dubaiNational.populateRatings - after discount, rateCommand:${rateCommand.premium}")

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        rateCommand.baseRate = getBaseRate(rateCommand, quoteCommand, applicableRate)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        if (isAgency && applicableRate.productId == PREMIUM_PRODUCT_ID && quoteCommand.makeId != null &&
            agencyMakeIds.contains(quoteCommand.makeId)) {
            log.info("dubaiNational.calculatePremium update agency rates for quoteCommand.makeId: ${quoteCommand.makeId} and rate.id: ${applicableRate.id}")
            Map updatedRatesAndMinPrem = updateAgencyBaseRateAndMinPremium(applicableRate)
            rateCommand.baseRate = updatedRatesAndMinPrem.get("baseRateAgency") as BigDecimal
            rateCommand.minPremium = updatedRatesAndMinPrem.get("minPremiumAgency") as BigDecimal
        }
        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.basePremium = rateCommand.premium

        rateCommand = applyBaseRate(rateCommand, quoteCommand)

        log.info("dubaiNational.calculatePremium - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY, isOffline)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
            log.info("dubaiNational.populateTplRatings update basePremium for quoteCommand.noClaimsDiscountId: ${quoteCommand.noClaimsDiscountId} and rate.id: ${rate.id}")
            rateCommand.basePremium = updateTplBasePremium(quoteCommand.noClaimsDiscountId, rate.basePremium)
        }
        rateCommand.premium = rateCommand.basePremium
        rateCommand.productId = rate.productId
        rateCommand = applyTplDiscount(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (quoteCommand.hasClaim) {
            return false
        }

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            /*if (quoteCommand.isBrandNew || quoteCommand.carAge <= 1) {
                isAgency = true
            }
            if (quoteCommand.carAge == 2 && quoteCommand.isOldAgency) {
                isAgency = true
            } else if (quoteCommand.carAge == 3 && quoteCommand.isOldAgency) {
                isAgency = true
            }

            if (ratingService.agencyBlacklisted(quoteCommand)) {
                isAgency = false
            }*/
            isAgency = false
        }

        return isAgency
    }

    private boolean isAllowedNationality(Integer nationalityId) {
        if (nationalityId in [
            2,    //ALBANIAN
            3,    //ALGERIAN
            166,    //AMERICAN
            4,    //ANDORRAN
            192,    //ANTIGUANS
            6,    //ARGENTINEAN
            7,    //ARMENIAN
            8,    //AUSTRALIAN
            9,    //AUSTRIAN
            11,    //BAHAMIAN
            14,    //Barbadian
            15,    //BELARUSIAN
            16,    //BELGIAN
            17,    //BELIZEAN
            20,    //BOLIVIAN
            21,    //BOSNIAN
            23,    //BRAZILIAN
            165,    //BRITISH
            26,    //BULGARIAN
            32,    //CANADIAN
            35,    //CHILEAN
            37,    //COLOMBIAN
            39,    //COSTA RICAN
            40,    //CROATIAN
            41,    //CUBAN
            42,    //CYPRIOT
            43,    //CZECH
            44,    //DANISH
            46,    //DOMINICAN
            47,    //
            48,    //ECUADOREAN
            53,    //ESTONIAN
            124,    //FILIPINO
            56,    //FINNISH
            57,    //FRENCH
            60,    //GEORGIAN
            61,    //GERMAN
            63,    //GREEK
            64,    //GRENADIAN
            65,    //GUATEMALAN
            67,    //GUYANESE
            68,    //HAITIAN
            69,    //HONDURAN
            70,    //HUNGARIAN
            71,    //ICELANDER
            72,    //INDIAN
            76,    //IRISH
            219,    //ISRAELI
            78,    //ITALIAN
            79,    //JAMAICAN
            185,    //KITTIAN AND NEVISIAN
            84,    //KUWAITI
            86,    //LATVIAN
            87,    //LEBANESE
            90,    //LIECHTENSTEINER
            91,    //LITHUANIAN
            92,    //LUXEMBOURGER
            93,    //MACEDONIAN
            99,    //MALTESE
            102,    //MEXICAN
            103,    //MOLDOVAN
            104,    //MONACAN
            106,    //MONTENEGRO
            111,    //NETHERLANDER
            112,    //NEW ZEALANDER
            113,    //NICARAGUAN
            117,    //NORWEGIAN
            120,    //PANAMANIAN
            122,    //PARAGUAYAN
            123,    //PERUVIAN
            125,    //POLISH
            126,    //PORTUGUESE
            128,    //ROMANIAN
            129,    //RUSSIAN
            222,    //SAINT LUCIAN
            50,    //SALVADORAN
            180,    //SAN MARINESE
            131,    //SAUDI
            132,    //SCOTTISH
            134,    //SERBIAN
            137,    //SINGAPOREAN
            138,    //SLOVAKIAN
            139,    //SLOVENIAN
            142,    //SOUTH AFRICAN
            144,    //SPANISH
            147,    //SURINAMER
            149,    //SWEDISH
            150,    //SWISS
            157,    //TRINIDADIAN OR TOBAGONIAN
            159,    //TURKISH
            163,    //UKRAINIAN
            167,    //URUGUAYAN
            171,    //VENEZUELAN
            173        //WELSH
        ]) {
            return true
        }
        return false
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE, boolean issOffline) {
        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        /*
        boolean licenseEligible = (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id &&
            quoteCommand.internationalDrivingExperienceId > DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.id) ||
            quoteCommand.localDrivingExperienceId > DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.id

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            (!licenseEligible || quoteCommand.isPolicyExpired)) {
            isEligible = false
        }

        if (quoteCommand.isBrandNew){
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY &&
            quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id) {
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE ) {
            if (quoteCommand.isNonGccSpec || quoteCommand.isThirdParty
                || (LocalDate.now().getYear() - quoteCommand.manufactureYear) > 11) {
                isEligible = false
            }
        }

        if (quoteCommand.insuredValue > 250000){
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.isNonGccSpec) {
            Period period = new Period(quoteCommand.firstRegistrationDate, LocalDate.now())
            int years = period.getYears()
            //Not Eligible for TPL if not registered more than 1 year
            if (years < 1) {
                isEligible = false
            }
        }

        //Only limited makes are allowed under HVV rating
        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            quoteCommand.insuredValue > 500000 && !(quoteCommand.makeId in [
                CarMakeEnum.MASERATI.id, CarMakeEnum.ASTON_MARTIN.id, CarMakeEnum.ROLLS_ROYCE.id,
                CarMakeEnum.PORSCHE.id, CarMakeEnum.RANGE_ROVER.id, CarMakeEnum.BMW.id,
                CarMakeEnum.MERCEDES.id, CarMakeEnum.AUDI.id, CarMakeEnum.BENTLEY.id,
                CarMakeEnum.MCLAREN.id
            ])) {
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.carAge > 30) {
            isEligible = false
        }

        if (quoteCommand.insuredValue < 500000 && (quoteCommand.customerAge < 21 || quoteCommand.customerAge > 65)) {
            isEligible = false
        }

        if (!(quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value])) {
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            quoteCommand.insuredValue < 500000 && quoteCommand.carAge > 11) {
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            quoteCommand.insuredValue > 500000 && quoteCommand.carAge > 6) {
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            quoteCommand.insuredValue > 500000 && quoteCommand.customerAge < 30) {
            isEligible = false
        }

        */

        isEligible
    }

    /**
     * Get base rate based on repair type and other conditions
     * @param rateCommand
     * @param quoteCommand
     * @param applicableRate
     * @return
     */
    BigDecimal getBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        baseRate
    }

    RateCommand applyTplDiscount(QuoteCommand quoteCommand, RateCommand rateCommand) {
        return rateCommand
    }

    RateCommand checkMinimumPremium(RateCommand rate, QuoteCommand quoteCommand) {

        rate.actualBasePremium = rate.premium

        // after discount if premium is less then minimum premium then use minimum premium
        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium

            rate.productDiscountAmount = null
            rate.productDiscountPercent = null
        }

        rate
    }

    private RateCommand applyBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand) {
        log.info("DubaiNationalRateService - applyBaseRate ${rateCommand}")

        Model model = Model.findById(quoteCommand.modelId)

        if (rateCommand.productId == PREMIUM_PRODUCT_ID && model.modelMaster.id in [ModelMasterEnum.NISSAN_PATROL.id, ModelMasterEnum.NISSAN_PATROL_SAFARI.id] && rateCommand.isAgencyRepair()) {
            rateCommand.baseRate = 2.25
            rateCommand.minPremium = 3000
        } else if (rateCommand.productId == PREMIUM_PRODUCT_ID && model.modelMaster.id in [ModelMasterEnum.NISSAN_PATROL.id, ModelMasterEnum.NISSAN_PATROL_SAFARI.id] && !rateCommand.isAgencyRepair()) {
            rateCommand.baseRate = 1.80
            rateCommand.minPremium = 2000
        }

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        return rateCommand

    }

    private Map updateAgencyBaseRateAndMinPremium(ProductBaseRate rate) {
        log.info("dubaiNational.updateComprehensiveApplicableRate Start - baseRateAgency:${rate.baseRateAgency}, minPremiumAgency:${rate.minPremiumAgency}")

        Map updatedRatesAndMinPrem = new HashMap<>()
        updatedRatesAndMinPrem.put("baseRateAgency", rate.baseRateAgency)
        updatedRatesAndMinPrem.put("minPremiumAgency", rate.minPremiumAgency)

        List<Double> saloonMakeAgencyRateList = [
            4.00, 4.50, 5.00, 4.00, 4.50, 5.00, 4.00, 4.50, 5.00, //ndc = 0
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75, //ndc = 1
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75, //ndc = 2
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75  //ndc = 3
        ]
        List<Long> saloonMakeAgencyMinPremList = [
            2400, 2650, 2900, 2300, 2500, 2750, 2750, 3000, 3250, //ndc = 0
            2300, 2500, 2750, 2200, 2400, 2600, 2500, 2750, 3000, //ndc = 1
            2300, 2500, 2750, 2200, 2400, 2600, 2500, 2750, 3000, //ndc = 2
            2300, 2500, 2750, 2200, 2400, 2600, 2500, 2750, 3000  //ndc = 3
        ]

        List<Double> fWDMakeAgencyRateList = [
            4.00, 4.50, 5.00, 4.00, 4.50, 5.00, 4.00, 4.50, 5.00, //ndc = 0
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75, //ndc = 1
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75, //ndc = 2
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75  //ndc = 3
        ]
        List<Long> fWDMakeAgencyMinPremList = [
            2200, 2400, 2700, 2200, 2500, 2750, 2750, 3000, 3250, //ndc = 0
            2100, 2300, 2500, 2200, 2500, 2750, 2750, 3000, 3250, //ndc = 1
            2100, 2300, 2500, 2200, 2500, 2750, 2750, 3000, 3250, //ndc = 2
            2100, 2300, 2500, 2200, 2500, 2750, 2750, 3000, 3250  //ndc = 3
        ]

        List<Double> tWDMakeAgencyRateList = [
            4.00, 4.50, 5.00, 4.00, 4.50, 5.00, 4.00, 4.50, 5.00, //ndc = 0
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75, //ndc = 1
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75, //ndc = 2
            3.80, 4.28, 4.75, 3.80, 4.28, 4.75, 3.80, 4.28, 4.75  //ndc = 3
        ]
        List<Long> tWDMakeAgencyMinPremList = [
            2250, 2500, 2750, 2300, 2500, 2750, 2750, 3000, 3250, //ndc = 0
            2300, 2500, 2750, 2200, 2400, 2600, 2500, 2750, 3000, //ndc = 1
            2300, 2500, 2750, 2200, 2400, 2600, 2500, 2750, 3000, //ndc = 2
            2300, 2500, 2750, 2200, 2400, 2600, 2500, 2750, 3000  //ndc = 3
        ]

        List nationalityList = ["NG1", "NG2", "NG3"]
        List valueFromList = [0, 100001, 250001]
        List valueToList = [100000, 250000, 500000]
        List ncdYears = [0, 1, 2, 3]
        int index = 0
        for (ncdYear in ncdYears) {
            for (int vehicleValueIndex = 0; vehicleValueIndex < valueFromList.size(); vehicleValueIndex++) {
                for (nationality in nationalityList) {
                    if (rate.productId == PREMIUM_PRODUCT_ID && rate.valueFrom == valueFromList.get(vehicleValueIndex)
                        && rate.valueTo == valueToList.get(vehicleValueIndex) && rate.noClaimYears == ncdYear &&
                        rate.nationalityCategory == nationality) {
                        if (rate.carCategory == "4WD") {
                            updatedRatesAndMinPrem.put("baseRateAgency", fWDMakeAgencyRateList.get(index))
                            updatedRatesAndMinPrem.put("minPremiumAgency", fWDMakeAgencyMinPremList.get(index))
                        } else if (rate.carCategory == "2WD") {
                            updatedRatesAndMinPrem.put("baseRateAgency", tWDMakeAgencyRateList.get(index))
                            updatedRatesAndMinPrem.put("minPremiumAgency", tWDMakeAgencyMinPremList.get(index))
                        } else {
                            // SALOON -> A
                            updatedRatesAndMinPrem.put("baseRateAgency", saloonMakeAgencyRateList.get(index))
                            updatedRatesAndMinPrem.put("minPremiumAgency", saloonMakeAgencyMinPremList.get(index))
                        }
                        break
                    }
                    index++
                }
            }
        }

        log.info("dubaiNational.updateComprehensiveApplicableRate End - baseRateAgency:${rate.baseRateAgency}, minPremiumAgency:${rate.minPremiumAgency}")
        return updatedRatesAndMinPrem
    }

    private BigDecimal updateTplBasePremium(Integer noClaimsDiscountId, BigDecimal basePremium) {
        log.info("dubaiNational.updateTplBasePremium Start - basePremium:${basePremium}")

        if (noClaimsDiscountId == NcdEnum.YEAR1.value()) {
            basePremium = basePremium.subtract(basePremium * (0.10))
        } else if (noClaimsDiscountId == NcdEnum.YEAR2.value()) {
            basePremium = basePremium.subtract(basePremium * (0.15))
        } else if (noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
            basePremium = basePremium.subtract(basePremium * (0.20))
        }

        log.info("dubaiNational.updateTplBasePremium End - basePremium:${basePremium}")
        return basePremium
    }

}
