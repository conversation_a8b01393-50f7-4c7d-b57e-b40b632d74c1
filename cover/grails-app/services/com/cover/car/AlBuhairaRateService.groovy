package com.cover.car

import com.safeguard.CarMakeEnum
import com.safeguard.CityEnum
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.ProductEnum
import com.safeguard.RepairTypeEnum
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import grails.transaction.Transactional
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.vehicle.Model
import org.joda.time.LocalDate

import java.time.LocalDateTime

/**
 * Calculate premium for Al Buhaira Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class AlBuhairaRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 31
    public static final Integer COMPREHENSIVE_PRODUCT_ID = 1009

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }


    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)
        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId, model.noOfCyl, quoteCommand.customerAge,
                        isOffline, null, true, quoteCommand.requestSource)

           if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }

        }
        rateCommand
    }


    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate,quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }


    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
       // rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        //rateCommand = applyLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        //rateCommand = addMandatoryPABFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)
        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }
        rateCommand
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {
        //Applying 10% discount if quote is non agency and have no claim certificate
        if (!rate.agencyRepair) {
            Model model = Model.read(quoteCommand.modelId)
            if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
                // If Sedan or SUV, or if Coupe/Convertible and cyl is 4 or below or if coupe/covertibale and cyl > 4 then age to be more than 30
                if (model.vehicleTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value] ||
                    (model.vehicleTypeId in [VehicleTypeEnum.COUPE.value, VehicleTypeEnum.CONVERTIBLE.value, VehicleTypeEnum.SPORTS.value] &&
                        (model.noOfCyl <= 4 || (quoteCommand.customerAge > 30)))) {

                    rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
                }
            }
        }
        rate
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        boolean isAgency = checkAgency(quoteCommand)
        BigDecimal baseRate = getBaseRate(isAgency, quoteCommand, applicableRate)
        BigDecimal minPremium = getMinimumPremium(isAgency, quoteCommand, applicableRate)
        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = rateCommand.premium
        rateCommand
    }

    private BigDecimal getBaseRate(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        baseRate
    }

    private BigDecimal getMinimumPremium(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        BigDecimal minPremium  = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        minPremium
    }

    private boolean checkAgency(QuoteCommand quoteCommand) {

        if (!ratingService.allowAgency()){
            return false
        }

        int carAge = quoteCommand.carAge
        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (!quoteCommand.isThirdParty) {

                if (quoteCommand.isBrandNew || carAge <= 1) {
                    isAgency = true
                }
//                if (carAge == 1 && !quoteCommand.hasClaim) {
//                    isAgency = true
//                }
//                if (carAge == 2 && !quoteCommand.hasClaim && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
//                    isAgency = true
//                }
            }
        }
        isAgency
    }



    private RateCommand applyLoadings(QuoteCommand  quoteCommand, RateCommand rate)
    {
        int carAge = quoteCommand.carAge

        if (carAge == 2 && rate.agencyRepair && !quoteCommand.hasClaim && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value() && !quoteCommand.isBrandNew) {
            rate.premium = rate.premium.add(rate.basePremium * (0.10))
        }
        rate
    }


    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {
        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        Make make = Make.load(quoteCommand.makeId)
        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE && (quoteCommand.carAge > 7 || ((LocalDateTime.now().year - quoteCommand.manufactureYear) > 7)))  {
            isEligible = false
        }

        if (productTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.insuredValue > 20000){
            isEligible = false
        }

        if (quoteCommand.isNonGccSpec){
            isEligible = false
        }

        if (make.id == CarMakeEnum.TESLA.id || make.country.id as long == CountryEnum.CHINA.id){
            isEligible = false
        }

        if(quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId())
        {
            isEligible = false;
        }

//        Model model = Model.read(quoteCommand.modelId)
//        if (model.makeId == CarMakeEnum.CHEVROLET.id
//            && model.modelMaster.id in [
//            97 //Camaro
//        ] && quoteCommand.customerAge <= 30) {
//            isEligible = false
//        }

        // If customer age is less than 30 and coupe with 4 Cyl
//        if (model.vehicleTypeId.intValue() in [VehicleTypeEnum.CONVERTIBLE.value, VehicleTypeEnum.COUPE.value] &&
//            model.noOfCyl > 4 && quoteCommand.customerAge < 30) {
//            isEligible = false
//        }

        isEligible
    }

//    private RateCommand addMandatoryPABFees(RateCommand rateCommand, QuoteCommand quoteCommand) {
//
//        Model model = Model.read(quoteCommand.modelId)
//        rateCommand.premium = rateCommand.premium.add(120 as BigDecimal) //PAB for Driver
//        rateCommand.premium = rateCommand.premium.add(30 * (model.numberOfSeats - 1) as BigDecimal) // PAB for Passenger
//        rateCommand
//    }
}


