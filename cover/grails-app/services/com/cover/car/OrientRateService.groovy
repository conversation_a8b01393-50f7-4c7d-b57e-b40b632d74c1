package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import grails.transaction.Transactional
import org.joda.time.LocalDate

@Transactional
class OrientRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 18

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("getRates - entering with quoteCommand:${quoteCommand}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID


        boolean checkEligibility = checkEligibility(quoteCommand)
        log.info("getRates - carAge:${quoteCommand.carAge}")

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

//            quoteCommand.carCategory = null
//            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
//            if (applicableRates) {
//                log.info("getRates - applicableRates:${applicableRates.size()}")
//
//                for (rate in applicableRates) {
//                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
//                    rateList.add(rateCommand)
//                }
//            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

//            quoteCommand.carCategory = null
//            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
//            if (applicableRates) {
//                ProductBaseRate rate = applicableRates.first()
//                rateCommand = populateRatings(quoteCommand, rate)
//            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        log.info("populateRatings - rate:${rate.id}, agency:${rate.baseRateAgency}, garage:${rate.baseRateGarage}")

        if (isHighRateVehicle(quoteCommand)) {
            rate.baseRateAgency = 3.68
            rate.baseRateGarage = 3.68
            rate.minPremiumAgency = 1950
            rate.minPremiumGarage = 1950
        }

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)

        rateCommand = applyDiscounts(quoteCommand, rateCommand)


        log.info("populateRatings - after min premium, rateCommand:${rateCommand.premium}")

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, false)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        rateCommand.baseRate = getBaseRate(rateCommand, quoteCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        log.info("calculatePremium - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

//            Model model = Model.read(quoteCommand.modelId)
//            List<ProductTplRate> applicableRates =
//                ratingService.
//                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
//                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)
//
//            if (applicableRates) {
//                ProductTplRate rate = applicableRates.first()
//                rateCommand = populateTplRatings(quoteCommand, rate)
//            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = applyTplDiscount(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    //Done
    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            isAgency = false
        }
        return isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        isEligible
    }


    /**
     * Get base rate based on repair type and other conditions
     * @param rateCommand
     * @param quoteCommand
     * @param applicableRate
     * @return
     */
    BigDecimal getBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        baseRate
    }

    RateCommand applyTplDiscount(QuoteCommand quoteCommand, RateCommand rateCommand) {
        log.info("applyTplDiscount - before discount, premium is:${rateCommand.premium}")

        if (quoteCommand.noClaimsDiscountId) {
            Integer applicableDiscountsYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(quoteCommand)

            if (applicableDiscountsYears == 1) {
                rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (0.20))
            } else if (applicableDiscountsYears == 2) {
                rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (0.25))
            } else if (applicableDiscountsYears >= 3) {
                rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (0.30))
            }

        } else if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) && !quoteCommand.isPolicyExpired &&
            quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            Integer applicableDiscountsYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)

            if (applicableDiscountsYears == 1) {
                // 10% discount for 1 year old car with 1 year self declaration
                rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (0.20))
                rateCommand.noClaimDiscountPercent = 20   //20%
                rateCommand.requiredSelfDeclarationNumber = 1  //At least 1 year self declaration is required

            } else if (applicableDiscountsYears == 2) {
                // 15% discount for 1 year old car with 1 year self declaration
                rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (0.25))
                rateCommand.noClaimDiscountPercent = 25   //25%
                rateCommand.requiredSelfDeclarationNumber = 2  //At least 2 year self declaration is required

            } else if (applicableDiscountsYears >= 3) {

                // 20% discount for 1 year old car with 1 year self declaration
                rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (0.30))
                rateCommand.noClaimDiscountPercent = 30   //30%
                rateCommand.requiredSelfDeclarationNumber = 3  //At least 3 year self declaration is required
            }

        }

        log.info("applyTplDiscount - after discount, premium is:${rateCommand.premium}")

        rateCommand
    }

    private boolean isHighRateVehicle(QuoteCommand quoteCommand) {
        ModelMaster modelMaster = Model.read(quoteCommand.modelId).modelMaster

        if (modelMaster.id in [
                287, //Lexus IS
                373 //Mitsubishi Attrange
        ]) {
            return true
        }

        return false
    }

    /**
     * Apply discount based on vehicle type
     * @param quoteCommand
     * @param rateCommand
     * @return rateCommand
     */
    private RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rateCommand) {
        float discountPercentage = 0
        if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
            // 40% discount
            discountPercentage = 0.40
        } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
            // 35% discount
            discountPercentage = 0.35
        }
        if (discountPercentage != 0) {
            rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * discountPercentage)
        }

        return rateCommand
    }


}

