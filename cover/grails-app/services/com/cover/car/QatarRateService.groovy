package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.City
import com.safeguard.CityEnum
import com.safeguard.Country
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Ratings calculation for Qatar Insurance.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class QatarRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 3
    public static final Integer PREMIUM_ID = 7
    public static final Integer PRESTIGE_ID = 8
    public static final Integer PLATINUM_ID = 9

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.qatarCategory
            Country country = Country.get(quoteCommand.nationalityId)
            if (model && model.qatarCategory && country.qatarLoading) {
                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

                if (applicableRates) {

                    for (rate in applicableRates) {

                        RateCommand rateCommand = populateRatings(quoteCommand, rate)
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.qatarCategory
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {

                rateCommand = populateRatings(quoteCommand, applicableRates.first())
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = applyDiscountLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        // for Qatar we need to add TPL rates into OD for calculating comprehensive.
        RateCommand tplRateCmd = getTplRate(quoteCommand, false, false, true)
        rateCommand.premium = rateCommand.premium + tplRateCmd.basePremium
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)

        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline,
                           boolean isActive = true, boolean allowAbove8Cyl = false) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY, allowAbove8Cyl)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId, model.noOfCyl,
                        quoteCommand.customerAge, isOffline, null, isActive, quoteCommand.requestSource)
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand.premium = isAgency ?
            ratingService.calculate(applicableRate.baseRateAgency, quoteCommand.insuredValue) :
            ratingService.calculate(applicableRate.baseRateGarage, quoteCommand.insuredValue)
        rateCommand.minPremium = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            int carAge = quoteCommand.carAge

            if (carAge <= 1) {
                isAgency = true
            }
            if (applicableRate.productId in [PLATINUM_ID, PRESTIGE_ID]) {
                if (carAge == 2) {
                    isAgency = true
                } else if (carAge == 3 && quoteCommand.noClaimsDiscountId) {
                    isAgency = true
                }
            }

            if (quoteCommand.hasClaim) {
                isAgency = false
            }
        }

        isAgency
    }

    RateCommand applyDiscountLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {

        Country country = Country.get(quoteCommand.nationalityId)
        City city = City.get(quoteCommand.registrationCityId)
        Integer ageCustomer = quoteCommand.customerAge
        Integer carAge = quoteCommand.carAge

        BigDecimal discount = BigDecimal.ZERO
        BigDecimal loading = BigDecimal.ZERO

        // nationality based loading / discount
        if (country.qatarLoading && country.qatarLoading > 0) {
            loading = loading.add(country.qatarLoading)
        } else {
            discount = discount.add(country.qatarLoading * (-1))
        }

        // registration city based loading / discount
        if (city.qatarLoading && city.qatarLoading > 0) {
            loading = loading.add(city.qatarLoading)
        } else {
            discount = discount.add((city.qatarLoading * (-1)))
        }

        // customer age based loading / discount
        if (ageCustomer >= 18 && ageCustomer <= 30) {
            loading = loading.add(20)
        } else if (ageCustomer >= 31 && ageCustomer <= 40) {
            discount = discount.add(10)
        } else if (ageCustomer >= 41 && ageCustomer <= 60) {
            loading = loading.add(5)
        } else if (ageCustomer >= 61 && ageCustomer <= 75) {
            loading = loading.add(20)
        }


        // discount
        rateCommand.premium = rateCommand.premium.subtract(ratingService.percentage(rateCommand.basePremium, discount))

        // check for min premium
        if (rateCommand.premium < rateCommand.minPremium) {
            rateCommand.premium = rateCommand.minPremium
            rateCommand.basePremium = rateCommand.minPremium
        }

        // loading
        rateCommand.premium = rateCommand.premium.add(ratingService.percentage(rateCommand.basePremium, loading))



        // agency loading
        // if after applying `agencyLoading` on premium it is less then `agencyMinimum` then use `agencyMinimum`
        BigDecimal agencyLoading = 0
        if (rateCommand.productId == PREMIUM_ID && rateCommand.agencyRepair) {
            if (carAge == 2) {
                agencyLoading = 10 // loading in %
                int agencyMinimum = 500
                agencyLoading = ratingService.percentage(rateCommand.premium, agencyLoading) > agencyMinimum ?
                    ratingService.percentage(rateCommand.premium, agencyLoading) : agencyMinimum

            } else if (carAge == 3) {
                agencyLoading = 15 // loading in %
                int agencyMinimum = 750
                agencyLoading = ratingService.percentage(rateCommand.premium, agencyLoading) > agencyMinimum ?
                    ratingService.percentage(rateCommand.premium, agencyLoading) : agencyMinimum
            }
        } else if (rateCommand.productId == PRESTIGE_ID && rateCommand.agencyRepair) {
            if (carAge == 3) {
                agencyLoading = 25 // loading in %
                int agencyMinimum = 1000
                agencyLoading = ratingService.percentage(rateCommand.premium, agencyLoading) > agencyMinimum ?
                    ratingService.percentage(rateCommand.premium, agencyLoading) : agencyMinimum
            }
        }

        rateCommand.premium = rateCommand.premium.add(agencyLoading)

        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE,
                             boolean allowAbove8Cyl = false) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            /*
            not eligible if
            - current policy is third party
            */
            if (quoteCommand.isThirdParty) {
                isEligible = false
            }
        }

        if (!allowAbove8Cyl && quoteCommand.noOfCyl > 8) {
            isEligible = false
        }

        // not eligible if vehicle type is Pickup or Van.
        if (quoteCommand.vechileTypeId in [VehicleTypeEnum.PICKUP.value, VehicleTypeEnum.VAN.value]) {
            isEligible = false
        }

        // not eligible if uae driving experience is less then 1 year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }


        isEligible
    }
}
