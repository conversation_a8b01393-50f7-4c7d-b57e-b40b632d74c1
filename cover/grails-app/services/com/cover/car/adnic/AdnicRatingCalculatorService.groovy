package com.cover.car.adnic

import com.safeguard.Product
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.adnic.AdnicRates
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.joda.time.LocalDateTime

import java.math.RoundingMode

@Transactional
class AdnicRatingCalculatorService {

    public static final Integer PROVIDER_ID = 12
    public static final Integer STANDARD_ID = 139
    public static final Integer GOLD_PRODUCT_ID = 138  //Offline now
    public static final Integer REPAIR_TYPE_FIRST_AGENCY = 1
    public static final Integer REPAIR_TYPE_SECOND_AGENCY = 2
    public static final Integer REPAIR_TYPE_THIRD_AGENCY = 3
    public static final Integer REPAIR_TYPE_NON_AGENCY = 0
    public static final Double MIN_RATE = 1.50
    public static final Double MAX_RATE = 5.00
    public static final Integer MIN_PREMIUM = 2000
    public static final Integer MIN_PREMIUM_SALOON = 1500
    public static final Integer MIN_PREMIUM_NON_AGENCY_STANDARD_SALOON = 1300

    ProductBaseRate getRates(Integer productId,
                             Integer customerAge,
                             Integer ncdYear,
                             Model model,
                             Integer manufactureYear,
                             Long vehicleTypeId) {

        log.info("productId: ${productId} -- customerAge: ${customerAge} -- ncdYear: ${ncdYear} -- manufactureYear: ${manufactureYear}")

        Integer minPremiumSaloonNA = productId == STANDARD_ID ? MIN_PREMIUM_NON_AGENCY_STANDARD_SALOON : MIN_PREMIUM_SALOON
        Integer repairType = getRepairType(manufactureYear)
        ProductBaseRate productBaseRate = new ProductBaseRate()

        Product product = Product.findById(productId)
        //Product and Provider have to be active, otherwise return empty array (rates)
        if (!product.active || !product.provider.active) {
            return null
        }

        productBaseRate.product = product

        log.info("The ycModelId: ${model.id}")
        AdnicRates nonAgencyRate = AdnicRates
            .findByMakeAndModelAndYearAndRepairType(
                model.make.nameEn.toUpperCase(),
                model.modelMaster.nameEn.toUpperCase(),
                String.valueOf(manufactureYear),
                REPAIR_TYPE_NON_AGENCY,  [sort: "year", order: "desc"])
        if (nonAgencyRate == null) {
            return null
        }
        log.info("NonAgency Rate: ${nonAgencyRate.rate}")

        productBaseRate.baseRateGarage = nonAgencyRate.rate * productFactor(productId) * ageLoading(customerAge)

        productBaseRate.baseRateGarage = Math.max(productBaseRate.baseRateGarage, MIN_RATE)
        productBaseRate.baseRateGarage = Math.min(productBaseRate.baseRateGarage, MAX_RATE)
        productBaseRate.baseRateGarage = productBaseRate.baseRateGarage.setScale(3, RoundingMode.CEILING)
        productBaseRate.minPremiumGarage = vehicleTypeId.intValue() == VehicleTypeEnum.SEDAN.value ? minPremiumSaloonNA : MIN_PREMIUM
        productBaseRate.baseRateAgency = BigDecimal.ZERO

        if (repairType != REPAIR_TYPE_NON_AGENCY) {
            AdnicRates agencyRate = AdnicRates
                .findByMakeAndModelAndYearAndRepairType(
                    model.make.nameEn.toUpperCase(),
                    model.modelMaster.nameEn.toUpperCase(),
                    String.valueOf(manufactureYear),
                    repairType,  [sort: "year", order: "desc"])

            if (agencyRate == null) {
                return productBaseRate
            }
            log.info("Agency Rate: ${agencyRate.rate}")

            productBaseRate.baseRateAgency = agencyRate.rate * productFactor(productId) * ageLoading(customerAge)

            productBaseRate.baseRateAgency = Math.max(productBaseRate.baseRateAgency, MIN_RATE)
            productBaseRate.baseRateAgency = Math.min(productBaseRate.baseRateAgency, MAX_RATE)
            productBaseRate.baseRateAgency = productBaseRate.baseRateAgency.setScale(3, RoundingMode.CEILING)
            productBaseRate.minPremiumAgency = vehicleTypeId.intValue() == VehicleTypeEnum.SEDAN.value ? MIN_PREMIUM_SALOON : MIN_PREMIUM
        }

        return productBaseRate
    }


    private Integer getRepairType(Integer manufactureYear) {
        int currentYear = LocalDateTime.now().year

        if (manufactureYear == currentYear || manufactureYear == (currentYear + 1)) {
            return REPAIR_TYPE_FIRST_AGENCY
        }

        if (manufactureYear == (currentYear - 1)) {
            return REPAIR_TYPE_SECOND_AGENCY
        }

        if (manufactureYear == (currentYear - 2)) {
            return REPAIR_TYPE_THIRD_AGENCY
        }

        return REPAIR_TYPE_NON_AGENCY;
    }

    private Double productFactor(Integer productId) {
        if (productId == STANDARD_ID) {
            return 0.90
        }

        if (productId == GOLD_PRODUCT_ID) {
            return 1.00
        }

        return 1.00
    }

    private Double ageLoading(int age) {
        if (age < 21) {
            return 2.55
        }

        if (age > 21 && age < 26) {
            return 1.90
        }

        if (age > 25 && age < 30) {
            return 1.40
        }

        if (age > 29 && age < 35) {
            return 1.15
        }

        if (age > 34 && age < 40) {
            return 1.00
        }

        if (age > 39 && age < 50) {
            return 0.95
        }

        if (age > 49 && age < 60) {
            return 1.00
        }

        if (age > 59 && age < 70) {
            return 1.05
        }

        if (age > 69) {
            return 1.10
        }

        return 1.00
    }
}
