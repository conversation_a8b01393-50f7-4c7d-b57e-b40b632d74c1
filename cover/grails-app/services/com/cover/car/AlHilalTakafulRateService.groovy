package com.cover.car

import com.RoadsideAssistanceEnum
import com.safeguard.CityEnum
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import grails.transaction.Transactional
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime


@Transactional
class AlHilalTakafulRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 33
    public static final Integer ALHILAL_COMPREHENSIVE = 1023

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }
        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }
        rateCommand
    }


    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)

            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId, model.noOfCyl, quoteCommand.customerAge,
                        isOffline, null, true, quoteCommand.requestSource)
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }

        }
        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)

        //rateCommand = applyLoadings(quoteCommand, rateCommand)

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }
        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = applyTplDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyTplCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate,quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }


    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand = applyBaseRate(rateCommand, quoteCommand, applicableRate)

        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private RateCommand applyBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        BigDecimal minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        // if customer age is less then 25 then baseRate will be 5%
        if (LocalDate.now() <= new LocalDate("2019-06-02") && quoteCommand.policyStartDate <= new LocalDate("2019-06-02")
            && quoteCommand.registrationCityId in [CityEnum.ABU_DHABI.value(), CityEnum.DUBAI.value(),
                                                   CityEnum.AL_AIN.value(), CityEnum.SHARJAH.value()]) {

            int vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId
            if (vehicleTypeId == VehicleTypeEnum.SEDAN.value) {
                if (rateCommand.agencyRepair) {
                    baseRate = 2.45
                    minPremium = 1500
                } else {
                    baseRate = 1.90
                    minPremium = 1300
                }
            } else if (vehicleTypeId == VehicleTypeEnum.FOURx4.value) {
                if (rateCommand.agencyRepair) {
                    baseRate = 2.45
                    minPremium = 2000
                } else {
                    baseRate = 1.90
                    minPremium = 2000
                }
            }  else if (vehicleTypeId in [VehicleTypeEnum.COUPE.value, VehicleTypeEnum.CONVERTIBLE.value]) {
                if (rateCommand.agencyRepair) {
                    baseRate = 2.75
                    minPremium = 2500
                } else {
                    baseRate = 2.45
                    minPremium = 2000
                }
            }

        }
        rateCommand.baseRate = baseRate
        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            //2 years from  the registration date
            if ((quoteCommand.isBrandNew || quoteCommand.carAge <= 1)
                && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                isAgency = true
            } else if (quoteCommand.carAge == 2 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()
                && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                isAgency = true
            }
        }
        return isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        //This check is for both comprehensive and third party
        //Local Driving license experience not less than one year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }

        if (quoteCommand.isNonGccSpec && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            isEligible = false
        }

        isEligible
    }

    RateCommand applyTplDiscounts(QuoteCommand quoteCommand, RateCommand rateCommand) {

        int customerAge = quoteCommand.customerAge

        //promotion discount until End of Ramadan
        if (LocalDate.now() <= new LocalDate("2019-06-02") && quoteCommand.policyStartDate <= new LocalDate("2019-06-02")) {
            rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (0.10))
        }

        rateCommand
    }

    RateCommand applyCovers(QuoteCommand quoteCommand, RateCommand rateCommand) {

        //promotion discount until End of Ramadan
        if (LocalDate.now() <= new LocalDate("2019-06-02") && quoteCommand.policyStartDate <= new LocalDate("2019-06-02")) {
            rateCommand.thirdPartyDamageLimit = 'Up to AED 3.7 Million'
            rateCommand.thirdPartyLiabilityAmount = 3700000
            rateCommand.windScreenCover = '3000'
            rateCommand.naturalCalamity = 'yes'

            rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.getById(30).getName(quoteCommand.locale))
            rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.getById(25).getName(quoteCommand.locale))
            rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.getById(10).getName(quoteCommand.locale))
            rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.getById(16).getName(quoteCommand.locale))

        }

        rateCommand
    }

    RateCommand applyTplCovers(QuoteCommand quoteCommand, RateCommand rateCommand) {

        //promotion discount until End of Ramadan
        if (LocalDate.now() <= new LocalDate("2019-06-02") && quoteCommand.policyStartDate <= new LocalDate("2019-06-02")) {
            rateCommand.thirdPartyDamageLimit = 'Up to AED 3.7 Million'
            rateCommand.thirdPartyLiabilityAmount = 3700000
            rateCommand.breakdownCover = 'silver'
        }

        rateCommand
    }
}

