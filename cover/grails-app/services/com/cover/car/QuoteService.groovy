package com.cover.car


import com.cover.api.CarQuoteDetailV2Command
import com.cover.api.CarQuoteV2Command
import com.cover.api.DrivingLicenseDocumentCommand
import com.cover.api.EmiratesIdDocumentCommand
import com.cover.api.UserDocumentsCommand
import com.cover.api.VehicleRegistrationDocumentCommand
import com.cover.car.commands.DriverCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.commands.VehicleCommand
import com.cover.util.UtilService
import com.safeguard.*
import com.safeguard.base.Comparison
import com.safeguard.car.*
import com.safeguard.car.translation.AddonTranslation
import com.safeguard.car.vehicle.*
import com.safeguard.docs.DrivingLicenseDocument
import com.safeguard.docs.EmiratesIdentityDocument
import com.safeguard.docs.VehicleRegistrationDocument
import com.safeguard.health.HealthQuote
import com.safeguard.history.LeadTypeHistory
import com.safeguard.home.HomeQuote
import com.safeguard.payment.RecurringPayment
import com.safeguard.util.ConfigurationService
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.async.Promise
import grails.gorm.DetachedCriteria
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.grails.datastore.mapping.query.api.BuildableCriteria
import org.hibernate.FetchMode
import org.joda.time.DateTime
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import static grails.async.Promises.tasks

import java.math.RoundingMode

@Transactional
class QuoteService {

    def apiQuoteService
    def additionalChargesService
    def adntRateService
    def aiawRateService
    def unionRateService
    def gatewayRateService
    def sukoonRateService
    def ascanaRateService
    def qatarRateService
    def qatarRateV2Service
    def noorRateService
    def wataniaRateService
    def dubaiRateService
    def alSagrRateService
    def salamaRateService
 //   def wathbaRateService
    def wathbaRateV2Service
    def adamjeeRateService
    //def adamjeeRateV2Service
    def orientalRateService
    def dubaiNationalRateV2Service
    def darRateService
    def amanRateService
    def gigRateService
    def gigRateV2Service
    def ratingService
    def utilService
    def commonUtilService
    def checkoutService
    def grailsApplication
    def crmService
    def paymentService
    def alBuhairaRateService
    def alFujairahNICRatingService
    def alHilalTakafulRateService
    def methaqRatingService
    //def newIndiaRateService
    def newIndiaRateV2Service
    def insuranceHouseRateService
    def dubaiNationalRateService
    def tokioMarineRateService
    def tokioMarineRateV2Service
    def adnicRateService
    def messageSource
    def leadSgService
    def commonPolicySgService
    def configurationService
    def noorRateV2Service
    def qicApiService
    def rakRateV2Service
    def valuationService
    def orientRateService
    def quoteSgService
    def wathbaApiService
    def yasTakafulRateService

    def lifeQuoteService
    def lifeUtilService
    def sessionService

    @Transactional(readOnly = true)
    List<RateCommand> getRatings(QuoteCommand quoteCommand, boolean isOffline) {
        log.info "quote.service.getRatings - ${quoteCommand}"

        List<RateCommand> rateList = []
        List synchronizedRateList = Collections.synchronizedList(rateList)

        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.vechileTypeId = model.vehicleType.id
        quoteCommand.productId = null
        quoteCommand.providerId = null

        List<RateCommand> ratings

        Promise<List<List<RateCommand>>> compRatingPromises = tasks([
            {
                rakRateV2Service.getRates(quoteCommand.clone(), isOffline)
            },
            {
                qatarRateV2Service.getRates(quoteCommand.clone(), isOffline)
            },
            //{
                //adamjeeRateV2Service.getRates(quoteCommand.clone(), isOffline)
            //},
            {
                newIndiaRateV2Service.getRates(quoteCommand.clone(), isOffline)
            },
            {
                tokioMarineRateV2Service.getRates(quoteCommand.clone(), isOffline)
            },
            {
                gigRateV2Service.getRates(quoteCommand.clone(), isOffline)
            },
            {
                dubaiNationalRateV2Service.getRates(quoteCommand.clone(), isOffline)
            },
            {
                wathbaRateV2Service.getRates(quoteCommand.clone(), isOffline)
            }
        ])


        Promise<List<RateCommand>> tplRatingPromises = tasks ([{
            qatarRateV2Service.getTplRate(quoteCommand.clone(), isOffline)
        }, {
            //adamjeeRateV2Service.getTplRate(quoteCommand.clone(), isOffline)
        }])

        List<RateCommand> dubaiRatings = dubaiRateService.getRates(quoteCommand, isOffline)
        rateList.addAll(dubaiRatings)
        rateList.addAll(getNonAgencyRatings(quoteCommand, dubaiRatings, dubaiRateService))

        if (quoteCommand.valuationSource) {

            List<RateCommand> unionRatings = unionRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(unionRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, unionRatings, unionRateService))

            List<RateCommand> noorRatings = noorRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(noorRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, noorRatings, noorRateService))

            List<RateCommand> adamjeeRatings = adamjeeRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(adamjeeRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, adamjeeRatings, adamjeeRateService))

            //rateList.addAll(gatewayRateService.getRates(quoteCommand, isOffline))
            List<RateCommand> omanRatings = sukoonRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(omanRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, omanRatings, sukoonRateService))

//            List<RateCommand> dubaiNationalRatings = dubaiNationalRateService.getRates(quoteCommand, isOffline)
//            rateList.addAll(dubaiNationalRatings)
//            rateList.addAll(getNonAgencyRatings(quoteCommand, dubaiNationalRatings, dubaiNationalRateService))

//            rateList.addAll(getNonAgencyRatings(quoteCommand, qatarRatings, qatarRateV2Service))


            List<RateCommand> alSagrRatings = alSagrRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(alSagrRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, alSagrRatings, alSagrRateService))

            List<RateCommand> adnicRatings = adnicRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(adnicRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, adnicRatings, adnicRateService))

//            List<RateCommand> wathabaRatings = wathbaRateService.getRates(quoteCommand, isOffline)
//            rateList.addAll(wathabaRatings)
//            rateList.addAll(getNonAgencyRatings(quoteCommand, wathabaRatings, wathbaRateService))

            List<RateCommand> orientalRatings = orientalRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(orientalRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, orientalRatings, orientalRateService))

            List<RateCommand> darRatings = darRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(darRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, darRatings, darRateService))

            List<RateCommand> amanRatings = amanRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(amanRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, amanRatings, amanRateService))

            List<RateCommand> adntRatings = adntRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(adntRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, adntRatings, adntRateService))

            List<RateCommand> alBuhairaRatings = alBuhairaRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(alBuhairaRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, alBuhairaRatings, alBuhairaRateService))

            List<RateCommand> alFujairahRatings = alFujairahNICRatingService.getRates(quoteCommand, isOffline)
            rateList.addAll(alFujairahRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, alFujairahRatings, alFujairahNICRatingService))

            List<RateCommand> alHilalTakafulRatings = alHilalTakafulRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(alHilalTakafulRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, alHilalTakafulRatings, alHilalTakafulRateService))

            List<RateCommand> methaqRatings = methaqRatingService.getRates(quoteCommand, isOffline)
            rateList.addAll(methaqRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, methaqRatings, methaqRatingService))

            List<RateCommand> insuranceHouseRatings = insuranceHouseRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(insuranceHouseRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, insuranceHouseRatings, insuranceHouseRateService))

//        List<RateCommand> tokioMarineRatings = tokioMarineRateService.getRates(quoteCommand, isOffline)
//        rateList.addAll(tokioMarineRatings)
//        rateList.addAll(getNonAgencyRatings(quoteCommand, tokioMarineRatings, tokioMarineRateService))

            // No need to query again for Non agency, as getRates() return all products
            // rateList.addAll(getNonAgencyRatings(quoteCommand, newIndiaRatings, newIndiaRateV2Service))

            List<RateCommand> orientRatings = orientRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(orientRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, orientRatings, orientRateService))

            List<RateCommand> yasTakafulRatings = yasTakafulRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(yasTakafulRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, yasTakafulRatings, yasTakafulRateService))

            List<RateCommand> aiawRatings = aiawRateService.getRates(quoteCommand, isOffline)
            rateList.addAll(aiawRatings)
            rateList.addAll(getNonAgencyRatings(quoteCommand, aiawRatings, aiawRateService))


//          RateCommand dubaiNationalCmd = dubaiNationalRateService.getTplRate(quoteCommand, isOffline)
//          if (dubaiNationalCmd) {
//              rateList.add(dubaiNationalCmd)
//          }

            RateCommand noorTplCmd = noorRateService.getTplRate(quoteCommand, isOffline)
            if (noorTplCmd) {
                rateList.add(noorTplCmd)
            }

            RateCommand sagrCmd = alSagrRateService.getTplRate(quoteCommand, isOffline)
            if (sagrCmd) {
                rateList.add(sagrCmd)
            }

            RateCommand ascanaCmd = ascanaRateService.getTplRate(quoteCommand, isOffline)
            if (ascanaCmd) {
                rateList.add(ascanaCmd)
            }

//            RateCommand wathbaCmd = wathbaRateService.getTplRate(quoteCommand, isOffline)
//            if (wathbaCmd) {
//                rateList.add(wathbaCmd)
//            }

            RateCommand amanCmd = amanRateService.getTplRate(quoteCommand, isOffline)
            if (amanCmd) {
                rateList.add(amanCmd)
            }

            RateCommand orientalCmd = orientalRateService.getTplRate(quoteCommand, isOffline)
            if (orientalCmd) {
                rateList.add(orientalCmd)
            }

            RateCommand adntCmd = adntRateService.getTplRate(quoteCommand, isOffline)
            if (adntCmd) {
                rateList.add(adntCmd)
            }

            RateCommand adamjeeTplCmd = adamjeeRateService.getTplRate(quoteCommand, isOffline)
            if (adamjeeTplCmd) {
                rateList.add(adamjeeTplCmd)
            }

            RateCommand alBuhCmd = alBuhairaRateService.getTplRate(quoteCommand, isOffline)
            if (alBuhCmd) {
                rateList.add(alBuhCmd)
            }

            RateCommand alFujCmd = alFujairahNICRatingService.getTplRate(quoteCommand, isOffline)
            if (alFujCmd) {
                rateList.add(alFujCmd)
            }

            RateCommand alDarCmd = darRateService.getTplRate(quoteCommand, isOffline)
            if (alDarCmd) {
                rateList.add(alDarCmd)
            }

            RateCommand alHilalTakafulCmd = alHilalTakafulRateService.getTplRate(quoteCommand, isOffline)
            if (alHilalTakafulCmd) {
                rateList.add(alHilalTakafulCmd)
            }

            //methaqRatingService
            RateCommand alMethaqCmd = methaqRatingService.getTplRate(quoteCommand, isOffline)
            if (alMethaqCmd) {
                rateList.add(alMethaqCmd)
            }

            RateCommand insuranceHouseCmd = insuranceHouseRateService.getTplRate(quoteCommand, isOffline)
            if (insuranceHouseCmd) {
                rateList.add(insuranceHouseCmd)
            }

            RateCommand dubaiTplCmd = dubaiRateService.getTplRate(quoteCommand, isOffline)
            if (dubaiTplCmd) {
                rateList.add(dubaiTplCmd)
            }

//        RateCommand tokioTplCmd = tokioMarineRateService.getTplRate(quoteCommand, isOffline)
//        if (tokioTplCmd) {
//            rateList.add(tokioTplCmd)
//        }

            RateCommand orientTplCmd = orientRateService.getTplRate(quoteCommand, isOffline)
            if (orientTplCmd) {
                rateList.add(orientTplCmd)
            }

        }

        List<RateCommand> salamaRatings = salamaRateService.getRates(quoteCommand, isOffline)
        rateList.addAll(salamaRatings)
        rateList.addAll(getNonAgencyRatings(quoteCommand, salamaRatings, salamaRateService))

        RateCommand salamaCmd = salamaRateService.getTplRate(quoteCommand, isOffline)
        if (salamaCmd) {
            rateList.add(salamaCmd)
        }

        RateCommand sukoonCmd = sukoonRateService.getTplRate(quoteCommand, isOffline)
        if (sukoonCmd) {
            rateList.add(sukoonCmd)
        }

        /*RateCommand gigTplCmd = gigRateService.getTplRate(quoteCommand, isOffline)
        if (gigTplCmd) {
            rateList.add(gigTplCmd)
        }*/

        boolean hasFoundGigTPL = false
        if (compRatingPromises) {
            compRatingPromises.get().each { List<RateCommand> rateCommands ->
                if (rateCommands)
                    if (rateCommands.find {it.productId.intValue() == GigRateV2Service.PRODUCT_TPL_ID}) {
                        hasFoundGigTPL = true
                    }

                    rateList.addAll(rateCommands)
            }
        }

        tplRatingPromises.get().each { RateCommand rateCommand ->
            if (rateCommand) {
                rateList.add(rateCommand)
            }
        }

        // remove null rates may be got from other internal methods.
        rateList.removeAll([null])

        if (hasFoundGigTPL) {
            //Remove Sukoon Rate if GIG is found
            rateList = rateList.findAll { it.productId != SukoonRateService.PRODUCT_TPL_ID}
        }
        return rateList

        /*List<RateCommand> rateCommandsAfterAdditionCharges = additionalChargesService
            .addAdditionalChargesToPremiums(rateList, ProductType.findByName('CAR'))

        return rateCommandsAfterAdditionCharges*/
    }

    private List<RateCommand> getNonAgencyRatings(QuoteCommand quoteCommand, List<RateCommand> ratings,
                                                  def rateService) {
        List<RateCommand> nonAgencyRatings = []
        if (ratings) {
            ratings.each { RateCommand rateCommand ->
                // for union 2 agency products.
                if (rateCommand.isAgencyRepair() && !(rateCommand.productId in [3, 89])) {
                    QuoteCommand newQuoteCommand = new QuoteCommand()
                    InvokerHelper.setProperties(newQuoteCommand, quoteCommand.properties)

                    newQuoteCommand.selectedRepairType = RepairTypeEnum.GARAGE

                    newQuoteCommand.productId = rateCommand.productId

                    RateCommand nonAgencyRating = rateService.getRate(newQuoteCommand, false)

                    if (nonAgencyRating){
                        nonAgencyRatings.add(nonAgencyRating)
                    }

                }
            }
        }

        return nonAgencyRatings
    }

    @Transactional(readOnly = true)
    RateCommand getRating(CarQuote carQuote, Integer productId = null, RepairTypeEnum selectedRepairType = null, String standAloneDiscountCode = null, String standAloneDiscountGroup = null, boolean isOffline = false) {
        log.info("${UtilService.getQuoteLoggingPrefix('getRating', carQuote.id)} - carQuoteProductId ${carQuote.productId}, product: ${productId}, selectedRepairType:${selectedRepairType}")

        QuoteCommand quoteCommand = toQuoteCommand(carQuote)
        if (selectedRepairType) {
            quoteCommand.selectedRepairType = selectedRepairType
        } /*else {
            quoteCommand.selectedRepairType = carQuote.isAgencyRepair ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE
        }*/
        if (standAloneDiscountCode) {
            quoteCommand.standAloneDiscountCode = standAloneDiscountCode
        } else if (standAloneDiscountGroup) {
            quoteCommand.standAloneDiscountGroup = standAloneDiscountGroup
        }
        quoteCommand.productId = productId ?: carQuote.productId

        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.vechileTypeId = model.vehicleType.id

        RateCommand rateCommand = null

        // CASE#01: Creating rateCommand for renewal product
        if (carQuote.isRenewal) {
            Renewal renewal = Renewal.findByCarQuote(carQuote)
            if (renewal && quoteCommand.productId == (Integer) renewal.productId &&
                    !(renewal.product.providerId.toInteger() == SalamaRateService.PROVIDER_ID &&
                    carQuote.model.vehicleTypeId.intValue() in [VehicleTypeEnum.COUPE.value,
                                                            VehicleTypeEnum.CONVERTIBLE.value])) {
                log.info("${UtilService.getQuoteLoggingPrefix('getRating', quoteCommand.quoteId)} - Creating rateCommand for renewal")

                RateCommand renewalRateCommand = new RateCommand(productId: renewal.productId)
                renewalRateCommand.currency = quoteCommand.currency
                quoteCommand.providerId = renewal.product.providerId
                ratingService.applyCovers(quoteCommand, renewalRateCommand)
                renewalRateCommand.toRenewal(renewal)
                renewalRateCommand = ratingService.applyRenewalCover(quoteCommand, renewalRateCommand)
                renewalRateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(renewalRateCommand)
                ratingService.applyC4meFees(renewalRateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
                renewalRateCommand = ratingService.applyExtraDiscount(quoteCommand, renewalRateCommand, renewalRateCommand.providerId)

                Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
                if (pricesInclusiveVat) {
                    renewalRateCommand = ratingService.applyVAT(renewalRateCommand)
                }

                rateCommand = renewalRateCommand
            }

        }

        // CASE#02: Fetching rateCommand from API/DB
        if (!rateCommand) {
            log.info("${UtilService.getQuoteLoggingPrefix('getRating', quoteCommand.quoteId)} - Fetching rateCommand from API/DB")
            rateCommand = ratingService.getRating(quoteCommand, isOffline)
        }

        if (!rateCommand) {
            log.warn "${UtilService.getQuoteLoggingPrefix('getRating', quoteCommand.quoteId)} - rateCommad is null so returning null"
            return null
        }

        rateCommand.carAge = quoteCommand.getCarAge()
        rateCommand.carQuoteId = carQuote.id

        return rateCommand
    }

    @Transactional(readOnly = true)
    RateCommand getRating(QuoteCommand quoteCommand) {
        log.info "quote.getRating - with quoteCommand - ${quoteCommand}"

        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.vechileTypeId = model.vehicleType.id

        RateCommand rateCommand = ratingService.getRating(quoteCommand, false)

        if (!rateCommand) {
            log.warn "quote.getRating - quoteCommand ${quoteCommand}, rateCommad is null so returning null"
            return null
        }

        rateCommand.carAge = quoteCommand.getCarAge()

        return rateCommand
    }


    @Transactional(readOnly = true)
    getRatings(Integer quoteId, boolean isOffline = false, String standAloneDiscountCode = null, String standAloneDiscountGroup = null) {
        log.info "quote.service.getRatings with quoteId - ${quoteId}, isOffline - ${isOffline}"

        CarQuote carQuote = CarQuote.get(quoteId)
        List<RateCommand> ratings = []
        if (carQuote) {
            QuoteCommand quoteCommand = toQuoteCommand(carQuote)
            if (standAloneDiscountCode) {
                quoteCommand.standAloneDiscountCode = standAloneDiscountCode
            } else if (standAloneDiscountGroup) {
                quoteCommand.standAloneDiscountGroup = standAloneDiscountGroup
            }

            if (quoteCommand.modelId == QuoteCommand.UNKNOWN_MODEL_ID) {
                return [[], carQuote]
            }

            ratings = getRatings(quoteCommand, isOffline)

            if (carQuote.isRenewal) {

                Renewal renewal = Renewal.findByCarQuote(carQuote)

                if (renewal && !(renewal.product.providerId.toInteger() == SalamaRateService.PROVIDER_ID &&
                                carQuote.model.vehicleTypeId.intValue() in [VehicleTypeEnum.COUPE.value,
                                                                            VehicleTypeEnum.CONVERTIBLE.value])) {
                    //Remove renewal product and products from same provider
                    ratings = ratings.findAll {
                        Product p = Product.read(it.productId)

                        (p.providerId != renewal.product.providerId && p.id != renewal.productId)
                    }

                    Provider renewalProvider = Provider.load(renewal.product.providerId)

                    if (renewal.hasPreviousProvider && renewalProvider?.showForCar) {
                        RateCommand renewalRateCommand = new RateCommand(productId: renewal.productId)
                        renewalRateCommand.currency = quoteCommand.currency
                        quoteCommand.providerId = renewal.product.providerId
                        ratingService.applyCovers(quoteCommand, renewalRateCommand)
                        renewalRateCommand.toRenewal(renewal)
                        renewalRateCommand = ratingService.applyRenewalCover(quoteCommand, renewalRateCommand)
                        renewalRateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(renewalRateCommand)
                        ratingService.applyC4meFees(renewalRateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

                        renewalRateCommand = ratingService.applyExtraDiscount(quoteCommand, renewalRateCommand, renewalRateCommand.providerId)

                        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
                        if (pricesInclusiveVat) {
                            renewalRateCommand = ratingService.applyVAT(renewalRateCommand)
                        }

                        ratings << renewalRateCommand //Adding renewal product
                    }

                }
            }
        }

        return [ratings, carQuote]
    }

    /**
     * Create Car Quote with provided data
     *
     * @param driverCommand
     * @param year
     * @param model
     * @param registrationCity
     * @param firstRegistrationDate
     * @param insuredValue
     * @param isNonGcc
     * @param isExpiredPolicy
     * @param isThirdParty
     * @param isOldAgency
     * @param queryString
     * @param insuredValueMin
     * @param insuredValueMax
     * @param policyExpiryDate
     * @param valuationSource
     * @param countryEnum
     * @param language
     * @return
     */
    def createCarQuote(DriverCommand driverCommand, year, model, String makeModelTrim, registrationCity, firstRegistrationDate, insuredValue,
                       isNonGcc, isExpiredPolicy, isThirdParty, isOldAgency, purchaseDate, isNotBoughtYet, isBrandNew, isFirstCar,
                       isBuyingUsedCar, queryString, insuredValueMin, insuredValueMax, policyExpiryDate, CountryEnum countryEnum,
                       String language, String chassisNumber, String plateNumber, String plateCode, Integer plateCategory, ValuationSourceEnum valuationSource = ValuationSourceEnum.YC,
                       Long financeInstitutionId, String financeInstitutionName,
                       String requestSource = RequestSourceEnum.WEB.toString(), Boolean campaignInterest = null,
                       Long campaignId = null, String dealerId = null, Long mileage = null, VehicleConditionEnum vehicleCondition = null,
                       Boolean communicationOptIn = false, CarQuote duplicatedFromQuote = null,
                       User createdBySalesPerson = null, String autodataSpec = null,
                       String admeId = null, SubRequestSourceEnum subRequestSource = null) {

        requestSource = requestSource ?: RequestSourceEnum.WEB.toString()
        User user = User.findByEmail(driverCommand.email)
        Model modelObj = Model.findById(model)
        City registrationCityObj = null
        if (registrationCity) {
            registrationCityObj = City.get(registrationCity)
        }
        Country licenseCountryObj = Country.get(driverCommand.firstLicenseCountry)
        Country nationalityObj = Country.get(driverCommand.nationality)
        DrivingExperience internationalExperienceObj = DrivingExperience.get(driverCommand.internationalExperience)
        DrivingExperience localExperienceObj = DrivingExperience.get(driverCommand.localExperience)
        NoClaimDiscount noClaimDiscountObj = NoClaimDiscount.get(driverCommand.ncd)
        Boolean newUser = false
        Country country = Country.get(countryEnum.id)
        CarFinanceInstitution financeInstitution = financeInstitutionId ?
            CarFinanceInstitution.get(financeInstitutionId) : null

        if (!user) {
            //Create new user if user is not found in db
            user = new User(email: driverCommand.email, password: 'quote', leadType: LeadType.NORMAL)
            newUser = true
        }

        if (!user.isEnabled()) {
            user.name = driverCommand.name
            user.dob = new LocalDate(driverCommand.dob)
            user.nationality = nationalityObj
            user.country = country
        }

        if (!user.userDetails || !user.userDetails.mobileVerified) {
            user.mobile = driverCommand.mobile
        }

        if (!user.leadType) {
            user.leadType = LeadType.NORMAL
        }

        if (user.leadType == LeadType.CLOSED_LOST_FEEDBACK) {
            List<LeadTypeHistory> leadTypeHistoryList = LeadTypeHistory.findAllByNewTypeAndUser(LeadType.CLOSED_LOST_FEEDBACK, user, [sort: "dateCreated", order: "desc", max: 1])
            LeadType newLeadType = LeadType.NORMAL
            if (leadTypeHistoryList) {
                LeadTypeHistory leadTypeHistory = leadTypeHistoryList.first()
                newLeadType = leadTypeHistory.oldType ?: LeadType.NORMAL
            }

            user.changeLeadType(newLeadType, ProductTypeEnum.CAR.name().toLowerCase(), "WEB")
        }

        user.save(flush: true, failOnError: true)
        notify AsyncEventConstants.LEAD_MOBILE_MAPPING, [userId: user.id, phone: user.mobile]

        log.info("quote.claimPeriod - ${driverCommand.lastClaimPeriod}")

        RequestSourceEnum requestSourceEnum = RequestSourceEnum.findRequestSource(requestSource)
        WhiteLabelBrand whiteLabelBrand = WhiteLabelBrand.read(WhiteLabelBrandEnum.getWhiteLabelBrandEnumFromRequestSource(requestSourceEnum).id)

        //Create and save quote object with details
        CarQuote quote = new CarQuote(user: user, year: year, model: modelObj, registrationCity: registrationCityObj,
            firstRegistrationDate: new LocalDate(firstRegistrationDate), insuredValue: insuredValue, isNonGcc: isNonGcc,
            isExpiredPolicy: isExpiredPolicy, isThirdParty: isThirdParty, nationality: nationalityObj,
            licenseCountry: licenseCountryObj, internationalExperience: internationalExperienceObj,
            localExperience: localExperienceObj, policyStartDate: new LocalDate(driverCommand.policyStartDate),
            noClaimDiscount: noClaimDiscountObj, quoteCountry: country, isFirstCar: isFirstCar,
            currency: country.currency, isOldAgency: isOldAgency, email: driverCommand.email,
            name: driverCommand.name, dob: new LocalDate(driverCommand.dob), mobile: driverCommand.mobile,
            hasClaim: driverCommand.hasClaim, purchaseDate: purchaseDate, isNotBoughtYet: isNotBoughtYet,
            isBrandNew: isBrandNew, queryString: driverCommand.marketingTracking.queryString,
            utmSource: driverCommand.marketingTracking.utmSource, utmMedium: driverCommand.marketingTracking.utmMedium,
            utmCampaign: driverCommand.marketingTracking.utmCampaign, gclid: driverCommand.marketingTracking.gclid,
            fbclid: driverCommand.marketingTracking.fbclid, lang: language, requestSource: requestSourceEnum,
            insuredValueMin: insuredValueMin, insuredValueMax: insuredValueMax, claimsInTheLastYear: driverCommand.claimsInTheLastYear,
            coverPreference: driverCommand.coverPreference, valuationSource: valuationSource, isBuyingUsedCar: isBuyingUsedCar,
            financeInstitution: financeInstitution, financeInstitutionName: financeInstitutionName,
            chassisNumber: chassisNumber, plateNumber: plateNumber, plateCode: plateCode, plateCategory: plateCategory,
            dealer: Dealer.load(dealerId), lastClaimPeriod: driverCommand.lastClaimPeriod, mileage: mileage,
            vehicleCondition: vehicleCondition, communicationOptIn: communicationOptIn, duplicatedFromQuote: duplicatedFromQuote, createdBySalesPerson: createdBySalesPerson)

        if (isExpiredPolicy) {
            quote.oldPolicyExpiryDate = policyExpiryDate ? new LocalDate(policyExpiryDate) : null
        } else {
            quote.oldPolicyExpiryDate = null
        }

        quote.type = user.leadType
        quote.save(failOnError: true, flush: true)

        paymentService.changePaymentStatus(quote, PaymentStatusEnum.DRAFT)

        log.debug "testing user data before save ${user.id} ${user.email} ${user.name} ${user.password}"
        quote.user.password = quote.user.password ?: 'whynull'

        // tagging mysyara lead, if the lead was created offline by an agent
        if (user.userDetails?.createLeadReason == CreateLeadReasonEnum.MYSYARA && !quote.utmSource && !CarQuote.countByUser(user)) {
            quote.utmSource = "mysyara"
        }
        quote.save(flush: true, failOnError: true)

        if (makeModelTrim) {
            quote.model = Model.read(QuoteCommand.UNKNOWN_MODEL_ID)
            quote.save(flush: true, failOnError: true)

            QuoteExtraField extraField = new QuoteExtraField()
            extraField.quoteId = quote.id
            extraField.insuranceType = InsuranceTypeEnum.CAR
            extraField.extraFieldCode = ExtraFieldCodeEnum.MAKE_MODEL_TRIM
            extraField.extraFieldValue = makeModelTrim
            extraField.save(failOnError: true)
        }

        if (autodataSpec && autodataSpec.contains(":")) {
            String[] autodataIdDesc = autodataSpec.split(":")
            quoteSgService.handleChangeInAutoDataSpecForQuote(quote.model, autodataIdDesc[0], autodataIdDesc[1], quote)
        } else if (admeId) {
            AutoDataTrim autoDataTrim = AutoDataTrim.findByAdmeId(admeId, [sort:'id', order:'desc'])
            quoteSgService.handleChangeInAutoDataSpecForQuote(quote.model, admeId, autoDataTrim.trimDescription, quote)
        }

        if (subRequestSource) {
            QuoteExtraField extraField = new QuoteExtraField()
            extraField.quoteId = quote.id
            extraField.insuranceType = InsuranceTypeEnum.CAR
            extraField.extraFieldCode = ExtraFieldCodeEnum.SUB_REQUEST_SOURCE
            extraField.extraFieldValue = subRequestSource
            extraField.save(failOnError: true)
        }

        Comparison comparison = leadSgService.createLeadAndComparison(quote)
        if (createdBySalesPerson) {
            comparison.creator = CreatorEnum.SALES_PERSON
            comparison.save()
        }

        UserDetails userDetails = UserDetails.findByUser(user)
        if (!createdBySalesPerson) {
            if ((userDetails && userDetails.carStatusReason)) {
                //Stopping the re open of lead with wrong number and wrong email
                if (userDetails.carStatusReason.id.intValue() != LeadStatusReasonEnum.WRONG_EMAIL.value()
                    && userDetails.carStatusReason.id.intValue() != LeadStatusReasonEnum.WRONG_NUMBER.value()) {
                    crmService.handleCrmEvents(user.id, 'car', quote)
                }
            } else {
                crmService.handleCrmEvents(user.id, 'car', quote)
            }
        }

        if (User.countByPhoneLead(user.phoneLead) > 1) {
            newUser = false
        }

        if (newUser && Boolean.valueOf(configurationService.getValue(ConfigurationService.ENABLE_MARKETING_NORMAL_LEADS_ASSIGNMENT))) {
            User salesPerson = User.findByEmail(configurationService.getValue(ConfigurationService.MARKETING_NORMAL_LEADS_AGENT_EMAIL))
            user.assignTo(salesPerson, "QuoteService.createCarQuote", ProductTypeEnum.CAR)
            user.save()
        }

        CarQuoteDetail carQuoteDetail = new CarQuoteDetail(
            crmStatus: CrmStatusEnum.DRAFT,
            crmStatusUpdated: LocalDateTime.now(),
            quote: quote,
            campaignInterest: campaignInterest,
            campaignId: campaignId
        )
        carQuoteDetail.save(failOnError: true)

        CarQuoteCrmStatus carQuoteCrmStatus = new CarQuoteCrmStatus(
            crmStatus: CrmStatusEnum.DRAFT,
            quote: quote
        )
        carQuoteCrmStatus.save(failOnError: true)

        [quote, newUser]
    }

    QuoteCommand toQuoteCommand(CarQuote quote) {
        QuoteCommand quoteCommand = new QuoteCommand(
            quoteId: quote.id,
            manufactureYear: quote.year,
            makeId: quote.model.make.id,
            modelId: quote.modelId,
            registrationCityId: quote.registrationCityId,
            firstRegistrationDate: quote.firstRegistrationDate,
            insuredValue: quote.insuredValue,
            isNonGccSpec: quote.isNonGcc,
            isPolicyExpired: quote.isExpiredPolicy,
            oldPolicyExpiryDate: quote.oldPolicyExpiryDate,
            nationalityId: quote.nationalityId,
            firstLicenseCountryId: quote.licenseCountryId,
            localDrivingExperienceId: quote.localExperienceId,
            internationalDrivingExperienceId: quote.internationalExperienceId,
            policyStartDate: quote.policyStartDate,
            noClaimsDiscountId: quote.noClaimDiscountId,
            name: quote.name,
            email: quote.email,
            dob: quote.dob,
            vechileTypeId: quote.model.vehicleTypeId,
            productId: quote.productId,
            isBrandNew: quote.isBrandNew,
            isFirstCar: quote.isFirstCar,
            claimsInTheLastYear: quote.claimsInTheLastYear,
            lastClaimPeriod: quote.lastClaimPeriod,
            currency: quote.currency,
            countryEnum: CountryEnum.findCountryByDfp(quote.quoteCountry.code),
            locale: quote.lang,
            requestSource: quote.requestSource,
            leadType: quote.type,
            noOfCyl: quote.model.noOfCyl,
            discountCodeFromAgentId: CarQuoteDetail.findByQuote(quote)?.discountCodeFromAgent?.id,
            autoDataSpecId: QuoteExtraField.findByQuoteIdAndInsuranceTypeAndExtraFieldCode(quote.id,
                InsuranceTypeEnum.CAR, ExtraFieldCodeEnum.AUTODATA_SPEC_ID)?.extraFieldValue,
            autoDataSpecDesc: QuoteExtraField.findByQuoteIdAndInsuranceTypeAndExtraFieldCode(quote.id,
                InsuranceTypeEnum.CAR, ExtraFieldCodeEnum.AUTODATA_SPEC_ID)?.extraFieldDescription,
            valuationSource: quote.valuationSource,
            insuredValueMin: quote.insuredValueMin,
            insuredValueMax: quote.insuredValueMax,
            requestSubSource: quote.requestSubSource,
            carValuation: new CarValuationDto(valuationSource: quote.valuationSource,
                valuationMin: quote.insuredValueMin, valuationMax: quote.insuredValueMax)
        )

        if (quote.hasClaim != null) {
            quoteCommand.hasClaim = quote.hasClaim
            if (quoteCommand.hasClaim == true) {
                quoteCommand.lastClaimPeriod = ClaimPeriodEnum.TWELVE_MONTHS
            }
        }
        //If no claim period is defined but old quote had claim information
        if (!quoteCommand.lastClaimPeriod && quoteCommand.hasClaim == false && quote.oldQuote) {
            if (quote.oldQuote.hasClaim) {
                quoteCommand.lastClaimPeriod = ClaimPeriodEnum.TWENTY_FOUR_MONTHS
            } else {
                quoteCommand.lastClaimPeriod = ClaimPeriodEnum.THIRTY_SIX_MONTHS
            }
        }

        if (quote.isThirdParty != null) {
            quoteCommand.isThirdParty = quote.isThirdParty
        }
        if (quote.isOldAgency != null) {
            quoteCommand.isOldAgency = quote.isOldAgency
        }
        log.info("quoteCommand.carValuation:${quoteCommand.carValuation}")
        quoteCommand
    }

    /**
     * Returns QuoteCommand object from CarQuoteCommand object
     * @param quote
     * @return
     */
    QuoteCommand toQuoteCommand(Model model, VehicleCommand vehicleCommand,
                                DriverCommand driverCommand, productId, country, lang, String source) {

        QuoteCommand quoteCommand = new QuoteCommand(
            manufactureYear: vehicleCommand.year,
            makeId: model.make.id,
            modelId: model.id,
            registrationCityId: vehicleCommand.city,
            firstRegistrationDate: new LocalDate(vehicleCommand.firstRegistrationDate),
            insuredValue: vehicleCommand.insuredValue,
            isNonGccSpec: vehicleCommand.isNonGcc,
            isPolicyExpired: vehicleCommand.isExpiredPolicy,
            nationalityId: driverCommand.nationality,
            firstLicenseCountryId: driverCommand.firstLicenseCountry,
            localDrivingExperienceId: driverCommand.localExperience,
            internationalDrivingExperienceId: driverCommand.internationalExperience,
            policyStartDate: new LocalDate(driverCommand.policyStartDate),
            noClaimsDiscountId: driverCommand.ncd,
            lastClaimPeriod: driverCommand.lastClaimPeriod,
            name: driverCommand.name,
            email: driverCommand.email,
            dob: new LocalDate(driverCommand.dob),
            productId: productId,
            vechileTypeId: model.vehicleTypeId,
            isBrandNew: vehicleCommand.isBrandNew,
            isFirstCar: vehicleCommand.isFirstCar,
            claimsInTheLastYear: driverCommand.claimsInTheLastYear,
            currency: country.currency,
            countryEnum: CountryEnum.findCountryById(country.id),
            locale: lang,
            requestSource: source
        )

        if (driverCommand.hasClaim != null) {
            quoteCommand.hasClaim = driverCommand.hasClaim
            if (quoteCommand.hasClaim == true) {
                quoteCommand.lastClaimPeriod = ClaimPeriodEnum.TWELVE_MONTHS
            }
        }

        if (vehicleCommand.isThirdParty != null) {
            quoteCommand.isThirdParty = vehicleCommand.isThirdParty
        }
        if (vehicleCommand.isOldAgency != null) {
            quoteCommand.isOldAgency = vehicleCommand.isOldAgency
        }

        log.info("quoteCommand.carValuation:${quoteCommand.caCrValuation}")
        quoteCommand
    }

    /**
     * To update CarQuote records
     *
     * @param quote
     * @param rateCommand
     * @param totalPremuim
     */
    def updateCarQuote(CarQuote quote, RateCommand rateCommand, totalPremuim) {
        log.debug "quote.updateCarQuote - entering with [quote: ${quote.id}, rateCommand:${rateCommand}, totalPremuim:${totalPremuim}]"

        Product product = Product.get(rateCommand.productId)
        quote.product = product
        quote.requiredSelfDeclarationNumber = rateCommand.requiredSelfDeclarationNumber
        quote.noClaimDiscountPercent = rateCommand.noClaimDiscountPercent

        // Set premium null for offline quotes
        if (rateCommand.isOfflineQuotes){
            quote.totalPrice = null
            quote.totalPriceWithoutVat = null
            quote.policyPrice = null
            quote.policyPriceVat = null
        } else {
            quote.totalPrice = totalPremuim
            quote.totalPriceWithoutVat = totalPremuim.subtract(rateCommand.premiumVAT ?: 0)
            quote.policyPrice = rateCommand.getPremiumWithoutVAT()
            quote.policyPriceVat = rateCommand.premiumVAT
        }

        quote.excess = rateCommand.excess
        quote.isAgencyRepair = rateCommand.agencyRepair
        if (rateCommand.agencyRepair) {
            quote.repairType = RepairTypeEnum.AGENCY
        } else if (rateCommand.hasPremiumGarage) {
            quote.repairType = RepairTypeEnum.PREMIUM_GARAGE
        } else {
            quote.repairType = RepairTypeEnum.GARAGE
        }

        quote.policyReference = commonUtilService.generatePolicyRef(quote, product.provider)

        quote.c4meFee = rateCommand.originalC4meFee
        quote.c4meFeeVat = rateCommand.c4meFeeVAT
        quote.addonVat = null
        quote.ycAddonVat = null

        log.info("quote.updateCarQuote - rateCommand.originalC4meFee:${rateCommand.originalC4meFee}")

        quote.addonPrice = null
        quote.ycAddonPrice = null
        quote.discount = null
        quote.discountCode = null
        quote.providerPolicyReference = null
        quote.basicPremiumRate = null

        def rateService = ratingService.getRatingService(product.providerId,
            (SubRequestSourceEnum)quote.requestSubSource, product.typeId)

        CarQuoteCover carQuoteCover = null
        if (rateService.PROVIDER_ID == QatarRateV2Service.PROVIDER_ID) {
            carQuoteCover = CarQuoteCover.
                findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                    Provider.load(rateService.PROVIDER_ID), LocalDateTime.now(), product.type)
        } else if (rateService.PROVIDER_ID == AdamjeeRateService.PROVIDER_ID){
            /*List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                Provider.load(rateService.PROVIDER_ID), LocalDateTime.now(), product.type)
                carQuoteCover = rateService.getCoverByProduct(carQuoteCovers, quote.product.id)*/
        } else if (rateService instanceof GigRateV2Service){
            QuoteCommand quoteCommand = toQuoteCommand(quote)
            quoteCommand.selectedRepairType = rateCommand.agencyRepair ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE
            rateService.createQuote(quoteCommand)

            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                    Provider.load(rateService.PROVIDER_ID), LocalDateTime.now(), product.type)

            if (carQuoteCovers.size()) {
                quote.providerPolicyReference = carQuoteCovers.last().providerQuoteNo
            }

        } else if (rateService.PROVIDER_ID == NewIndiaRateV2Service.PROVIDER_ID){
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                    Provider.load(rateService.PROVIDER_ID), LocalDateTime.now(), product.type)
            carQuoteCover = rateService.getCoverByProduct(carQuoteCovers, quote.product.id)
        } else if (rateService.PROVIDER_ID == RakRateV2Service.PROVIDER_ID) {
            carQuoteCover = CarQuoteCover.findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageTypeAndRepairType(false, quote,
                Provider.load(rateService.PROVIDER_ID), LocalDateTime.now(), product.type, quote.repairType)

        } else if (rateService.PROVIDER_ID == TokioMarineRateV2Service.PROVIDER_ID){
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                    false, quote, Provider.load(rateService.PROVIDER_ID), LocalDateTime.now())
            carQuoteCover = rateService.getCarQuoteCoverByProductId(quote.product.id, carQuoteCovers)
        } else if (rateService.PROVIDER_ID == DubaiNationalRateV2Service.PROVIDER_ID) {
            carQuoteCover = CarQuoteCover.findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(false, quote,
                Provider.load(rateService.PROVIDER_ID), LocalDateTime.now())
        }

        if (carQuoteCover && carQuoteCover.providerQuoteNo) {
            quote.providerPolicyReference = carQuoteCover.providerQuoteNo
        }


        clearAddons(quote)

        // When provider has different insured value, then use it as actual insured insured value
        //if (rateCommand.insuredValue != quote.insuredValue) {
        quote.actualInsuredValue = rateCommand.insuredValue
        //} else {
        //    quote.actualInsuredValue = null
        //}

        if (quote.comparisonId) {
            Comparison comparison = Comparison.get(quote.comparisonId)
            if (comparison) {
                comparison.policyReference = quote.policyReference
                comparison.save(flush: true, failOnError: true)
            }
        }

        quote.save(flush: true, failOnError: true)

        quoteSgService.updateCarQuoteDetail(quote)

        quote
    }

    /**
     * Doing following 3 operations in this method
     *
     * 1. Save addons against CarQuote
     * 2. Update total price in CarQuote including addons
     * 3. Set payment method in CarQuote
     *
     * @param params - addons id
     * @param paymentMethodEnum - cod or credit card
     *
     * @return updated CarQuote object
     */
    def updateCarQuote(params, PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.CREDITCARD, String standAloneDiscountCode = null, String standAloneDiscountGroup = null) {
        log.info("quote.updateCarQuote - enting with params:${params}")

        //Make sure quoteId is passed
        if (!params.quoteId) {
            throw new IllegalArgumentException("quoteId cannot be passed null")
        }

        CarQuote carQuote = CarQuote.get(params.quoteId)

        carQuote.product = Product.read(params.productId)
        //This will force update to repair type provided, in case if user opens in two tabs 1 with agency and other with garage.
        //but continue with garage later on. If we dont update repair type, it will continue to be the selected one.
        if (carQuote.requestSource == RequestSourceEnum.WEB) {
            carQuote.isAgencyRepair = Boolean.parseBoolean(params.isAgencyRepair)
        }

//        if (carQuote.product.providerId == qatarRateV2Service.PROVIDER_ID){
//            CarQuoteCover cover = CarQuoteCover.findByQuote(quote)
//            if (cover){
//                cover.product = carQuote.product
//                cover.schemeCode = qatarRateV2Service.getSchemeCode(carQuote.productId)
//
//            }else {
//                throw new IllegalArgumentException("Something Went Wrong")
//            }
//
//        }

        def carHireCashBenefit = params.carHireCashBenefit
        def personalAccidentPax = params.personalAccidentPax
        def paCover = params.paCover
        def personalAccident247 = params.personalAccident247
        def replacementCar = params.replacementCar
        def replacementCarPlusRSA = params.replacementCarPlusRSA
        def breakdownRecovery = params.breakdownRecovery
        def breakdownCover = params.breakdownCover
        def roadsideAssistance = params.roadsideAssistance
        def offRoadDesertRecoveryAndRoadsideAssistance = params.offRoadDesertRecoveryAndRoadsideAssistance
        def dynatrade = params.dynatrade
        def lifeInsurance = params.lifeInsurance
        def discountCode = params.discountCode
        def homeInsurance = params.homeInsurance
        def warrantyCar = params.warrantyCar
        def accidentalLossOfLife = params.accidentalLossOfLife
        def bulletService = params.bulletService
        def annualMultiTravel = params.annualMultiTravel
        def singleTripTravel = params.singleTripTravel
        def smileSaverDentalCard = params.smileSaverDentalCard
        def naturalCalamity = params.naturalCalamity
        def orangeCard = params.orangeCard
        def extraBodilyDamageCover = params.extraBodilyDamageCover
        def cancellationOfClause = params.cancellationOfClause
        def extraPassengerCover = params.extraPassengerCover
        def extraMaterialCoverage = params.extraMaterialCoverage
        def waiverSubrogation = params.waiverSubrogation
        def tplPolicyAddon = params.tplPolicyAddon
        def omanODOC = params.omanODOC
        def omanOD = params.omanOD
        def gccCoverExcludingOman = params.gccCoverExcludingOman
        def windscreenDamage = params.windscreenDamage
        def keyReplacement = params.keyReplacement
        def offRoadAssistance = params.offRoadAssistance
        def omanExtension = params.omanExtension
        def naturalPerils = params.naturalPerils
        def emergencyMedicalExpenses = params.emergencyMedicalExpenses
        def personalBelongings = params.personalBelongings
        def takafulIncomeProtectinPlan = params.takafulIncomeProtectinPlan
        def dynamicAddons = params.dynamicAddon
        def donationAmount = params.donationBox ?: 0
        RepairTypeEnum selectedRepairType = carQuote.repairType ?: (params.isAgencyRepair ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE)

        def lifeInusranceAddonRateId = params.lifeInsuranceAddon
        def mashreqCardId = params.mashreqCard

        RateCommand rateCommand = getRating(carQuote, null, selectedRepairType)

        BigDecimal discountAmount = 0
        BigDecimal addonPrice = 0
        BigDecimal ycAddonPrice = 0

        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, checking for addons")
        List<CarQuoteAddon> quoteAddons = []
        if (isNotNullAndGreaterThanZero(carHireCashBenefit)) {
            def carHireCashBenefitAddon = AddonTranslation.findById(carHireCashBenefit)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: carHireCashBenefitAddon,
                price: carHireCashBenefitAddon.price, code: carHireCashBenefitAddon.code))
        }

        if (isNotNullAndGreaterThanZero(personalAccidentPax)) {
            def personalAccidentPaxAddon = AddonTranslation.findById(personalAccidentPax)
            int numberOfSeats = carQuote.model.numberOfSeats - 1 //minus driver

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: personalAccidentPaxAddon,
                price: personalAccidentPaxAddon.price.multiply(numberOfSeats), code: personalAccidentPaxAddon.code))
        }

        if (isNotNullAndGreaterThanZero(personalAccident247)) {
            AddonTranslation personalAccident247Addon = AddonTranslation.findById(personalAccident247)

            //TODO: We need to make first month free for Monthly option
            BigDecimal pa247AddonPrice = personalAccident247Addon.paymentPlan == PaymentPlanEnum.MONTHLY ? 0 : personalAccident247Addon.price
            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: personalAccident247Addon,
                price: pa247AddonPrice,
                code: personalAccident247Addon.code))
        }

        if (isNotNullAndGreaterThanZero(paCover)) {
            def paCoverAddon = AddonTranslation.findById(paCover)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: paCoverAddon,
                price: paCoverAddon.price, code: paCoverAddon.code))
        }

        //For omanODOC
        if (isNotNullAndGreaterThanZero(omanODOC)) {
            def omanODOCAddon = AddonTranslation.findById(omanODOC)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: omanODOCAddon,
                price: omanODOCAddon.price, code: omanODOCAddon.code))
        }

        //For omanOD
        if (isNotNullAndGreaterThanZero(omanOD)) {
            def omanODAddon = AddonTranslation.findById(omanOD)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: omanODAddon,
                price: omanODAddon.price, code: omanODAddon.code))
        }

        //For gccCoverExcludingOman
        if (isNotNullAndGreaterThanZero(gccCoverExcludingOman)) {
            def gccCoverExcludingOmanAddon = AddonTranslation.findById(gccCoverExcludingOman)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: gccCoverExcludingOmanAddon,
                price: gccCoverExcludingOmanAddon.price, code: gccCoverExcludingOmanAddon.code))
        }

        if (isNotNullAndGreaterThanZero(replacementCar)) {
            def replacementCarAddon = AddonTranslation.findById(replacementCar)
            Make make = Make.load(carQuote.model.makeId)
            if (rateCommand.productId == AlSagrRateService.COMPREHENSIVE_PRODUCT_ID && make.country.id.toLong() != CountryEnum.JAPAN.id){
                replacementCarAddon.price = 150
            }
            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: replacementCarAddon,
                price: replacementCarAddon.price, code: replacementCarAddon.code))

        }

        if (isNotNullAndGreaterThanZero(replacementCarPlusRSA)) {
            def replacementCarPlusRSAAddon = AddonTranslation.findById(replacementCarPlusRSA)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: replacementCarPlusRSAAddon,
                price: replacementCarPlusRSAAddon.price, code: replacementCarPlusRSAAddon.code))
        }

        if (isNotNullAndGreaterThanZero(breakdownRecovery)) {
            def breakdownRecoveryAddon = AddonTranslation.findById(breakdownRecovery)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: breakdownRecoveryAddon,
                price: breakdownRecoveryAddon.price, code: breakdownRecoveryAddon.code))
        }

        if (isNotNullAndGreaterThanZero(breakdownCover)) {
            def breakdownCoverAddon = AddonTranslation.findById(breakdownCover)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: breakdownCoverAddon,
                price: breakdownCoverAddon.price, code: breakdownCoverAddon.code))
        }

        if (isNotNullAndGreaterThanZero(roadsideAssistance)) {
            def roadsideAssistanceAddon = AddonTranslation.findById(roadsideAssistance)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: roadsideAssistanceAddon,
                price: roadsideAssistanceAddon.price, code: roadsideAssistanceAddon.code))
        }

        if (isNotNullAndGreaterThanZero(offRoadDesertRecoveryAndRoadsideAssistance)) {
            def offRoadDesertRecoveryAndRoadsideAssistanceAddon = AddonTranslation.findById(offRoadDesertRecoveryAndRoadsideAssistance)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: offRoadDesertRecoveryAndRoadsideAssistanceAddon,
                price: offRoadDesertRecoveryAndRoadsideAssistanceAddon.price, code: offRoadDesertRecoveryAndRoadsideAssistanceAddon.code))
        }

        if (isNotNullAndGreaterThanZero(naturalCalamity)) {
            def naturalCalamityAddon = AddonTranslation.findById(naturalCalamity)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: naturalCalamityAddon,
                price: naturalCalamityAddon.price, code: naturalCalamityAddon.code))
        }

        if (isNotNullAndGreaterThanZero(orangeCard)) {
            def orangeCardAddon = AddonTranslation.findById(orangeCard)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: orangeCardAddon,
                price: orangeCardAddon.price, code: orangeCardAddon.code))
        }

        if (isNotNullAndGreaterThanZero(dynatrade)) {
            def dynatradeAddon = AddonTranslation.findById(dynatrade)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: dynatradeAddon,
                price: dynatradeAddon.price, code: dynatradeAddon.code))

            //Add dynatrade addon only if price is above zero
        }

        if (isNotNullAndGreaterThanZero(lifeInsurance)) {
            def lifeInsuranceAddon = AddonTranslation.findById(lifeInsurance)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: lifeInsuranceAddon,
                price: lifeInsuranceAddon.price, code: lifeInsuranceAddon.code))

        }

        if (isNotNullAndGreaterThanZero(homeInsurance)) {
            def homeInsuranceAddon = AddonTranslation.findById(homeInsurance)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: homeInsuranceAddon,
                price: homeInsuranceAddon.price, code: homeInsuranceAddon.code))
        }

        if (isNotNullAndGreaterThanZero(warrantyCar)) {
            def warrantyCarAddon = AddonTranslation.findById(warrantyCar)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: warrantyCarAddon,
                price: warrantyCarAddon.price, code: warrantyCarAddon.code))
        }

        if (isNotNullAndGreaterThanZero(accidentalLossOfLife)) {
            def accidentalLossOfLifeAddon = AddonTranslation.findById(accidentalLossOfLife)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: accidentalLossOfLifeAddon,
                price: accidentalLossOfLifeAddon.price, code: accidentalLossOfLifeAddon.code))
        }

        if (isNotNullAndGreaterThanZero(bulletService)) {
            def bulletServiceAddon = AddonTranslation.findById(bulletService)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: bulletServiceAddon,
                price: bulletServiceAddon.price, code: bulletServiceAddon.code))
        }

        if (isNotNullAndGreaterThanZero(annualMultiTravel)) {
            def annualMultiTravelAddon = AddonTranslation.findById(annualMultiTravel)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: annualMultiTravelAddon,
                price: annualMultiTravelAddon.price, code: annualMultiTravelAddon.code))
        }

        if (isNotNullAndGreaterThanZero(singleTripTravel)) {
            def singleTripTravelAddon = AddonTranslation.findById(singleTripTravel)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: singleTripTravelAddon,
                price: singleTripTravelAddon.price, code: singleTripTravelAddon.code))
        }

        if (isNotNullAndGreaterThanZero(smileSaverDentalCard)) {
            def smileSaverDentalCardAddon = AddonTranslation.findById(smileSaverDentalCard)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: smileSaverDentalCardAddon,
                price: smileSaverDentalCardAddon.price, code: smileSaverDentalCardAddon.code))
        }

        if (isNotNullAndGreaterThanZero(extraBodilyDamageCover)) {
            def extraBodilyDamageCoverAddon = AddonTranslation.findById(extraBodilyDamageCover)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: extraBodilyDamageCoverAddon,
                price: extraBodilyDamageCoverAddon.price, code: extraBodilyDamageCoverAddon.code))
        }

        if (isNotNullAndGreaterThanZero(cancellationOfClause)) {
            def cancellationOfClauseAddon = AddonTranslation.findById(cancellationOfClause)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: cancellationOfClauseAddon,
                price: cancellationOfClauseAddon.price, code: cancellationOfClauseAddon.code))
        }

        if (isNotNullAndGreaterThanZero(extraPassengerCover)) {
            def extraPassengerCoverAddon = AddonTranslation.findById(extraPassengerCover)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: extraPassengerCoverAddon,
                price: extraPassengerCoverAddon.price, code: extraPassengerCoverAddon.code))
        }

        if (isNotNullAndGreaterThanZero(extraMaterialCoverage)) {
            def extraMaterialCoverageAddon = AddonTranslation.findById(extraMaterialCoverage)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: extraMaterialCoverageAddon,
                price: extraMaterialCoverageAddon.price, code: extraMaterialCoverageAddon.code))
        }

        if (isNotNullAndGreaterThanZero(waiverSubrogation)) {
            def waiverSubrogationAddon = AddonTranslation.findById(waiverSubrogation)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: waiverSubrogationAddon,
                price: waiverSubrogationAddon.price, code: waiverSubrogationAddon.code))
        }

        if (isNotNullAndGreaterThanZero(tplPolicyAddon)) {
            def tplAddon = AddonTranslation.findById(tplPolicyAddon)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: tplAddon,
                price: tplAddon.price, code: tplAddon.code))
        }
        // life add on
        BigDecimal lifeInsuranceAddonPrice = 0.00
        if (isNotNullAndGreaterThanZero(lifeInusranceAddonRateId)) {
            try {
                CarQuoteAddon carQuoteAddon = lifeUtilService.getAddOnDetails(lifeInusranceAddonRateId as Long, carQuote)
                if (carQuoteAddon) {
                    lifeInsuranceAddonPrice = carQuoteAddon.price as BigDecimal
                    quoteAddons.add(carQuoteAddon)
                }
            } catch (Exception e) {
                log.error "could not get details from life addon ", e
                throw new RuntimeException("could not get details from life addon. carQuote ID: $carQuote.id", e.getCause())
            }
        }

        if (isNotNullAndGreaterThanZero(windscreenDamage)) {
            def windscreenDamageAddon = AddonTranslation.findById(windscreenDamage)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: windscreenDamageAddon,
                price: windscreenDamageAddon.price, code: windscreenDamageAddon.code))
        }

        if (isNotNullAndGreaterThanZero(keyReplacement)) {
            def keyReplacementAddon = AddonTranslation.findById(keyReplacement)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: keyReplacementAddon,
                price: keyReplacementAddon.price, code: keyReplacementAddon.code))
        }

        if (isNotNullAndGreaterThanZero(offRoadAssistance)) {
            def offRoadAssistanceAddon = AddonTranslation.findById(offRoadAssistance)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: offRoadAssistanceAddon,
                price: offRoadAssistanceAddon.price, code: offRoadAssistanceAddon.code))
        }

        if (isNotNullAndGreaterThanZero(omanExtension)) {
            def omanExtensionAddon = AddonTranslation.findById(omanExtension)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: omanExtensionAddon,
                price: omanExtensionAddon.price, code: omanExtensionAddon.code))
        }

        if (isNotNullAndGreaterThanZero(naturalPerils)) {
            def naturalPerilsAddon = AddonTranslation.findById(naturalPerils)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: naturalPerilsAddon,
                price: naturalPerilsAddon.price, code: naturalPerilsAddon.code))
        }

        if (isNotNullAndGreaterThanZero(emergencyMedicalExpenses)) {
            def emergencyMedicalExpensesAddon = AddonTranslation.findById(emergencyMedicalExpenses)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: emergencyMedicalExpensesAddon,
                price: emergencyMedicalExpensesAddon.price, code: emergencyMedicalExpensesAddon.code))
        }

        if (isNotNullAndGreaterThanZero(personalBelongings)) {
            def personalBelongingsAddon = AddonTranslation.findById(personalBelongings)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: personalBelongingsAddon,
                price: personalBelongingsAddon.price, code: personalBelongingsAddon.code))
        }

        if (isNotNullAndGreaterThanZero(takafulIncomeProtectinPlan)) {
            def takafulIncomeProtectinPlanAddon = AddonTranslation.findById(takafulIncomeProtectinPlan)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: takafulIncomeProtectinPlanAddon,
                price: takafulIncomeProtectinPlanAddon.price, code: takafulIncomeProtectinPlanAddon.code))
        }

        if (isNotNullAndGreaterThanZero(mashreqCardId)) {
            AddonTranslation mashreqCardAddon = AddonTranslation.findById(mashreqCardId)

            quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: mashreqCardAddon,
                price: mashreqCardAddon.price, code: mashreqCardAddon.code))
        }
        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, addons check completed, checking for dynamic addons")
        List selectedDynamicAddons = []

        if (dynamicAddons) {
            log.info("dynamicAddons found :$dynamicAddons, while total dynamic adds are:${rateCommand.dynamicAddons}")
            AddonTranslation dynamicAddonAddon = AddonTranslation.findByCode(AddonCodeEnum.DYNAMIC_ADDON.code)

            selectedDynamicAddons = rateCommand.dynamicAddons.findAll {
                return it.providerCode == dynamicAddons || it.providerCode in dynamicAddons
            }
            log.info("selectedDynamicAddons:$selectedDynamicAddons")

            selectedDynamicAddons.each {
                quoteAddons.add(new CarQuoteAddon(carQuote: carQuote, addonTranslation: dynamicAddonAddon,
                    addonDescription: it.label, price: it.price, code: dynamicAddonAddon.code,
                    providerAddonCode: it.providerCode))
            }

        }
        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, dynamic addons check completed")
        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, saving car quote")

        def rateService = ratingService.getRatingService(carQuote.product.providerId,
            (SubRequestSourceEnum)carQuote.requestSubSource, carQuote.product.typeId)

        if (carQuote.product.providerId.intValue() in [AdamjeeRateV2Service.PROVIDER_ID, NewIndiaRateV2Service.PROVIDER_ID]) {
            if (rateService instanceof AdamjeeRateV2Service || rateService instanceof NewIndiaRateV2Service) {
                rateService.saveQuoteDetails(carQuote, selectedDynamicAddons)
            }
        } else if (carQuote.product.providerId == GigRateV2Service.PROVIDER_ID && dynamicAddons){
            QuoteCommand quoteCommandGig = toQuoteCommand(carQuote)
            quoteCommandGig.selectedRepairType = rateCommand.agencyRepair ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE
            rateService.updateQuoteWithAddons(quoteCommandGig, carQuote, selectedDynamicAddons)
        } else if (carQuote.product.providerId == TokioMarineRateV2Service.PROVIDER_ID){
            QuoteCommand quoteCommandTokio = toQuoteCommand(carQuote)
            String providerQuoteNumber = rateService.saveQuoteDetails(quoteCommandTokio, selectedDynamicAddons)
            log.info("QuoteService.updateCarQuote - providerQuoteNumber: ${providerQuoteNumber}")

            carQuote.providerPolicyReference = providerQuoteNumber
        }

        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, car quote saved")
        // Update donation information
        commonPolicySgService.updateDonation(carQuote, ProductTypeEnum.CAR, donationAmount, DonationTypeEnum.CHARITY)

        //Update required self declaration number and no claim discount percentage
        if (carQuote.requestSource in [RequestSourceEnum.WEB, RequestSourceEnum.NATIONALBONDS,
                                       RequestSourceEnum.ETISALAT_SMILES, RequestSourceEnum.ADIB]) {
            carQuote.requiredSelfDeclarationNumber = rateCommand.requiredSelfDeclarationNumber
            carQuote.noClaimDiscountPercent = rateCommand.noClaimDiscountPercent
        }

        carQuote.policyPrice = rateCommand.getPremiumWithoutVAT()
        BigDecimal priceBeforeVAT = rateCommand.getPremiumWithoutVAT()
        def discount, discountCodeObj
        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, applying discount")
        if (discountCode) {
            try {
                CountryEnum countryEnum = CountryEnum.findCountryByIsoAlpha2Code(carQuote.quoteCountry.code)

                (discount, discountCodeObj) = checkoutService.getDiscount(discountCode,
                    priceBeforeVAT, ProductType.CAR, carQuote.product.id, countryEnum.code, carQuote.id)

                discountAmount = BigDecimal.valueOf(discount)
                carQuote.discountCode = discountCodeObj
                carQuote.discount = discountAmount

            } catch (Exception codeExp) {
                log.error(".QuoteService.saveAddon Error while getting discount", codeExp)
                carQuote.discountCode = null
                carQuote.discount = 0d
            }
        } else {

            QuoteCommand quoteCommand = toQuoteCommand(carQuote)
            quoteCommand.standAloneDiscountCode = standAloneDiscountCode
            quoteCommand.standAloneDiscountGroup = standAloneDiscountGroup
//            Long discountProviderId = null
            User user = User.findByEmail(carQuote.email)
//            (discount, discountCodeObj) = ratingService
//                .getDiscount(priceBeforeVAT, isTpl, carQuote.product.providerId, user.leadType, carQuote.isAgencyRepair, countryEnum, carQuote.product.id, standAloneDiscountCode, standAloneDiscountGroup)
            (discount, discountCodeObj) = ratingService.getDiscount(priceBeforeVAT, rateCommand, quoteCommand, user)


            if (discount) {
                discountAmount = BigDecimal.valueOf(discount)
                carQuote.discountCode = discountCodeObj
                carQuote.discount = discountAmount
            } else {
                carQuote.discountCode = null
                carQuote.discount = 0d
            }
        }

        rateCommand = ratingService.applyDiscount(rateCommand, discount, discountCodeObj)

        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, discount check completed")

        if (carQuote.isUAEQuote()
            && !Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        clearAddons(carQuote)

        //Save all the addons
        quoteAddons*.save()

        //Delete existing additional charges
        if (carQuote.carQuoteAdditionalCharges) {
            carQuote.carQuoteAdditionalCharges*.delete()
            carQuote.carQuoteAdditionalCharges.clear()
        }

        //Add charges based on the selected product
        List<AdditionalCharges> charges = additionalChargesService.getAdditionalCharges(rateCommand, ProductType.findByName('CAR'))
        charges.each { charge ->
            BigDecimal additionalAmount = null
            if (charge.isPercentage) {
                additionalAmount = rateCommand.originalPremium * (charge.price / 100)
            } else {
                additionalAmount = charge.price
            }
            carQuote.addToCarQuoteAdditionalCharges(new CarQuoteAdditionalCharges(quote: carQuote, additionalCharges: charge, amount: additionalAmount))
        }

        List<String> ycAddonCodesList = grailsApplication.config.getProperty('ycAddonCodes', List.class)

        quoteAddons.each { CarQuoteAddon carQuoteAddon ->
            addonPrice += carQuoteAddon.price

            if (carQuoteAddon.code in ycAddonCodesList) {
                ycAddonPrice += carQuoteAddon.price
            } else if (carQuoteAddon.addonTranslation.code == 'lifeInsuranceAddon' && carQuoteAddon.addonTranslation.code in ycAddonCodesList) {
                ycAddonPrice += carQuoteAddon.price
            }
        }

        //Set total addon & YC addon prices (and VATs in case of UAE quote)
        carQuote.addonPrice = addonPrice
        carQuote.ycAddonPrice = ycAddonPrice
        if (carQuote.isUAEQuote()) {
            if (lifeInsuranceAddonPrice != 0.00 && quoteAddons.size() == 1) {
                // for life insurance vat if there is only life insurance
                carQuote.ycAddonVat = 0.00
                carQuote.addonVat = 0.00
            } else if (lifeInsuranceAddonPrice != 0.00 && quoteAddons.size() > 1) {
                // if there is life insurance with more add on
                carQuote.ycAddonVat = commonUtilService.getVATAmount((carQuote.ycAddonPrice - lifeInsuranceAddonPrice))
                carQuote.addonVat = commonUtilService.getVATAmount((carQuote.addonPrice - lifeInsuranceAddonPrice))
            } else {
                carQuote.ycAddonVat = commonUtilService.getVATAmount(carQuote.ycAddonPrice)
                carQuote.addonVat = commonUtilService.getVATAmount(carQuote.addonPrice)
            }
        }

        log.info("quoteService.updateCarQuote - updating order for car quote ${carQuote.id}, addon calculation completed")
        //Vat on C4ME
        carQuote.c4meFeeVat = rateCommand.c4meFeeVAT
        carQuote.policyPriceVat = rateCommand.premiumVAT

        carQuote.additionalCharges = rateCommand.additionalCharges
        carQuote.additionalChargesVAT = rateCommand.additionalChargesVAT

        //Set payment method
        carQuote.paymentMethod = paymentMethodEnum

        //Set payment status to pending
        paymentService.changePaymentStatus(carQuote, PaymentStatusEnum.PENDING)

        BigDecimal totalVAT = carQuote.getTotalVAT()
        log.info("totalVAT:$totalVAT")
        //Calculate price again add addon price and minus discount
        carQuote.totalPriceWithoutVat = (carQuote.policyPrice - discountAmount).plus(carQuote.addonPrice).plus(carQuote.additionalCharges ?: 0)
        log.info("carQuote.totalPriceWithoutVat:${carQuote.totalPriceWithoutVat}")
        carQuote.totalPrice = carQuote.totalPriceWithoutVat.plus(totalVAT)

//        if (carQuote.product.provider.id == InsuranceProviderEnum.SALAMA.id) {
//            QuoteCommand quoteCommandForBaseRate = toQuoteCommand(carQuote)
//            quoteCommandForBaseRate.selectedRepairType = carQuote.isAgencyRepair ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE
//
//            boolean calculateBasePremiumRate = true
//            if (carQuote.type == LeadType.RENEWAL) {
//                Renewal renewal = Renewal.findByCarQuoteAndProductAndIsSystemCreated(carQuote, carQuote.product, false)
//                if (renewal) {
//                    calculateBasePremiumRate = false
//                }
//            }
//            if (calculateBasePremiumRate) {
//                BigDecimal basicPremiumRate = salamaRateService.calculateBasicPremiumRateForInstantIssueApi(quoteCommandForBaseRate)
//                // this property is needed for Salama Instant Issue API
//                carQuote.basicPremiumRate = basicPremiumRate
//            }
//        }

        carQuote.save(flush: true, failOnError: true)

        quoteSgService.updateCarQuoteDetail(carQuote)

        if (rateService instanceof BaseRatingService) {
            CarQuoteCover carQuoteCover = CarQuoteCover.
                findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, carQuote,
                Provider.load(rateService.PROVIDER_ID), LocalDateTime.now(), carQuote.product.type)

            def list1 = quoteAddons.findAll { it.code == AddonCodeEnum.DYNAMIC_ADDON.code }.toList()
            log.info("list1:$list1")
            def providerAddonCodes = quoteAddons.findAll { it.code == AddonCodeEnum.DYNAMIC_ADDON.code }.collect { it.providerAddonCode }.toList()
            log.info("providerAddonCodes: $providerAddonCodes")
            /*List providerAddonCodes = quoteAddons.
                findAll { it.code == AddonCodeEnum.DYNAMIC_ADDON}.
                collect { it.providerAddonCode}.toList()*/

            if (carQuote.product.provider == QatarRateV2Service.PROVIDER_ID) {
                log.info("quoteService.updateCarQuote - QIC product for carQuote:${carQuote.id}")
                BigDecimal providerGrossPremium = rateService.getGrossPremium(carQuote, carQuoteCover.providerQuoteNo, providerAddonCodes)

                BigDecimal totalDynamicAddonPrice = quoteAddons.findAll { it.code == AddonCodeEnum.DYNAMIC_ADDON.code }.sum { it.price }

                BigDecimal calculatedGrossPremium = carQuote.policyPrice - (carQuote.c4meFee ?: 0) + (totalDynamicAddonPrice ?: 0)
                log.info("providerGrossPremium:$providerGrossPremium, calculatedGrossPremium:$calculatedGrossPremium")
                if (providerGrossPremium != calculatedGrossPremium) {
                    log.error("quote.updateCarQuote - Premium didnt match for car quote:$carQuote, " +
                        "providerGrossPremium:$providerGrossPremium, calculatedGrossPremium:$calculatedGrossPremium")

                    throw new RuntimeException("Premium didnt match for car quote")
                }
            }

        }
        log.info("quoteService.updateCarQuote - returning after updating carQuote:${carQuote.id}, policyPriceVat:${carQuote.policyPriceVat}")
        carQuote
    }

    def clearAddons(CarQuote carQuote) {
        CarQuoteAddon.executeUpdate("delete CarQuoteAddon a where a.carQuote.id = :id", [id: carQuote.id])
    }

    /**
     * Set car quote status to no-quote
     *
     * @param quote
     */
    def setNoQuote(CarQuote quote) {
        quote.policyReference = commonUtilService.generatePolicyRef(quote)
        quote.save(flush: true)

        //Set status to no quote
        checkoutService.changePaymentStatus(quote.id, PaymentStatusEnum.NOQUOTE)
    }

    /**
     *
     * @param value
     * @return true if passed value is not null and greater than 0
     */
    private boolean isNotNullAndGreaterThanZero(value) {
        boolean ret = false

        if (value) {
            if (value.toInteger() > 0) {
                ret = true
            }
        }

        ret
    }

    /**
     * Save all quotes
     * @param ratings
     * @param quoteId
     */
    def saveAllQuotes(List<RateCommand> ratings, Long quoteId) {
        log.info("saving qoutes ${quoteId}")
        CarQuote quote = CarQuote.read(quoteId)

        ratings.eachWithIndex { rating, idx ->
            def qoute = new QuoteResult()
            Product product = Product.get(rating.productId)
            qoute.position = idx
            qoute.provider = product?.provider?.nameEn
            if (product.typeId.toInteger() == CoverageTypeEnum.COMPREHENSIVE.value()) {
                qoute.insuredValue = rating.insuredValue
            }
            qoute.paCover = rating.paCover
            qoute.productName = product?.nameEn
            qoute.agencyRepair = rating.agencyRepair
            qoute.breakdownCover = rating.breakdownCover
            qoute.replacementCar = rating.replacementCar
            qoute.basePremium = rating.basePremium
            qoute.premium = rating.premium
            qoute.minPremium = rating.minPremium
            qoute.excess = rating.excess
            qoute.productId = rating.productId
            qoute.repairType = rating.agencyRepair ? RepairTypeEnum.AGENCY : (rating.hasPremiumGarage ? RepairTypeEnum.PREMIUM_GARAGE : RepairTypeEnum.GARAGE)
            qoute.qouteId = quoteId
            /*qoute.insuredValueMin = rating.carValuation ? rating.carValuation.valuationMin : quote.insuredValueMin
            qoute.insuredValueMax = rating.carValuation ? rating.carValuation.valuationMax : quote.insuredValueMax
            qoute.valuationSource = rating.carValuation ? rating.carValuation.valuationSource : quote.valuationSource*/
            qoute.save(failOnError: true)

        }

    }

    /**
     * return - newly create @CarQuoteKsa
     */
    def createKsaCarQuote(DriverCommand driverCommand, year, model, registrationCity, firstRegistrationDate, insuredValue,
                          isNonGcc, isExpiredPolicy, isThirdParty, isOldAgency, purchaseDate, isNotBoughtYet, isBrandNew,
                          queryString, insuredValueMin, insuredValueMax, String dealerId = null) {

        User user = User.findByEmail(driverCommand.email)
        Model modelObj = Model.findById(model)
        City registrationCityObj = City.get(registrationCity)
        Country licenseCountryObj = Country.get(driverCommand.firstLicenseCountry)
        Country nationalityObj = Country.get(driverCommand.nationality)
        DrivingExperience internationalExperienceObj = DrivingExperience.get(driverCommand.internationalExperience)
        DrivingExperience localExperienceObj = DrivingExperience.get(driverCommand.localExperience)
        NoClaimDiscount noClaimDiscountObj = NoClaimDiscount.get(driverCommand.ncd)

        if (!user) {
            //Create new user if user is not found in db
            user = new User(email: driverCommand.email, password: 'ksa', leadType: LeadType.NORMAL)
        }

        user.name = driverCommand.name
        user.dob = new LocalDate(driverCommand.dob)
        user.mobile = driverCommand.mobile
        if (!user.leadType) {
            user.leadType = LeadType.NORMAL
        }
        user.save(flush: true)

        CarQuoteKsa quoteKsa = new CarQuoteKsa(user: user, year: year, model: modelObj, registrationCity: registrationCityObj,
            firstRegistrationDate: new LocalDate(firstRegistrationDate), insuredValue: insuredValue, isNonGcc: isNonGcc,
            isExpiredPolicy: isExpiredPolicy, isThirdParty: isThirdParty, nationality: nationalityObj,
            licenseCountry: licenseCountryObj, internationalExperience: internationalExperienceObj,
            localExperience: localExperienceObj, policyStartDate: new LocalDate(driverCommand.policyStartDate),
            noClaimDiscount: noClaimDiscountObj, quoteCountry: utilService.getCountry(),
            currency: utilService.getCurrency(), isOldAgency: isOldAgency, email: driverCommand.email,
            name: driverCommand.name, dob: new LocalDate(driverCommand.dob), mobile: driverCommand.mobile,
            hasClaim: driverCommand.hasClaim, purchaseDate: purchaseDate, isNotBoughtYet: isNotBoughtYet,
            isBrandNew: isBrandNew, queryString: queryString, lang: utilService.getLanguage(),
            insuredValueMin: insuredValueMin, insuredValueMax: insuredValueMax, dealer: Dealer.load(dealerId))

        quoteKsa.save(failOnError: true)
        quoteKsa.policyReference = commonUtilService.generateKsaPolicyRef(quoteKsa)

        quoteKsa.save(flush: true)

        // TODO: Here we have to create Car Quote Detail and Car Quote Crm Status

        quoteKsa
    }

    /**
     * Verify that all data is available for payment
     *
     * @param CarQuote
     * @return
     */
    boolean isEligibleForPayment(CarQuote carQuote) {
        log.debug("quote.isEligibleForPayment - carQuote:${carQuote.id}")

        if (carQuote.isUAEQuote()) {

            boolean allDataAvailable = carQuote.policyPrice && carQuote.policyPriceVat &&
                carQuote.totalPrice && carQuote.totalPriceWithoutVat &&
                //(!carQuote.addonPrice || carQuote.addonPrice && carQuote.addonVat) &&
                //(!carQuote.c4meFee || carQuote.c4meFee && carQuote.c4meFeeVat) &&
                (!carQuote.discount || carQuote.discount && carQuote.discountCode)

            log.info("pp:${carQuote.policyPrice}, ppvat:${carQuote.policyPriceVat}, " +
                "tp:${carQuote.totalPrice}, tpwovat:${carQuote.totalPriceWithoutVat}, " +
                "add:${carQuote.addonPrice}, addVAT:${carQuote.addonVat}," +
                "c4me:${carQuote.c4meFee}, c4meVAT:${carQuote.c4meFeeVat}, " +
                "discount:${carQuote.discount}, discountCode:${carQuote.discountCode?.id}")

            BigDecimal totalPrice = carQuote.policyPrice.add(carQuote.addonPrice ?: 0)
                .add(carQuote.additionalCharges ?: 0)
                .subtract(carQuote.discount ?: 0)
                .add(carQuote.getTotalVAT())

            if (!allDataAvailable || totalPrice != carQuote.totalPrice) {
                log.error("quote.isEligibleForPayment - quote:${carQuote.id}, prices not matched. " +
                    "Total:${totalPrice}, carQuote.TotalPrice:${carQuote.totalPrice}, allDataAvailable:${allDataAvailable}")
                return false
            }

        } else {
            boolean allDataAvailable = carQuote.policyPrice && carQuote.totalPrice &&
                (!carQuote.discount || carQuote.discount && carQuote.discountCode)

            log.info("pp:${carQuote.policyPrice}, tp:${carQuote.totalPrice}, add:${carQuote.addonPrice}, " +
                "c4me:${carQuote.c4meFee}, discount:${carQuote.discount}, discountCode:${carQuote.discountCode?.id}")

            BigDecimal totalPrice = carQuote.policyPrice.add(carQuote.addonPrice ?: 0)
                .add(carQuote.additionalCharges ?: 0)
                .subtract(carQuote.discount ?: 0)


            if (!allDataAvailable || totalPrice != carQuote.totalPrice) {
                log.error("quote.isEligibleForPayment - quote:${carQuote.id}, prices not matched. " +
                    "Total:${totalPrice}, carQuote.TotalPrice:${carQuote.totalPrice}, allDataAvailable:${allDataAvailable}")
                return false
            }
        }

        return true

    }

    /**
     * Get AddOns based on product
     *
     * @param rateCommand
     * @param model
     * @return
     */
    @Transactional(readOnly = true)
    def getAddOns(RateCommand rateCommand, Model model, RequestSourceEnum requestSource,
                  def selectedAddons = [:],
                  BigDecimal insuredValue = null, CarQuote quote = null) {
        int numberOfSeats = model.numberOfSeats - 1 //minus driver

        Product product = Product.get(rateCommand.productId)
        String countryCode = product.provider.country.code

        CountryEnum country = CountryEnum.findCountryByDfp(countryCode)

        Set checkboxList = []
        Set dropdownList = []

        DetachedCriteria criteria = Addon.where {
            active == true && showForCar == true && (product == null || product == product) &&
                (numberOfSeats == model.numberOfSeats || numberOfSeats == null) &&
                (vehicleType == model.vehicleType || vehicleType == null)
        }
        List addons = criteria.list(sort: "sortOrder")

        addons.each { Addon addon ->
            if (addon.type == AddonTypeEnum.CHECKBOX) {
                AddonTranslation translation = AddonTranslation.findByAddon(addon)

                log.info("translation:${translation.id}${translation.code}")

                if (translation.code in [AddonCodeEnum.PA_247.code]) {

                    if (!isAddonApplicable(rateCommand, translation.code)) {
                        return
                    }
                }

                if (translation.code == AddonCodeEnum.PA_247.code && (requestSource == RequestSourceEnum.ETISALAT_SMILES)) {
                    return
                }


                if (translation.code == "paCover" && rateCommand.providerId == UnionRateService.PROVIDER_ID &&
                    insuredValue && insuredValue > 70000) {
                    return
                }

                if (translation.code == "replacementCar" && rateCommand.productId == AlSagrRateService.COMPREHENSIVE_PRODUCT_ID){
                    Make make = Make.load(quote.model.makeId)
                    if (make.country.id.toLong() != CountryEnum.JAPAN.id){
                        translation.price = 150
                    }
                }

                def selected = false

                if (translation.isDefault) {
                    selected = true
                }

                if (selectedAddons[translation.code]) {
                    selected = true
                }

                def price = translation.price

                if (translation.code == AddonCodeEnum.PA_247.code &&
                    translation.paymentPlan == PaymentPlanEnum.MONTHLY) {
                    //TODO: free first month, for now making it 0
                    price = 0
                }

                if (translation.code.equals("personalAccidentPax")) {
                    price = translation.price.multiply(numberOfSeats)
                } else if (translation.code.equals("dynatrade") && rateCommand.providerId == MethaqRatingService.PROVIDER_ID && rateCommand.carAge > 7){
                    return
                }

                checkboxList.add([label: translation.label, value: price, valueText: translation.priceText, description: translation.description,
                                  name : translation.code, selected: selected, id: translation.id])

            } else if (addon.type == AddonTypeEnum.SELECT) {
                List<AddonTranslation> translations = AddonTranslation.findAllByAddon(addon, [sort: "sortOrder", order: "asc", readOnly: true])
                def options = []
                def selected = null

                translations.each { option ->
                    if (option.code in [AddonCodeEnum.PA_247.code]) {
                        if (!isAddonApplicable(rateCommand, option.code)) {
                            return
                        }
                    }
                    if (option.code in [AddonCodeEnum.ANNUAL_MULTI_TRAVEL.code, AddonCodeEnum.SINGLE_TRIP_TRAVEL.code ]) {
                        if (!isAddonApplicableForSukoon(rateCommand, option.code)) {
                            rateCommand.showAnnualMultiTravelAddon = false
                            rateCommand.showSingleTripTravelAddon = false
                        }
                    }

                    if (option.code == AddonCodeEnum.PA_247.code && (requestSource == RequestSourceEnum.ETISALAT_SMILES)) {
                        return
                    }

                    if (option.code == "paCover" && rateCommand.providerId == UnionRateService.PROVIDER_ID &&
                        insuredValue && insuredValue > 70000) {
                        return
                    }

                    if (option.isDefault) {
                        selected = option.id
                    }

                    def value = selectedAddons[option.code]

                    if (option.price == value) {
                        selected = option.id
                    }

                    def price = option.price
                    if (option.code == AddonCodeEnum.PA_247.code &&
                        option.paymentPlan == PaymentPlanEnum.MONTHLY) {
                        //TODO: free first month, for now making it 0
                        price = 0
                    }

                    if (translations.code.equals("personalAccidentPax")) {
                        price = option.price.multiply(numberOfSeats)
                    }

                    options.add([option: option.value, value: price, valueText: translations.priceText, id: option.id])
                }

                Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))

                if (country == CountryEnum.UAE && pricesInclusiveVat) {
                    options = applyVAT(options)
                }
                if (translations.size() == 0) {
                    return
                }
                dropdownList.add([label  : translations.first().label, description: translations.first().description,
                                  options: options, name: translations.first().code, value: selected])
            }
        }
        if (Boolean.parseBoolean(grailsApplication.config.getProperty('lifeInsuranceAddon.car'))) {
            String brand = sessionService.getBrandCode()
            if (brand != WhiteLabelBrandEnum.NATIONALBONDS.code && brand != WhiteLabelBrandEnum.ADIB.code) {
                dropdownList = lifeUtilService.addLifeAddonToDropDownList(quote, dropdownList, 'car')
            }
        }

        // clear yc addons for wathba & gig
        if (rateCommand.providerId == WathbaRateV2Service.PROVIDER_ID ||
            rateCommand.providerId == GigRateV2Service.PROVIDER_ID ){
            checkboxList = []
            dropdownList = []
        }

        rateCommand.dynamicAddons.each {
            boolean isSelected = false

            CarCoversEnum coverEnum = null
            if (rateCommand.providerId == QatarRateV2Service.PROVIDER_ID) {
                coverEnum = CarCoversEnum.findByQicCode(it.providerCode)

            } else if (rateCommand.providerId == NoorRateV2Service.PROVIDER_ID) {
                coverEnum = CarCoversEnum.findByNoorCode(it.providerCode)
            } else if (rateCommand.providerId == RakRateV2Service.PROVIDER_ID) {
                coverEnum = CarCoversEnum.findByRakCode(it.providerCode)
            } else if (rateCommand.providerId == AdamjeeRateV2Service.PROVIDER_ID) {
                coverEnum = CarCoversEnum.findByAdcCode(it.providerCode)
            } else if (rateCommand.providerId == GigRateV2Service.PROVIDER_ID) {
                coverEnum = CarCoversEnum.findByGigCode(it.providerCode)
            } else if (rateCommand.providerId == TokioMarineRateV2Service.PROVIDER_ID) {
                coverEnum = CarCoversEnum.findByTokioCode(it.providerCode)
            } else if (rateCommand.providerId == NewIndiaRateV2Service.PROVIDER_ID) {
                coverEnum = CarCoversEnum.findByNiaCode(it.providerCode)
            } else if (rateCommand.providerId == DubaiNationalRateV2Service.PROVIDER_ID) {
                if (rateCommand.productId == DubaiNationalRateV2Service.TPL_PRODUCT_ID) {
                    coverEnum = CarCoversEnum.findByDnircTplCode(it.providerCode)
                } else {
                    coverEnum = CarCoversEnum.findByDnircCode(it.providerCode)
                }
            } else if (rateCommand.providerId == WathbaRateV2Service.PROVIDER_ID){
                if (rateCommand.productId == WathbaRateV2Service.COMPREHENSIVE_PRODUCT_ID){
                    coverEnum = CarCoversEnum.findByWathbaCode(it.providerCode)
                } else if (rateCommand.productId == WathbaRateV2Service.TPL_PRODUCT_ID){
                    coverEnum = CarCoversEnum.findByWathbaTplCode(it.providerCode)
                }
            }

            if (coverEnum && selectedAddons[coverEnum.ycCode] &&
                selectedAddons[coverEnum.ycCode].toString().isNumber() && new BigDecimal(selectedAddons[coverEnum.ycCode] + "") == it.price) {
                log.info("YC Code ${coverEnum.ycCode} isSelected: TRUE")
                isSelected = true
            }
            def dynamicAddon = [label: it.label, value: it.price, valueText: '', description: it.description,
                                name : it.code, selected: isSelected, id: it.providerCode]

            checkboxList.add(dynamicAddon)
        }

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))

        if (country == CountryEnum.UAE && pricesInclusiveVat) {
            checkboxList = applyVAT(checkboxList)
        }

        if (!rateCommand.showPaCoverAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.PA_COVER.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
        }

        if (!rateCommand.showPersonalAccidentPaxAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.PAX_COVER.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
        }

        if (!rateCommand.showOmanODAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.OMAN_OD.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
        }

        if (!rateCommand.showOmanTplAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.OMAN_TPL.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
            for (ddItem in dropdownList) {
                if (ddItem.name == AddonCodeEnum.OMAN_TPL.code) {
                    dropdownList.remove(ddItem)
                    break
                }
            }
        }

        if (!rateCommand.showBreakdownAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.BREAKDOWN_COVER.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
            for (ddItem in dropdownList) {
                if (ddItem.name == AddonCodeEnum.BREAKDOWN_COVER.code) {
                    dropdownList.remove(ddItem)
                    break
                }
            }
        }

        if (!rateCommand.showReplacementCarAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.REPLACEMENT_CAR.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
            for (ddItem in dropdownList) {
                if (ddItem.name == AddonCodeEnum.REPLACEMENT_CAR.code) {
                    dropdownList.remove(ddItem)
                    break
                }
            }
        }

        if (!rateCommand.showOffRoadAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.OFFROAD_ASSISTANCE.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
            for (ddItem in dropdownList) {
                if (ddItem.name == AddonCodeEnum.OFFROAD_ASSISTANCE.code) {
                    dropdownList.remove(ddItem)
                    break
                }
            }
        }

        if (!rateCommand.showReplacementCarPlusRSAAddon) {
            for (cbItem in checkboxList) {
                if (cbItem.name == AddonCodeEnum.REPLACEMENT_CAR_PLUS_RSA.code) {
                    checkboxList.remove(cbItem)
                    break
                }
            }
            for (ddItem in dropdownList) {
                if (ddItem.name == AddonCodeEnum.REPLACEMENT_CAR_PLUS_RSA.code) {
                    dropdownList.remove(ddItem)
                    break
                }
            }
        }

        if (!rateCommand.showHomeInsuranceAddon) {
            checkboxList = removeAddOnFromCollection(checkboxList, AddonCodeEnum.HOME_INSURANCE)
            dropdownList = removeAddOnFromCollection(dropdownList, AddonCodeEnum.HOME_INSURANCE)
        }

        if (!rateCommand.showWarrantyCarAddon) {
            checkboxList = removeAddOnFromCollection(checkboxList, AddonCodeEnum.WARRANTY_CAR)
            dropdownList = removeAddOnFromCollection(dropdownList, AddonCodeEnum.WARRANTY_CAR)
        }

        if (!rateCommand.showAccidentalLossOfLifeAddon) {
            checkboxList = removeAddOnFromCollection(checkboxList, AddonCodeEnum.ACCIDENTAL_LOSS_OF_LIFE)
            dropdownList = removeAddOnFromCollection(dropdownList, AddonCodeEnum.ACCIDENTAL_LOSS_OF_LIFE)
        }

        if (!rateCommand.showAnnualMultiTravelAddon) {
            checkboxList = removeAddOnFromCollection(checkboxList, AddonCodeEnum.ANNUAL_MULTI_TRAVEL)
            dropdownList = removeAddOnFromCollection(dropdownList, AddonCodeEnum.ANNUAL_MULTI_TRAVEL)
        }

        if (!rateCommand.showSingleTripTravelAddon) {
            checkboxList = removeAddOnFromCollection(checkboxList, AddonCodeEnum.SINGLE_TRIP_TRAVEL)
            dropdownList = removeAddOnFromCollection(dropdownList, AddonCodeEnum.SINGLE_TRIP_TRAVEL)
        }

        log.info("checkboxList:${checkboxList?.size()}, dropDownList:${dropdownList.size()}")

        [checkboxList: checkboxList, dropdownList: dropdownList]

    }

    private Collection removeAddOnFromCollection(Collection collection, AddonCodeEnum addonEnum) {
        collection.removeIf({ c -> c.name.equals(addonEnum.code) })
        return collection
    }

    def applyVAT(def items) {
        items.each { item ->
            item.originalValue = item.value
            item.value = commonUtilService.addVAT(item.value)
            item.value = new BigDecimal(item.value).setScale(2, RoundingMode.CEILING)
        }

        items
    }

    /**
     * Get quote by id and insuranceType
     * @param quoteId
     * @param insurnceType
     * @return
     */
    def getQuoteById(Long quoteId, String insuranceType) {

        insuranceType = insuranceType.toLowerCase()
        def quote

        switch (insuranceType) {
            case ProductTypeEnum.HOME.toString().toLowerCase():
                quote = HomeQuote.get(quoteId)
                break
            case ProductTypeEnum.HEALTH.toString().toLowerCase():
                quote = HealthQuote.get(quoteId)
                break
            default:
                quote = CarQuote.get(quoteId)
                break
        }

        return quote

    }

    /**
     * Gets details for active car quotes by provided user. Active means that the user has paid for the quote,
     * the policy is not in cancellation process at the moment and in case the policy is issued the policy start date
     * should not be older than 13 months(13 months is the maximum lifetime of policy).
     *
     * @param user - the user for whom the active quotes should be retrieved
     * @param quote - only this quote
     * @return list of quote details
     */
    List<CarQuoteDetail> getActiveCarQuoteDetailsList(User user, CarQuote carQuote = null) {
        BuildableCriteria criteria = CarQuoteDetail.createCriteria()

        List<CarQuoteDetail> carQuoteDetailList = (List<CarQuoteDetail>) criteria.list {
            fetchMode("quote.model", FetchMode.JOIN)
            fetchMode("quote.model.make", FetchMode.JOIN)
            quote {
                and {
                    eq('user', user)
                    isNotNull('paidDate')
                    or {
                        isNull('actualPolicyStartDate')
                        gt('actualPolicyStartDate', LocalDate.now().minusMonths(13))
                    }
                    if (carQuote) {
                        eq "id", carQuote.id
                    }
                }

                order("actualPolicyStartDate", "desc")
            }
            not {
                'in'("crmStatus", [
                    CrmStatusEnum.PENDING_CANCEL,
                    CrmStatusEnum.CANCELLATION_DOCS_PENDING,
                    CrmStatusEnum.CANCELLATION_DOCS_UPDATE_PENDING,
                    CrmStatusEnum.CANCELLATION_DOCS_RECEIVED,
                    CrmStatusEnum.CANCELLATION_DOCS_REJECTED,
                    CrmStatusEnum.CANCELLATION_INSURER_REVIEW,
                    CrmStatusEnum.CANCELLATION_APPROVED,
                    CrmStatusEnum.CANCELLATION_PROCESSED
                ])
            }
        }

        carQuoteDetailList
    }

    /**
     * Handles policy cancellation requests. Actually creates Car Policy Cancellation Request records in db
     * with DRAFT status and sends an email to the customer with the list of his active policies in order for
     * him to choose the needed policy and cancel it.
     * @param carQuoteDetailList - the list of car quote detail, one for each active policy
     * @param user - the user requested the cancellation
     * @param country - country of application deployment
     * @param lang - preferred language of the user
     */
    void handlePolicyCancellationRequest(List<CarQuoteDetail> carQuoteDetailList, User user, String country,
                                         String lang, boolean notifyUser = true) {

        carQuoteDetailList.each { CarQuoteDetail carQuoteDetail ->

            // checking if there is already a DRAFT request for this quote
            CarPcr cancellationRequest = CarPcr.findByQuoteAndStatusId(
                carQuoteDetail.quote,
                CarPcrStatusEnum.DRAFT.id
            )

            if (cancellationRequest) {
                // reuse an existing DRAFT request, just update the creation date and status updated date
                DateTime currentDateTime = DateTime.now()
                cancellationRequest.dateCreated = currentDateTime
                cancellationRequest.statusUpdatedDate = currentDateTime
            } else {
                cancellationRequest = new CarPcr(
                    uuid: UUID.randomUUID().toString(),
                    quote: carQuoteDetail.quote,
                    statusUpdatedDate: DateTime.now(),
                    statusId: CarPcrStatusEnum.DRAFT.id
                )

                cancellationRequest.save(flush: true, failOnError: true)
            }

            carQuoteDetail.metaClass.cancelReqId = cancellationRequest.id
            carQuoteDetail.metaClass.cancelReqUuid = cancellationRequest.uuid
        }

        if (notifyUser) {
            notify AsyncEventConstants.CAR_PCR_EMAIL_CONFIRM_REQUEST_TO_CUSTOMER, [
                quoteDetailList: carQuoteDetailList, user: user, productType: ProductTypeEnum.CAR,
                country        : country, lang: lang
            ]
        }
    }

    /**
     * Store User documents data against the provided quote
     *
     * @param quote
     * @param documentsCommand
     */
    void storeUserDocumentsData(CarQuote quote, UserDocumentsCommand documentsCommand) {
        log.info("quote.storeUserDocumentsData - entering with quote:${quote.id}, documentsCommand:${documentsCommand}")

        //Store Emirates ID Document Data
        EmiratesIdDocumentCommand eidCommand = documentsCommand.eidDocument

        if (eidCommand && eidCommand.validate()) {
            EmiratesIdentityDocument eidDocument = EmiratesIdentityDocument.findByCarQuote(quote)
            if (!eidDocument) {
                eidDocument = new EmiratesIdentityDocument(carQuote: quote)
            }


            eidDocument.with {
                idNumber = eidCommand.idn
                nationality = eidCommand.nationality
                name = eidCommand.name
                gender = eidCommand.gender
                dob = commonUtilService.parseLocalDate(eidCommand.dob)
                cardNo = eidCommand.idCardNumber
                expiryDate = commonUtilService.parseLocalDate(eidCommand.idCardExpiryDate)
            }
            eidDocument.save(failOnError: true)
        } else {
            log.error("eidCommand:${eidCommand.errors}")
        }

        //Store Driving License Document Data
        DrivingLicenseDocumentCommand dlCommand = documentsCommand.drivingLicenseDocument

        if (dlCommand && dlCommand.validate()) {
            DrivingLicenseDocument dlDocument = DrivingLicenseDocument.findByCarQuote(quote)
            if (!dlDocument) {
                dlDocument = new DrivingLicenseDocument(carQuote: quote)
            }

            dlDocument.with {
                licenseNo = dlCommand.licenseNumber
                issueDate = commonUtilService.parseLocalDate(dlCommand.issueDate)
                expiryDate = commonUtilService.parseLocalDate(dlCommand.expiryDate)
                trafficCodeNo = dlCommand.trafficCodeNo
                placeOfIssue = dlCommand.licenseSource
                nationality = eidCommand.nationality
                name = eidCommand.name
                dob = commonUtilService.parseLocalDate(eidCommand.dob)
            }
            dlDocument.save(failOnError: true)
        } else {
            log.error("dlDocument:${dlCommand.errors}")
        }

        //Store Vehicle Registration Document Data
        VehicleRegistrationDocumentCommand vrCommand = documentsCommand.vehicleRegistrationDocument

        if (vrCommand && vrCommand.validate()) {
            VehicleRegistrationDocument vrDocument = VehicleRegistrationDocument.findByCarQuote(quote)
            if (!vrDocument) {
                vrDocument = new VehicleRegistrationDocument(carQuote: quote)
            }

            vrDocument.with {
                makeName = quote.model.modelMaster.make.nameEn
                modelName = quote.model.modelMaster.nameEn
                chassisNo = vrCommand.chassisNo
                trafficPlateNo = vrCommand.plateNo
                trafficPlateCode = vrCommand.plateCode
                plateColor = vrCommand.plateColor
                tcNo = vrCommand.trafficFileNumber
                policyNo = vrCommand.insuranceNumber
                owner = vrCommand.owner
                nationality = vrCommand.nationality
                expiryDate = commonUtilService.parseLocalDateTimeZone(vrCommand.regExpiryDate)
                registrationDate = commonUtilService.parseLocalDateTimeZone(vrCommand.registrationDate)
                insuranceExpiryDate = commonUtilService.parseLocalDateTimeZone(vrCommand.insuranceValidUntil)
                engineNo = vrCommand.engineNo
                placeOfIssue = vrCommand.plateSource ?: vrCommand.placeOfIssue
                plateType = vrCommand.plateCategory
                insurer = vrCommand.insuranceCompanyNameEn
                insuranceType = vrCommand.insuranceTypeDescEn
                color = vrCommand.color
                model = vrCommand.modelYear.toInteger()
                noOfPassengers = vrCommand.noOfSeats.toInteger()
                origin = vrCommand.vehicleCountry
                gvw = vrCommand.carryWeight
                emptyWeight = vrCommand.unloadedWeight
                mortgageBy = vrCommand.mortgageCompanyName
            }
            vrDocument.save(failOnError: true)
        } else {
            log.error("vrCommand:${vrCommand.errors}")
        }

    }

    void storeSurveyBooking(CarQuote carQuote, Date bookingDate) {
        SurveyBooking surveyBooking = new SurveyBooking()
        surveyBooking.bookingDate = new LocalDate(bookingDate)
        surveyBooking.carQuote = carQuote
        surveyBooking.save()
    }


    /**
     * Check if a specific Addon is applicable
     * @param rateCommand
     * @return
     */
    private boolean isAddonApplicable(RateCommand rateCommand, String addonCode) {

        boolean isApplicable = false
        CarQuote quote = CarQuote.read(rateCommand.carQuoteId)
        if (addonCode in [AddonCodeEnum.PA_247.code]) {
            isApplicable = quote.userAge >= 18 && quote.userAge < 65
        }
        if (addonCode == AddonCodeEnum.PA_247.code) {
            List<RecurringPayment> recurringPayments = RecurringPayment.findAllByCustomerEmail(quote.email)
            recurringPayments.each {
                if (it.active && !it.endDate && it.paLead) {
                    isApplicable = false
                }
            }
        }
        log.info("${addonCode} Addon is Applicable ${isApplicable}")
        return isApplicable
    }

    /**
     * Check if Sukoon Addon is applicable
     * @param rateCommand
     * @return
     */
    private boolean isAddonApplicableForSukoon(RateCommand rateCommand, String addonCode) {

        boolean isApplicableForSukoon = false
        CarQuote quote = CarQuote.read(rateCommand.carQuoteId)
        if (addonCode in [AddonCodeEnum.ANNUAL_MULTI_TRAVEL.code, AddonCodeEnum.SINGLE_TRIP_TRAVEL.code]) {
            isApplicableForSukoon = quote.userAge >= 18 && quote.userAge < 60
        }

        log.info("${addonCode} Sukoon Addon is Applicable ${isApplicableForSukoon}")
        return isApplicableForSukoon
    }

    /**
     * Remove old Autodata vehicle information and add new one against the quote
     *
     * @param model
     * @param autodataVehId
     * @param autodataVehDesc
     * @param quote
     *//*
    void handleChangeInAutoDataSpecForQuote(Model model, String autodataVehId, String autodataVehDesc, CarQuote quote) {
        log.info("quoteService.handleChangeInAutoDataSpecForQuote - [model:$model, adId:$autodataVehId, adDesc:$autodataVehDesc")

        QuoteExtraField extraField =
            QuoteExtraField.findByInsuranceTypeAndQuoteIdAndExtraFieldCode(InsuranceTypeEnum.CAR,
                quote.id, ExtraFieldCodeEnum.AUTODATA_SPEC_ID)
        if (extraField) {
            extraField.delete()
        }

        //Clear all old quote covers by marking as deleted
        List<CarQuoteCover> carQuoteCovers = CarQuoteCover.findAllByIsDeletedAndQuote(false, quote)
        carQuoteCovers.each {
            it.isDeleted = true
            it.save()
        }

        if (autodataVehId) {
            QuoteExtraField newExtraField = new QuoteExtraField()
            newExtraField.quoteId = quote.id
            newExtraField.insuranceType = InsuranceTypeEnum.CAR
            newExtraField.extraFieldCode = ExtraFieldCodeEnum.AUTODATA_SPEC_ID
            newExtraField.extraFieldValue = autodataVehId
            newExtraField.extraFieldDescription = autodataVehDesc
            newExtraField.save(failOnError: true)

            if (autodataVehId != "0") {
                ModelExternal newModelExternal = new ModelExternal()
                newModelExternal.externalDataSource = ExternalDataSource.load(ExternalDataSourceEnum.AUTODATA.id)
                newModelExternal.externalId = autodataVehId
                newModelExternal.externalIdDescription = autodataVehDesc
                newModelExternal.model = model
                newModelExternal.save(failOnError: true)
            }
        }
    }*/

    /**
     * Get Car valuation based on provided AutoData Specification ID (AdmeID) using Qatar Insurane API.
     * If no valuation is found, fallback to YallaCompare Valuation
     * @param admeId
     * @param quote
     * @return
     */
    def getValuation(String admeId, CarQuote quote) {
        BigDecimal valuationMax = null
        BigDecimal valuationMin = null
        ValuationSourceEnum valuationSource = null
        Country country = quote.quoteCountry

        if (admeId && Integer.parseInt(admeId) > 0) {
            def qicValuation = getQicValuation(admeId, quote.model, quote.year)
            log.info("qicValuation:${qicValuation}")

            if (qicValuation.lowValue) {
                valuationSource = ValuationSourceEnum.QIC_AUTODATA.toString()
                valuationMin = qicValuation.lowValue
                valuationMax = qicValuation.highValue
            }
            //TODO: call NoorApiService in case Qic Valuation isn't working
        }

        if (valuationMin == null || valuationMin == 0) {
            def ycValuation = valuationService.getValuation(quote.model.id, quote.year,
                quote.isBrandNew, country, null, !quote.isNonGcc)
            log.info("ycValuation:${ycValuation}")

            if (ycValuation.minimum) {
                valuationSource = ycValuation.valuationSource
                valuationMin = ycValuation.minimum
                valuationMax = ycValuation.maximum
            }
        }

        return [maximum: valuationMax, minimum: valuationMin, currency: country.currency, valuationSource: valuationSource]
    }

    private getQicValuation(String admeId, Model model, Integer manufactureYear) {
        ExternalDataSource adDataSource = ExternalDataSource.load(ExternalDataSourceEnum.AUTODATA.id)

        Make make = model.modelMaster.make
        ModelMasterExternal adModelMasterExternal = ModelMasterExternal.findByExternalDataSourceAndModelMasterAndModel(adDataSource, model.modelMaster, model)
        if (!adModelMasterExternal) {
            adModelMasterExternal = ModelMasterExternal.findByExternalDataSourceAndModelMaster(adDataSource, model.modelMaster)
        }

        String adMakeCode = MakeExternal.findByExternalDataSourceAndMake(adDataSource, make)?.externalId
        String adModelCode = adModelMasterExternal?.externalId
        String adModelYear = ModelYearExternal.findByExternalDataSourceAndYear(adDataSource, manufactureYear)?.externalId

        def adValuation = qicApiService.getAutoDataValuation(adModelYear,
            adMakeCode, adModelCode, admeId)

        return adValuation
    }

    /**
     * Get autodata trims by year and selected model
     * @param year
     * @param model
     * @param encQuoteId
     * @return
     */
    List<AutoDataTrimDto> getAutoDataTrims(Integer year, Model model, String encQuoteId) {
        //qicApiService.getAutoDataTrims(year, model, encQuoteId)
        return wathbaApiService.getAutoDataTrims(year, model, encQuoteId)
    }

    /**
     * Create Car Quote and update product and pricing
     *
     * @param carQuoteCommand
     * @param vehicleCommand
     * @param driverCommand
     * @return
     */
    CarQuote createCarQuoteAndUpdateProduct(CarQuoteV2Command carQuoteCommand, VehicleCommand vehicleCommand,
                                            DriverCommand driverCommand, Country country, String lang) {
        log.info("quoteService.createCarQuoteAndUpdateProduct - vehicleCommand:$vehicleCommand, driverCommand:$driverCommand")

        def valuation = valuationService.getValuation(vehicleCommand.model, vehicleCommand.year,
            vehicleCommand.isBrandNew, country, vehicleCommand.chassisNumber, !vehicleCommand.isNonGcc)

        log.info("apiV2.car.createQute - carQuoteCommand:${carQuoteCommand.toString()}, valuation:${valuation}")

        CarQuote quote
        Boolean newUser = false

        (quote, newUser) = createCarQuote(driverCommand, vehicleCommand.year,
            vehicleCommand.model, vehicleCommand.makeModelTrim, vehicleCommand.city,
            vehicleCommand.firstRegistrationDate, vehicleCommand.insuredValue,
            vehicleCommand.isNonGcc, vehicleCommand.isExpiredPolicy,
            vehicleCommand.isThirdParty, vehicleCommand.isOldAgency,
            vehicleCommand.purchaseDate, vehicleCommand.isNotBoughtYet,
            vehicleCommand.isBrandNew, vehicleCommand.isFirstCar,
            vehicleCommand.isBuyingUsedCar, carQuoteCommand.queryString,
            valuation.minimum, valuation.maximum, vehicleCommand.oldPolicyExpiryDate,
            CountryEnum.findCountryById(country.id), lang,
            null, null,
            null, null, valuation.valuationSource,
            null, null,
            carQuoteCommand.source, null, null, null, null, null, false,
            null, null, null, null,
            carQuoteCommand.subSource)

        QuoteExtraField extraField = new QuoteExtraField()
        extraField.quoteId = quote.id
        extraField.insuranceType = InsuranceTypeEnum.CAR
        extraField.extraFieldCode = ExtraFieldCodeEnum.COMIN_REFERENCE
        extraField.extraFieldValue = carQuoteCommand.cominReference
        extraField.save(failOnError: true)

        Integer productId = 5053
        if (carQuoteCommand.product) {
            //TODO: parse product and get the required product
        }

       /* def requestParams = [:]
        requestParams.quoteId = quote.id
        requestParams.productId = productId
        requestParams.isAgencyRepair = carQuoteCommand.repairType && carQuoteCommand.repairType == RepairTypeEnum.AGENCY ? "true" : "false"

        RateCommand rateCommand = getRating(quote, productId, carQuoteCommand.repairType)

        apiQuoteService.updateCarQuote(quote, rateCommand, requestParams)*/

        return quote
    }

    void saveQuoteRawData(Long quoteId, InsuranceTypeEnum insuranceType, String rawJsonData) {
        QuoteRawData rawData = new QuoteRawData()
        rawData.quoteId = quoteId
        rawData.insuranceType = insuranceType
        rawData.rawJsonData = rawJsonData
        rawData.save(failOnError: true)
    }

    CarQuote updateCarQuoteDetail(CarQuote quote, CarQuoteDetailV2Command detailV2Command,
                              VehicleCommand vehicleCommand, DriverCommand driverCommand,
                              EmiratesIdDocumentCommand emiratesIdDocCommand,
                              DrivingLicenseDocumentCommand drivingLicenseDocCommand,
                              VehicleRegistrationDocumentCommand vehRegDocCommand) {
        log.info("quoteService.updateCarQuoteDetail - quoteId:${quote.id}, " +
            "detailV2Command:$detailV2Command," +
            "vehicleCommand:$vehicleCommand," +
            "driverCommand:$driverCommand," +
            "emiratesIdDocCommand:$emiratesIdDocCommand," +
            "drivingLicenseDocCommand:$drivingLicenseDocCommand," +
            "vehRegDocCommand:$vehRegDocCommand")

        quote.policyStartDate = new LocalDate(driverCommand.policyStartDate)
        quote.dob = new LocalDate(driverCommand.dob)
        quote.nationality = Country.load(driverCommand.nationality)
        quote.localExperience = DrivingExperience.load(driverCommand.localExperience)
        quote.licenseCountry = Country.load(driverCommand.firstLicenseCountry)
        quote.lastClaimPeriod = driverCommand.lastClaimPeriod
        quote.hasClaim = driverCommand.hasClaim
        quote.noClaimDiscount = NoClaimDiscount.load(driverCommand.ncd)
        quote.insuredValue = vehicleCommand.insuredValue
        quote.year = vehicleCommand.year
        quote.firstRegistrationDate = new LocalDate(vehicleCommand.firstRegistrationDate)
        quote.registrationCity = City.load(vehicleCommand.city)
        quote.isNonGcc = vehicleCommand.isNonGcc.booleanValue()
        quote.isBrandNew = vehicleCommand.isBrandNew.booleanValue()
        //quote.model = Model.load(vehicleCommand.model)
        quote.save(flush: true, failOnError: true)

        QuoteExtraField extraField = new QuoteExtraField()
        extraField.quoteId = quote.id
        extraField.insuranceType = InsuranceTypeEnum.CAR
        extraField.extraFieldCode = ExtraFieldCodeEnum.GIG_MODEL_CODE
        extraField.extraFieldValue = detailV2Command.modelVersion
        extraField.save(failOnError: true)

        UserDocumentsCommand allDocumentsCommand = new UserDocumentsCommand()
        allDocumentsCommand.eidDocument = emiratesIdDocCommand
        allDocumentsCommand.vehicleRegistrationDocument = vehRegDocCommand
        allDocumentsCommand.drivingLicenseDocument = drivingLicenseDocCommand

        storeUserDocumentsData(quote, allDocumentsCommand)

       /* def requestParams = [:]
        requestParams.quoteId = quote.id
        requestParams.productId = GigRateService.PRODUCT_TPL_ID
        requestParams.isAgencyRepair = "false"

        RateCommand rateCommand = getRating(quote, requestParams.productId, null)

        apiQuoteService.updateCarQuote(quote, rateCommand, requestParams)*/

        return quote
    }
}
