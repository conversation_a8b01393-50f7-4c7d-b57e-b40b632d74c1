package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Ratings calculation for Noor Insurance.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class NoorRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 4
    //public static final Integer STANDARD_PRODUCT_ID = 18 //Obsolete
    public static final Integer TPL_PRODUCT_ID = 1043
    public static final Integer MUMTAZ_PRODUCT_ID = 136
    public static final Integer CAR_HIRE_VALUE = 70


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info(".getRates - entering with quoteCommand:${quoteCommand}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).wataniaCategory
        Integer selfDecNoClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
        Integer ncdYears = findNoClaimYears(quoteCommand, selfDecNoClaimYears)
        boolean checkEligibility = checkEligibility(quoteCommand)

        log.info(".getRates - ncdYears:${ncdYears}, nationalityCategory: ${quoteCommand.nationalityCategory}, checkEligibility: ${checkEligibility}")

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info(".getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.noorCategory
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline, ncdYears)
            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("noorRate.getRate - entering with quoteCommand:${quoteCommand}, isOffline:$isOffline")

        RateCommand rateCommand = null
        quoteCommand.nationalityCategory = Country.get(quoteCommand.nationalityId).wataniaCategory
        Integer selfDecNoClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
        Integer ncdYears = findNoClaimYears(quoteCommand, selfDecNoClaimYears)
        boolean checkEligibility = checkEligibility(quoteCommand)
        log.info(".getRate - ncdYears:${ncdYears}, nationalityCategory: ${quoteCommand.nationalityCategory}, checkEligibility: ${checkEligibility}")

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.noorCategory
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline, ncdYears)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate productBaseRate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, productBaseRate)
        /* log.info(".populateRatings - Minimum premium before update: ${rateCommand.minPremium}")
        rateCommand = updateMinimumPremium(quoteCommand, rateCommand)
        log.info(".populateRatings - Minimum premium after update: ${rateCommand.minPremium}") */

        rateCommand = ratingService.checkMinimumPremium(rateCommand)

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    /* RateCommand updateMinimumPremium(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (rateCommand.productId == MUMTAZ_PRODUCT_ID && !rateCommand.agencyRepair) {
            if (quoteCommand.nationalityCategory.equalsIgnoreCase("GR1") &&
                quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value && quoteCommand.insuredValue < 80000) {

                Integer selfDecNoClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
                Integer noClaimYears = findNoClaimYears(quoteCommand, selfDecNoClaimYears)

                BigDecimal discountPercentage = 0
                if (noClaimYears == 1) {
                    discountPercentage = 0.05
                } else if (noClaimYears == 2) {
                    discountPercentage = 0.10
                } else if (noClaimYears >= 3) {
                    discountPercentage = 0.15
                }

                rateCommand.minPremium = rateCommand.minPremium.subtract(rateCommand.minPremium * discountPercentage)
            }
        }

        return rateCommand
    } */

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        boolean isAgency = checkAgency(quoteCommand, applicableRate)
        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency

        rateCommand.productId = product.id

        rateCommand = applyBaseRate(rateCommand, quoteCommand, applicableRate)

        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            //Volkswagen Passat. Noor Consider Passat as 6 cylinder
            Integer noOfCyl = model.noOfCyl
            if (model.modelMasterId == 523 && model.noOfCyl < 6) {
                noOfCyl = 6
            }
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = updateTplMinimumPremium(quoteCommand, rateCommand)
        rateCommand = applyTplDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        //rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (!ratingService.allowAgency()) {
            return false
        }

        boolean isAgency = false

        if (quoteCommand.hasClaim) {
            return false
        }

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            int carAge = quoteCommand.carAge

            if (applicableRate.productId == MUMTAZ_PRODUCT_ID && (quoteCommand.isBrandNew || carAge <= 3)) {
                isAgency = true
            }
        }


        isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        // not eligible if uae driving experience is less then 1 year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId() && quoteCommand.internationalDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id) {
            isEligible = false
        }

        Make make = Make.read(quoteCommand.makeId)
        if (make.countryId == CountryEnum.CHINA.id) {
            isEligible = false
        }

        // Not Eligible if last year had claim

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {

            if (quoteCommand.isPolicyExpired) {
                isEligible = false
            }

            if (quoteCommand.isThirdParty) {
                isEligible = false
            }

        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY) {

            if (quoteCommand.isPolicyExpired &&
                (quoteCommand.policyExpiryMonths == null || quoteCommand.policyExpiryMonths >= 6)) {
                isEligible = false
            }

            if (quoteCommand.carAge > 20) {
                isEligible = false
            }

            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.TWO_TO_THREE.id) {
                isEligible = false
            }
        }

        isEligible
    }


    RateCommand applyTplDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        if (quoteCommand.isPolicyExpired || quoteCommand.isNonGccSpec) {
            //No Discount on TPL if policy is expired or if Non GCC
            return rate
        }

        boolean preferredCase = quoteCommand.registrationCityId in [CityEnum.DUBAI.value(), CityEnum.SHARJAH.value(),
                                                                    CityEnum.ABU_DHABI.value(), CityEnum.AL_AIN.value()]
        boolean isPreferredNationality = isNationalityGroup1ForTpl(quoteCommand.nationalityId)
        log.info("noor.applyDiscount - preferredCase:${preferredCase}, isPreferredNationality:$isPreferredNationality")

        if (quoteCommand.vechileTypeId != VehicleTypeEnum.COUPE.value && isPreferredNationality) {
            Integer selfDecNoClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
            Integer noClaimYears = findNoClaimYears(quoteCommand, selfDecNoClaimYears)

            BigDecimal discountPercentage = 0
            if (noClaimYears == 1) {
                discountPercentage = 0.10
            } else if (noClaimYears == 2) {
                discountPercentage = 0.15
            } else if (noClaimYears >= 3) {
                discountPercentage = 0.20
            }

            if (!quoteCommand.noClaimsDiscountId && selfDecNoClaimYears > 0) {
                rate.requiredSelfDeclarationNumber = selfDecNoClaimYears
            }
            rate.premium = rate.premium.subtract(rate.basePremium * discountPercentage)

        }

        rate
    }

    private int findNoClaimYears(QuoteCommand quoteCommand, Integer selfDecNoClaimYears) {
        if (quoteCommand.noClaimsDiscountId) {
            return quoteCommand.noClaimsDiscountId
        }
        return selfDecNoClaimYears
    }

    private RateCommand applyBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        BigDecimal minPremium = rateCommand.agencyRepair ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        Integer noClaimYearsByNCD = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(quoteCommand)
        Integer noClaimYearsBySelfDec = 0
        if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) && !quoteCommand.isPolicyExpired) {
            noClaimYearsBySelfDec = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
        }
        log.info("noor.applyBaseRate - noClaimYearsBySelfDec:${noClaimYearsBySelfDec}")

        if (noClaimYearsByNCD == null && noClaimYearsBySelfDec != null && !rateCommand.agencyRepair &&
            quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value && rateCommand.productId == MUMTAZ_PRODUCT_ID &&
            quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            rateCommand.requiredSelfDeclarationNumber = noClaimYearsBySelfDec
            baseRate = 3.15
        }

        log.info("Base Rate - ${baseRate}")
        log.info("Minimum Premium - ${minPremium}")
        rateCommand.baseRate = baseRate
        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)

        rateCommand.minPremium = minPremium

        return rateCommand
    }

    /**
     * Preferred nationality for discount
     * @param nationalityId
     * @return
     */
    boolean isNationalityGroup3ForTpl(Integer nationalityId) {
        if (nationalityId == CountryEnum.BANGLADESH.id) {
            return true
        }
        return false
    }

    boolean isNationalityGroup1ForTpl(Integer nationalityId) {
        if (nationalityId in [6, 9, 8, 16, 15, 32, 150, 36, 37, 41, 42, 43, 61, 44, 144, 56,
                              57, 165, 63, 181, 40, 68, 70, 76, 72, 71, 78, 80, 143, 90, 145,
                              91, 92, 86, 104, 99, 102, 96, 111, 112, 238, 124, 125, 126, 129,
                              149, 137, 241, 139, 138, 150, 155, 159, 152, 166, 167, 170, 254,
                              255, 176, 142]) {
            return true
        }
        return false
    }


    boolean isNationalityGroup1ForComp(Integer nationalityId) {
        if (nationalityId in [6, 9, 8, 16, 15, 32, 150, 36, 37, 41, 42, 43, 61, 44, 144, 56,
                              57, 165, 63, 181, 40, 68, 70, 76, 72, 71, 78, 80, 143, 90, 145,
                              91, 92, 86, 104, 99, 102, 96, 111, 112, 238, 124, 125, 126, 129,
                              149, 137, 241, 139, 138, 150, 155, 159, 152, 166, 167, 170, 254,
                              255, 176, 142]) {
            return true
        }
        return false
    }


    /*boolean isNationalityGroup1(QuoteCommand quoteCommand){
        if (quoteCommand.nationalityId in [6, 8, 9, 15, 16, 32, 36, 37, 40, 41, 42, 43, 44,
                                           56, 57, 63, 68, 70, 71, 72, 76, 78, 80, 86, 90, 91, 92, 96, 99, 102, 104, 111, 112,
                                           124, 125, 126, 129, 137, 138, 139, 142, 143, 144, 145, 149, 150, 152, 155, 159, 165,
                                           167, 170, 176, 181, 238, 166]){
            return true
        }

        return false
    }*/

    RateCommand updateTplMinimumPremium(QuoteCommand quoteCommand, RateCommand rate) {
        Model model = Model.findById(quoteCommand.modelId)

        if (isNationalityGroup3ForTpl(quoteCommand.nationalityId)) {
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                if (model.noOfCyl <= 4) rate.premium = 1300
                else if (model.noOfCyl <= 6) rate.premium = 1400
                else if (model.noOfCyl <= 8) rate.premium = 1600
                else if (model.noOfCyl > 8) rate.premium = 2100
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                if (model.noOfCyl <= 4) rate.premium = 1750
                else if (model.noOfCyl <= 6) rate.premium = 1900
                else if (model.noOfCyl <= 8) rate.premium = 1950
                else if (model.noOfCyl > 8) rate.premium = 2150
            }
        }
        return rate
    }
}
