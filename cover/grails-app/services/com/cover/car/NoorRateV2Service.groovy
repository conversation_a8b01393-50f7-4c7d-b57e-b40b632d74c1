package com.cover.car

import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.noor.NoorQuoteCommand
import com.cover.car.noor.NoorRateCommand
import com.safeguard.AddonCodeEnum
import com.safeguard.ClaimPeriodEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.ExtraFieldCodeEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.NoClaimDiscount
import com.safeguard.Product
import com.safeguard.Provider
import com.safeguard.QuoteExtraField
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.MakeExternal
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMasterExternal
import com.safeguard.car.vehicle.ModelYearExternal
import com.safeguard.car.vehicle.VehicleType
import com.safeguard.car.vehicle.VehicleTypeExternal
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime

@Transactional(readOnly = true)
class NoorRateV2Service extends BaseRatingService  {

    def noorRateService
    def noorApiService
    def ratingService

    public static Integer PROVIDER_ID = 4

    public static Integer ASAS_ID = 135
    public static Integer MUMTAZ_ID = 136
    public static Integer ALMASA_ID = 142
    public static Integer EDGE_ID = 141
    public static Integer THIRD_PARTY_ID = 1043
    public static Integer NW_COMPREHENSIVE_SME_ID = 143

    public static AG_MUMTAZ_SCHEME_CODES = ['PL57', 'PL58', 'PL59', 'PL60', 'PL61', 'PL62']
    public static AG_EDGE_SCHEME_CODES = ['PL63', 'PL64']
    public static AG_ALMASA_SCHEME_CODES = ['PL65', 'PL66']
    public static AG_ASAS_SCHEME_CODES = ['PL67']
    public static AG_THIRD_PARTY_LIABILITY_CODES = ['PL68', 'PL69', 'PL70']
    public static NW_COMPREHENSIVE_SME_CODES = ['PL28', 'PL29', 'PL30', 'PL31', 'PL32']

    /***
     * Get rates form Noor's API
     * @param quoteCommand
     * @param coverageTypeEnum
     * @param isOffline
     * @return
     */
    List<RateCommand> getRates(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum, boolean isOffline) {

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.lastClaimPeriod == ClaimPeriodEnum.TWELVE_MONTHS) {
            // don't show comprehensive quotes if there are claims in the previous year
            return []
        }

        InsuranceProviderEnum noorProvider = InsuranceProviderEnum.NOOR_TAKAFUL
        Boolean showRatings = showProviderRatings(noorProvider.id, isOffline)
        showRatings = showRatings && ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)
        if (!showRatings) {
            return []
        }

        CarQuote carQuote = CarQuote.load(quoteCommand.quoteId)
        QuoteExtraField quoteExtraField = QuoteExtraField.findByInsuranceTypeAndQuoteIdAndExtraFieldCode(InsuranceTypeEnum.CAR, carQuote.id, ExtraFieldCodeEnum.AUTODATA_SPEC_ID)
        if (!quoteExtraField || quoteExtraField.extraFieldValue == "0") {
            return []
        }
        List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
            findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, carQuote,
                Provider.load(noorProvider.id), LocalDateTime.now(), CoverageType.load(coverageTypeEnum.value()), [sort: "id"])

        NoorQuoteCommand noorQuoteCommand = NoorQuoteCommand.generateNoorQuoteCommand(quoteCommand)
        List<NoorRateCommand> noorRateCommands = []
        if (carQuoteCovers) {
            carQuoteCovers.each { CarQuoteCover carQuoteCover ->
                JSONElement cover = JSON.parse(carQuoteCover.covers)
                if (cover.Error == "1") {
                    return
                }
                noorRateCommands += noorApiService.convertSchemeToRateCommand(cover.schemes[0], carQuoteCover.insuredValue)
            }
        } else {
            noorQuoteCommand = toNoorQuoteCommand(quoteCommand,  CoverageTypeEnum.COMPREHENSIVE)
            try {
                noorRateCommands = noorApiService.getBaseRateMinPremium(noorQuoteCommand, Model.read(quoteCommand.modelId), coverageTypeEnum, quoteCommand.isBrandNew)
            } catch (Exception e) {
                log.error("NoorRateV2Service.getRates - Exception occurred while fetching rates for Noor", e)
            }
        }

        List<RateCommand> rateCommands = []
        noorRateCommands.each { NoorRateCommand noorRateCommand ->
            RateCommand rateCommand = toRateCommand(noorRateCommand, noorQuoteCommand, quoteCommand)
            if (rateCommand) {
                rateCommand = populateRatings(ratingService, noorQuoteCommand, rateCommand)
                rateCommands += rateCommand
            }
        }

        rateCommands
    }

    /**
     * Convert QuoteCommand instance to NoorQuoteCommand instance
     * @param quoteCommand
     * @param coverageTypeEnum
     * @return
     */
    private NoorQuoteCommand toNoorQuoteCommand(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum) {
        NoorQuoteCommand noorQuoteCommand = NoorQuoteCommand.generateNoorQuoteCommand(quoteCommand)

        ExternalDataSource noorDataSource = ExternalDataSource.read(ExternalDataSourceEnum.NOOR_TAKAFUL.id)
        Make make = Make.load(quoteCommand.makeId)
        Model model = Model.load(quoteCommand.modelId)
        noorQuoteCommand.vMake = MakeExternal.findByExternalDataSourceAndMake(noorDataSource, make).externalId.toInteger()
        noorQuoteCommand.vModel = ModelMasterExternal.findByExternalDataSourceAndModelMaster(noorDataSource, model.modelMaster).externalId.toInteger()
        noorQuoteCommand.vManuf = ModelYearExternal.findByExternalDataSourceAndYear(noorDataSource, quoteCommand.manufactureYear).externalId.toInteger()
        noorQuoteCommand.vBody = VehicleTypeExternal.findByExternalDataSourceAndVehicleType(noorDataSource, model.vehicleType).externalId.toInteger()

        noorQuoteCommand.noOfCylinders = model.noOfCyl.toString()
        noorQuoteCommand.licenseCountry = Country.read(quoteCommand.nationalityId).code
        noorQuoteCommand.coverdpassengers = model.numberOfSeats - 1
        noorQuoteCommand.seats = model.numberOfSeats
        noorQuoteCommand.sumInsured = quoteCommand.insuredValue
        noorQuoteCommand.providerId = InsuranceProviderEnum.NOOR_TAKAFUL.id

        ClaimPeriodEnum claimPeriodEnum = quoteCommand.lastClaimPeriod
        Integer noClaimYears
        Integer experienceInYears = DrivingExperienceEnum.findById(quoteCommand.localDrivingExperienceId).experienceInYears
        if (quoteCommand.noClaimsDiscountId) {
            noClaimYears = NoClaimDiscount.read(quoteCommand.noClaimsDiscountId).years
        } else {
            if (claimPeriodEnum == ClaimPeriodEnum.NEVER) {
                noClaimYears = experienceInYears
            } else {
                noClaimYears = claimPeriodEnum.noClaimYears()
            }
        }
        noorQuoteCommand.ncbYear = noClaimYears

        LocalDate drivingLicenceIssueDate = new LocalDate()
        drivingLicenceIssueDate = drivingLicenceIssueDate.minusYears(experienceInYears)
        noorQuoteCommand.eDate = drivingLicenceIssueDate.toString("yyyy/MM/dd")

        noorQuoteCommand
    }

    /**
     * Convert NoorRateCommand instance to RateCommand instance
     * @param noorRateCommand
     * @param noorQuoteCommand
     * @return
     */
    private RateCommand toRateCommand(NoorRateCommand noorRateCommand, NoorQuoteCommand noorQuoteCommand, QuoteCommand quoteCommand) {
        RateCommand rateCommand = new RateCommand()

        Integer productId = getProductId(noorRateCommand.schemeCode)
        if (!productId) {
            return null
        }

        rateCommand.productId = productId
        rateCommand = ratingService.applyCovers(noorQuoteCommand, rateCommand)

        rateCommand.insuredValue = noorRateCommand.insuredValue
        rateCommand.excess = noorRateCommand.excess
        rateCommand.premium = noorRateCommand.netPremium
        rateCommand.currency = noorQuoteCommand.currency
        rateCommand.agencyRepair = !(noorRateCommand.schemeName.contains("Non Agency") || noorRateCommand.schemeName.contains("Non-Agency") || rateCommand.productId == THIRD_PARTY_ID)
        if (rateCommand.productId != THIRD_PARTY_ID && !rateCommand.agencyRepair) {
            rateCommand.hasPremiumGarage = true
        }

        rateCommand.dynamicAddons = noorRateCommand.optionalCovers.
            findAll {it.premium != "0" }.
            collect {
                [label: it.name, price: new BigDecimal(it.premium), providerCode: it.code,
                 code: AddonCodeEnum.DYNAMIC_ADDON.code,
                 description: it.help]
            }

        List allCovers = []
        allCovers.addAll(noorRateCommand.optionalCovers)
        allCovers.addAll(noorRateCommand.covers)
        ProviderRateCommand.RateCoverCommand pax = allCovers.find {
            it.code == CarCoversEnum.PAB_PASSENGER.noorCode }
        rateCommand.personalAccidentPax = pax ? (pax.premium == "0" ? "yes" : pax.premium) : "no"

        ProviderRateCommand.RateCoverCommand pad = allCovers.find {
            it.code == CarCoversEnum.PAB_DRIVER.noorCode }
        rateCommand.paCover = pad ? (pad.premium == "0" ? "yes" : pad.premium) : "no"

        ProviderRateCommand.RateCoverCommand rac = allCovers.find {
            it.code == CarCoversEnum.RENT_A_CAR_7.noorCode }
        rateCommand.replacementCar = rac ? (rac.premium == "0" ? "yes" : rac.premium) : null

        if (!rateCommand.replacementCar) {
            ProviderRateCommand.RateCoverCommand rac2 = allCovers.find {
                it.code == CarCoversEnum.RENT_A_CAR_7_2.noorCode
            }
            rateCommand.replacementCar = rac2 ? (rac2.premium == "0" ? "yes" : rac2.premium) : "no"
        }

        ProviderRateCommand.RateCoverCommand offroadCover = allCovers.find {
            it.code == CarCoversEnum.OFF_ROAD_COVER_4WD_ONLY.noorCode }
        rateCommand.offRoadDesertRecovery = offroadCover ? (offroadCover.premium == "0" ? "yes" : offroadCover.premium) : "no"

        ProviderRateCommand.RateCoverCommand carRegService = allCovers.find {
            it.code == CarCoversEnum.CAR_REGISTRATION.noorCode }
        rateCommand.carRegService = carRegService ? (carRegService.premium == "0" ? "yes" : carRegService.premium) : "no"

        ProviderRateCommand.RateCoverCommand damageToYourVehicleOman = allCovers.find {
            it.code == CarCoversEnum.OMAN_OD.noorCode }
        rateCommand.damageToYourVehicleOman = damageToYourVehicleOman

        ProviderRateCommand.RateCoverCommand rsaSilver = allCovers.find {
            it.code == CarCoversEnum.RSA_SILVER.noorCode }
        rateCommand.breakdownCover = rsaSilver ? 'silver' : "none"

        if (rateCommand.productId == MUMTAZ_ID && quoteCommand.vechileTypeId != VehicleTypeEnum.FOURx4.value) {
            rateCommand.offRoadDesertRecovery = "no"
        }

        rateCommand
    }

    /**
     * Get product ID from scheme code
     * @param schemeCode
     * @return
     */
    Integer getProductId(String schemeCode) {
        if (schemeCode in AG_MUMTAZ_SCHEME_CODES) {
            return MUMTAZ_ID
        }

        if (schemeCode in AG_ASAS_SCHEME_CODES) {
            return ASAS_ID
        }

        if (schemeCode in AG_ALMASA_SCHEME_CODES) {
            return ALMASA_ID
        }

        if (schemeCode in AG_EDGE_SCHEME_CODES) {
            return EDGE_ID
        }

        if (schemeCode in AG_THIRD_PARTY_LIABILITY_CODES) {
            return THIRD_PARTY_ID
        }

        if (schemeCode in NW_COMPREHENSIVE_SME_CODES) {
            return NW_COMPREHENSIVE_SME_ID
        }
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        getRate(quoteCommand, isOffline, CoverageTypeEnum.THIRD_PARTY)
    }

    /**
     * Fetches rate from Noor's API
     * @param quoteCommand
     * @param isOffline
     * @return
     */
    RateCommand getRate(QuoteCommand quoteCommand, Boolean isOffline, CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {
        NoorQuoteCommand noorQuoteCommand = NoorQuoteCommand.generateNoorQuoteCommand(quoteCommand)

        boolean checkEligibility = ratingService.generalEligibilityCheck(quoteCommand)
        Integer providerId = InsuranceProviderEnum.NOOR_TAKAFUL.id

        if (!checkEligibility) {
            return []
        }

        NoorRateCommand noorRateCommand
        RateCommand rateCommand = null
        Model model = Model.read(quoteCommand.modelId)
        String schemeCode = noorApiService.getSchemeCode(Product.read(quoteCommand.productId), quoteCommand.selectedRepairType, VehicleTypeEnum.findById(model.vehicleTypeId.toInteger()))
        CarQuote quote = CarQuote.load(quoteCommand.quoteId)
        CarQuoteCover carQuoteCover = CarQuoteCover.
            findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageTypeAndSchemeCode(false, quote,
                Provider.load(providerId), LocalDateTime.now(), CoverageType.load(coverageTypeEnum.value()), schemeCode)

        if (carQuoteCover) {
            JSONElement cover = JSON.parse(carQuoteCover.covers)
            if (cover.Error == "1") {
                return rateCommand
            }
            noorRateCommand = noorApiService.convertSchemeToRateCommand(cover.schemes[0], carQuoteCover.insuredValue)
            rateCommand = toRateCommand(noorRateCommand, noorQuoteCommand, quoteCommand)
            if (rateCommand) {
                rateCommand = populateRatings(ratingService, noorQuoteCommand, rateCommand)
            }
        }

        return rateCommand
    }

    /**
     * Get Premium without VAT
     * @param carQuote
     * @param providerQuoteNo
     * @param providerAddonCodes
     * @return
     */
    BigDecimal getGrossPremium(CarQuote carQuote, String providerQuoteNo, List<String> providerAddonCodes) {

        Product product = carQuote.product
        String schemeCode = noorApiService.getSchemeCode(product, carQuote.isAgencyRepair ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE, VehicleTypeEnum.findById(carQuote.model.vehicleTypeId))
        String encryptedQuoteId = AESCryption.encrypt(carQuote.id.toString())
        CarQuoteCover carQuoteCover = CarQuoteCover.findByProviderAndProviderQuoteNoAndSchemeCodeAndQuote(Provider.load(InsuranceProviderEnum.NOOR_TAKAFUL.id), providerQuoteNo, schemeCode, carQuote)

        NoorQuoteCommand noorQuoteCommand = new NoorQuoteCommand()
        noorQuoteCommand.quoteId = carQuote.id
        noorQuoteCommand.providerId = InsuranceProviderEnum.NOOR_TAKAFUL.id
        CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.findById(carQuoteCover.coverageTypeId.toInteger())
        NoorRateCommand noorRateCommand = noorApiService.calculatePremium(providerQuoteNo, schemeCode, noorQuoteCommand, carQuoteCover.insuredValue, coverageTypeEnum, encryptedQuoteId, null, providerAddonCodes)
        noorRateCommand.netPremium
    }
}
