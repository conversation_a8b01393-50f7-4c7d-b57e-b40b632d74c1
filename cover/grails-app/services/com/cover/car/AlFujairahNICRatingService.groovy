package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.joda.time.LocalDateTime

@Transactional
class AlFujairahNICRatingService {
    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 32
    public static final Integer AFNIC_Comprehensive_Basic = 1013
    public static final Integer AFNIC_Comprehensive_Enhanced = 1014

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }
        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }
        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)

            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId, model.noOfCyl, quoteCommand.customerAge,
                        isOffline, null, true, quoteCommand.requestSource)
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }

        }
        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)

        rateCommand = applyLoadings(quoteCommand, rateCommand)

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }
        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand.baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            //Calculating the car make
            int carMakeYear = ((quoteCommand.policyStartDate.year - quoteCommand.manufactureYear) + 1)

            //2 years from  the registration date and make not older than 2 years
            if (quoteCommand.carAge <= 1) {
                isAgency = true
            } else if (quoteCommand.carAge == 2 && carMakeYear <= 2 && !quoteCommand.hasClaim && quoteCommand.isOldAgency) {
                if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
                    isAgency = true
                }
            }
        }
        return isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        //Conditions only from comprehensive
        if (productTypeEnum.value() == CoverageTypeEnum.COMPREHENSIVE.value()) {

            //Driving Experience less then 1 year
            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                isEligible = false
            }

            //If Shifting from third party to comprehensive
            if (quoteCommand.isThirdParty) {
                isEligible = false
            }

            //If insurance already expired
            if (quoteCommand.isPolicyExpired) {
                isEligible = false
            }
        }
        //Condition for third party
        if (productTypeEnum.value() == CoverageTypeEnum.THIRD_PARTY.value()) {
            //Driving Experience less then 1 year
            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                isEligible = false
            }
        }
        isEligible
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (checkAgency(quoteCommand)) {
            //For Agency Repair with one year no claim certificate 15% loading
            if ((rateCommand.basePremium * (0.15)) < 300) {
                rateCommand.premium = rateCommand.premium.add(300)
            } else {
                rateCommand.premium = rateCommand.premium.add(rateCommand.basePremium * (0.15))
            }
        }

        //If Customer Age is from 21-23 then 20% loading
        //if(quoteCommand.customerAge >= 21 && quoteCommand.customerAge <= 23) {
        if (quoteCommand.customerAge <= 23) {
            rateCommand.premium = rateCommand.premium.add(rateCommand.basePremium * (0.20))
        }

        rateCommand
    }

    RateCommand applyAdditionalFees(RateCommand rateCommand, QuoteCommand quoteCommand) {
        //For AFNIC Enhance plan
        //adding oman cover,personal accident driver,personal accident passenger,replacement car
        if (rateCommand.productId == AFNIC_Comprehensive_Enhanced) {
            rateCommand.premium = rateCommand.premium.add(50)
            rateCommand.premium = rateCommand.premium.add((30 * (Model.findById(quoteCommand.modelId).numberOfSeats-1)))
            rateCommand.premium = rateCommand.premium.add(120)
            rateCommand.premium = rateCommand.premium.add(150)
        }
        rateCommand
    }
}
