package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.home.commands.HomeRateCommand
import com.cover.pet.commands.PetRateCommand
import com.cover.travel.TravelRateCommand
import com.cover.util.IConstant
import com.safeguard.AddonCodeEnum
import com.safeguard.AsyncEventConstants
import com.safeguard.CallRequest
import com.safeguard.CarPcrRejectionReasonEnum
import com.safeguard.Constants
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.CrmStatusEnum
import com.safeguard.DonationTypeEnum
import com.safeguard.EmailJob
import com.safeguard.EmailJobEnum
import com.safeguard.GenderEnum
import com.safeguard.KnetPaymentResponse
import com.safeguard.KnetPaymentStatusEnum
import com.safeguard.MandrillMessage
import com.safeguard.PaLead
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentPlanEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.ProductTypeEnum
import com.safeguard.Provider
import com.safeguard.ProviderContactToRole
import com.safeguard.Donation
import com.safeguard.QuoteResult
import com.safeguard.RequestSourceEnum
import com.safeguard.SubRequestSourceEnum
import com.safeguard.Subscription
import com.safeguard.User
import com.safeguard.base.Lead
import com.safeguard.boat.BoatQuote
import com.safeguard.business.BusinessQuote
import com.safeguard.car.CarPcr
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.CarQuoteKsa
import com.safeguard.fleet.FleetQuote
import com.safeguard.gadget.GadgetQuote
import com.safeguard.gap.GapQuote
import com.safeguard.general.GeneralQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.life.LifeQuote
import com.safeguard.motorbike.MotorbikeQuote
import com.safeguard.payment.QuoteAdditionalPayment
import com.safeguard.pet.PetQuote
import com.safeguard.sendinblue.SendinblueMessage
import com.safeguard.sendinblue.SendinblueRecipient
import com.safeguard.sendinblue.SendinblueService
import com.safeguard.travel.TravelDestinationCountryZoneEnum
import com.safeguard.travel.TravelQuote
import com.safeguard.travel.Traveler
import com.safeguard.util.AESCryption
import com.safeguard.watch.WatchQuote
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.converters.JSON
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import grails.util.Environment
import grails.util.Holders
import org.apache.commons.lang3.StringUtils
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.springframework.util.Assert
import reactor.bus.Event
import reactor.spring.context.annotation.Consumer
import reactor.spring.context.annotation.Selector
import com.safeguard.life.LifeAdditionalInfo

@Consumer
@Transactional
class EmailService {

    def commonUtilService
    def emailJobService
    def grailsApplication
    def groovyPageRenderer
    def homeQuoteService
    def kwtQuoteService
    def lbnQuoteService
    def egyQuoteService
    def mailChimpService
    def mailService
    def quoteService
    def mandrillMessageService
    def callRequestService
    def travelQuoteService
    def emailSgService
    def sendinblueService
    def messageSource
    def emailCheckerService
    def utilService
    def petRatingService
    def lifeUtilService
    def commonPolicySgService

    @Selector(AsyncEventConstants.CAR_QUOTE_CREATED)
    def sendCarQuoteEmail(Event event) {
        Integer quoteId = event.data.quoteId

        if (QuoteResult.findByQouteId(quoteId)) {
            //No need to proceed if quote results are already stored
            return
        }

        // wait for 30 seconds to avoid concurrent transaction exception
        Thread.sleep(30000L)

        log.info "Sending car quote email for car quote ${event.data}"

        CarQuote carQuote = CarQuote.get(quoteId)
        if (carQuote.paymentStatus == PaymentStatusEnum.NOQUOTE ||
            carQuote.modelId.toInteger() == QuoteCommand.UNKNOWN_MODEL_ID) {
            log.info("emailService.sendCarQuoteEmail - NoQuote status, no need to send email for car quote ${event.data} ")
            return
        }

        if (carQuote.requestSubSource == SubRequestSourceEnum.COMIN.toString()) {
            log.info("emailService.sendCarQuoteEmail - COMIN Quote, skipping create quote email. ${event.data} ")
            return
        }

        log.info("Subscription:${Subscription.get(carQuote.user.id).hardBounce}")
        boolean isEmailVerified = emailCheckerService.isEmailVerified(carQuote.email)

        QuoteCommand quoteCommand = quoteService.toQuoteCommand(carQuote)
        def ratings
        if(quoteCommand.countryEnum == CountryEnum.UAE) {
            ratings = quoteService.getRatings(quoteCommand, Boolean.FALSE)

            if (carQuote.requestSource in [RequestSourceEnum.NATIONALBONDS, RequestSourceEnum.ADIB]) {
                ratings = ratings.findAll{
                    it.isTakaful
                }
            }

            //quoteService.saveAllQuotes(ratings, quoteId as Long)

        } else if (quoteCommand.countryEnum == CountryEnum.LBN) {
            ratings = lbnQuoteService.getRatings(quoteCommand, Boolean.FALSE)
            lbnQuoteService.saveAllQuotes(ratings, carQuote)

        } else if (quoteCommand.countryEnum == CountryEnum.KWT) {
            ratings = kwtQuoteService.getRatings(quoteCommand, Boolean.FALSE)
            kwtQuoteService.saveAllQuotes(ratings, carQuote)

        } else if (quoteCommand.countryEnum == CountryEnum.EGYPT) {
            ratings = egyQuoteService.getRatings(quoteCommand, Boolean.FALSE)
            egyQuoteService.saveAllQuotes(ratings, carQuote)

        }
        log.info("emailService - checking ratings")

        if (ratings) {
            log.info("emailService - ratings found")
            ratings.sort { a, b ->
                // Compare by type and then by premium
                a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
            }
            if (quoteCommand.countryEnum == CountryEnum.UAE) {
                quoteService.saveAllQuotes(ratings, quoteId as Long)
            }

            if (carQuote.createdBySalesPerson) {
                log.info("emailService.sendCarQuoteEmail - Quote:${carQuote.id} is created by Sales Agent, no need to send email.")
                return
            }

            RateCommand tplRateCommand = ratings.find { RateCommand rateCommand ->
                rateCommand.coverageTypeId == CoverageTypeEnum.THIRD_PARTY.value() &&
                    rateCommand.isOfflineQuotes == false
            }

            RateCommand compRateCommand = ratings.find { RateCommand rateCommand ->
                rateCommand.coverageTypeId == CoverageTypeEnum.COMPREHENSIVE.value() && !rateCommand.agencyRepair &&
                    rateCommand.isOfflineQuotes == false
            }

            RateCommand compAgencyRateCommand = ratings.find { RateCommand rateCommand ->
                rateCommand.coverageTypeId == CoverageTypeEnum.COMPREHENSIVE.value() && rateCommand.agencyRepair &&
                    rateCommand.isOfflineQuotes == false
            }

            String baseUrl = event.data.baseUrl

            if (carQuote.quoteCountry.id == CountryEnum.EGYPT.id.toInteger()){
                log.warn "Egypt Quote email won't be sent as no ratings for quoteId ${quoteId}"
                return
            }

            if (carQuote.quoteCountry.id != CountryEnum.UAE.id.toInteger()
                || !(carQuote.requestSource in [RequestSourceEnum.WEB, RequestSourceEnum.WEB_CAR, RequestSourceEnum.ZOHO, RequestSourceEnum.MOBILEAPP])) {
                log.info("emailService - non uae and non web quote")

                ratings?.removeAll{it.providerId == com.cover.car.egy.EgyAropeRateService.PROVIDER_ID}
                RateCommand rateCommand = ratings?.first()
                emailJobService.createSrpEmail(carQuote, rateCommand.providerImage, rateCommand.provider,
                    rateCommand.excess, rateCommand.premium, rateCommand.agencyRepair, null, baseUrl)
            }
            else {
                log.info("emailService - checking verified email")

                if (isEmailVerified) {
                    EmailJobEnum emailJobEnum = EmailJobEnum.CAR_QUOTE_SRP_V2
                    EmailJob emailJob = new EmailJob()

                    emailJob.with {
                        isSent = false
                        isError = false
                        tags = emailJobEnum.tags
                        type = EmailJobEnum.CAR_QUOTE_SRP_V2
                        quotesCount = ratings.size()

                        tplPremium = tplRateCommand?.premium
                        tplLimit = tplRateCommand?.thirdPartyLiabilityAmount
                        tplRoadside = utilService.getRoadsideAssistanceByLang(tplRateCommand?.getRoadsideAssistanceSummary(), carQuote.lang)
                        tplProduct = tplRateCommand ? Product.read(tplRateCommand.productId) : null

                        compPremium = compRateCommand?.premium
                        compExcess = compRateCommand?.excess
                        compRoadside = utilService.getRoadsideAssistanceByLang(compRateCommand?.getRoadsideAssistanceSummary(), carQuote.lang)
                        compProduct = compRateCommand ? Product.read(compRateCommand.productId) : null

                        compAgencyPremium = compAgencyRateCommand?.premium
                        compAgencyExcess = compAgencyRateCommand?.excess
                        compAgencyRoadside = utilService.getRoadsideAssistanceByLang(compAgencyRateCommand?.getRoadsideAssistanceSummary(), carQuote.lang)
                        compAgencyProduct = compAgencyRateCommand ? Product.read(compAgencyRateCommand.productId) : null
                    }
                    emailJob.carQuote = carQuote
                    emailJob.lang = carQuote.lang
                    emailJob.toAddress = carQuote.email
                    emailJob.baseUrl = baseUrl

                    if (carQuote.requestSource in [RequestSourceEnum.NATIONALBONDS, RequestSourceEnum.ADIB]) {
                        emailJob.subject = messageSource.getMessage("takaful.recent.quote", [].toArray(), (carQuote.lang == "ar" ? new Locale("ar") : Locale.ENGLISH))
                    }
                    log.info("emailService - saving email job")

                    emailJob.save(flush: true, failOnError: true)
                    log.info("emailService - emailJob:${emailJob}")
                }
            }
        } else {
            log.warn "Quote email won't be sent as no ratings for quoteId ${quoteId}"
        }

        if (isEmailVerified) {
            mailChimpService.subscribe(carQuote.email, carQuote, ProductTypeEnum.CAR)
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.EMAIL_HOME_QUOTE_CREATED)
    def sendHomeQuoteEmail(Event event) {

        // send quote email after 3 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        Integer quoteId = event.data.quoteId
        String lang = event.data.lang
        log.info "Sending home quote email for home quote ${event.data.quoteId}"

        HomeQuote homeQuote = HomeQuote.get(quoteId)
        List<HomeRateCommand> ratings = homeQuoteService.getRatings(homeQuoteService.toHomeQuoteCommand(homeQuote))

        if (ratings) {
            User user = User.findByEmail(homeQuote.email)
            if (user.shouldSendEmail()) {
                String subjectText = (lang == "en") ? "Your recent home insurance quote" : "سعر بوليصة تأمينك المنزلي حاليا"

                //If current env is not production
                if (Environment.current != Environment.PRODUCTION) subjectText = "[${Environment.current}] ${subjectText}"

                String encId = AESCryption.encrypt(homeQuote.id.toString())

                HomeRateCommand rateCommand = ratings.sort { it.premium }.first()

                if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(homeQuote.email)) {
                    String emailTemplate = groovyPageRenderer.render([view: "/common/email", model: [
                        quote: homeQuote,
                        rate: rateCommand,
                        encId: encId,
                        emailBody: '/homeInsurance/emails/quoteCreated',
                        locale: lang,
                        country: event.data.country,
                        baseUrl  : grailsApplication.config.getProperty('yallacompare.baseURL'),
                        dir: lang == 'ar' ? 'rtl' : 'ltr',
                        imageCode: "compareit4meLogo.url"
                    ]])

                    if (emailCheckerService.isEmailVerified(homeQuote.email)) {
                        mailService.sendMail {
                            to homeQuote.email
                            headers "X-MC-Tags": "home, quote",
                                "X-MC-Important": "true"
                            replyTo grailsApplication.config.grails.mail.default.replyTo
                            subject subjectText
                            html emailTemplate
                        }
                        mailChimpService.subscribe(homeQuote.email, homeQuote, ProductTypeEnum.HOME)
                    }

                } else {
                    log.warn "Quote email won't be sent to ${homeQuote.email} on non production env"
                }
            } else {
                log.warn "Home Quote email won't be sent to ${homeQuote.email} as blacklisted"
            }
        } else {
            log.warn "Quote email won't be sent as no ratings for quoteId ${quoteId}"
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.EMAIL_TRAVEL_QUOTE_CREATED)
    def sendTravelQuoteEmail(Event event) {
        // send quote email after 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        Integer quoteId = event.data.quoteId
        String lang = event.data.lang
        String country = event.data.country

        log.info "Sending quote email for travel quote ${event.data.quoteId}"

        TravelQuote travelQuote = TravelQuote.get(quoteId)
        List<TravelRateCommand> ratings = travelQuoteService.getRatings(travelQuote)

        if (ratings && ratings.size()) {
            String encId = AESCryption.encrypt(travelQuote.id.toString())

            TravelRateCommand rateCommand = ratings.sort { it.totalPrice }.first()

            SendinblueMessage sendinblueMessage = new SendinblueMessage()
            sendinblueMessage.to += new SendinblueRecipient(name: travelQuote.name, email: travelQuote.email)
            sendinblueMessage.templateId = SendinblueService.SRP_TRAVEL
            sendinblueMessage.params = [
                name        :   travelQuote.name,
                allLink     :   "${grailsApplication.config.getProperty('server.cover')}/${country}/${lang}/travel/quotes/${encId}",
                numQuotes   :   ratings.size(),
                lowestPrice :   rateCommand.totalPrice
            ]
            sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

            log.info("Sending Travel Quote email: [id: ${travelQuote.id}] to ${travelQuote.email}")

            sendinblueService.sendEmail(sendinblueMessage)

        } else {
            log.warn "Travel Quote email won't be sent as no ratings for quoteId ${quoteId}"
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CAR_QUOTE_PURCHASED)
    def sendQuotePurcahsedEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        log.info "Sending quote purchased email for car quote ${event.data}"

        String lang = event.data.lang

        CarQuote carQuote = CarQuote.get(event.data.quoteId)

        if (carQuote.requestSubSource == SubRequestSourceEnum.COMIN.toString()) {
            log.info("emailService.sendQuotePurcahsedEmail - COMIN Quote, skipping purchase quote email. ${event.data} ")
            return
        }

        if (carQuote.user.shouldSendTransactionalEmail()) {

            Donation donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(carQuote.id, ProductTypeEnum.CAR.value(), DonationTypeEnum.CHARITY)
            log.info("emailService.sendQuotePurcahsedEmail - quote:${carQuote.id}, donation id:${donation?.id}")
            CarQuoteAddon[] addons = CarQuoteAddon.findAllByCarQuoteAndIsDeleted(carQuote, false)

            def bulletServiceAddon
            def homeInsuranceAddon
            def lifeInsuranceAddon

            BigDecimal bulletServiceAddonVat
            BigDecimal homeInsuranceAddonVat
            BigDecimal lifeInsuranceAddonVat

            List addonList = []

            addons.collect {

                def addon = [:]
                if ('ar'.equalsIgnoreCase(lang)) {
                    addon.label = it.addonTranslation.displayAr
                    addon.display = it.addonTranslation.displayAr
                } else {
                    addon.label = it.addonTranslation.labelEn
                    addon.display = it.addonTranslation.displayEn
                }

                // life Insurance addon on car insurance
                if(it.addonTranslation.code == AddonCodeEnum.LIFE_INSURANCE.code){
                    try{
                        Long rateId = it.code.split('-')[1] as Long
                        if(rateId){
                            String str = lifeUtilService.getRateLabelByRateId(rateId, event.data.country)
                            addon.label = str
                            addon.display = str
                            // adding a flag to detect its life addon to use in emailTemplate
                            addon.translationCode = it.addonTranslation.code
                        }
                    }
                    catch (Exception e){
                        log.error "Error fetching life rate in add on $it.code in email service. $e"
                    }

                }

                if (it.addonTranslation.code == AddonCodeEnum.DYNAMIC_ADDON.code) {
                    addon.label = it.addonDescription
                    addon.display = it.addonDescription
                }

                addon.code = it.code
                addon.price = it.price

                addonList.add(addon)

                if(addon.code == AddonCodeEnum.BULLET_SERVICE.code) {
                    bulletServiceAddon = addon
                } else if(addon.code == AddonCodeEnum.HOME_INSURANCE.code) {
                    homeInsuranceAddon = addon
                } else if(it.addonTranslation.code == AddonCodeEnum.LIFE_INSURANCE.code) {
                    lifeInsuranceAddon = addon
                }

            }

            BigDecimal insuranceSubTotalVat = 0
            BigDecimal insuranceSubTotal = 0

            if (carQuote.isUAEQuote()) {
                if (bulletServiceAddon) {
                    bulletServiceAddonVat = commonUtilService.getVATAmount(bulletServiceAddon.price)
                }
                if (homeInsuranceAddon) {
                    homeInsuranceAddonVat = commonUtilService.getVATAmount(homeInsuranceAddon.price)
                }
                if (lifeInsuranceAddon) {
                    lifeInsuranceAddonVat = commonUtilService.getVATAmount(lifeInsuranceAddon.price)
                }

                //Total vat excluding non yc addons
                insuranceSubTotalVat = carQuote.policyPriceVat
                    .plus(carQuote.addonVat ?: 0)
//                    .minus(carQuote.ycAddonVat ?: 0)
                    .plus(carQuote.additionalChargesVAT ?: 0)

            }

            //Total price excluding non yc addons
            insuranceSubTotal = carQuote.policyPrice
                .minus(carQuote.discount ?: 0)
                .plus(insuranceSubTotalVat)
                .plus(carQuote.addonPrice ?:0)
                .minus(carQuote.ycAddonPrice ?: 0)
                .plus(carQuote.additionalCharges ?: 0)

            insuranceSubTotalVat = insuranceSubTotalVat.minus(carQuote.c4meFeeVat ?: 0)
            //insuranceSubTotal = insuranceSubTotal.minus(carQuote.c4meFee ?: 0).minus(carQuote.c4meFeeVat)

            String subjectText = (lang == 'en') ? 'Woohoo! Your payment has been authorised' : 'ووهو! تم اعتماد دفعتك'

            //If current env is not production
            if (Environment.current != Environment.PRODUCTION) subjectText = "[${Environment.current}] ${subjectText}"

            if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(carQuote.email)) {
                carQuote.model?.loadTransients('en')
                String htmlView = "/car/emails/quotePurchased"

                if (carQuote.isCod() && carQuote.paidDate == null) {
                    htmlView = "/car/emails/codQuotePurchased"
                    subjectText = (lang == 'en') ? 'Your car insurance policy payment is pending!' : 'المبلغ الواجب سداده نظير التأمين على السيارة لم يُستوفَ'
                }
                def country = event.data.country.code
                def discountCode = event.data.discountCode
                def hash = event.data.hash

                def baseUrl = grailsApplication.config.getProperty("yallacompare.baseURL")
                String discountCodeUsageLink = "$baseUrl/insurance/$country/$lang/discount-code/usage/$discountCode"
                String uploadDocsLink = "$baseUrl/insurance/$country/$lang/car/policy/upload-policy-docs/$hash"

                if(event.data.brand && event.data.brand != WhiteLabelBrandEnum.YALLACOMPARE.code){
                    baseUrl = WhiteLabelDomain.findByDomainAndActive(event.data.serverName, true)?.baseUrl
                    discountCodeUsageLink = "$baseUrl/$country/$lang/discount-code/usage/$discountCode"

                    uploadDocsLink = "$baseUrl/$country/$lang/car/policy/upload-policy-docs/$hash"
                } else {

                }


                /**
                 * Installment and CC quote purchased is exact same. (23.02.2017)
                 *
                 * if(carQuote.isInstallment()) {*  htmlView = "/car/emails/installmentQuotePurchased"
                 * }
                 */

                KnetPaymentResponse knetResponse = null
                if (carQuote.quoteCountry.id == CountryEnum.KWT.getId().intValue()) {
                    knetResponse = KnetPaymentResponse.findByCarQuoteAndResult(carQuote, KnetPaymentStatusEnum.CAPTURED.getValue())
                }

                String replyToBrand = "yallacompare <${grailsApplication.config.grails.mail.default.replyTo}>"
                if (event.data.brand && event.data.brand != WhiteLabelBrandEnum.YALLACOMPARE.code &&
                    event.data.brand != WhiteLabelBrandEnum.ADIB.code) {

                    WhiteLabelBrand brand = WhiteLabelBrand.findByCode(event.data.brand.toString())
                    replyToBrand = "$brand.nameEn <$brand.replyToEmail>"
                    log.info "Sending email from: $replyToBrand"
                }
                String egySupportEmail = null
                if (carQuote.quoteCountryId == CountryEnum.EGYPT.id){
                    egySupportEmail = grailsApplication.config.grails.mail.default.egyReplyTo
                    replyToBrand = "yallacompare <${egySupportEmail}>"
                }

                if (carQuote.paymentGateway in [PaymentGatewayEnum.CHECKOUT, PaymentGatewayEnum.TAP_PAYMENT] ||
                    (carQuote.isCod() && carQuote.paidDate)) {
                    htmlView = "/car/emails/quotePurchased_capturedPayment"
                    subjectText = messageSource.getMessage("email.purchase.subject.captured", [].toArray(), new Locale(lang))
                }

                WhiteLabelBrandEnum whiteLabel = WhiteLabelBrandEnum.getWhiteLabelBrandEnumFromRequestSource(carQuote.requestSource)
                String emailTemplate = groovyPageRenderer.render([
                    view : htmlView,
                    model: [
                        quote                : carQuote,
                        addons               : addonList,
                        bulletServiceAddon   : bulletServiceAddon,
                        homeInsuranceAddon   : homeInsuranceAddon,
                        lifeInsuranceAddon   : lifeInsuranceAddon,
                        bulletServiceAddonVat: bulletServiceAddonVat,
                        homeInsuranceAddonVat: homeInsuranceAddonVat,
                        lifeInsuranceAddonVat: lifeInsuranceAddonVat,
                        subTotal             : insuranceSubTotal,
                        subTotalVat          : insuranceSubTotalVat,
                        locale               : lang,
                        dir                  : lang == 'en' ? 'ltr' : 'rtl',
                        imageCode            : "compareit4meLogo.url",
                        discountCode         : event.data.discountCode,
                        discountCodeUsageLink: discountCodeUsageLink,
                        uploadDocsLink       : uploadDocsLink,
                        knetResponse         : knetResponse,
                        whiteLabel           : whiteLabel,
                        egySupportEmail      : egySupportEmail,
                        donation             : donation ? donation.amount : 0
                    ]
                ])

                if (carQuote.requestSource == RequestSourceEnum.NATIONALBONDS) {
                    if (emailCheckerService.isEmailVerified(carQuote.email)) {
                        mailService.sendMail {
                            to carQuote.email
                            headers "X-MC-Tags": "car, order",
                                "X-MC-BccAddress": "<EMAIL>"
                            replyTo replyToBrand
                            from replyToBrand
                            subject subjectText
                            html emailTemplate
                            bcc "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "Sarah Daroub <<EMAIL>>", "Milad Issa<<EMAIL>>", "Wahab Saeed<<EMAIL>>", "Syed Tariq<<EMAIL>>"
                        }
                    }
                } else {
                    if (emailCheckerService.isEmailVerified(carQuote.email)) {
                        mailService.sendMail {
                            to carQuote.email
                            headers "X-MC-Tags": "car, order",
                                "X-MC-BccAddress": "<EMAIL>"
                            replyTo replyToBrand
                            from replyToBrand
                            subject subjectText
                            html emailTemplate
                        }
                        log.info("sent email to :${carQuote.email}")
                    } else {
                        log.info("cant send email to ${carQuote.email}, address is not verified")
                    }
                }

            } else {
                log.warn "Email won't be sent to ${carQuote.email} on non production env"
            }
        } else {
            log.warn "Quote purchase email won't be sent to ${carQuote.email} as blacklisted"
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.EMAIL_HOME_QUOTE_PURCHASED)
    def sendHomeQuotePurcahsedEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        log.info "Sending quote purchased email for home quote ${event.data.quoteId}"

        HomeQuote homeQuote = HomeQuote.get(event.data.quoteId)
        User user = User.findByEmail(homeQuote.email)

        if (user.shouldSendEmail()) {
            String lang = event.data.lang

            def addons = CarQuoteAddon.findAllByHomeQuoteAndIsDeleted(homeQuote, false)
            Donation donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(homeQuote.id, ProductTypeEnum.HOME.value(), DonationTypeEnum.CHARITY)


            List addonList = []

            addons.collect {
                addonList.add([label: it.addonTranslation.display, price: it.price])
            }

            String subjectText = (lang == "en") ? "Home Insurance - Acknowledgement of Policy Application" : "التأمين المنزلي- معرفة كيفية تطبيق سياسة التأمين"

            //If current env is not production
            if (Environment.current != Environment.PRODUCTION) subjectText = "[${Environment.current}] ${subjectText}"

            if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(homeQuote.email)) {
                String htmlView = "/homeInsurance/emails/quotePurchasedCreditCard"

                if (homeQuote.isCod()) {
                    htmlView = "/homeInsurance/emails/quotePurchasedCashOnDelivery"
                    //TODO: Do we need to change subject as well?
                }

                if (homeQuote.isInstallment()) {
                    htmlView = "/homeInsurance/emails/quotePurchasedInstallments"
                }

                if (homeQuote.paymentGateway in [PaymentGatewayEnum.CHECKOUT, PaymentGatewayEnum.TAP_PAYMENT]) {
                    htmlView = "/homeInsurance/emails/quotePurchased_capturedPayment"
                    subjectText = messageSource.getMessage("email.purchase.subject.captured", [].toArray(), new Locale(lang))
                }

                String emailTemplate = groovyPageRenderer.render([view: '/common/email', model: [
                    quote    : homeQuote,
                    addons   : addonList,
                    emailBody: htmlView,
                    locale   : lang,
                    baseUrl  : grailsApplication.config.getProperty('yallacompare.baseURL'),
                    dir      : lang == 'ar' ? 'rtl' : 'ltr',
                    imageCode: "compareit4meLogo.url",
                    donation : donation ? donation.amount : 0,
                ]])

                if (emailCheckerService.isEmailVerified(homeQuote.email)) {
                    mailService.sendMail {
                        to homeQuote.email
                        headers "X-MC-Tags": "home, order",
                            "X-MC-BccAddress": "<EMAIL>"
                        replyTo grailsApplication.config.grails.mail.default.replyTo
                        subject subjectText
                        html emailTemplate
                    }
                }

            } else {
                log.warn "Email won't be sent to ${homeQuote.email} on non production env"
            }
        } else {
            log.warn "Home Quote purchase email won't be sent to ${homeQuote.email} as blacklisted"
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.EMAIL_TRAVEL_QUOTE_PURCHASED)
    def sendTravelQuotePurchasedEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        log.info "Sending quote purchased email for travel quote ${event.data.quoteId}"

        TravelQuote travelQuote = TravelQuote.get(event.data.quoteId)
        TravelDestinationCountryZoneEnum destinationZone = TravelDestinationCountryZoneEnum.findByCountry(travelQuote.destinationCountryId)
        Traveler policyHolder = travelQuote.travelerList.find { it.isPolicyHolder }
        String encryptedUser = AESCryption.encrypt(travelQuote.user.id.toString())

        Donation donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(travelQuote.id, ProductTypeEnum.TRAVEL.value(), DonationTypeEnum.CHARITY)

        BigDecimal totalPrice = travelQuote.totalPrice
            .plus(donation ? donation.amount : 0)

        SendinblueMessage sendinblueMessage = new SendinblueMessage()
        sendinblueMessage.to += new SendinblueRecipient(name: travelQuote.name, email: travelQuote.email)
        if (travelQuote.capturedAmount && travelQuote.capturedAmount > 0) {
            sendinblueMessage.templateId = SendinblueService.QUOTE_PURCHASED_TRAVEL_CAPTURED
        } else {
            sendinblueMessage.templateId = SendinblueService.QUOTE_PURCHASED_TRAVEL
        }
        sendinblueMessage.params = [
            name                :   travelQuote.name,
            firstName           :   commonUtilService.getFirstName(travelQuote.name).capitalize(),
            travellingRegion    :   destinationZone.getText("en"),
            policyReference     :   travelQuote.policyReference,
            startDate           :   travelQuote.startDate.toString(Constants.READABLE_DATE_FORMAT),
            endDate             :   travelQuote.endDate.toString(Constants.READABLE_DATE_FORMAT),
            policyHolderName    :   policyHolder.firstName,
            policyHolderDOB     :   policyHolder.birthDate.toString(Constants.READABLE_DATE_FORMAT),
            policyHolderGender  :   GenderEnum.getById(policyHolder.genderId),
            price               :   travelQuote.policyPrice,
            vat                 :   travelQuote.totalVAT,
            totalPrice          :   totalPrice,
            myAccountLink       :   "${grailsApplication.config.getProperty('bankingUrl.myAccount.link')}?id=${encryptedUser}",
            donation            :   donation ? donation.amount : 0
        ]

        sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

        log.info("Sending Travel Quote Purchase email: [id: ${travelQuote.id}] to ${travelQuote.email}")

        sendinblueService.sendEmail(sendinblueMessage)
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.GENERAL_QUOTE_PURCHASED)
    def sendGeneralQuotePurchasedEmail(Event event) {
        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        log.info "Sending quote purchased email for general quote ${event.data.quoteId}"

        GeneralQuote quote = GeneralQuote.get(event.data.quoteId)

        SendinblueMessage sendinblueMessage = new SendinblueMessage()
        sendinblueMessage.to += new SendinblueRecipient(name: quote.name, email: quote.email)
        sendinblueMessage.templateId = SendinblueService.QUOTE_PURCHASED_GENERAL_CAPTURED

        sendinblueMessage.params = [
            name                :   quote.name,
            item                :   quote.product.name,
            order_id            :   quote.policyReference,
            price               :   quote.policyPrice,
            vat                 :   quote.totalVAT,
            total               :   quote.totalPrice
        ]

        sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

        log.info("Sending General Quote Purchase email: [id: ${quote.id}] to ${quote.email}")

        sendinblueService.sendEmail(sendinblueMessage)
    }

    @Transactional(readOnly = true)
    @Selector('contactus.email')
    def sendContactUsEmail(Event<Map> event) {
        log.info "Sending contact us email ${event.data}"
        Map params = event.data

        String content = """
        <strong>Topic:</strong> ${params.insurance} <br>
        <strong>Name:</strong> ${params.name} <br>
        <strong>Email:</strong> ${params.email} <br>
        <strong>Phone:</strong> ${params.mobile} <br>
        <strong>Policy Number:</strong> ${params.policyNo} <br>
        <strong>Message:</strong> ${params.message} <br>
        """

        String recipient = ""
        switch (params.country) {
            case "uae":
                recipient = grailsApplication.config.grails.mail.default.replyTo
                break
            case "egy":
                recipient = grailsApplication.config.grails.mail.default.egyReplyTo
                break
        }

        if (emailCheckerService.isEmailVerified(recipient)) {
            mailService.sendMail {
                to recipient
                headers "X-MC-Important": "true"
                subject "Insurance ${params.insurance} - Contact US"
                html content
            }
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.KSA_CAR_QUOTE_CREATED)
    def sendCarQuoteKsaEmail(Event event) {

        // send quote email after 3 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        Integer quoteId = event.data.quoteId
        String lang = event.data.lang
        log.info "Sending ksa car quote email for car quote ${event.data}"

        CarQuoteKsa carQuote = CarQuoteKsa.get(quoteId)

        if (carQuote.user.shouldSendEmail()) {
            String subjectText = (lang == 'ar') ? '👍 وصلنا بريدك الإلكتروني' : 'We got your email 👍'

            //If current env is not production
            if (Environment.current != Environment.PRODUCTION) subjectText = "[${Environment.current}] ${subjectText}"

            if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(carQuote.email)) {
                String emailTemplate = groovyPageRenderer.render([view: "/car/emails/ksaQuoteCreated", model: [
                    quote    : carQuote,
                    locale   : lang,
                    dir      : lang == 'ar' ? 'rtl' : 'ltr',
                    imageCode: "yallacompareLogo.url"]])

                if (emailCheckerService.isEmailVerified(carQuote.email)) {
                    mailService.sendMail {
                        to carQuote.email
                        from grailsApplication.config.grails.mail.default.ksaFrom
                        headers "X-MC-Tags": "car, quote, ksa",
                            "X-MC-Important": "true"
                        replyTo grailsApplication.config.grails.mail.default.ksaReplyTo
                        subject subjectText
                        html emailTemplate
                    }
                }
            } else {
                log.warn "KSA Quote email won't be sent to ${carQuote.email} on non production env"
            }
        } else {
            log.warn "KSA Quote email won't be sent to ${carQuote.email} as blacklisted"
        }

    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.SEND_VERIFICATION_EMAIL)
    def sendProfileActivationEmail(Event event) {
        Thread.sleep(4000L)
        log.info "Sending verify profile email ${event.data}"
        User user = (User) event.data.user

        if (user.shouldSendTransactionalEmail()) {
            if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(user.email)) {
                String subjectText = "Verify your email address"

                if (Environment.current != Environment.PRODUCTION) {
                    subjectText = "[${Environment.current}] ${subjectText}"
                }

                RestBuilder rest = new RestBuilder()

                String api = grailsApplication.config.getProperty("mandrill.endpoint")

                def body = [
                    "key"             : grailsApplication.config.getProperty("mandrill.apikey"),
                    "template_name"   : "Signup Email",
                    "template_content": [],
                    "message"         : [
                        "subject"          : subjectText,
                        "from_email"       : "<EMAIL>",
                        "from_name"        : "yallacompare",
                        "track_opens"      : true,
                        "track_clicks"     : true,
                        "inline_css"       : true,
                        "merge_language"   : "handlebars",
                        "to"               : [
                            [
                                "email": "${user.email}",
                                "name" : "${user.name}",
                                "type" : "to"
                            ]
                        ],
                        "global_merge_vars": [
                            [
                                "name"   : "name",
                                "content": "${user.name}"
                            ], [
                                "name"   : "link",
                                "content": "${event.data.link}&utm_source=mandrill&utm_medium=email&utm_campaign=signup&utm_content=email"
                            ]
                        ],
                        "tags": [
                            "signup-email", "my-account"
                        ]
                    ]
                ]

                if (emailCheckerService.isEmailVerified(user.email)) {
                    def resp = rest.post(api) {
                        contentType 'application/json'
                        json body as JSON
                    }
                    log.info "Response returned with status:${resp.status} and json:${resp.json}"
                }
            }
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.SEND_FORGOT_PASSWORD_EMAIL)
    def sendForgotPasswordEmail(Event event) {
        log.info "Sending forgot password email ${event.data}"
        User user = (User) event.data.user

            if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(user.email)) {
                String subjectText = "Forgot your password?"

                if (Environment.current != Environment.PRODUCTION) {
                    subjectText = "[${Environment.current}] ${subjectText}"
                }

                RestBuilder rest = new RestBuilder()

                String api = grailsApplication.config.getProperty("mandrill.endpoint")

                String resetPasswordLink = "${Holders.config.cover.domain}/v1/profile/verifyForgotPasswordEmail/${event.data.hash}?email=${user.email}&utm_source=mandrill&utm_medium=email&utm_campaign=forgot-password&utm_content=email"
                def body = [
                    "key"             : grailsApplication.config.getProperty("mandrill.apikey"),
                    "template_name"   : "Forgot Password Email",
                    "template_content": [],
                    "message"         : [
                        "subject"          : subjectText,
                        "from_email"       : "${grailsApplication.config.emails.support}",
                        "from_name"        : "yallacompare",
                        "track_opens"      : true,
                        "track_clicks"     : true,
                        "inline_css"       : true,
                        "merge_language"   : "handlebars",
                        "to"               : [
                            [
                                "email": "${user.email}",
                                "name" : "${user.name}",
                                "type" : "to"
                            ]
                        ],
                        "global_merge_vars": [
                            [
                                "name"   : "name",
                                "content": "${user.name}"
                            ], [
                                "name"   : "link",
                                "content": resetPasswordLink
                            ]
                        ],
                        "tags": [
                            "forgot-password-email", "my-account"
                        ]
                    ]
                ]

                def resp = rest.post(api) {
                    contentType 'application/json'
                    json body as JSON
                }
                log.info "Response returned with status:${resp.status} and json:${resp.json}"

                /*if (resp.json[0].queued_reason) {
                    //try sending with ses service
                    List toAddresses = [user.email]
                    def model = [resetPasswordLink:resetPasswordLink, name: user.name ]
                    String htmlBody = groovyPageRenderer.render([view: "/common/forgotPasswordTemplate", model:model])

                    emailSgService.sendEmailViaSES(null, toAddresses, null, subjectText, htmlBody, null)
                }*/
        }
    }

    @Selector(AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT)
    def sendPrePaymentLinkNotification(Event event) {
        log.info(".sendPrePaymentLinkNotification - data:${event.data}")
        Long quoteId = event.data.quoteId
        Long productId = event.data.productId
        Product product = Product.read(productId)
        ProductTypeEnum productTypeEnum = ProductTypeEnum.findById(product.productTypeId.toInteger())

        def quote = commonPolicySgService.getQuote(quoteId, productTypeEnum)
        User customer = quote.user, salesPerson

        if (productTypeEnum == ProductTypeEnum.CAR) {
            Lead lead = customer.phoneLead
            customer = lead.user
            salesPerson = lead.salesPerson
        } else if (productTypeEnum == ProductTypeEnum.PA) {
            //take the default salesperson
        } else {
            salesPerson = quote.user[User.salesPersonCol(productTypeEnum.name().toLowerCase())]
        }

        List toAddresses, ccAddress
        String subject = "Create ${StringUtils.capitalize(productTypeEnum.name().toLowerCase())} Payment Link for ${quote.id}"

        if (Environment.current == Environment.PRODUCTION) {
            ccAddress = ["<EMAIL>", "<EMAIL>"]
            if (salesPerson) {
                toAddresses = [salesPerson.email]
                if (productTypeEnum == ProductTypeEnum.HEALTH) {
                    ccAddress += "<EMAIL>"
                }
            } else {
                if (productTypeEnum == ProductTypeEnum.HEALTH) {
                    toAddresses = ["<EMAIL>"]
                } else {
                    toAddresses = ["<EMAIL>", "<EMAIL>"]
                }
            }
        } else {
            toAddresses = ["<EMAIL>"]
            subject = "[TEST] " + subject
        }

        String brokerHost = grailsApplication.config.server.broker
        String body = "Hi, <br/><br/>" +
            "Customer has selected ${product.provider.name} - ${product.name} against ${productTypeEnum.name().toLowerCase()} quote id:${quote.id} <br/>" +
            "Kindly create payment link and share with the customer.  <br />" +
            "Remember to pick and update the payment link here ${brokerHost}/paymentIntent/list <br />" +
                "Customer details can be viewed here: ${brokerHost}/crm/addNote?userId=${customer.id}"

        emailSgService.sendEmailViaSES(null, toAddresses, ccAddress, subject, body, null)
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.POLICY_DOCS_RECEIVED)
    def sendPolicyDocumentsReceivedEmail(Event event) {
        log.info("email.sendPolicyDocumentsReceivedEmail - event:${event}")

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        Integer quoteId = event.data.quoteId
        log.info "Sending documents receved email for car quote ${event.data}"

        CarQuote carQuote = CarQuote.get(quoteId)

        if (emailCheckerService.isEmailVerified(carQuote.getEmail())) {
            emailJobService.createDocumentReceivedEmail(carQuote)
        }
    }

    /**
     * Sends an email to a customer notifying him that he has successfully confirmed the policy cancellation request
     *
     * @param event - event containing the data required in email content
     */
    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CAR_PCR_EMAIL_REQUEST_CONFIRMED_TO_CUSTOMER)
    void sendPcrConfirmedEmailToCustomer(Event<Map<String, Object>> event) {
        def eventData = event.data
        def cancellationRequest = eventData.cancellationRequest
        def quote = cancellationRequest.quote
        User user = quote.user

        String withdrawLink = null
        if (eventData.isPolicyIssued) {
            withdrawLink = "${grailsApplication.config.cover.domain}/${eventData.country}/${eventData.lang}/car" +
                "/policy/withdraw-cancellation?uuid=${AESCryption.encrypt(cancellationRequest.uuid)}&?utm_source=mandrill&utm_medium=email&utm_campaign=cancel-request-confirmed&utm_content=email"
        }

        if (user.shouldSendTransactionalEmail()) {
            MandrillMessage mandrillMessage = new MandrillMessage(
                subject: "Request to Cancel Policy Confirmed (PCR-$cancellationRequest.id)",
                templateName: "Cancel Request Confirmed Email",
                fromEmail: grailsApplication.config.emails.support,
                fromName: "yallacompare Insurance",
                trackOpens: true,
                trackClicks: true,
                inlineCss: true
            )

            mandrillMessage.to.add(new MandrillMessage.Recipient(email: user.email, name: user.name, type: 'to'))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'name', content: user.name))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'referenceNumber', content: quote.policyReference))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'insuranceProvider', content: quote.product.provider.nameEn))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'isPolicyIssued', content: eventData.isPolicyIssued))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'cancelReqId', content: Constants.PCR_ID_PREFIX + cancellationRequest.id))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(
                name: 'link',
                content: "${grailsApplication.config.cover.domain}/${eventData.country}/${eventData.lang}/car/policy" +
                    "/upload-cancellation-document?uuid=${AESCryption.encrypt(cancellationRequest.uuid)}&utm_source=mandrill&utm_medium=email&utm_campaign=cancel-request-confirmed&utm_content=email"))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'withdrawLink', content: withdrawLink ?: ""))
            mandrillMessage.tags.add('cancel-request-confirmed-email')

            if (Environment.PRODUCTION == Environment.current) {
                mandrillMessage.bccAddress = grailsApplication.config.emails.cancellation
            }

            if (emailCheckerService.isEmailVerified(user.email)) {
                mandrillMessageService.send(mandrillMessage)
            }
        } else {
            log.warn "Policy Cancellation Confirmation email won't be sent to ${user.email} as the email subscription " +
                "for it is either blacklisted or completely absent"
        }
    }

    /**
     * Sends an email to internal "support" and "cancellation" teams about policy cancellation request.
     *
     * @param event - event containing the data about policy cancellation request
     */
    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.PCR_EMAIL_REQUEST_RECEIVED)
    void sendPcrRequestReceivedEmail(Event<Map<String, Object>> event) {
        def quote = event.data.quote
        User user = (User) event.data.user
        ProductTypeEnum productType = (ProductTypeEnum) event.data.productType

        String subjectText = "$productType Policy Cancellation Request ${quote.policyReference}"

        String toAddress = Environment.PRODUCTION == Environment.current ?
            grailsApplication.config.emails.cancellation : user.email

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(toAddress)) {
            String emailTemplate = groovyPageRenderer.render([view: "/car/emails/cancelPolicyRequestReceived", model: [
                productType: productType.toString(),
                policyRef  : quote.policyReference,
                policyNo   : quote.policyNo ?: 'N/A',
                user       : user
            ]
            ])

            if (emailCheckerService.isEmailVerified(toAddress)) {
                mailService.sendMail {
                    to toAddress
                    replyTo grailsApplication.config.grails.mail.default.replyTo
                    headers "X-MC-Tags": "car, cancel-policy", "X-MC-Important": "true"
                    subject subjectText
                    html emailTemplate
                }
            }
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CAR_PCR_EMAIL_CONFIRM_REQUEST_TO_CUSTOMER)
    void sendPcrConfirmRequestEmailToCustomer(Event<Map<String, Object>> event) {
        User user = (User) event.data.user
        def quoteDetailList = event.data.quoteDetailList
        ProductTypeEnum productType = (ProductTypeEnum) event.data.productType
        String country = event.data.country
        String lang = event.data.lang

        if (user.shouldSendTransactionalEmail()) {
            def policies = []
            quoteDetailList.each {
                LocalDate expiryDate = (it.crmStatus == CrmStatusEnum.ISSUED) ? it.quote.actualPolicyStartDate?.plusMonths(13) : it.quote.getStartDate()?.plusMonths(13)

                policies.push([
                    year       : it.quote.year,
                    make       : it.quote.model.make.slug,
                    model      : it.quote.model.getName("en"),
                    reference  : it.quote.policyReference,
                    cancelReqId: "${Constants.PCR_ID_PREFIX + it.cancelReqId}",
                    link       : "${grailsApplication.config.cover.domain}/$country/$lang/car/quote/confirm-policy-cancellation?" +
                        "uuid=${AESCryption.encrypt(it.cancelReqUuid)}&utm_source=mandrill&utm_medium=email&utm_campaign=cancel-policy&utm_content=email",
                    expires    : expiryDate ? DateTimeFormat.forPattern('yyyy/MM/dd').print(expiryDate) : "N/A"
                ])
            }

            MandrillMessage mandrillMessage = new MandrillMessage(
                subject: "Please Confirm Request to Cancel Policy",
                templateName: "Cancel Policy Email",
                fromEmail: grailsApplication.config.emails.support,
                fromName: "yallacompare Insurance",
                trackOpens: true,
                trackClicks: true,
                inlineCss: true
            )

            mandrillMessage.to.add(new MandrillMessage.Recipient(email: user.email, name: user.name, type: 'to'))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'name', content: user.name))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'policies', content: policies))
            mandrillMessage.tags.add('cancel-policy-email')

            if (emailCheckerService.isEmailVerified(user.email)) {
                mandrillMessageService.send(mandrillMessage)
            }
        } else {
            log.warn "Policy Cancellation Confirmation email won't be sent to ${user.email} as the email subscription " +
                "for it is either blacklisted or completely absent"
        }
    }

    /**
     * Sends an email to a customer notifying him that he has successfully uploaded policy cancellation request documents.
     *
     * @param event - event containing the data required in email content
     */
    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CAR_PCR_EMAIL_DOCS_RECEIVED_TO_CUSTOMER)
    void sendPcrDocsReceivedEmailToCustomer(Event<Map<String, Object>> event) {
        def eventData = event.data
        def quote = eventData.quote
        def cancelReqId = eventData.cancelReqId
        User user = (User) quote.user

        if (user.shouldSendTransactionalEmail()) {
            MandrillMessage mandrillMessage = new MandrillMessage(
                subject: "Policy Cancellation Documents Received (PCR-$cancelReqId)",
                templateName: "Cancel Documents Received Email",
                fromEmail: grailsApplication.config.emails.support,
                fromName: "yallacompare Insurance",
                trackOpens: true,
                trackClicks: true,
                inlineCss: true
            )

            mandrillMessage.to.add(new MandrillMessage.Recipient(email: user.email, name: user.name, type: 'to'))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'name', content: user.name))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'referenceNumber', content: quote.policyReference))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'insuranceProvider', content: quote.product.provider.nameEn))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'cancelReqId', content: Constants.PCR_ID_PREFIX + cancelReqId))
            mandrillMessage.tags.add('cancel-documents-received-email')

            if (Environment.PRODUCTION == Environment.current) {
                mandrillMessage.bccAddress = grailsApplication.config.emails.cancellation
            }

            if (emailCheckerService.isEmailVerified(user.email)) {
                mandrillMessageService.send(mandrillMessage)
            }
        } else {
            log.warn "Policy Cancellation Confirmation email won't be sent to ${user.email} as the email subscription " +
                "for it is either blacklisted or completely absent"
        }
    }

    /**
     * Sends an email to a customer notifying him that he has successfully withdrawn the policy cancellation request.
     *
     * @param event - event containing the data required in email content
     */
    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CAR_PCR_EMAIL_WITHDRAWN_TO_CUSTOMER)
    void sendPcrWithdrawnEmailToCustomer(Event<Map<String, Object>> event) {
        def eventData = event.data
        def cancelReqId = eventData.cancelReqId
        User user = (User) eventData.user

        if (user.shouldSendTransactionalEmail()) {
            MandrillMessage mandrillMessage = new MandrillMessage(
                subject: "Policy Cancellation Request has been withdrawn (PCR-$cancelReqId)",
                templateName: "Cancellation Request Withdrawn",
                fromEmail: grailsApplication.config.emails.support,
                fromName: "yallacompare Insurance",
                trackOpens: true,
                trackClicks: true,
                inlineCss: true
            )

            mandrillMessage.to.add(new MandrillMessage.Recipient(email: user.email, name: user.name, type: 'to'))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'name', content: user.name))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'refNumber', content: eventData.referenceNumber))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'cancelReqId', content: Constants.PCR_ID_PREFIX + cancelReqId))
            mandrillMessage.tags.add('cancel-request-withdrawn-by-customer-email')

            if (Environment.PRODUCTION == Environment.current) {
                mandrillMessage.bccAddress = grailsApplication.config.emails.cancellation
            }

            if (emailCheckerService.isEmailVerified(user.email)) {
                mandrillMessageService.send(mandrillMessage)
            }
        } else {
            log.warn "Policy Cancellation Request withdawal email won't be sent to ${user.email} as the email subscription " +
                "for it is either blacklisted or completely absent"
        }
    }

    /**
     * Sends an email with cancellation documents to the insurer as a confirmation for their review result
     *
     * @param event - event containing the data required in email content
     */
    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CAR_PCR_EMAIL_REVIEW_RESULT_TO_INSURER)
    void sendPcrReviewResultEmailToInsurer(Event<Map<String, Object>> event) {
        String policyNo = event.data.policyNo
        Provider insuranceProvider = (Provider) event.data.insuranceProvider
        CarPcr cancellationRequest = (CarPcr) event.data.cancellationRequest
        Integer rejectionReasonId = (Integer) event.data.rejectionReasonId
        String currency = event.data.currency

        String status
        String subject
        if (rejectionReasonId) {
            switch (rejectionReasonId) {
                case CarPcrRejectionReasonEnum.CUSTOMER_HAS_CLAIMS.id:
                    subject = "Cancellation Request Rejected - $policyNo (PCR-$cancellationRequest.id)"
                    status = "rejected-claims"
                    break
                case CarPcrRejectionReasonEnum.TOO_LATE.id:
                    subject = "Cancellation Request Rejected - $policyNo (PCR-$cancellationRequest.id)"
                    status = "rejected-too-late"
                    break
                case CarPcrRejectionReasonEnum.INVALID_DOCUMENT.id:
                    subject = "Cancellation Request Incomplete - $policyNo (PCR-$cancellationRequest.id)"
                    status = "incomplete-wrong-doc"
                    break
                default:
                    throw new RuntimeException("Unknown Rejection Reason Id = $rejectionReasonId has been detected")
            }
        } else {
            subject = "Cancellation Request Approved - $policyNo (PCR-$cancellationRequest.id)"
            status = "approved"
        }

        boolean isSentToCancellationTeam = false
        event.data.providerContactToRoleList.each { ProviderContactToRole providerContactToRole ->
            MandrillMessage mandrillMessage = new MandrillMessage(
                subject: subject,
                templateName: "Cancellation Reviewed - To Insurer",
                fromEmail: grailsApplication.config.emails.support,
                fromName: "yallacompare Insurance",
                trackOpens: true,
                trackClicks: true,
                inlineCss: true
            )

            mandrillMessage.to.add(new MandrillMessage.Recipient(email: providerContactToRole.contact.emailAddress, name: insuranceProvider.nameEn, type: 'to'))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'status', content: status))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'policyNo', content: policyNo))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'refundAmount', content: cancellationRequest.refundAmountByInsurer))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'currency', content: currency))
            mandrillMessage.tags.add('cancellation-reviewed-insurer-email')

            if (Environment.PRODUCTION == Environment.current && !isSentToCancellationTeam) {
                mandrillMessage.bccAddress = grailsApplication.config.emails.cancellation
            }

            if (emailCheckerService.isEmailVerified(providerContactToRole.contact.emailAddress)) {
                mandrillMessageService.send(mandrillMessage)
            }

            isSentToCancellationTeam = true
        }
    }

    /**
     * Sends an email to a customer notifying him about the results of policy cancellation insurer review.
     *
     * @param event - event containing the data required in email content
     */
    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CAR_PCR_EMAIL_CANCELLATION_REJECTED_TO_CUSTOMER)
    void sendPcrCancellationRejectedEmailToCustomer(Event<Map<String, Object>> event) {
        def eventData = event.data
        String policyNo = eventData.policyNo
        String insuranceProvider = eventData.insuranceProvider
        CarPcr cancellationRequest = (CarPcr) eventData.cancellationRequest
        User user = (User) eventData.user

        if (user.shouldSendTransactionalEmail()) {
            Integer rejectionReasonId = (Integer) eventData.rejectionReasonId
            String subject = "Policy Cancellation Request has been rejected (PCR-$cancellationRequest.id)"

            String status
            if (rejectionReasonId == CarPcrRejectionReasonEnum.CUSTOMER_HAS_CLAIMS.id) {
                status = "rejected-claims"
            } else if (rejectionReasonId == CarPcrRejectionReasonEnum.TOO_LATE.id) {
                status = "rejected-too-late"
            } else {
                throw new RuntimeException("Failed to send Policy Cancellation Request Rejection email to customer as " +
                    "unsupported rejection reason id has been provided. Rejection reason id: '$rejectionReasonId'")
            }

            MandrillMessage mandrillMessage = new MandrillMessage(
                subject: subject,
                templateName: "Cancellation Rejected - To Customer",
                fromEmail: grailsApplication.config.emails.support,
                fromName: "yallacompare Insurance",
                trackOpens: true,
                trackClicks: true,
                inlineCss: true
            )

            mandrillMessage.to.add(new MandrillMessage.Recipient(email: user.email, name: user.name, type: 'to'))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'status', content: status))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'name', content: user.name))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'insuranceProvider', content: insuranceProvider))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'policyNo', content: policyNo))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'cancelReqId', content: Constants.PCR_ID_PREFIX + cancellationRequest.id))

            mandrillMessage.tags.add('cancellation-reviewed-customer-email')

            if (Environment.PRODUCTION == Environment.current) {
                mandrillMessage.bccAddress = grailsApplication.config.emails.cancellation
            }

            if (emailCheckerService.isEmailVerified(user.email)) {
                mandrillMessageService.send(mandrillMessage)
            }

        } else {
            log.warn "Policy Cancellation Request Rejection email won't be sent to ${user.email} as the email " +
                "subscription for it is either blacklisted or completely absent"
        }
    }

    /**
     * Sends an email to provided recipient notifying that the quote payment has been received.
     *
     * @param event - event containing the data required in email content
     */
    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.QUOTE_PAYMENT_RECEIVED_EMAIL_TO_YC)
    void sendQuotePaidEmailToYc(Event<Map<String, Object>> event) {
        def eventData = event.data
        def quote = eventData.quote
        String recipientEmail = eventData.recipientEmail

        Assert.notNull(quote, "'quote' can not be null")
        Assert.notNull(quote.paidDate, "'paidDate' of the quote(id = $quote.id) can not be null")

        String quoteType
        if (quote instanceof CarQuote) {
            quoteType = "car"
        } else if (quote instanceof HealthQuote) {
            quoteType = "health"
        } else if (quote instanceof HomeQuote) {
            quoteType = "home"
        } else {
            throw new RuntimeException("Provided quote type is not supported. Quote: $quote")
        }

        String policyRef = quote.policyReference
        String quoteLink = "${grailsApplication.config.getProperty('server.broker')}/$quoteType/policy/show/$quote.id"

        MandrillMessage mandrillMessage = new MandrillMessage(
            subject: "Quote Payment ($policyRef)",
            templateName: "Quote Payment Received - To YC",
            fromEmail: grailsApplication.config.emails.support,
            fromName: "yallacompare Insurance",
            trackOpens: true,
            trackClicks: true,
            inlineCss: true
        )

        mandrillMessage.to.add(new MandrillMessage.Recipient(email: recipientEmail, type: 'to'))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'policyRef', content: policyRef))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'quoteLink', content: quoteLink))

        mandrillMessage.tags.add('quote-payment-has-been-made')

        if (emailCheckerService.isEmailVerified(recipientEmail)) {
            mandrillMessageService.send(mandrillMessage)
        }
    }

    @Selector(AsyncEventConstants.LIFE_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendLifeQuoteEmail(Event event) {

        Integer quoteId = event.data.quoteId
        log.info "Sending life quote email for car quote ${event.data}"

        LifeQuote lifeQuote = LifeQuote.get(quoteId)

        if (lifeQuote.user.shouldSendEmail() && (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(lifeQuote.user.email))) {
            MandrillMessage mandrillMessage = new MandrillMessage(
                subject: "Complete your life insurance quote online!",
                templateName: "Life Quote Email",
                fromEmail: grailsApplication.config.emails.support,
                fromName: "yallacompare",
                trackOpens: true,
                trackClicks: true,
                inlineCss: true
            )

            String encId = AESCryption.encrypt(lifeQuote.id.toString())
            CountryEnum countryEnum = CountryEnum.findCountryByDfp(lifeQuote.quoteCountry.code)
            String server = grailsApplication.config.getProperty('grails.serverURL')
            String url = "${server}/${countryEnum.code}/${lifeQuote.lang}/life/quotes/${encId}"
            String queryString = "utm_source=mandrill&utm_medium=email&utm_campaign=rmkt|insurance|life|quote-created&utm_content=email"


            mandrillMessage.to.add(new MandrillMessage.Recipient(email: lifeQuote.user.email, name: lifeQuote.user.name, type: 'to'))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'NAME', content: "$lifeQuote.user.firstName"))
            mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'LINK', content: "$url?$queryString"))

            if (emailCheckerService.isEmailVerified(lifeQuote.user.email)) {
                mandrillMessageService.send(mandrillMessage)
            }
        }
    }

    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.CALL_REQUESTED)
    def sendCallRequestReceivedEmail(Integer callRequestId) {
        assert callRequestId != null, "(Integer callRequestId) cannot be null"

        CallRequest callRequest = callRequestService.getCallRequestById(callRequestId)

        MandrillMessage mandrillMessage = new MandrillMessage(
            subject: "Customer Call Request",
            templateName: "Customer Call Request",
            fromEmail: grailsApplication.config.emails.support,
            fromName: "yallacompare",
            trackOpens: true,
            trackClicks: true,
            inlineCss: true
        )

        mandrillMessage.to.add(new MandrillMessage.Recipient(email: "<EMAIL>", name: "Yasser Yousef", type: 'to'))
        mandrillMessage.to.add(new MandrillMessage.Recipient(email: "<EMAIL> ", name: "Mohamed Mosallam", type: 'to'))
        mandrillMessage.to.add(new MandrillMessage.Recipient(email: "<EMAIL>", name: "Tech", type: 'to'))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'INSURANCE', content: "$callRequest.type"))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'COUNTRY', content: "$callRequest.country"))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'LANGUAGE', content: "$callRequest.lang"))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'NUMBER', content: "$callRequest.phone"))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'PAGE', content: "$callRequest.page"))
        mandrillMessage.globalMergeVars.add(new MandrillMessage.GlobalMergeVar(name: 'BUTTON', content: "$callRequest.button"))

        mandrillMessageService.send(mandrillMessage)
    }

    @Selector(AsyncEventConstants.HEALTH_EMPLOYEES_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendHealthEmployeesEmail(Event event) {

        if ((event.data.numberOfEmployees as Integer) < 10) {
            return
        }

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        log.info "Sending health mail for an application submitted by a company: quote ${event.data.quoteId}"

        String quoteId = event.data.quoteId
        HealthQuote healthQuote = HealthQuote.get(quoteId)
        String numberOfEmployees = event.data.numberOfEmployees
        String companyName = event.data.companyName
        String subjectText = "[Group Health] ${companyName} - ${numberOfEmployees} employees"

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(healthQuote.email)) {
            String htmlView = "/health/emails/applicationSubmitted"
            log.info("Inside check to send group health emails")
            WhiteLabelBrandEnum whiteLabel = WhiteLabelBrandEnum.YALLACOMPARE;
            String emailTemplate = groovyPageRenderer.render([
                view : htmlView,
                model: [
                    quoteId              : quoteId,
                    locale               : 'en',
                    companyName          : companyName,
                    numberOfEmployees    : numberOfEmployees,
                    customerType         : healthQuote.type,
                    whiteLabel           : whiteLabel
                ]
            ])
            String leadLink = "${grailsApplication.config.server.broker}/health/crm/addNote?userId=${healthQuote.userId}"
            String policyLink = "${grailsApplication.config.server.broker}/health/policy/show/${healthQuote.id}"

            String content = "<strong>Company Name:</strong> ${companyName}<br>" +
                "<strong>Number Of Employees:</strong> ${numberOfEmployees}<br>" +
                "<strong>Customer Type:</strong> ${healthQuote.type}<br>" +
                "<strong>ID:</strong> ${quoteId}<br>" +
                "<strong>Lead Link: </strong> <a href='${leadLink}' target='_blank'>${leadLink}</a> <br>" +
                "<strong>Policy Link: </strong> <a href='${policyLink}' target='_blank'>${policyLink}</a>"

            log.info("after emailTemplate,,, ${emailTemplate?.substring(1, 100)}")

            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = grailsApplication.config.emails.support

            log.info("before sending email to group mailTo:${mailTo}, fromMail:${fromMail}")

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from '<EMAIL>'
                subject subjectText
                html content
            }

            log.info("Group Health Email sending finished..")

        } else {
            log.warn "Email won't be sent to ${healthQuote.email} on non production env!"
        }
    }

//  -------------  new lead forms
    @Selector(AsyncEventConstants.BOAT_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendBoatEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        BoatQuote quote = BoatQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Boat] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Boat Details:</strong> ${event.data.boatDetails}<br>"
            content += "<strong>Boat Engine:</strong> ${event.data.boatEngine}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.email} on non production env!"
        }
    }

    @Selector(AsyncEventConstants.BUSINESS_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendBusinessEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        BusinessQuote quote = BusinessQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Business] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Company Name:</strong> ${event.data.companyName}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.email} on non production env!"
        }
    }

    @Selector(AsyncEventConstants.GADGET_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendGadgetEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        GadgetQuote quote = GadgetQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Gadget] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Gadget Type:</strong> ${event.data.gadgetType}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.email} on non production env!"
        }
    }

    @Selector(AsyncEventConstants.GAP_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendGapEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        GapQuote quote = GapQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Gap] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Make:</strong> ${event.data.make}<br>"
            content += "<strong>Model:</strong> ${event.data.model}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.email} on non production env!"
        }
    }

    @Selector(AsyncEventConstants.MOTORBIKE_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendMotorbikeEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        MotorbikeQuote quote = MotorbikeQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Motorbike] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Make:</strong> ${event.data.make}<br>"
            content += "<strong>Model:</strong> ${event.data.model}<br>"
            content += "<strong>Engine:</strong> ${event.data.engine}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.email} on non production env!"
        }
    }

/*
    @Selector(AsyncEventConstants.PET_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendPetEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        PetQuote quote = PetQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.user.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Pet] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Animal Type:</strong> ${event.data.animalType}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.user.email} on non production env!"
        }
    }
*/

    @Selector(AsyncEventConstants.WATCH_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendWatchEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        WatchQuote quote = WatchQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Watch] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Model:</strong> ${event.data.model}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.email} on non production env!"
        }
    }

    @Selector(AsyncEventConstants.FLEET_QUOTE_CREATED)
    @Transactional(readOnly = true)
    def sendFleetEmployeesEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        FleetQuote quote = FleetQuote.get(event.data.quoteId)

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(quote.email)) {
            List mailTo = grailsApplication.config.emails.healthSupportGroup
            String fromMail = "<EMAIL>"
            String subjectText = "[Fleet] Quote ID: ${event.data.quoteId}"

            String content = ""
            content += "<strong>ID:</strong> ${event.data.quoteId}<br>"
            content += "<strong>Company Name:</strong> ${event.data.companyName}<br>"
            content += "<strong>Fleet Size:</strong> ${event.data.fleetSize}"

            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html content
            }
        } else {
            log.warn "Email won't be sent to ${quote.email} on non production env!"
        }
    }

    @Selector(AsyncEventConstants.AXA_POLICY_ISSUED)
    @Transactional(readOnly = true)
    def sendAxaQuoteCreaPurchasedEmail(Event event) {

        // wait for 4 seconds to avoid concurrent transaction exception
        Thread.sleep(4000L)

        log.info "Sending Axa life mail for quote: ${event.data.quoteId}"

        String quoteId = event.data.quoteId
        File policySchedule = (File)event.data.attach
        LifeQuote lifeQuote = LifeQuote.get(quoteId)
        LifeAdditionalInfo additionalInfo = LifeAdditionalInfo.findByQuote(lifeQuote)
        String finalPolicyNo = additionalInfo?.finalPolicyNo
        String subjectText = "Your Life Insurance Policy Issued!"

        if (Environment.PRODUCTION == Environment.current || grailsApplication.config.emails.testRecipients.contains(lifeQuote.email)) {
            String htmlView = "/life/axaPolicyIssued"
            WhiteLabelBrandEnum whiteLabel = WhiteLabelBrandEnum.YALLACOMPARE;
            String emailTemplate = groovyPageRenderer.render([
                view : htmlView,
                model: [
                    quoteId              : quoteId,
                    locale               : 'en',
                    name                 : lifeQuote.user.name,
                    product              : lifeQuote.product.name,
                    premium              : lifeQuote.totalPrice,
                    referenceNum         : lifeQuote.policyReference,
                    policyNo             : finalPolicyNo,
                    whiteLabel           : whiteLabel
                ]
            ])
            List mailTo = new ArrayList<String>()
            mailTo.add(lifeQuote.email)
            String fromMail = grailsApplication.config.emails.support
            for (String mail: mailTo.toArray()) {
                if (!emailCheckerService.isEmailVerified(mail)) {
                    mailTo.remove(mail)
                }
            }

            mailService.sendMail {
                multipart true
                to mailTo.toArray()
                from fromMail
                subject subjectText
                html emailTemplate
                attach policySchedule
            }
            log.info("Life Email sending done..")

        } else {
            log.warn "Email won't be sent to ${lifeQuote.email} on non production env!"
        }
    }

    @Selector(AsyncEventConstants.PA_YOUTH_SALE)
    void sendPaYouthSaleEmail(Event<Map<String, Object>> event) {
        log.info("inside sendPaYouthSaleEmail - event: ${event}")
        println("inside sendPaYouthSaleEmail - event: ${event}")

        // wait for 5 seconds to avoid concurrent transaction exception
        Thread.sleep(5000L)

        String merchantReference = event.data.merchantReference
        PaLead lead = PaLead.findByMerchantRef(merchantReference)

        if (lead == null || lead.paidDate == null) {
            log.error("PA Youth payment not found for merchant ref:$merchantReference, cannot send email")
            return
        }

        def params = [
            "School"            : lead.paSchools.name,
            "Student"           : lead.studentName,
            "Student DOB"        : lead.dob.toString(),
            "Student Age"       : lead.studentAge,
            "Guardian Name"      : lead.guardianName,
            "Guardian Email"     : lead.email,
            "Mobile"            : lead.mobile,
            //"Payment Link"       : payment.paymentLink,
            "Merchant Ref"       : lead.merchantRef,
            "Payment Date"       : lead.paidDate.toString("dd/MM/yyyy HH:mm"),
            "Amount"            : lead.totalPrice
        ]

        String subjectText = "Payment Notification for Personal Accident for Youth "

        if (Environment.current != Environment.PRODUCTION) {
            subjectText = "[${Environment.current}] ${subjectText}"
        }

        StringBuilder str = new StringBuilder()
        params.each {
            str.append("<strong>${it.key}: </strong> ${it.value.toString()} <br>")
        }

        List recipients = grailsApplication.config.emails.paYouthPaymentGroup

        mailService.sendMail {
            to recipients.toArray()
            headers "X-MC-Important": "true"
            subject subjectText
            html str.toString()
        }
    }



    @Selector(AsyncEventConstants.PA_YOUTH_CREATED)
    void sendPaYouthPaymentLink(Event<Map<String, Object>> event) {

        // send quote email after 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        Integer paLeadId = event.data.paLeadId
        String lang = event.data.lang
        String country = event.data.country

        String paLeadIdCrypted = AESCryption.encrypt(paLeadId.toString())

        String paymentReturnUrl = "${grailsApplication.config.getProperty('yallacompare.baseURL')}/${country}/${lang}/youth-pa/post-payment/${paLeadIdCrypted}"
        String paymentUrl = "${grailsApplication.config.getProperty('yallacompare.baseURL')}/insurance/v1/pa/checkout/${country}/${lang}/${paLeadIdCrypted}/paynow?returnUrl=${paymentReturnUrl}"

        PaLead lead = PaLead.get(paLeadId)

        SendinblueMessage sendinblueMessage = new SendinblueMessage()
        sendinblueMessage.to += new SendinblueRecipient(name: lead.guardianName, email: lead.email)
        sendinblueMessage.templateId = SendinblueService.NEW_SEND_PA_YOUTH_PAYMENT_LINK_EMAIL
        sendinblueMessage.params = [
            "school"            : lead.paSchools.name,
            "student"           : lead.studentName,
            "student_dob"       : lead.dob.toString(),
            "student_age"       : lead.studentAge,
            "guardian_name"     : lead.guardianName,
            "guardian_email"    : lead.email,
            "mobile"            : lead.mobile,
            "amount"            : lead.totalPrice,
            "payment_url"       : paymentUrl
        ]

        String subjectText = "Payment Notification for Personal Accident for Youth "

        if (Environment.current != Environment.PRODUCTION) {
            subjectText = "[${Environment.current}] ${subjectText}"
        }

        sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

        log.info("Sending payment link for Personal Accident for Youth: [id: ${lead.id}] to ${lead.email}")

        sendinblueService.sendEmail(sendinblueMessage)

    }


    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.PET_QUOTE_CREATED)
    def sendPetQuoteEmail(Event event) {
        // send quote email after 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        Integer quoteId = event.data.quoteId
        String lang = event.data.lang
        String country = event.data.country

        log.info "Sending quote email for pet quote ${event.data.quoteId}"

        PetQuote petQuote = PetQuote.get(quoteId)
        List<PetRateCommand> ratings = petRatingService.getRatings(petQuote).sort { it.baseRateYearly }

        if (ratings && ratings.size()) {
            String encId = AESCryption.encrypt(petQuote.id.toString())

            SendinblueMessage sendinblueMessage = new SendinblueMessage()
            sendinblueMessage.to += new SendinblueRecipient(name: petQuote.user.name, email: petQuote.user.email)
            sendinblueMessage.templateId = SendinblueService.QUOTE_CREATED_PET_EN

            PetRateCommand bronze = ratings.stream()
                .filter({ customer -> "Bronze".equals(customer.productNameEn) })
                .findFirst()
                .orElse(null);

            PetRateCommand silver = ratings.stream()
                .filter({ customer -> "Silver".equals(customer.productNameEn) })
                .findFirst()
                .orElse(null);

            PetRateCommand gold = ratings.stream()
                .filter({ customer -> "Gold".equals(customer.productNameEn) })
                .findFirst()
                .orElse(null);

            sendinblueMessage.params = [
                name        :   petQuote.user.firstName+' '+petQuote.user.lastNames,
                quote_link  :   "${grailsApplication.config.getProperty('yallacompare.baseURL')}/${country}/${lang}/pet-insurance/quote/${encId}?utm_source=sendinblue&utm_campaign=yc-en-petinsurance&utm_medium=email",
                numQuotes   :   ratings.size(),
                pet_name    :   petQuote.pet.petName,
                bronze_price_monthly:   bronze?.baseRateMonthly,
                bronze_price_yearly :   bronze?.baseRateYearly,
                silver_price_monthly:   silver?.baseRateMonthly,
                silver_price_yearly :   silver?.baseRateYearly,
                gold_price_monthly  :   gold?.baseRateMonthly,
                gold_price_yearly   :   gold?.baseRateYearly,
                saving_percent      : IConstant.PET_INSURANCE_SAVING_PERCENTAGE
            ]
            sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

            log.info("Sending Pet Quote email: [id: ${petQuote.id}] to ${petQuote.user.email}")

            sendinblueService.sendEmail(sendinblueMessage)

        } else {
            log.warn "Pet Quote email won't be sent as no ratings for quoteId ${quoteId}"
        }
    }


    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.EMAIL_PET_QUOTE_PURCHASED)
    def sendPetQuotePurcahsedEmail(Event event) {
        // send quote email after 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        Integer quoteId = event.data.quoteId
        String lang = event.data.lang
        String country = event.data.country

        log.info "Sending email purchased pet quote ${event.data.quoteId}"

        PetQuote petQuote = PetQuote.get(quoteId)

        String encId = AESCryption.encrypt(petQuote.id.toString())

        SendinblueMessage sendinblueMessage = new SendinblueMessage()
        sendinblueMessage.to += new SendinblueRecipient(name: petQuote.user.name, email: petQuote.user.email)
        sendinblueMessage.templateId = SendinblueService.QUOTE_PURCHASED_PET_EN

        sendinblueMessage.params = [
                name        :   petQuote.user.firstName+' '+petQuote.user.lastNames,
                pet_name    :   petQuote.pet.petName]

        sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

        log.info("Sending purchased Pet Quote email: [id: ${petQuote.id}] to ${petQuote.user.email}")

        sendinblueService.sendEmail(sendinblueMessage)

    }


    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.EMAIL_PET_QUOTE_PURCHASED_SALAMA)
    def sendPetQuotePurcahsedEmailSalama(Event event) {
        // send quote email after 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        Integer quoteId = event.data.quoteId
        String lang = event.data.lang
        String country = event.data.country

        log.info "Sending email purchased pet quote ${event.data.quoteId}"

        PetQuote petQuote = PetQuote.get(quoteId)

        String encId = AESCryption.encrypt(petQuote.id.toString())

        SendinblueMessage sendinblueMessage = new SendinblueMessage()


        List toEmails =  grailsApplication.config.providers.salama.emails.petPolicyToEmails
        List ccEmails =  grailsApplication.config.providers.salama.emails.petPolicyCcEmails

        toEmails.each {
                sendinblueMessage.to += new SendinblueRecipient(name: "Salama", email: it)
        }
        ccEmails.each {
                sendinblueMessage.cc += new SendinblueRecipient(name: "", email: it)
        }

        sendinblueMessage.templateId = SendinblueService.QUOTE_PURCHASED_PET_EN_SALAMA

        BigDecimal providerBaseRate = petQuote.paymentPlan == PaymentPlanEnum.ANNUAL ?
            petQuote.providerBasicYearlyPremium : (petQuote.providerBasicYearlyPremium / 12).setScale(2, BigDecimal.ROUND_HALF_UP)

        sendinblueMessage.params = [
                name                :   petQuote.user.firstName+' '+petQuote.user.lastNames,
                upload_policy_link  :   "${grailsApplication.config.getProperty('yallacompare.baseURL')}/${country}/${lang}/pet-insurance/policy-document/${encId}",
                policy_reference    :   petQuote.policyReference,
                pet_name            :   petQuote.pet.petName,
                pet_age             :   petQuote.pet.petAge,
                pet_type            :   petQuote.pet.petType.name,
                pet_breed           :   petQuote.pet.petBreed.name,
                product_plan        :   petQuote.product.name,
                annual_excess       :   petQuote.annualExcess,
                payment_plan        :   petQuote.paymentPlan.toString(),
                policy_start_date   :   petQuote.policyStartDate.toString(),
                emirates_id_number  :   petQuote.emiratesIdNumber,
                microchip           :   petQuote.pet.microchip,
                total_price         :   petQuote.totalPrice,
                provider_base_rate  :   providerBaseRate,
                email               :   petQuote.email
        ]

        sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

        log.info("Sending purchased Pet Quote email: [id: ${petQuote.id}] to ${petQuote.user.email}")

        sendinblueService.sendEmail(sendinblueMessage)

    }


    @Transactional(readOnly = true)
    @Selector(AsyncEventConstants.EMAIL_PET_REMOVED)
    def sendPetRemovedEmail(Event event) {
        // send quote email after 4 second to avoid concurrent modification exception
        Thread.sleep(4000L)

        String userName = event.data.user_name
        String userEmail = event.data.user_name
        String petName = event.data.pet_name

        log.info "Sending email pet removed ${petName}"


        SendinblueMessage sendinblueMessage = new SendinblueMessage()
        sendinblueMessage.to += new SendinblueRecipient(name: userName, email: userEmail)
        sendinblueMessage.templateId = SendinblueService.EMAIL_PET_REMOVED_EN

        sendinblueMessage.params = [
                name        :   userName,
                pet_name    :   petName]

        sendinblueMessage.replyTo = new SendinblueRecipient(email: grailsApplication.config.grails.mail.default.replyTo, name: "yallacompare")

        log.info("Sending Pet Removed email to ${userEmail}")

        sendinblueService.sendEmail(sendinblueMessage)

    }


}
