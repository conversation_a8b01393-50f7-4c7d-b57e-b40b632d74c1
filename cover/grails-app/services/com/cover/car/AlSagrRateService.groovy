package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Ratings calculation for Al-Sagr.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class AlSagrRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 8
    public static final Integer COMPREHENSIVE_PRODUCT_ID = 19

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        //rateCommand = applyAdditionalFees(rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
      //  rateCommand = applyAdditionalFees(rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        BigDecimal baseRate = getBaseRate(isAgency, quoteCommand, applicableRate)
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id



        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        Make make = Make.load(quoteCommand.makeId)

        if (make.country.id.toLong() in [CountryEnum.JAPAN.getId(), CountryEnum.SOUTH_KOREA.getId()] && !rateCommand.agencyRepair
            && quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.getValue(), VehicleTypeEnum.FOURx4.getValue()]){
            baseRate = 1.50
            rateCommand.minPremium = quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value ? 1350 : 1700
        }


        rateCommand.baseRate = baseRate
        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }


    private BigDecimal getBaseRate(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        BigDecimal minPremium = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        BigDecimal calculatedPremium = (baseRate * quoteCommand.insuredValue)/100
        log.info("alsagar.getBaseRate - baseRate:${baseRate}, minPremium:${minPremium}, calculatedPremium:${calculatedPremium}")

        baseRate
    }

    private boolean checkAgency(QuoteCommand quoteCommand) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false
        Make make = Make.load(quoteCommand.makeId)
        int carAge = quoteCommand.carAge

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            if (quoteCommand.isBrandNew && carAge <= 1) {
                isAgency = true
            }

            if (carAge <= 1 && quoteCommand.noClaimsDiscountId && quoteCommand.isOldAgency) {
                isAgency = true
            }

            if (quoteCommand.isThirdParty) {
                isAgency = false
            }

        }
        if (make.country.id.toLong()  in [CountryEnum.JAPAN.getId(), CountryEnum.SOUTH_KOREA.getId()]){
            isAgency = false
        }

        if (carAge > 2) {
            isAgency = false
        }

        isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)
        Make make = Make.load(quoteCommand.makeId)

        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            // for Comprehensive only
            if (!quoteCommand.isBrandNew && quoteCommand.isThirdParty) {
                // Eligible only if current policy is comprehensive
                isEligible = false
            }

            if (make.country.id.toLong() in [CountryEnum.JAPAN.id, CountryEnum.KOREA.id] && quoteCommand.insuredValue > 100000) {
                isEligible = false
            }


            if (quoteCommand.hasClaim){
                isEligible = false
            }

        } else {
            // for TPL only
            if (quoteCommand.carAge >= 15) {
                // Eligible only if car age is less than 15
                isEligible = false
            }
        }

        // For both TPL and comprehensive
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId() ||
            quoteCommand.isNonGccSpec) {
            isEligible = false
        }

        if (make.countryId == CountryEnum.CHINA.id){
            isEligible = false
        }

        isEligible
    }

    RateCommand applyAdditionalFees(RateCommand rateCommand) {

        // additional documentation fee of 50 AED
        rateCommand.premium = rateCommand.premium.add(50)

        rateCommand
    }
}
