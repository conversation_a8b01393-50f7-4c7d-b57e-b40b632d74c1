package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.*
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Ratings calculation for Yas Takaful Insurance.
 */
@Transactional(readOnly = true)
class YasTakafulRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 72
    public static final Integer PLAN_2_PREM_GARAGE = 5195
    public static final Integer PLAN_3_NON_AGENCY = 5112

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("YasTakaful.getRates- quoteCommand - ${quoteCommand} - isOffline - ${isOffline}")
        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)
        log.info("YasTakaful.getRates - checkEligibility:${checkEligibility}")
        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            CarMakeEnum makeENum = CarMakeEnum.findById(quoteCommand.makeId.intValue())
            log.info("makeENum:${makeENum}")
            quoteCommand.carCategory = makeENum.isAsian() ? 'A' : (makeENum.isEuropean() ? 'E' : '-')
            log.info("quoteCommand.carCategory:${quoteCommand.carCategory}")
            quoteCommand.nationalityCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            log.info("YasTakaful.getRates - applicableRates:${applicableRates}")

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("YasTakaful.getRate - ${quoteCommand} - isOffline - ${isOffline}")
        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            CarMakeEnum makeENum = CarMakeEnum.findById(quoteCommand.makeId.intValue())
            quoteCommand.carCategory = makeENum.isAsian() ? 'A' : (makeENum.isEuropean() ? 'E' : null)
            quoteCommand.nationalityCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            log.info("Rates - ${applicableRates.size()}")
            if (applicableRates) {
                rateCommand = populateRatings(quoteCommand, applicableRates.first())
            }

        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)

        //rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = applyLoadings(quoteCommand, rateCommand)
        log.info("YasTakaful.populateRatings - rate.premium after loading:${rateCommand.premium}")

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, false)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        //BigDecimal baseRate = calculateBaseRate(quoteCommand, provider, product)

        rateCommand.agencyRepair = checkAgency(product, quoteCommand)
        rateCommand.productId = product.id

        rateCommand = applyBaseRate(rateCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        if (rateCommand.premium < rateCommand.minPremium) {
            rateCommand.premium = rateCommand.minPremium
        }
        rateCommand
    }

    private boolean checkAgency(Product product, QuoteCommand quoteCommand) {

        boolean isAgency = false

        isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)

        if (quoteCommand.customerAge < 25 || quoteCommand.hasClaim) {
            return false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.isThirdParty) {
            return false
        }
        if (quoteCommand.registrationCityId == CityEnum.DUBAI.value()) {
            return false
        }

        Make make = Make.load(quoteCommand.makeId)
        if (make.countryId.longValue() == CountryEnum.CHINA.id) {
            return false
        }
        // not eligible if uae driving experience is less then 1 Year
        /*if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }*/

        isEligible
    }

    private RateCommand applyBaseRate(RateCommand rateCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        rateCommand.baseRate = baseRate
        return rateCommand
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        //No discount if premium is more than min premium
        if (rate.premium > rate.minPremium) {
            return rate
        }
        log.info("YasTakaful.applyDiscount - rate.premium before :${rate.premium}, rate.basePremium:${rate.basePremium}")
        if (quoteCommand.noClaimsDiscountId && !quoteCommand.isPolicyExpired && quoteCommand.insuredValue <= 200000)  {
            if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                rate.premium = rate.premium.subtract(rate.premium * (0.10)) //10% Discount
            } else if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR2.value()) {
                rate.premium = rate.premium.subtract(rate.premium * (0.15)) //15% Discount
            } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
                rate.premium = rate.premium.subtract(rate.premium * (0.20)) //20% Discount
            }
        }
        log.info("YasTakaful.applyDiscount - rate.premium after :${rate.premium}")

        rate
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rate) {
        BigDecimal loadingRate = 0

        if (quoteCommand.customerAge < 25) {
            loadingRate += 0.25 // 25% of base premium
        }

        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            loadingRate += 0.25 //25% Loading on base premium
        }

        if (loadingRate) {
            rate.premium = rate.premium.add(rate.basePremium * loadingRate)
        }

        rate
    }

}
