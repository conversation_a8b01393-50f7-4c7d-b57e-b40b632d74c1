package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CarMakeEnum
import com.safeguard.ClaimPeriodEnum
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperience
import com.safeguard.DrivingExperienceEnum
import com.safeguard.ModelMasterEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import org.codehaus.groovy.runtime.InvokerHelper
import grails.transaction.Transactional

@Transactional
class MethaqRatingService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 34
    public static final Integer METHAQ_COMPREHENSIVE = 1026
    public static final Integer METHAQ_THIRD_PARTY = 1027

    public static final Integer MAKE_KOREAN = 143
    public static final Integer MAKE_JAPANESE = 80
    public static final Integer MAKE_INDIAN = 72


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            Model model = Model.get(quoteCommand.modelId)
//            quoteCommand.carCategory = model.methaqCategory

            //List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            QuoteCommand tempQuoteCommand = getCloneQuoteCommand(quoteCommand)

            if (model.methaqCategory == VehicleTypeEnum.SPORTS.toString()) {
                tempQuoteCommand.vechileTypeId = VehicleTypeEnum.SPORTS.value
            }
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(tempQuoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }
        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(quoteCommand.modelId)
//            quoteCommand.carCategory = model.methaqCategory

            QuoteCommand tempQuoteCommand = getCloneQuoteCommand(quoteCommand)

            if (model.methaqCategory == VehicleTypeEnum.SPORTS.toString()) {
                tempQuoteCommand.vechileTypeId = VehicleTypeEnum.SPORTS.value
            }
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(tempQuoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }
        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)

            Integer vehicleTypeId = (Integer) model.vehicleTypeId
            if (model.methaqCategory == VehicleTypeEnum.SPORTS.toString()) {
                vehicleTypeId = VehicleTypeEnum.SPORTS.value
            }


            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, vehicleTypeId, model.noOfCyl, quoteCommand.customerAge,
                        isOffline,null, true, quoteCommand.requestSource)
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }

        }
        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        applyBasePremiumTpl(rateCommand, quoteCommand, rate)
        rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)

        //No Admin fee if no claim.
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
//        if (quoteCommand.hasClaim || quoteCommand.isPolicyExpired) {
//            rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
//        } else {
//            rateCommand.c4meFee = 30
//            rateCommand.originalC4meFee = rateCommand.c4meFee
//
//            rateCommand.premium = rateCommand.premium.add(rateCommand.c4meFee)
//        }

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = checkMinimumPremium(rateCommand, quoteCommand)
        rateCommand = applyDiscounts(quoteCommand, rateCommand)

       // rateCommand = applyLoadings(quoteCommand, rateCommand)

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }
        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand.baseRate = getBaseRate(rateCommand, quoteCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            isAgency = false // No Agency for now
        }

        isAgency
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rate) {
        int carAge = quoteCommand.carAge
        // Apply loading of 500 if car age is greater then eq to 2 and isAgency
        if (carAge >= 2 && rate.agencyRepair) {
            rate.premium = rate.premium.add(500)
        }

        rate
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {
        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        Make make = Make.load(quoteCommand.makeId)
        Model model = Model.load(quoteCommand.modelId)

        //Conditions for comprehensive
        if (productTypeEnum.value() == CoverageTypeEnum.COMPREHENSIVE.value()) {
            //Car age greater then or equal to 10
            if (quoteCommand.carAge > 10) {
                isEligible = false
            }

            if (quoteCommand.carAge > 8 && quoteCommand.insuredValue < 20000){
                isEligible = false
            }

            if (make.countryId == CountryEnum.CHINA.id){
                isEligible = false
            }

            if ((make.id == CarMakeEnum.BENTLEY.id && model.modelMasterId == 56) ||
                (make.id == CarMakeEnum.DODGE.id && model.modelMasterId == 137) || (make.id == CarMakeEnum.NISSAN.id
                && model.modelMasterId.intValue() == ModelMasterEnum.NISSAN_PATROL_SAFARI.id)){
                isEligible = false
            }
        }

        if (quoteCommand.isNonGccSpec || quoteCommand.isThirdParty) {
            isEligible = false
        }

        if (quoteCommand.customerAge < 25){
            isEligible = false
        }

        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id){
            isEligible = false
        }

        isEligible
    }

    BigDecimal getBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        if (rateCommand.agencyRepair && quoteCommand.carAge == 3 &&
            quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value]) {
            //Base rate for 3rd year agency
            baseRate = 2.50
        }

        if (quoteCommand.customerAge < 21 ||
            (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id &&
            quoteCommand.internationalDrivingExperienceId < DrivingExperienceEnum.THREE_TO_FOUR.id)) {
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                baseRate = 5.0
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                baseRate = 5.0
            } else if (quoteCommand.vechileTypeId  in [VehicleTypeEnum.COUPE.value, VehicleTypeEnum.CONVERTIBLE.value, VehicleTypeEnum.SPORTS.value]) {
                baseRate = 7.0
            } else if (quoteCommand.vechileTypeId  == VehicleTypeEnum.PICKUP.value) {
                baseRate = 7.0
            }
            // Condition add to treat some models of Volkswagen VW Golf GT/GTI/R as Sports car
            if (quoteCommand.carCategory == VehicleTypeEnum.SPORTS.toString()) {
                baseRate = 7.0
            }
        }

        baseRate
    }

    void applyBasePremiumTpl(RateCommand rateCommand, QuoteCommand quoteCommand, ProductTplRate applicableRate) {
        Model tempModel = Model.read(quoteCommand.modelId)

        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id &&
            quoteCommand.internationalDrivingExperienceId < DrivingExperienceEnum.THREE_TO_FOUR.id) {
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                if (tempModel.noOfCyl == 4) {
                    rateCommand.basePremium = 1300
                    rateCommand.premium = 1300
                } else if (tempModel.noOfCyl == 6) {
                    rateCommand.basePremium = 1400
                    rateCommand.premium = 1400
                } else if (tempModel.noOfCyl == 8) {
                    rateCommand.basePremium = 1600
                    rateCommand.premium = 1600
                } else if (tempModel.noOfCyl == 10) {
                    rateCommand.basePremium = 2100
                    rateCommand.premium = 2100
                } else if (tempModel.noOfCyl == 12) {
                    rateCommand.basePremium = 2100
                    rateCommand.premium = 2100
                }
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                if (tempModel.noOfCyl == 4) {
                    rateCommand.basePremium = 1750
                    rateCommand.premium = 1750
                } else if (tempModel.noOfCyl == 6) {
                    rateCommand.basePremium = 1900
                    rateCommand.premium = 1900
                } else if (tempModel.noOfCyl == 8) {
                    rateCommand.basePremium = 1950
                    rateCommand.premium = 1950
                } else if (tempModel.noOfCyl == 10) {
                    rateCommand.basePremium = 2150
                    rateCommand.premium = 2150
                } else if (tempModel.noOfCyl == 12) {
                    rateCommand.basePremium = 2150
                    rateCommand.premium = 2150
                }
            } else if (quoteCommand.vechileTypeId in [VehicleTypeEnum.COUPE.value, VehicleTypeEnum.CONVERTIBLE.value, VehicleTypeEnum.SPORTS.value]) {
                if (tempModel.noOfCyl == 4) {
                    rateCommand.basePremium = 1300
                    rateCommand.premium = 1300
                } else if (tempModel.noOfCyl == 6) {
                    rateCommand.basePremium = 1400
                    rateCommand.premium = 1400
                } else if (tempModel.noOfCyl == 8) {
                    rateCommand.basePremium = 1600
                    rateCommand.premium = 1600
                } else if (tempModel.noOfCyl == 10) {
                    rateCommand.basePremium = 2100
                    rateCommand.premium = 2100
                } else if (tempModel.noOfCyl == 12) {
                    rateCommand.basePremium = 2100
                    rateCommand.premium = 2100
                }
            } else if (quoteCommand.vechileTypeId in [VehicleTypeEnum.PICKUP.value]) {
                rateCommand.basePremium = 2300
                rateCommand.premium = 2300
            }

            // Condition add to treat some models of Volkswagen VW Golf GT/GTI/R as Sports car for Third Party
            if (quoteCommand.carCategory == VehicleTypeEnum.SPORTS.toString()) {
                if (tempModel.noOfCyl == 4) {
                    rateCommand.basePremium = 1300
                    rateCommand.premium = 1300
                } else if (tempModel.noOfCyl == 6) {
                    rateCommand.basePremium = 1400
                    rateCommand.premium = 1400
                } else if (tempModel.noOfCyl == 8) {
                    rateCommand.basePremium = 1600
                    rateCommand.premium = 1600
                } else if (tempModel.noOfCyl == 10) {
                    rateCommand.basePremium = 2100
                    rateCommand.premium = 2100
                } else if (tempModel.noOfCyl == 12) {
                    rateCommand.basePremium = 2100
                    rateCommand.premium = 2100
                }
            }
        }
    }

    // 10% discount for Sedan and 4x4 only
    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

//        if (quoteCommand.noClaimsDiscountId && !quoteCommand.hasClaim && !quoteCommand.isPolicyExpired &&
//            quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value]) {
//
//            rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
//
//        } else if (!quoteCommand.isPolicyExpired && ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
//            quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS &&
//            quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value]) {
//
//            // 10% discount for 1 year old car with 1 year self declaration
//            rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
//            rate.noClaimDiscountPercent = 10   //10%
//            rate.requiredSelfDeclarationNumber = 1  //At least 1 year self declaration is required
//        }

        boolean isAgency = rate.agencyRepair

        // NCD Discount for comprehensive non agency only
        if (!isAgency && quoteCommand.noClaimsDiscountId) {

            BigDecimal discountPercentage = 0

            if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                discountPercentage = 0.10 //10% Discount
            } else if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR2.value()) {
                discountPercentage = 0.15 //15% Discount
            } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
                discountPercentage = 0.20 //20% Discount
            }

            rate.premium = rate.premium.subtract(rate.basePremium * discountPercentage)

            // Discount is not less than min premium
            if (rate.premium < rate.minPremium) {
                rate.premium = rate.minPremium
            }
        }

        log.info("methaq.applyDiscounts - rate.premium:${rate.premium}")

        rate

    }

    RateCommand checkMinimumPremium(RateCommand rate, QuoteCommand quoteCommand) {

        rate.actualBasePremium = rate.premium

        // Minimum Premium for new license is AED 2,500
//        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id &&
//            quoteCommand.internationalDrivingExperienceId < DrivingExperienceEnum.THREE_TO_FOUR.id) {
//            rate.minPremium = 2500
//        }

        // after discount if premium is less then minimum premium then use minimum premium
        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium

            rate.productDiscountAmount = null
            rate.productDiscountPercent = null
        }

        rate
    }

    QuoteCommand getCloneQuoteCommand(QuoteCommand quoteCommand) {
        QuoteCommand tempGuoteCommand = new QuoteCommand()
        InvokerHelper.setProperties(tempGuoteCommand, quoteCommand.properties)
        tempGuoteCommand
    }
}



