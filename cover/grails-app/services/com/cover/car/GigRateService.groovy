package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CityEnum
import com.safeguard.Country
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

@Transactional
class GigRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 64
    public static final Integer PRODUCT_TPL_ID = 5053

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY, isOffline)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)
            log.info("quoteCommand:${quoteCommand}, applicableRates:${applicableRates}")
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand.providerId = PROVIDER_ID
        rateCommand = applyTplBasePremium(rateCommand, quoteCommand, rate)
        rateCommand.premium = rateCommand.basePremium
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    private RateCommand applyTplBasePremium(RateCommand rateCommand, QuoteCommand quoteCommand,
                                            ProductTplRate productTplRate) {

        BigDecimal basePremium = productTplRate.basePremium
        Integer noOfCyl = Model.read(quoteCommand.modelId).noOfCyl

        Integer noClaimYearsByNCD = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(quoteCommand)
        Integer applicableDiscountsYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)

        Integer noClaimYears = applicableDiscountsYears
        log.info(".applyTplBasePremium - noClaimYears:${noClaimYears}")

        if (!quoteCommand.isPolicyExpired && noClaimYears != null && noClaimYears >= 1
                && quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.TWO_TO_THREE.id
                && quoteCommand.registrationCityId.intValue() in [CityEnum.DUBAI.value(), CityEnum.ABU_DHABI.value()]) {

            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                if (noOfCyl == 4) {
                    basePremium = 600
                } else if (noOfCyl == 6) {
                    basePremium = 680
                } else if (noOfCyl == 8) {
                    basePremium = 760
                } else if (noOfCyl > 8) {
                    basePremium = 1040
                }

            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.COUPE.value) {
                if (noOfCyl == 4) {
                    basePremium = 650
                } else if (noOfCyl == 6) {
                    basePremium = 730
                } else if (noOfCyl == 8) {
                    basePremium = 810
                } else if (noOfCyl > 8) {
                    basePremium = 1080
                }

            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                if (noOfCyl == 4) {
                    basePremium = 800
                } else if (noOfCyl == 6) {
                    basePremium = 840
                } else if (noOfCyl == 8) {
                    basePremium = 880
                } else if (noOfCyl > 8) {
                    basePremium = 1400
                }

            }

            if (!noClaimYearsByNCD) {
                rateCommand.requiredSelfDeclarationNumber = noClaimYears
            }

        }

        rateCommand.basePremium = basePremium

        return rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE, boolean isOffline) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

       /* Country nationality = Country.load(quoteCommand.nationalityId)
        if (nationality.isArabic){
            return false
        }*/

        return isEligible
    }
}
