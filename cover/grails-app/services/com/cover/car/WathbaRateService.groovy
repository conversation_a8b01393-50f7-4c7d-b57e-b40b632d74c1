package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CarMakeEnum
import com.safeguard.CityEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.joda.time.LocalDate

import java.math.RoundingMode

/**
 * Calculate premium for Wathba Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class WathbaRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 10
    public static final Integer PRODUCT_COMPREHENSIVE_ID = 39
    public static final Integer TPL_PRODUCT_ID = 40

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = applyDiscounts(quoteCommand, rateCommand, CoverageTypeEnum.THIRD_PARTY)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = c4meFees(quoteCommand, rateCommand, rate.product)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
                //if (rateCommand.premium == rateCommand.minPremium) {
        //    // apply discount only when minimum premium
            rateCommand = applyDiscounts(quoteCommand, rateCommand, CoverageTypeEnum.COMPREHENSIVE)

        //}
        rateCommand = applyLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = c4meFees(quoteCommand, rateCommand, rate.product)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand c4meFees(QuoteCommand quoteCommand, RateCommand rateCommand, Product product) {

        // added credit card fee as part of c4me fee for wathba.
        BigDecimal ccPercentage = 0.017 // 11.7% CC fee

        BigDecimal ccFee = rateCommand.premium * ccPercentage //Not Applicable
        ccFee = ccFee.setScale(0, RoundingMode.CEILING)

        rateCommand.premium = rateCommand.premium + ccFee
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
        rateCommand.c4meFee = rateCommand.c4meFee + ccFee
        //rateCommand.premium = rateCommand.premium + ccFee
        rateCommand.originalC4meFee = rateCommand.c4meFee

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        BigDecimal baseRate = getBaseRate(isAgency, quoteCommand, applicableRate)
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)

        rateCommand.with {
            agencyRepair = isAgency
            productName = product.name
            productId = product.id
            minPremium = agencyRepair ?
                applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
            basePremium = premium
        }

        rateCommand
    }

    private BigDecimal getBaseRate(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        //promotion Offer until End of Ramadan 2019
        if (LocalDate.now() <= new LocalDate("2019-06-02") && quoteCommand.policyStartDate <= new LocalDate("2019-06-02")) {
            Model model = Model.read(quoteCommand.modelId)

            if (model.makeId.intValue() == CarMakeEnum.ROLLS_ROYCE.id) {
                baseRate = 1.85

            } else if (quoteCommand.insuredValue > 250000) {
                baseRate = 1.95
            }
        }

        log.info("wathba.getBaseRate - baseRate:${baseRate}")

        baseRate
    }

    private boolean checkAgency(QuoteCommand quoteCommand) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            if (quoteCommand.carAge <= 1) {
                isAgency = true
            } else if (quoteCommand.carAge == 2 && quoteCommand.noClaimsDiscountId) {
                isAgency = true
            }
        }

        if (quoteCommand.insuredValue < 50000) {
            isAgency = false
        }

        isAgency
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {

        int carAge = quoteCommand.carAge

        if (carAge >= 9) {
            //25% loading on car older then 8 years
            rateCommand.premium = rateCommand.premium + (rateCommand.premium * 0.25)
        }

        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        // not eligible if any claim
        if (quoteCommand.hasClaim) {
            isEligible = false
        }

        // not eligible if uae driving experience is less then 1 year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }

        isEligible
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate, CoverageTypeEnum coverageTypeEnum) {
        log.info("wathba.applyDiscounts - rate.premium:${rate.premium}, product:${rate.productId}")

        //No Discount for under age or non GCC cars or above 250K vehicles
        if (quoteCommand.customerAge < 25 ||
            (quoteCommand.isNonGccSpec && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) ||
            (rate.productId != TPL_PRODUCT_ID && quoteCommand.insuredValue > 250000)) {
            return rate
        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.isNonGccSpec) {
            //No discount for non gcc vehicles
            return rate
        }

        // NCD Discount
        if (!quoteCommand.hasClaim && quoteCommand.noClaimsDiscountId) {
            float discountPercentage = 0.0
//            if (rate.productId != TPL_PRODUCT_ID && rate.agencyRepair) {
//                //discountPercentage = 0.20
//            }
            /*else if (rate.productId != TPL_PRODUCT_ID && !rate.agencyRepair) {
                if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.getValue() &&
                    quoteCommand.makeId in [CarMakeEnum.TOYOTA.id, CarMakeEnum.MITSUBISHI.id,
                                            CarMakeEnum.LEXUS.id, CarMakeEnum.NISSAN.id,
                                            CarMakeEnum.FORD.id]) {
                    discountPercentage = 0.40
                } else {
                    discountPercentage = 0.30
                }

            } else if (rate.productId == TPL_PRODUCT_ID) {
                discountPercentage = 0.50
            } */  if (rate.productId == PRODUCT_COMPREHENSIVE_ID && quoteCommand.insuredValue < 250000){
                discountPercentage = 0.30
            } else if (rate.productId == TPL_PRODUCT_ID){
                discountPercentage = 0.30
            }

            rate.premium = rate.premium.subtract(rate.basePremium * (discountPercentage))
        }

        log.info("wathba.applyDiscounts - After discount rate.premium:${rate.premium}")

        rate
    }

}
