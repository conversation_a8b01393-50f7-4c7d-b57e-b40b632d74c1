package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CarMakeEnum
import com.safeguard.ClaimPeriodEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.joda.time.LocalDateTime

@Transactional(readOnly = true)
class DarRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 26
    public static final Integer PRODUCT_ALMASA_PLUS_ID = 1001
    public static final Integer PRODUCT_TS_SCHEME_COMPREHENSIVE_ID = 1002
    public static final Integer PRODUCT_MUMTAZ_COMPREHENSIVE_ID = 1007
    public static final Integer PRODUCT_ASAS_COMPREHENSIVE_ID = 1008

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            QuoteCommand tempQuoteCommand = getCloneQuoteCommand(quoteCommand)

            if (tempQuoteCommand.modelId in [33583, 33651]) {
                tempQuoteCommand.vechileTypeId = VehicleTypeEnum.SEDAN.value
            }

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(tempQuoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    Product product = rate.product

                    boolean isProductEligible = checkProductEligibility(tempQuoteCommand, product)

                    if (isProductEligible) {
                        RateCommand rateCommand = populateRatings(tempQuoteCommand, rate)
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.carCategory = null

            QuoteCommand tempQuoteCommand = getCloneQuoteCommand(quoteCommand)

            if (tempQuoteCommand.modelId in [33583, 33651]) {
                tempQuoteCommand.vechileTypeId = VehicleTypeEnum.SEDAN.value
            }

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(tempQuoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()

                boolean isProductEligible = checkProductEligibility(tempQuoteCommand, rate.product)

                if (isProductEligible) {
                    rateCommand = populateRatings(tempQuoteCommand, rate)
                }
            }
        }

        rateCommand
    }



    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand,CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = applyTplDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        log.info("rateCommand.tpl limit:${rateCommand.thirdPartyLiability}")
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = checkMinimumPremium(quoteCommand, rateCommand)
        rateCommand = applyLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        rateCommand = applyBaseRate(rateCommand, quoteCommand, applicableRate)

        rateCommand.basePremium = rateCommand.premium

        log.info("darRateService.calculatePremium - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (!ratingService.allowAgency()){
            return false
        }

        int carAge = quoteCommand.carAge
        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (applicableRate.productId == PRODUCT_ALMASA_PLUS_ID && carAge <= 3) {
                if (quoteCommand.isOldAgency) {
                    isAgency = true
                }

            } else if (applicableRate.productId == PRODUCT_MUMTAZ_COMPREHENSIVE_ID) {

                if (quoteCommand.insuredValue <= 350000) {
                    if (quoteCommand.isBrandNew || carAge <= 1) {
                        isAgency = true

                    } else if (carAge <= 2 && quoteCommand.noClaimsDiscountId
                        && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                        isAgency = true
                    } else if (carAge <= 3 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()
                        && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                        isAgency = true
                    }
                }

            } else if (applicableRate.productId == PRODUCT_ASAS_COMPREHENSIVE_ID) {
                isAgency = false
            } else if (applicableRate.productId == PRODUCT_TS_SCHEME_COMPREHENSIVE_ID) {
                if ((quoteCommand.isBrandNew || carAge <= 1) && quoteCommand.isOldAgency
                    && !quoteCommand.hasClaim) {
                    isAgency = true

                } else if (carAge <= 2 && quoteCommand.noClaimsDiscountId && quoteCommand.isOldAgency
                    && !quoteCommand.hasClaim) {
                    isAgency = true
                }
            }
        }

        isAgency
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        Model model = Model.read(quoteCommand.modelId)

        if (rate.productId == PRODUCT_MUMTAZ_COMPREHENSIVE_ID) {
            boolean discountApplied = false

            if (!discountApplied && quoteCommand.insuredValue <= 350000) {
                boolean isAsian = isAsianNational(quoteCommand)
                boolean isWestern = isWesternNational(quoteCommand)

                if (isAsian || (isWestern && !rate.agencyRepair)) {
                    // 20% discount
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
                    discountApplied = true
                } else if (isWestern && rate.agencyRepair) {
                    // 30% discount
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.30))
                    discountApplied = true
                }
            }

            // NCD Discount
            if (!discountApplied && rate.agencyRepair &&
                quoteCommand.makeId in [CarMakeEnum.NISSAN.id, CarMakeEnum.TOYOTA.id, CarMakeEnum.LEXUS.id]) {

                //10% Discount if Lexus, Toyota or Nissan
                rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
                discountApplied = true
            }

        } else if (rate.productId == PRODUCT_ASAS_COMPREHENSIVE_ID) {
            if (quoteCommand.noClaimsDiscountId && !quoteCommand.isPolicyExpired)  {

                if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.10)) //10% Discount
                } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()) {
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.15)) //15% Discount
                }

            } else if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
                ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand) &&
                !quoteCommand.isPolicyExpired && quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

                rate.premium = rate.premium.subtract(rate.basePremium * (0.10)) //10% Discount
                rate.requiredSelfDeclarationNumber = 1
                rate.noClaimDiscountPercent = 10
            }
        }

        rate
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rate) {

        Model model = Model.read(quoteCommand.modelId)

        if (rate.productId == PRODUCT_MUMTAZ_COMPREHENSIVE_ID) {

            BigDecimal loadingRate = 0

            if (model.vehicleTypeId in [VehicleTypeEnum.COUPE, VehicleTypeEnum.CONVERTIBLE]) {
                loadingRate += 0.25 // 25% of base premium
            }

            if (rate.agencyRepair &&
                (quoteCommand.makeId == CarMakeEnum.INFINITI.id
                    && model.vehicleTypeId == VehicleTypeEnum.SEDAN.value)) {
                loadingRate += 0.05 //5% Loading on base premium
            }

            if (rate.agencyRepair &&
                quoteCommand.makeId in [CarMakeEnum.BMW.id, CarMakeEnum.AUDI.id, CarMakeEnum.VOLKSWAGEN.id,
                                        CarMakeEnum.CHEVROLET.id, CarMakeEnum.JEEP.id, CarMakeEnum.MASERATI.id]) {
                loadingRate += 0.10 //10% Loading on base premium
            }

            //Age between 25 & 30 and agency repair and no NCD
            if (rate.agencyRepair && quoteCommand.customerAge >= 25 && quoteCommand.customerAge <= 30) {
                loadingRate += 0.10 //10% Loading on base premium
            }

            if (loadingRate) {
                rate.premium = rate.premium.add(rate.basePremium * loadingRate)
            }

        }

        rate
    }

    boolean checkEligibility(QuoteCommand quoteCommand, Product product = null) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand,
                                                                CoverageTypeEnum.COMPREHENSIVE, product?.id)

        if (quoteCommand.isThirdParty) {
            isEligible = false
        }

        if (quoteCommand.isPolicyExpired || quoteCommand.isNonGccSpec || quoteCommand.hasClaim) {
            isEligible = false
        }

        //At least 1 year old for all products
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }

        isEligible
    }


    boolean checkEligibility(QuoteCommand quoteCommand, CoverageTypeEnum productTypeEnum) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand,
            productTypeEnum)

        if (quoteCommand.customerAge < 25) {
            isEligible = false
        }

        if(quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()){
            isEligible = false
        }

        isEligible
    }

    /**
     * Check if product is Eligible to be quoted
     * @param quoteCommand
     * @param product
     * @return
     */
    private boolean checkProductEligibility(QuoteCommand quoteCommand, Product product) {
        boolean isEligible = true

        if (product.id == PRODUCT_ALMASA_PLUS_ID) {

            isEligible = checkEligibility(quoteCommand, product)

        } else if (product.id == PRODUCT_TS_SCHEME_COMPREHENSIVE_ID) {

            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                isEligible = false
            }

            Model model = Model.read(quoteCommand.modelId)
            if (!(quoteCommand.makeId in [
                CarMakeEnum.LEXUS.id, CarMakeEnum.TOYOTA.id, CarMakeEnum.NISSAN.id, CarMakeEnum.MERCEDES.id,
                CarMakeEnum.LAND_ROVER.id] && model.vehicleTypeId == VehicleTypeEnum.FOURx4.value)) {
                isEligible = false
            }


        } else if (product.id == PRODUCT_MUMTAZ_COMPREHENSIVE_ID ||
                    product.id == PRODUCT_ASAS_COMPREHENSIVE_ID) {

            Model model = Model.read(quoteCommand.modelId)

            //Not eligible if Models' production has been stopped. Exception for Honda Accord Coupe
            if (model.yearTo && model.yearTo < LocalDateTime.now().getYear() && !(model.id in [33583, 33651])) {
                isEligible = false
            }

        }

        return isEligible
    }


    private RateCommand applyBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (quoteCommand.isBrandNew && applicableRate.baseRateBrandNew && rateCommand.agencyRepair) {
            rateCommand.baseRate = applicableRate.baseRateBrandNew
        } else {
            rateCommand.baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        }

        Model model = Model.read(quoteCommand.modelId)
        if (rateCommand.productId == PRODUCT_TS_SCHEME_COMPREHENSIVE_ID) {
            if (rateCommand.agencyRepair) {
                if (quoteCommand.isBrandNew) {
                    if (quoteCommand.makeId in [CarMakeEnum.TOYOTA.id, CarMakeEnum.LEXUS.id]) {
                        rateCommand.baseRate = 1.80
                    } else if (quoteCommand.makeId in [CarMakeEnum.MERCEDES.id, CarMakeEnum.LAND_ROVER.id]) {
                        rateCommand.baseRate = 2.00
                    } else if (quoteCommand.makeId == CarMakeEnum.NISSAN.id) {
                        rateCommand.baseRate = 2.20
                    }
                } else {
                    if (quoteCommand.makeId in [CarMakeEnum.TOYOTA.id, CarMakeEnum.LEXUS.id]) {
                        rateCommand.baseRate = 2.00
                    } else if (quoteCommand.makeId in [CarMakeEnum.MERCEDES.id, CarMakeEnum.LAND_ROVER.id]) {
                        rateCommand.baseRate = 2.20
                    } else if (quoteCommand.makeId == CarMakeEnum.NISSAN.id) {
                        rateCommand.baseRate = 2.45
                    }
                }
            }

        } else if (rateCommand.productId == PRODUCT_ALMASA_PLUS_ID &&
            model.modelMasterId in [335]) {
            //Mercedes G-Class, apply Sports rate, currently its 2.4
            rateCommand.baseRate = 2.40
        }

        log.info("applyBaseRate - productId:${rateCommand.productId}, modelMaster:${model.modelMasterId}, agency:${rateCommand.agencyRepair}, " +
            "quoteCommand.carAge:${quoteCommand.carAge}, baseRate:${rateCommand.baseRate}")

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate , quoteCommand.insuredValue)
        if (quoteCommand.isBrandNew && applicableRate.minPremiumBrandNew && rateCommand.agencyRepair) {
            rateCommand.minPremium = applicableRate.minPremiumBrandNew
        } else {
            rateCommand.minPremium = rateCommand.agencyRepair ?
                applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        }

        rateCommand
    }

    /**
     * Apply Discount on TPL rates
     * @param quoteCommand
     * @param rate
     * @return
     */
    RateCommand applyTplDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        if (quoteCommand.noClaimsDiscountId) {
            if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
            } else if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR2.value()) {
                rate.premium = rate.premium.subtract(rate.basePremium * (0.15))
            } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
                rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
            }
        } /*else if (quoteCommand.requestSource == RequestSourceEnum.WEB &&
            !quoteCommand.hasClaim && !quoteCommand.noClaimsDiscountId && !quoteCommand.isFirstCar &&
            quoteCommand.lastClaimPeriod != null && quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            if (quoteCommand.lastClaimPeriod == ClaimPeriodEnum.TWENTY_FOUR_MONTHS) {
                // 10% discount for 1 year old car with 1 year self declaration
                rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
                rate.noClaimDiscountPercent = 10   //10%
                rate.requiredSelfDeclarationNumber = 1  //At least 1 year self declaration is required
            } else if (quoteCommand.lastClaimPeriod == ClaimPeriodEnum.THIRTY_SIX_MONTHS) {
                // 15% discount for 1 year old car with 1 year self declaration
                rate.premium = rate.premium.subtract(rate.basePremium * (0.15))
                rate.noClaimDiscountPercent = 15   //15%
                rate.requiredSelfDeclarationNumber = 2  //At least 2 year self declaration is required
            } else if (quoteCommand.lastClaimPeriod in [ClaimPeriodEnum.FOURTY_EIGHT_MONTHS,
                                                        ClaimPeriodEnum.MORE_THAN_FOURTY_EIGHT_MONTHS,
                                                        ClaimPeriodEnum.NEVER]) {
                // 20% discount for 1 year old car with 1 year self declaration
                rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
                rate.noClaimDiscountPercent = 20   //20%
                rate.requiredSelfDeclarationNumber = 3  //At least 3 year self declaration is required
            }
        }
*/
        rate
    }

    RateCommand checkMinimumPremium(QuoteCommand quoteCommand, RateCommand rate) {

        rate.actualBasePremium = rate.premium

        if (rate.productId == PRODUCT_MUMTAZ_COMPREHENSIVE_ID) {
            BigDecimal minPremiumRate = rate.agencyRepair ? 2.00 : 1.60

            BigDecimal minPremiumByRate = minPremiumRate * quoteCommand.insuredValue / 100

            if (minPremiumByRate > rate.minPremium) {
                rate.minPremium = minPremiumByRate
            }
        }

        // after discount if premium is less then minimum premium then use minimum premium
        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium

            rate.productDiscountAmount = null
            rate.productDiscountPercent = null
        }

        rate
    }

    QuoteCommand getCloneQuoteCommand(QuoteCommand quoteCommand) {
        QuoteCommand tempQuoteCommand = new QuoteCommand()
        InvokerHelper.setProperties(tempQuoteCommand, quoteCommand.properties)
        tempQuoteCommand
    }

    private boolean isAsianNational(QuoteCommand quoteCommand) {
        if (quoteCommand.nationalityId in [
            164,    //Afghanistan
            7,    //Armenia
            10,    //Azerbaijan
            13,    //Bangladesh
            19,    //Bhutan
            25,    //Brunei
            30,    //Cambodia
            36,    //China
            42,    //Cyprus
            60,    //Georgia
            72,    //India
            73,    //Indonesia
            80,    //Japan
            82,    //Kazakhstan
            184,    //Kyrgyzstan
            85,    //Laos
            96,    //Malaysia
            97,    //Maldives
            105,    //Mongolia
            28,     //Burman / Myanmar
            110,    //Nepal
            116,    //North Korea
            119,    //Pakistan
            124,    //Philippines
            129,    //Russia
            137,    //Singapore
            143,    //South Korea
            145,    //Sri Lanka
            152,    //Taiwan
            153,    //Tajikistan
            155,    //Thailand
            249,    //Timor-Leste
            159,    //Turkey
            160,    //Turkmenistan
            168,    //Uzbekistan
            172,    //Vietnam
        ]) {
            return true
        }
        return false
    }

    private boolean isWesternNational(QuoteCommand quoteCommand) {

        //Europe, USA and Canadian nationalitie
        if (quoteCommand.nationalityId in [
            9,    //Austria -
            8,    // Australia -
            16,    //Belgium -
            43,    //Czech Republic -
            44,    //Denmark -
            56,    //Finland -
            57,    //France -
            61,    //Germany -
            63,    //Greece -
            76,    //Ireland -
            80,    // Japan -
            78,    //Italy -
            86,    //Latvia -
            91,    //Lithuania -
            92,    //Luxembourg -
            111,    //Netherlands -
            112,    // New Zealand -
            117,    //Norway -
            125,    //Poland -
            126,    //Portugal -
            128,    //Romania -
            134,    //Serbia -
            138,    //Slovakia -
            142,    // South Afria
            143,    // South Korea -
            144,    //Spain -
            149,    //Sweden -
            150,    //Switzerland -
            165,    //United Kingdom -
            166,    //United States -
            32     //Canada -
            // Why Turkey, Singapore, China, Hongkong
        ]) {
            return true
        }
        return false
    }
}
