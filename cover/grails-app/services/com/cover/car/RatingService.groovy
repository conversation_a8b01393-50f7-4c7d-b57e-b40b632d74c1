package com.cover.car


import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.egy.*
import com.cover.car.kwt.*
import com.cover.car.lbn.LbnAssurexRateService
import com.cover.car.lbn.LbnCapitalRateService
import com.cover.car.lbn.LbnCommercialRateService
import com.cover.car.lbn.LbnFidelityRateService
import com.cover.util.UtilService
import com.safeguard.*
import com.safeguard.car.*
import com.safeguard.car.vehicle.AgencyBlacklisted
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import com.safeguard.util.ConfigurationService
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime

import java.util.stream.Collectors

/**
 * Parent service inherited by all Rating Services.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class RatingService {

    def additionalChargesService
    def grailsApplication
    def commonUtilService
    def messageSource
    def utilService
    def discountService
    def quoteService
    def sessionService
    def configurationService

    private static final String FIFTY_PERCENT_EXCESS_COVER = 'cover.special.feature.fiftyPercentExcess';
    private static final String OFFROAD_RECOVERY_MESSAGE_FOR_NOOR = 'common.specialFeatures.offRoadRecoveryForNT';

    List<ProductBaseRate> getBaseRateMinPremium(QuoteCommand quoteCommand, boolean isOffline, Integer noClaimYears = null) {
        log.info("quoteCommand: " + quoteCommand)
        ModelMaster modelMaster = Model.read(quoteCommand.modelId).modelMaster

        List<ProductBaseRate> applicableRates =
            findApplicableRates(quoteCommand.providerId, quoteCommand.makeId, quoteCommand.vechileTypeId,
                quoteCommand.insuredValue, quoteCommand.customerAge,
                quoteCommand.carAge, quoteCommand.productId, quoteCommand.carCategory, quoteCommand.noOfCyl, isOffline,
                quoteCommand.requestSource, quoteCommand.registrationCityId, quoteCommand.nationalityCategory,
                noClaimYears, modelMaster.id, quoteCommand.modelId)
        log.info("applicableRates:$applicableRates")
        if (applicableRates.size() > 0 && noClaimYears != null) {
            Map<Integer, ProductBaseRate> productBaseRateMap = new HashMap<>();
            applicableRates.forEach({ ar ->
                if (!productBaseRateMap.containsKey(ar.productId)) {
                    productBaseRateMap.put(ar.productId, ar)
                } else {
                    ProductBaseRate current = productBaseRateMap.get(ar.productId)
                    if (current.noClaimYears < ar.noClaimYears) {
                        productBaseRateMap.put(ar.productId, ar)
                    }
                }
            })

            applicableRates = productBaseRateMap.values().stream().collect(Collectors.toList())
        }

        applicableRates
    }

    RateCommand getRating(QuoteCommand quoteCommand, boolean isOffline) {

        Product product = Product.get(quoteCommand.productId)
        RateCommand rateCommand = null
        if (product) {
            def rateService = getRatingService(product.providerId, quoteCommand.requestSubSource, product.type.id)
            if (rateService) {
                quoteCommand.providerId = product.providerId
                log.info("ratingService.getRating - fetching RateCommand for car quote ${quoteCommand.quoteId}")
                if (product.typeId == CoverageTypeEnum.COMPREHENSIVE.value()) {
                    rateCommand = rateService.getRate(quoteCommand, isOffline)
                } else {
                    rateCommand = rateService.getTplRate(quoteCommand, isOffline)
                }
                log.info("ratingService.getRating - fetched RateCommand for car quote ${quoteCommand.quoteId}")
            }

            if (rateCommand) {
                List<RateCommand> rateCommandsAfterAdditionCharges = additionalChargesService
                    .addAdditionalChargesToPremiums([rateCommand], ProductType.findByName('CAR'))
                rateCommand = rateCommandsAfterAdditionCharges[0]

                log.info("rateCommand.additionalCharges:${rateCommand.additionalCharges}")
            }
        }

        return rateCommand
    }

    /**
     * Get productsByIsOfflineQuotes=true
     * Convert Products to RateCommands
     * Apply covers to RateCommands
     * Return RateCommands
     */
    List<RateCommand> getOfflineQuotes(QuoteCommand quoteCommand, Integer providerId) {
        List<RateCommand> rateList = []
        def c = Product.createCriteria()
        List<Product> products = c.list {
            eq 'active', true
            eq 'isOfflineQuotes', true
            provider {
                eq 'id', providerId
                eq 'active', true
            }
        } as List<Product>

        if (products) {
            for (product in products) {
                rateList.add(createOfflineQuoteRateCommand(quoteCommand, product))
            }
        }

        return rateList
    }

    RateCommand getOfflineQuote(QuoteCommand quoteCommand) {
        log.info("getOfflineQuote - quoteCommand.productId: ${quoteCommand.productId}")
        Product product = Product.get(quoteCommand.productId)
        if (product && product.isOfflineQuotes) {
            return createOfflineQuoteRateCommand(quoteCommand, product)
        }
        return null
    }

    RateCommand createOfflineQuoteRateCommand(QuoteCommand quoteCommand, Product product) {
        RateCommand rateCommand = new RateCommand()
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = false
        rateCommand.productId = product.id
        rateCommand.isOfflineQuotes = true
        rateCommand = applyCovers(quoteCommand, rateCommand)
        return rateCommand
    }

    List<ProductBaseRate> findApplicableRates(provId, makeId, vTypeId, insuredValue,
                                              ageCustomer, ageCar, pId, category, cyl,
                                              boolean isOffline, RequestSourceEnum requestSource, cityId,
                                              String natCategory, Integer noClaimYears = null,
                                              Integer modelMasterId = null,
                                              Integer modelId = null) {

        def c = ProductBaseRate.createCriteria()
        println("started product rating query...provId:$provId, makeId:$makeId, vTypeId:$vTypeId, insuredValue:$insuredValue," +
            "isOffline:$isOffline, requestSource:$requestSource, cityId:$cityId, " +
            "ageCustomer:$ageCustomer, ageCar:$ageCar, pId:$pId, category:$category, cyl:$cyl, natCategory:$natCategory, " +
            "noClaimYears:$noClaimYears, modelMasterId:$modelMasterId, modelId:$modelId")
        def list = c.list {
            product {
                provider {
                    eq 'id', provId
                    eq 'active', true
                }
                if (pId) {
                    eq 'id', pId
                }
                eq 'active', true
                eq 'isOfflineQuotes', false
                if (!isOffline) {
                    eq 'isOffline', false
                }
                if (requestSource == RequestSourceEnum.SMARTDUBAI) {
                    eq "activeForSmartDubai", true
                }
            }
            or {
                isNull 'vehicleType'
                eq 'vehicleType.id', vTypeId
            }
            or {
                isNull 'make'
                eq 'make.id', makeId
            }
            if (noClaimYears != null) {
                le 'noClaimYears', new BigDecimal(noClaimYears + "")
            } else {
                isNull 'noClaimYears'
            }

            le 'valueFrom', insuredValue
            ge 'valueTo', insuredValue
            le 'ageCustomerFrom', ageCustomer
            ge 'ageCustomerTo', ageCustomer
            le 'ageCarFrom', ageCar
            ge 'ageCarTo', ageCar
            if (category) {
                or {
                    isNull 'carCategory'
                    eq 'carCategory', category
                }
            }
            if (cityId) {
                or {
                    isNull 'city'
                    eq 'city.id', cityId
                }
            }
            if (cyl) {
                or {
                    eq 'noOfCyl', cyl
                    isNull 'noOfCyl'
                }
            }
            if (natCategory) {
                or {
                    eq 'nationalityCategory', natCategory
                    isNull 'nationalityCategory'
                }
            }
            if (modelMasterId) {
                or {
                    isNull 'modelMaster'
                    eq 'modelMaster.id', modelMasterId
                }
            }
            if (modelId) {
                or {
                    isNull 'model'
                    eq 'model.id', modelId
                }
            }
        }
        println("product rating query result...")
        list
    }

    List<ProductTplRate> findTplApplicableRates(providerId, vehicleTypeId, noOfCyl, ageCustomer,
                                                isOffline, carCategory = null, isActive = true,
                                                RequestSourceEnum requestSource = null,
                                                String nationalityCategory = null) {

        def c = ProductTplRate.createCriteria()
        def list = c.list {
            product {
                eq 'provider.id', providerId
                eq 'active', isActive
                eq 'isOfflineQuotes', false
                if (!isOffline) {
                    eq 'isOffline', false
                }
                if (requestSource == RequestSourceEnum.SMARTDUBAI) {
                    eq "activeForSmartDubai", true
                }
                provider {
                    eq 'active', true
                }
            }
            or {
                isNull 'vehicleType'
                eq 'vehicleType.id', vehicleTypeId
            }
            le 'ageCustomerFrom', ageCustomer
            ge 'ageCustomerTo', ageCustomer
            or {
                isNull 'noOfCyl'
                eq 'noOfCyl', noOfCyl
            }
            if (carCategory) {
                or {
                    isNull 'carCategory'
                    eq 'carCategory', carCategory
                }
            }
            if (nationalityCategory) {
                or {
                    eq 'nationalityCategory', nationalityCategory
                    isNull 'nationalityCategory'
                }
            }
        }

        list
    }

    RateCommand applyCovers(QuoteCommand quoteCommand, RateCommand rateCommand) {

        Model model = Model.get(quoteCommand.modelId)
        Product product = Product.get(rateCommand.productId)
        ProductSpecialFeature feature = ProductSpecialFeature.find {
            eq "product.id", product.id

            or {
                isNull 'carCategory'
                eq 'carCategory', model.kwtIntCategory
            }
        }
        Locale locale = new Locale(quoteCommand.locale)

        ProductBasicCover cover = ProductBasicCover.findByProduct(product)
        cover.loadTransients("en")
        List roadsideAssistancesList = ProductRoadsideAssistance.findAllByProduct(product)

        rateCommand.with {
            insuredValue = quoteCommand.insuredValue.toInteger()
            breakdownCover = cover.breakDownCover
            roadsideAssistances = roadsideAssistancesList.collect { it.roadsideAssistance.name }
            paCover = cover.personalAccidentDriver
            replacementCar = cover.replacementCar
            if (cover.personalAccidentPax && cover.personalAccidentPax.isNumber()) {
                personalAccidentPax = Integer.parseInt(cover.personalAccidentPax) * (model.numberOfSeats - 1)
            } else {
                personalAccidentPax = cover.personalAccidentPax
            }
            carHireCashBenefit = cover.carHireCashBenefit
            carRegService = cover.carRegService
            coverLink = cover.coverLink
            damageToYourVehicle = cover.damageToYourVehicle
            dentRepair = cover.dentRepair
            lossOfEmployment = cover.lossOfEmployment
            lossOfPersonalBelongings = cover.lossOfPersonalBelongings
            offRoadDesertRecovery = cover.offRoadDesertRecovery
            otherInformation = cover.otherInformation
            replacementLocks = cover.replacementLocks
            tplBodilyDamageLimit = cover.tplBodilyDamageLimit
            tplMaterialDamageLimit = cover.tplMaterialDamageLimit
            if (cover.thirdPartyDamageLimit?.equalsIgnoreCase('Unlimited')) {
                String message = messageSource.getMessage('car.srp.static.tplDamageLimitUnlimited', null, locale)
                thirdPartyDamageLimit = message
            }
            // Temporarily translate into Arabic for Egypt providers only:
            else if (cover.thirdPartyDamageLimit && product.provider.countryId == 49) {
                String upToMessage = messageSource.getMessage('car.srp.static.tplDamageLimitUpTo', null, locale)
                String egpMessage = messageSource.getMessage('car.srp.static.tplDamageLimitInEGP', null, locale)
                thirdPartyDamageLimit = upToMessage + " " + cover.thirdPartyDamageLimit + " " + egpMessage
            } else {
                thirdPartyDamageLimit = cover.thirdPartyDamageLimit
            }
            thirdPartyLiability = cover.thirdPartyLiability
            windScreenCover = cover.windScreenCover
            valetParkingTheft = cover.valetParkingTheft
            providerImage = product.provider.logo
            productName = product.name
            productNameEn = product.nameEn
            provider = product.provider.name
            providerEn = product.provider.nameEn
            coverageTypeId = product.typeId
            naturalCalamity = cover.naturalCalamity
            thirdPartyLiabilityAmount = cover.thirdPartyLiabilityAmount
            damageToYourVehicleOman = cover.damageToYourVehicleOman
            damageToThirdPartyOman = cover.damageToThirdPartyOman
            emergencyMedical = cover.emergencyMedical
            specialFeatures = feature?.name
            providerId = product.providerId
            hasPremiumGarage = rateCommand.agencyRepair ? false : product.hasPremiumGarage
            isTakaful = product.isTakaful
            shortNameEn = product.provider.shortNameEn
            shortNameAr = product.provider.shortNameAr
        }

        if (quoteCommand.providerId == UnionRateService.PROVIDER_ID) {
            //Driver cover is free for above AED 70,000
            if (quoteCommand.insuredValue > 70000) {
                rateCommand.paCover = "yes"
            }

        } else if (quoteCommand.providerId == SalamaRateService.PROVIDER_ID) {

            if (rateCommand.productId in [SalamaRateService.PRODUCT_STANDARD, SalamaRateService.PRODUCT_HIGH_VALUE]) {
                rateCommand.showBreakdownAddon = false

                /*if (rateCommand.insuredValue < 40000 *//*&&
                    model.vehicleTypeId.intValue() in [VehicleTypeEnum.SEDAN, VehicleTypeEnum.COUPE]*//*) {
                    rateCommand.showPersonalAccidentPaxAddon = true
                    rateCommand.personalAccidentPax = 30 * (model.numberOfSeats - 1)
                    rateCommand.showPaCoverAddon = true
                    rateCommand.paCover = '120'

                    if (model.vehicleTypeId.intValue() in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.COUPE.value]) {
                        rateCommand.showBreakdownAddon = true
                        rateCommand.breakdownCover = '20'
                    }
                }*/

                if (quoteCommand.customerAge < 30) {
                    rateCommand.showPaCoverAddon = true
                    rateCommand.paCover = '120'
                }

                if (Country.read(quoteCommand.nationalityId).salamaCategory in ['NG1', 'NG2']) {
                    rateCommand.emergencyMedical = '2000'
                    rateCommand.replacementLocks = '1000'
                    rateCommand.specialFeatures += 'Child Car Seat Replacement subject to police report and accident;'
                }

                if (quoteCommand.customerAge < 30 || Country.read(quoteCommand.nationalityId).salamaCategory == 'NG3') {
                    rateCommand.offRoadDesertRecovery = "no"
                }

            } else if (rateCommand.productId == SalamaRateService.PRODUCT_TPL) {
                if (quoteCommand.customerAge >= 30) {
                    rateCommand.showPaCoverAddon = false
                    rateCommand.paCover = 'yes'
                }
            }

        } else if (quoteCommand.providerId == KwtTazurRateService.PROVIDER_ID) {
            if (model.tazurCategory == KwtTazurRateService.LUXURY_VEHICLE_LOW_TYPE) {
                String openFile = messageSource.getMessage('cover.special.feature.100.open.file', null, locale)
                rateCommand.specialFeatures = rateCommand.specialFeatures + " " + openFile
            }
        }  else if (quoteCommand.providerId == AlSagrRateService.PROVIDER_ID){

            Make make = Make.load(quoteCommand.makeId)

            if (rateCommand.productId == AlSagrRateService.COMPREHENSIVE_PRODUCT_ID && quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value){
                rateCommand.offRoadDesertRecovery = 'yes'
            }

            if (rateCommand.productId == AlSagrRateService.COMPREHENSIVE_PRODUCT_ID && make.country.id.toLong() != CountryEnum.JAPAN.id){
                rateCommand.replacementCar = '150'
            }

        } else if (quoteCommand.providerId == KwtGulfRateService.PROVIDER_ID) {
            String openFile = null
            if (model.gulfCategory == KwtGulfRateService.LUXURY_VEHICLE_NO_OPEN_FILE) {
                openFile = messageSource.getMessage('cover.special.feature.no.open.file', null, locale)
            } else if (model.gulfCategory == KwtGulfRateService.LUXURY_VEHICLE_50_OPEN_FILE) {
                openFile = messageSource.getMessage('cover.special.feature.50.open.file', null, locale)
            } else if (model.gulfCategory == KwtGulfRateService.LUXURY_VEHICLE_100_OPEN_FILE) {
                openFile = messageSource.getMessage('cover.special.feature.100.open.file', null, locale)
            } else if (model.gulfCategory == KwtGulfRateService.LUXURY_VEHICLE_150_OPEN_FILE) {
                openFile = messageSource.getMessage('cover.special.feature.150.open.file', null, locale)
            } else if (model.gulfCategory == KwtGulfRateService.LUXURY_VEHICLE_200_OPEN_FILE) {
                openFile = messageSource.getMessage('cover.special.feature.200.open.file', null, locale)
            }

            if (openFile) {
                rateCommand.specialFeatures = rateCommand.specialFeatures + " " + openFile
            }
        } /* else if (quoteCommand.providerId == DubaiNationalRateService.PROVIDER_ID
            && rateCommand.productId != DubaiNationalRateService.TPL_PRODUCT_ID) {

            if (model.vehicleTypeId == VehicleTypeEnum.FOURx4.value) {
                rateCommand.offRoadDesertRecovery = "yes"
            }
            if (quoteCommand.insuredValue > 500000) {
                rateCommand.replacementCar = 'yes'
                rateCommand.showReplacementCarAddon = false
                rateCommand.thirdPartyDamageLimit = 'Up to AED 3.5 million'
                rateCommand.thirdPartyLiabilityAmount = 3500000
            }

            if (!rateCommand.isAgencyRepair() && quoteCommand.carCategory in ['2WD', '4WD']) {
                rateCommand.replacementCar = "yes"
                rateCommand.showReplacementCarAddon = false
            }

        } */ else if (quoteCommand.providerId == OrientalRateService.PROVIDER_ID &&
            rateCommand.productId != OrientalRateService.TPL_PRODUCT_ID) {


//            if (model.vehicleTypeId == VehicleTypeEnum.FOURx4.value) {
//                rateCommand.offRoadDesertRecovery = "yes"
//            }
//
//            //Own Damage in Oman covered for insured value above 250K
//            if (quoteCommand.carCategory == 'BLK' || quoteCommand.insuredValue >= 250000) {
//                rateCommand.damageToYourVehicleOman = true
//                rateCommand.damageToYourVehicle = "UAE & Oman Only"
//                rateCommand.showOmanODAddon = false
//
//                rateCommand.showReplacementCarPlusRSAAddon = true
//                rateCommand.showReplacementCarAddon = false
//            } else {
//                rateCommand.breakdownCover = 'gold'
//                rateCommand.showBreakdownAddon = false
//                rateCommand.replacementCar = '180'
//                rateCommand.showReplacementCarPlusRSAAddon = false
//                rateCommand.showReplacementCarAddon = true
//            }


        } else if (quoteCommand.providerId == KwtInternationalRateService.PROVIDER_ID) {
            String openFile = null
            if (model.kwtIntCategory == KwtInternationalRateService.LUXURY_VEHICLE_LOW_TYPE) {
                if (quoteCommand.insuredValue <= 25000) {
                    openFile = messageSource.getMessage('cover.special.feature.50.open.file', null, locale)
                } else if (quoteCommand.insuredValue <= 30000) {
                    openFile = messageSource.getMessage('cover.special.feature.100.open.file', null, locale)
                } else if (quoteCommand.insuredValue <= 35000) {
                    openFile = messageSource.getMessage('cover.special.feature.150.open.file', null, locale)
                }
            } else if (model.kwtIntCategory == KwtInternationalRateService.LUXURY_VEHICLE_HIGH_TYPE) {
                if (quoteCommand.insuredValue <= 25000) {
                    openFile = messageSource.getMessage('cover.special.feature.100.open.file', null, locale)
                } else if (quoteCommand.insuredValue <= 30000) {
                    openFile = messageSource.getMessage('cover.special.feature.150.open.file', null, locale)
                } else if (quoteCommand.insuredValue <= 35000) {
                    openFile = messageSource.getMessage('cover.special.feature.200.open.file', null, locale)
                }
            }
            if (openFile) {
                rateCommand.specialFeatures = rateCommand.specialFeatures + " " + openFile
            }

        } else if (rateCommand.providerId == WathbaRateService.PROVIDER_ID) {
//            if (rateCommand.productId == WathbaRateService.PRODUCT_COMPREHENSIVE_ID) {
//                if (!rateCommand.isAgencyRepair()) {
//                    String wathbaDynaTrade = messageSource.getMessage('cover.special.feature.wathba.dynatrade', null, locale)
//                    rateCommand.specialFeatures = wathbaDynaTrade + " " + rateCommand.specialFeatures
//                }
//            }

        } else if (quoteCommand.providerId == AdntRateService.PROVIDER_ID) {
            if (rateCommand.productId != AdntRateService.PRODUCT_TPL_ID && quoteCommand.insuredValue > 150000) {
                rateCommand.showOmanTplAddon = false
                rateCommand.damageToThirdPartyOman = true
                rateCommand.thirdPartyLiability = 'UAE & Oman Only'
                String freeOmanTPL = messageSource.getMessage('cover.special.feature.oman.tpl.free', null, locale)
                rateCommand.specialFeatures = rateCommand.specialFeatures + " " + freeOmanTPL
            }

            if (LocalDate.now() <= new LocalDate("2020-09-30") &&
                quoteCommand.policyStartDate <= new LocalDate("2020-09-30")) {
                rateCommand.personalAccidentPax = "yes"
                rateCommand.paCover = "yes"
                rateCommand.showPaCoverAddon = false
                rateCommand.showPersonalAccidentPaxAddon = false
            }
        } else if (quoteCommand.providerId == AdamjeeRateService.PROVIDER_ID && quoteCommand.customerAge > 24) {
//            if (quoteCommand.vechileTypeId in [VehicleTypeEnum.COUPE.value] &&
//                rateCommand.productId == AdamjeeRateService.PRODUCT_TPL_ID ) {
//                rateCommand.breakdownCover = "none"
//                rateCommand.showBreakdownAddon = false
//                rateCommand.personalAccidentPax = "yes"
//                rateCommand.showPersonalAccidentPaxAddon = false
//                rateCommand.paCover = "yes"
//                rateCommand.showPaCoverAddon = false
//            } else if (quoteCommand.vechileTypeId in [VehicleTypeEnum.COUPE.value] &&
//                rateCommand.productId == AdamjeeRateService.PRODUCT_COMPREHENSIVE_ID && !rateCommand.agencyRepair) {
//                rateCommand.breakdownCover = "silver"
//                rateCommand.showBreakdownAddon = false
//                rateCommand.personalAccidentPax = "yes"
//                rateCommand.showPersonalAccidentPaxAddon = false
//                rateCommand.paCover = "yes"
//                rateCommand.showPaCoverAddon = false
//            } else if (quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value] &&
//                rateCommand.productId == AdamjeeRateService.PRODUCT_TPL_ID) {
//                rateCommand.personalAccidentPax = "yes"
//                rateCommand.showPersonalAccidentPaxAddon = false
//                rateCommand.paCover = "yes"
//                rateCommand.showPaCoverAddon = false
//            } else if (quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value] &&
//                rateCommand.productId == AdamjeeRateService.PRODUCT_COMPREHENSIVE_ID && !rateCommand.agencyRepair) {
//                rateCommand.breakdownCover = "silver"
//                rateCommand.showBreakdownAddon = false
//            }
        } else if (quoteCommand.providerId == TokioMarineRateService.PROVIDER_ID &&
            rateCommand.productId == TokioMarineRateService.MUST_PRODUCT_ID) {
            Integer vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId.intValue()
            if (vehicleTypeId == VehicleTypeEnum.FOURx4.value) {
                rateCommand.offRoadDesertRecovery = "yes"
            }
        } else if (quoteCommand.providerId == AiawRateService.PROVIDER_ID) {
            Integer vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId.intValue()
            if (vehicleTypeId == VehicleTypeEnum.FOURx4.value) {
                rateCommand.offRoadDesertRecovery = "yes"
            }
        } else if (quoteCommand.providerId == MethaqRatingService.PROVIDER_ID){

            if (rateCommand.productId == MethaqRatingService.METHAQ_COMPREHENSIVE
                && quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value] && model.methaqCategory != VehicleTypeEnum.SPORTS.toString()){
                rateCommand.paCover = "yes"
                rateCommand.showPaCoverAddon = false
                rateCommand.personalAccidentPax = "yes"
                rateCommand.showPersonalAccidentPaxAddon = false

            }

        }
        /*else if (quoteCommand.providerId == NoorRateService.PROVIDER_ID) {
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                rateCommand.productId in [NoorRateService.MUMTAZ_CAR_HIRE_PRODUCT_ID,
                                          NoorRateService.MUMTAZ_NON_CAR_HIRE_PRODUCT_ID]) {
                rateCommand.offRoadDesertRecovery = "yes"
                rateCommand.roadsideAssistances.add(
                    RoadsideAssistanceEnum.getById(7).getName(quoteCommand.locale)
                )

                if (rateCommand.specialFeatures != null) {
                    log.info("These are special feature: ${rateCommand.specialFeatures}")
                    String offRoadMessage = messageSource
                        .getMessage(OFFROAD_RECOVERY_MESSAGE_FOR_NOOR, null, new Locale('en'))

                    rateCommand.specialFeatures = rateCommand.specialFeatures
                        .concat(offRoadMessage)
                }
            }
            if (rateCommand.productId == NoorRateService.MUMTAZ_NON_CAR_HIRE_PRODUCT_ID) {
                if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                    rateCommand.replacementCar = 'yes'
                    if (!rateCommand.agencyRepair) {
                        rateCommand.replacementCar = 'no'
                        rateCommand.carHireCashBenefit = 'no'
                    }
                } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value && rateCommand.agencyRepair) {
                    rateCommand.replacementCar = 'yes'
                    rateCommand.showReplacementCarAddon = false
                }
            }
            if (rateCommand.productId == NoorRateService.ASAS_PRODUCT_ID) {
                if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                    rateCommand.breakdownCover = "silver"
                    rateCommand.personalAccidentPax = "yes"
                    rateCommand.showPersonalAccidentPaxAddon = false
                    rateCommand.paCover = "yes"
                    rateCommand.showPaCoverAddon = false
                    rateCommand.replacementCar = '100'
                    rateCommand.showReplacementCarAddon = true
                    rateCommand.showReplacementCarPlusRSAAddon = false
                    rateCommand.carRegService = 'no'
                    rateCommand.roadsideAssistances.remove(
                        RoadsideAssistanceEnum.getById(RoadsideAssistanceEnum.CAR_REGISTRATION_SERVICE_ONCE_A_YEAR.id).getName(quoteCommand.locale)

                    )
                }
            }
            if (rateCommand.productId == NoorRateService.TPL_PRODUCT_ID) {
                if (quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value]) {
                    rateCommand.carRegService = 'no'
                    rateCommand.roadsideAssistances.remove(
                        RoadsideAssistanceEnum.getById(RoadsideAssistanceEnum.CAR_REGISTRATION_SERVICE_ONCE_A_YEAR.id).getName(quoteCommand.locale)
                    )
                }
            }
        }*/

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (quoteCommand.countryEnum == CountryEnum.UAE && pricesInclusiveVat) {
            rateCommand = applyVATOnCover(rateCommand)
        }

        rateCommand
    }

    /**
     * Apply additional covers applicable to renewal
     * @param quoteCommand
     * @param rateCommand
     * @param renewal
     * @return
     */
    RateCommand applyRenewalCover(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (!rateCommand.isRenewal) {
            return rateCommand
        }

        if (rateCommand.providerId == UnionRateService.PROVIDER_ID) {
            return applyUnionRenewalCover(quoteCommand, rateCommand)
        } else if (rateCommand.providerId == DubaiNationalRateService.PROVIDER_ID) {
            return applyDNIRCRenewalCover(quoteCommand, rateCommand)
        } else if (rateCommand.providerId == SalamaRateService.PROVIDER_ID) {
            return applySalamaRenewalCover(quoteCommand, rateCommand)
        }

        return rateCommand
    }

    private RateCommand applyUnionRenewalCover(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (rateCommand.coverageTypeId == CoverageTypeEnum.COMPREHENSIVE.value()) {
            String fiftyPercentExcess = messageSource
                .getMessage(FIFTY_PERCENT_EXCESS_COVER, null, new Locale(quoteCommand.locale))
            rateCommand.specialFeatures = rateCommand.specialFeatures
                .concat("; ")
                .concat(fiftyPercentExcess)
        }

        return rateCommand
    }

    private RateCommand applyDNIRCRenewalCover(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (rateCommand.coverageTypeId == CoverageTypeEnum.COMPREHENSIVE.value()) {
            rateCommand.breakdownCover = "silver"
            rateCommand.showBreakdownAddon = false
            rateCommand.paCover = "yes"
            rateCommand.showPaCoverAddon = false
        }

        return rateCommand
    }

    private RateCommand applySalamaRenewalCover(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (rateCommand.coverageTypeId == CoverageTypeEnum.COMPREHENSIVE.value()) {

            if (rateCommand.insuredValue < 40000 ) {
                rateCommand.showBreakdownAddon = false
                rateCommand.breakdownCover = 'gold'
            }
            rateCommand.paCover = "yes"
            rateCommand.showPaCoverAddon = false
            rateCommand.personalAccidentPax = "yes"
            rateCommand.showPersonalAccidentPaxAddon = false

        }

        return rateCommand
    }

    RateCommand applyVATOnCover(RateCommand rateCommand) {
        if (rateCommand.breakdownCover && rateCommand.breakdownCover.isNumber()) {
            rateCommand.breakdownCover =
                commonUtilService.addVAT(new BigDecimal(rateCommand.breakdownCover)).toInteger()
        }

        if (rateCommand.paCover && rateCommand.paCover.isNumber()) {
            rateCommand.paCover = commonUtilService.addVAT(new BigDecimal(rateCommand.paCover)).toInteger()
        }

        if (rateCommand.replacementCar && rateCommand.replacementCar.isNumber()) {
            rateCommand.replacementCar =
                commonUtilService.addVAT(new BigDecimal(rateCommand.replacementCar)).toInteger()
        }

        if (rateCommand.personalAccidentPax && rateCommand.personalAccidentPax.isNumber()) {
            rateCommand.personalAccidentPax =
                commonUtilService.addVAT(new BigDecimal(rateCommand.personalAccidentPax)).toInteger()
        }

        rateCommand
    }

    RateCommand applyExcess(QuoteCommand quoteCommand, RateCommand rateCommand, Boolean useProductId = null) {

        Model model = Model.read(quoteCommand.modelId)

        String carCategory = null
        // only for oman & oriental excess is based on category
        if (SukoonRateService.PROVIDER_ID == quoteCommand.providerId) {
            carCategory = quoteCommand.carCategory
        } else if (OrientalRateService.PROVIDER_ID == quoteCommand.providerId && model.orientalCategory == 'BLK') {
            carCategory = quoteCommand.carCategory
        } else if (EgyIskanRateService.PROVIDER_ID == quoteCommand.providerId) {
            carCategory = quoteCommand.carCategory
        }

        Integer productId = useProductId ? rateCommand.productId : null

        List<ProductExcess> excessList = ProductExcess.createCriteria().list {
            eq 'provider.id', quoteCommand.providerId
            if (productId) {
                eq 'product.id', productId
            } else {
                isNull 'product'
            }
            or {
                isNull 'vehicleType'
                eq 'vehicleType.id', quoteCommand.vechileTypeId
            }
            or {
                isNull 'repairType'
                eq 'repairType', rateCommand.isAgencyRepair() ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE
            }
            le 'valueFrom', quoteCommand.insuredValue
            ge 'valueTo', quoteCommand.insuredValue
            if (carCategory) {
                eq 'carCategory', carCategory
            }
        }

        if (excessList) {
            rateCommand.excess = excessList.first().excess
        }

        rateCommand
    }


    RateCommand applyC4meFees(RateCommand rateCommand, LocalDate policyStartDate, Integer vehicleTypeId) {

        /*List vehicleTypes = [
                VehicleTypeEnum.SEDAN.value,
                VehicleTypeEnum.CONVERTIBLE.value,
                VehicleTypeEnum.COUPE.value
        ]
        BigDecimal premium = rateCommand.actualBasePremium ?: rateCommand.premium
        if ( rateCommand.coverageTypeId == CoverageTypeEnum.THIRD_PARTY.value() ||
            ( !(vehicleTypeId in vehicleTypes) && vehicleTypeId != VehicleTypeEnum.FOURx4.value ) ||
            ( vehicleTypeId in vehicleTypes && premium > 1300 ) ||
            ( vehicleTypeId == VehicleTypeEnum.FOURx4.value && premium > 2000 )) {
            def c4meFee = commonUtilService.getC4meFees(rateCommand.premium, policyStartDate)
            rateCommand.c4meFee = c4meFee
            rateCommand.originalC4meFee = c4meFee
            rateCommand.premium = rateCommand.premium.add(c4meFee)
        } else {
            rateCommand.c4meFee = 0
            rateCommand.originalC4meFee = 0
        }*/

        //Apply Additional fee if there is any global discount on rate
        Provider provider = Provider.get(rateCommand.providerId)
        Product product = Product.read(rateCommand.productId)

        CountryEnum countryEnum = CountryEnum.findCountryById(provider.countryId as Integer)


//        old usage of getDiscount
//        def (discount, discountCodeObj) = getDiscount(rateCommand.premium, rateCommand.coverageTypeId == CoverageTypeEnum.THIRD_PARTY.value(),
//            rateCommand.providerId, rateCommand.leadType, rateCommand.agencyRepair, countryEnum, rateCommand.productId)

        // if decide to add additional fee on discounts
//        boolean applyAdditionalFee = false
//        CarQuote quote = CarQuote.read(rateCommand.carQuoteId)
//        QuoteCommand quoteCommand = quoteService.toQuoteCommand(quote)
//        def (discount, discountCodeObj) = getDiscount(rateCommand,  quoteCommand,  quote.user)
//
//        if (discountCodeObj) {
//            applyAdditionalFee = true
//        }

        //def c4meFee = commonUtilService.getC4meFees(rateCommand.premium, policyStartDate, applyAdditionalFee)
        BigDecimal c4meFee = commonUtilService.getC4meFees(countryEnum, provider, product, rateCommand.leadType, rateCommand.premium)

        rateCommand.c4meFee = c4meFee
        rateCommand.originalC4meFee = c4meFee

        rateCommand.premium = rateCommand.premium.add(c4meFee)

        rateCommand
    }

    RateCommand applyExtraDiscount(QuoteCommand quoteCommand, RateCommand rateCommand, Long providerId = null) {

        User user = User.findByEmail(quoteCommand.email)
        if (user == null) {
            log.warn("User is not found hence extra discount cannot be applied")
            return
        }

        def (discount, discountCodeObj) = getDiscount(rateCommand.premium, rateCommand, quoteCommand, user)
        rateCommand = applyDiscount(rateCommand, discount, discountCodeObj)
        if (rateCommand.discountAmount) {
            //Comment below code if we don't want to show discount on SRP per product
            rateCommand.productDiscountAmount = rateCommand.discountAmount
            rateCommand.productDiscountPercent = rateCommand.discountPercent
            rateCommand.productHasPercentDiscount = discountCodeObj?.hasPercentDiscount
        }

        return rateCommand
    }

    RateCommand applyDiscount(RateCommand rateCommand, BigDecimal discount, DiscountCode discountCodeObj) {

        if (discountCodeObj) {
            log.info("applying discount ... ${rateCommand.productId}, - ${discountCodeObj}")
            rateCommand.discountPercent = discountCodeObj.discount
            rateCommand.discountAmount = discount

            if (rateCommand.originalC4meFee) {
                BigDecimal newC4meFee = discount > rateCommand.originalC4meFee ? 0 : (rateCommand.originalC4meFee - discount)
                discount = discount > rateCommand.originalC4meFee ? discount - rateCommand.originalC4meFee : 0
                rateCommand.c4meFee = newC4meFee
            }

            rateCommand.premium = rateCommand.originalPremium.add(rateCommand.c4meFee ?: 0).subtract(discount)
        }

        return rateCommand
    }

    def getDiscount(double amount, RateCommand rateCommand, QuoteCommand quoteCommand, User user = null) {

//    def getDiscount (double amount, Boolean isTpl, Long providerId = null,
//                     LeadType leadType, boolean agencyRepair = false, CountryEnum countryEnum, Long productId = null, String standAloneDiscountCode=null, String standAloneDiscountGroup=null) {

        // setting the params here
        Boolean isTpl = rateCommand.coverageTypeId == CoverageTypeEnum.THIRD_PARTY.value()
        Long providerId = rateCommand.providerId
        LeadType leadType = user.leadType
        CountryEnum countryEnum = quoteCommand.countryEnum
        Long productId = rateCommand.productId
        String standAloneDiscountCode = quoteCommand.standAloneDiscountCode ? quoteCommand.standAloneDiscountCode : null
        String standAloneDiscountGroup = quoteCommand.standAloneDiscountGroup ? quoteCommand.standAloneDiscountGroup : null
        WhiteLabelBrand whiteLabelBrand = WhiteLabelBrand.read(WhiteLabelBrandEnum.getWhiteLabelBrandEnumFromRequestSource(quoteCommand.requestSource).id)

        BigDecimal discount
        DiscountCode discountCodeObj

        RepairTypeEnum repairType = rateCommand.agencyRepair ? RepairTypeEnum.AGENCY : isTpl ? null : rateCommand.hasPremiumGarage ? RepairTypeEnum.PREMIUM_GARAGE : RepairTypeEnum.GARAGE

        Boolean marketingDiscountEnabled = Boolean.valueOf(configurationService.getValue(ConfigurationService["ENABLE_MARKETING_${user.leadType.name()}_LEADS_ASSIGNMENT"]))
        User marketingSalesPerson = User.findByEmail(configurationService.getValue(ConfigurationService["MARKETING_${user.leadType.name()}_LEADS_AGENT_EMAIL"]))

        if (marketingDiscountEnabled && user.salesPerson == marketingSalesPerson) {
            List discountCodes = DiscountCodeRules.createCriteria().list {
                projections {
                    property("discountCode")
                }

                eq("salesPerson", marketingSalesPerson)
                eq("active", true)
                lte("startDate", LocalDateTime.now())
                or {
                    isNull("endDate")
                    gte("endDate", LocalDateTime.now())
                }
                eq("productType", ProductType.load(ProductTypeEnum.CAR.value()))
                discountCode {
                    if (whiteLabelBrand.id.toInteger() == WhiteLabelBrandEnum.YALLACOMPARE.id) {
                        isNull("brand")
                    } else {
                        eq("brand", whiteLabelBrand)
                    }
                }

                maxResults(1)
            }
            if (discountCodes) {
                discountCodeObj = discountCodes[0]
            }
        }

        if (!discountCodeObj) {
            // get global discount code
            discountCodeObj = discountService.getApplicableDiscountCode(
                countryEnum,
                ProductTypeEnum.CAR.value(), // as its car insurance
                leadType,
                providerId as Integer,
                productId as Integer,
                rateCommand.originalPremium,
                whiteLabelBrand,
                user.phoneLead.salesPerson,
                CoverageType.read(rateCommand.coverageTypeId),
                Country.read(quoteCommand.nationalityId),
                repairType,
                quoteCommand.makeId,
                quoteCommand.modelId,
                quoteCommand.insuredValue.toInteger(),
                standAloneDiscountCode,
                standAloneDiscountGroup
            )
        }


        //Is global discount enabled
//        if (Boolean.parseBoolean(grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.enabled")) && !isTpl) {
//
//            Long discountCodeId = null
//            Long providerDiscountId = null
//            //Any provider discount to checkout
//            if (providerId) {
//                String discountId = grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.${providerId}.id")
//                providerDiscountId = discountId ? Long.parseLong(discountId) : null
//
//                //If not Salama then use the provider discount, if Salama, then has to be non agency before 31 July 18
//                if (providerId != SalamaRateService.PROVIDER_ID ||
//                    (providerId == SalamaRateService.PROVIDER_ID && !agencyRepair)) {
//                    discountCodeId = providerDiscountId
//                }
//            }
//
//            //If no discount from provider, check the global discount
//            if (!discountCodeId) {
//                discountCodeId = Long.parseLong(grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.id"))
//            }
//
//            //If Lost lead, override the discount if available
//            if ((leadType == LeadType.LOST || leadType == LeadType.ACTIVE_LOST) &&
//                Boolean.parseBoolean(grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.lostLead.enabled"))) {
//
//                String lostLeadDiscountId =
//                    grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.lostLead.${providerId}.id")
//
//                Long lostLeadDiscountCodeId = null
//                if (lostLeadDiscountId) {
//                    lostLeadDiscountCodeId = lostLeadDiscountId ? Long.parseLong(lostLeadDiscountId) : null
//                }
//
//                //Using higher discount for Salama Agency repair
//                if (providerId == SalamaRateService.PROVIDER_ID && agencyRepair) {
//                    discountCodeId = providerDiscountId
//                } else {
//                    discountCodeId = lostLeadDiscountCodeId
//                }
//
//                //If no discount from provider, check the global discount
//                if (!discountCodeId) {
//                    discountCodeId = Long.parseLong(grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.lostLead.id"))
//                }
//            } else if (leadType == LeadType.RENEWAL &&
//                Boolean.parseBoolean(grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.renewalLead.enabled"))) {
//
//                String renewalLeadDiscountId =
//                    grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.renewalLead.${providerId}.id")
//
//                Long renewalLeadDiscountCodeId = null
//                if (renewalLeadDiscountId) {
//                    renewalLeadDiscountCodeId = renewalLeadDiscountId ? Long.parseLong(renewalLeadDiscountId) : null
//                }
//
//                //Using higher discount for Salama Agency repair
//                if (providerId == SalamaRateService.PROVIDER_ID && agencyRepair) {
//                    discountCodeId = providerDiscountId
//                } else {
//                    discountCodeId = renewalLeadDiscountCodeId
//                }
//
//                //If no discount from provider, check the global discount
//                if (!discountCodeId) {
//                    discountCodeId = Long.parseLong(grailsApplication.config.getProperty("cover.premium.discount.${countryEnum.code}.renewalLead.id"))
//                }
//            }
//
//            //Use the discount code if available
//            discountCodeObj = discountCodeId ? DiscountCode.get(discountCodeId) : null
//            //log.debug("Returning discount ${discount} for amount ${amount}")
//        }

        /*if (rateCommand.productId == AdamjeeRateService.PRODUCT_COMPREHENSIVE_ID) {
            Integer vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId.intValue()
            //If below criteria didnt match, then no discount
            if (!(quoteCommand.carAge <= 11                        //2009 and onwards
                && quoteCommand.insuredValue <= 70000               // Up to 70K
                && VehicleTypeEnum.FOURx4.value == vehicleTypeId    // 4x4 Only
                && !isTpl && repairType == RepairTypeEnum.GARAGE    // Only for Non Agency non TPL policies
                && quoteCommand.customerAge > 22                    // Above 22 age
                && quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.ONE_TO_TWO.getId()
                && quoteCommand.policyStartDate <= new LocalDate("2019-06-30"))) {
                discountCodeObj = null
            }
        }*/

        if (discountCodeObj) {
            discount = discountCodeObj.discount
            rateCommand.discountCode = discountCodeObj.code

            if (discountCodeObj.hasPercentDiscount) {
                discount = (discountCodeObj.discount * amount) / 100.toBigDecimal()
            }

            discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP)
            Product product = Product.read(productId)
            if (rateCommand.originalC4meFee > 0 && discount > rateCommand.originalC4meFee) {
                discount = rateCommand.originalC4meFee
                rateCommand.discountLimitedToC4meFees = true
            }

            BigDecimal discountAfterTPLValidation = discountService.applyDiscountForTPL(discountCodeObj, product, rateCommand.originalPremium, rateCommand.originalC4meFee, discount)
            if (discountAfterTPLValidation != discount) {
                discount = discountAfterTPLValidation
                rateCommand.bypassedDiscountForTPLMaxLimit = true
            }
        }

        [discount, discountCodeObj]
    }

    /**
     * Get Number of Years applicable for No Claim Discount based on Driving License and No Claim certificates
     * @param quoteCommand
     * @return
     */
    Integer getApplicableDiscountYearsByDrivingLicenseAndNcd(QuoteCommand quoteCommand) {

        DrivingExperienceEnum drivingExperience = DrivingExperienceEnum.findById(quoteCommand.localDrivingExperienceId)

        Integer applicableDiscountsYears =
            quoteCommand.noClaimsDiscountId > drivingExperience.experienceInYears ?
                drivingExperience.experienceInYears : quoteCommand.noClaimsDiscountId

        return applicableDiscountsYears
    }

    /**
     * Get Number of Years applicable for No Claim Discount based on Driving License and Claim Period
     * @param quoteCommand
     * @return
     */
    Integer getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(QuoteCommand quoteCommand) {

        DrivingExperienceEnum drivingExperience = DrivingExperienceEnum.findById(quoteCommand.localDrivingExperienceId)
        Integer noClaimYears = (quoteCommand.lastClaimPeriod?.noClaimYears()) ?: 0

        Integer applicableDiscountsYears =
            noClaimYears > drivingExperience.experienceInYears ?
                drivingExperience.experienceInYears : noClaimYears

        return applicableDiscountsYears
    }

    boolean isEligibleForSelfDecDiscount(QuoteCommand quoteCommand) {
        return quoteCommand.requestSource in [RequestSourceEnum.WEB, RequestSourceEnum.NATIONALBONDS,
                                              RequestSourceEnum.ETISALAT_SMILES, RequestSourceEnum.ADIB] &&
            !quoteCommand.hasClaim && !quoteCommand.noClaimsDiscountId && !quoteCommand.isFirstCar &&
            quoteCommand.lastClaimPeriod != null
    }

    boolean generalEligibilityCheck(QuoteCommand quoteCommand,
                                    CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE,
                                    Integer productId = null) {
        boolean showForCar = Provider.load(quoteCommand.providerId).showForCar
        log.info("${UtilService.getQuoteLoggingPrefix('generalEligibilityCheck', quoteCommand.quoteId, InsuranceProviderEnum.getById(quoteCommand.providerId))} - showForCar: ${showForCar}")

        if (!showForCar) {
            return false
        }

        boolean isEligible = commonUtilService.generalEligibilityCheck(quoteCommand.providerId, quoteCommand.makeId,
            quoteCommand.modelId, quoteCommand.isNonGccSpec, productTypeEnum, quoteCommand.countryEnum,
            productId)

        isEligible
    }

    boolean agencyBlacklisted(QuoteCommand quoteCommand) {

        boolean isBlacklisted = true

        // check if make or model is blacklisted
        def c = AgencyBlacklisted.createCriteria()
        Long blacklistedCount = c.get {
            projections {
                count('id')
            }
            and {
                eq 'provider.id', quoteCommand.providerId
                eq 'model.id', quoteCommand.modelId
            }
        }

        if (blacklistedCount == 0) {
            isBlacklisted = false
        }

        isBlacklisted
    }

    private getRatingService(Integer providerId, SubRequestSourceEnum requestSubSource = null,
                             Integer coverageTypeId = null) {
        log.info("getRatingService - providerId:${providerId}, requestSubSource:$requestSubSource")

        def service = null
        if (GatewayRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.gatewayRateService
        } else if (UnionRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.unionRateService
        } else if (InsuranceProviderEnum.NOOR_TAKAFUL.id == providerId) {

            service = grailsApplication.mainContext.noorRateService
        } else if (SukoonRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.sukoonRateService
        } else if (QatarRateV2Service.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.qatarRateV2Service
        } else if (DubaiRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.dubaiRateService
        } else if (AlSagrRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.alSagrRateService
        } else if (SalamaRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.salamaRateService
        } else if (WathbaRateV2Service.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.wathbaRateV2Service
        } else if (AdamjeeRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.adamjeeRateService
        } else if (LbnCommercialRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.lbnCommercialRateService
        } else if (LbnAssurexRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.lbnAssurexRateService
        } else if (LbnCapitalRateService.PROVIDER_ID == providerId) {

            service = grailsApplication.mainContext.lbnCapitalRateService
        } else if (LbnFidelityRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.lbnFidelityRateService

        } else if (KwtUnitedRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.kwtUnitedRateService

        } else if (KwtArabIslamicRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.kwtArabIslamicRateService

        } else if (KwtTazurRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.kwtTazurRateService

        } else if (KwtGulfRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.kwtGulfRateService

        } else if (KwtInternationalRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.kwtInternationalRateService

        } else if (KwtQatarRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.kwtQatarRateService

        } else if (OrientalRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.orientalRateService

        } else if (DarRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.darRateService

        } else if (AmanRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.amanRateService

        } else if (AdntRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.adntRateService

        } else if (AlBuhairaRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.alBuhairaRateService
        } else if (AlFujairahNICRatingService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.alFujairahNICRatingService
        } else if (AlHilalTakafulRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.alHilalTakafulRateService
        } else if (MethaqRatingService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.methaqRatingService
        } else if (InsuranceHouseRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.insuranceHouseRateService

        } else if (EgyGigRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.egyGigRateService

        } else if (EgyIskanRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.egyIskanRateService

        } else if (EgyAropeRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.egyAropeRateService

        } else if (EgyOrientRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.egyOrientRateService

        } else if (YasTakafulRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.yasTakafulRateService

        } else if (AiawRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.aiawRateService

        } else if (EgyMisrRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.egyMisrRateService

        } else if (DubaiNationalRateV2Service.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.dubaiNationalRateV2Service
        } else if (TokioMarineRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.tokioMarineRateV2Service

        } else if (NewIndiaRateV2Service.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.newIndiaRateV2Service

        } else if (EgyTokyoMarineRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.egyTokyoMarineRateService

        } else if (EgyMohandesRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.egyMohandesRateService
        } else if (AdnicRateService.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.adnicRateService
        } else if (InsuranceProviderEnum.RAK_INSURANCE.id == providerId) {
            service = grailsApplication.mainContext.rakRateV2Service
        } else if (GigRateService.PROVIDER_ID == providerId && requestSubSource == SubRequestSourceEnum.COMIN) {
            service = grailsApplication.mainContext.gigRateService
        } else if (GigRateV2Service.PROVIDER_ID == providerId) {
            service = grailsApplication.mainContext.gigRateV2Service
        } else if (AscanaRateService.PROVIDER_ID == providerId){
            service = grailsApplication.mainContext.ascanaRateService
        }

        service
    }

    BigDecimal calculate(BigDecimal rate, BigDecimal insuredValue) {
        BigDecimal oneHundred = new BigDecimal(100)
        BigDecimal premium = rate * (insuredValue) / oneHundred
        return premium
    }

    BigDecimal percentage(BigDecimal base, BigDecimal pct) {

        BigDecimal oneHundred = new BigDecimal(100)
        base * (pct) / (oneHundred)
    }

    RateCommand applyVAT(RateCommand rateCommand) {
        BigDecimal premiumVAT = commonUtilService.getVATAmount(rateCommand.originalPremium)
        rateCommand.c4meFeeVAT = commonUtilService.getVATAmount(rateCommand.c4meFee)
        log.info("rateCommand.c4meFee:${rateCommand.c4meFee}, rateCommand.c4meFeeVAT:${rateCommand.c4meFeeVAT}")

        rateCommand.premiumVAT = premiumVAT.add(rateCommand.c4meFeeVAT)
        log.info("rateCommand.premiumVAT:${rateCommand.premiumVAT}, rateCommand.c4meFeeVAT:${rateCommand.c4meFeeVAT}")
        rateCommand.originalC4meFeeVAT = commonUtilService.getVATAmount(rateCommand.originalC4meFee)

        rateCommand.premium = rateCommand.premium.plus(rateCommand.premiumVAT)

        rateCommand
    }

    /**
     *
     * @param rateCommand
     * @return
     */
    RateCommand storeOriginalPremiumBeforeC4meFee(RateCommand rateCommand) {
        rateCommand.originalPremium = rateCommand.premium
        rateCommand
    }

    RateCommand checkMaximumPremium(RateCommand rateCommand, QuoteCommand quoteCommand) {

        BigDecimal maxPremium =
            quoteCommand.vechileTypeId in
                [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.MPV.value] ?
                calculate(7, quoteCommand.insuredValue) : calculate(5, quoteCommand.insuredValue)

        if (maxPremium > rateCommand.minPremium && rateCommand.premium > maxPremium) {
            rateCommand.premium = maxPremium
            rateCommand.basePremium = maxPremium
        }

        rateCommand
    }

    /**
     * Get maximum premium set by Insurance Authority IA in UAE
     * @param quoteCommand
     * @return
     */
    BigDecimal getMaxPremium(QuoteCommand quoteCommand) {

        BigDecimal maxPremium =
            quoteCommand.vechileTypeId in
                [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.MPV.value] ?
                calculate(7, quoteCommand.insuredValue) : calculate(5, quoteCommand.insuredValue)

        if (quoteCommand.vechileTypeId in
            [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.MPV.value]) {
            maxPremium = maxPremium >= 2000 ? maxPremium : 2000
        } else {
            maxPremium = maxPremium >= 1300 ? maxPremium : 1300
        }

        return maxPremium
    }

    RateCommand checkMinimumPremium(RateCommand rate) {
        rate.actualBasePremium = rate.premium
        // after discount if premium is less then minimum premium then use minimum premium
        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium

            rate.productDiscountAmount = null
            rate.productDiscountPercent = null
        }

        rate
    }

    QuoteCommand cloneQuoteCommand(QuoteCommand quoteCommand) {
        QuoteCommand tempQuoteCommand = new QuoteCommand()
        InvokerHelper.setProperties(tempQuoteCommand, quoteCommand.properties)
        return tempQuoteCommand
    }

    List<ProductBaseRate> getProductBaseRate(QuoteCommand quoteCommand, Integer noClaimYears, boolean isOffline) {
        List<ProductBaseRate> applicableRates = getBaseRateMinPremium(quoteCommand, isOffline, noClaimYears)

        log.info("Total records fetched: " + applicableRates.size())

        for (ProductBaseRate baseRate : applicableRates) {
            log.info(baseRate.baseRateAgency.toString() + ":" + baseRate.baseRateGarage.toString() + ":" + baseRate.noClaimYears)
        }

        return applicableRates
    }

    Integer getNoClaimYears(QuoteCommand quoteCommand) {
        Integer noClaimYearsByNCD = getApplicableDiscountYearsByDrivingLicenseAndNcd(quoteCommand)

        Integer noClaimYearsBySelfDec = 0
        if (isEligibleForSelfDecDiscount(quoteCommand) && !quoteCommand.isPolicyExpired) {
            noClaimYearsBySelfDec = getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
        }

        return noClaimYearsByNCD ? noClaimYearsByNCD : noClaimYearsBySelfDec
    }

    Boolean allowAgency(){

        return Boolean.parseBoolean(grailsApplication.config.getProperty("provider.allow.agency"))
    }
}
