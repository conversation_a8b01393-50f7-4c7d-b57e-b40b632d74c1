package com.cover.car

import com.cover.car.adamjee.AdamjeeQuoteCommand
import com.cover.car.adamjee.AdamjeeRateCommand
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.common.CommonQuoteService
import com.safeguard.AddonCodeEnum
import com.safeguard.CarMakeEnum
import com.safeguard.City
import com.safeguard.ClaimPeriodEnum
import com.safeguard.Country
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.ModelMasterEnum
import com.safeguard.NoClaimDiscount
import com.safeguard.Provider
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.car.vehicle.CityExternal
import com.safeguard.car.vehicle.CountryExternal
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.MakeExternal
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import com.safeguard.car.vehicle.ModelMasterExternal
import com.safeguard.car.vehicle.VehicleType
import com.safeguard.car.vehicle.VehicleTypeExternal
import grails.converters.JSON
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime


@Transactional
class AdamjeeRateV2Service extends BaseRatingService{

    def ratingService
    def adamjeeApiService
    def adamjeeRateService

    public static final Integer PROVIDER_ID = 12

    public static final String VEHICLE_USE_CODE ="01" //FOR PRIVATE VEHICLES ONLY

    public static final Integer PRODUCT_COMPREHENSIVE_ID = 65
    public static final Integer PRODUCT_DYNA_TRADE_ID = 1011
    public static final Integer PRODUCT_TPL_ID = 66

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("AdamjeeRateV2Service.getRates - QuoteCommand ${quoteCommand}")

//        if (quoteCommand.isNonGccSpec){
//            return adamjeeRateService.getRates(quoteCommand, isOffline)
//        }

        try {
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return []
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE)
            if (!checkEligibility) {
                log.info("Adamjee is not eligible")
                return []
            }

            List<ProviderRateCommand> adamjeeRateCommands = []
            AdamjeeQuoteCommand adamjeeQuoteCommand = AdamjeeQuoteCommand.generateQuoteCommand(quoteCommand)

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(
                    false, quote, Provider.load(PROVIDER_ID) , LocalDateTime.now(), CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value()))
            adamjeeQuoteCommand = toAdamjeeQuoteCommand(quoteCommand, quote)

            if (!adamjeeQuoteCommand){
                log.info("Unable to create quoteCommand")
                return []
            }


            if (carQuoteCovers.size() > 0){
                log.info("Cover found in db")
                carQuoteCovers.each {
                    adamjeeRateCommands.add(adamjeeApiService.coverDetailsToRateCommand(JSON.parse(it.covers), it.providerQuoteNo))
                }
            }else {
                log.info("Not Found in DB, Calling API")
                log.info("Adamjee Quote Command ${adamjeeQuoteCommand}")
                if (adamjeeQuoteCommand){
                    def startTime = System.currentTimeMillis()

                    adamjeeRateCommands = adamjeeApiService.getBaseRateMinPremium(adamjeeQuoteCommand, CoverageTypeEnum.COMPREHENSIVE)

                    def endTime = System.currentTimeMillis()
                    def elapsedTime = (endTime - startTime)/1000.0
                    log.info("Adamjee API call took ${elapsedTime} seconds against quoteId: ${quoteCommand.quoteId}")
                }
            }
            log.info("adamjeeRateV2.getRates - adamjeeRateCommands:$adamjeeRateCommands")

            List<RateCommand> rateCommands = []
            adamjeeRateCommands.each {
                if (it.productCode != adamjeeApiService.TPL_PRODUCT_CODE){
                    RateCommand rateCommand = toRateCommand(adamjeeQuoteCommand, it)
                    if (rateCommand) {
                        log.info("Rate Command ${rateCommand}")
                        RateCommand command = populateRatings(ratingService, adamjeeQuoteCommand, rateCommand)
                        rateCommands.add(command)
                        log.info("After Populating Rate ${command}")
                    } else {
                        log.error("Null rateCommand for $it ")
                    }
                }

            }

            return rateCommands

        }catch (Exception e){
            log.error("adamjeeRateV2.getRates - Exception ${e.getMessage()}", e)
            return []
        }

    }


    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("AdamjeeRateV2Service.gateRate - QuoteCommand ${quoteCommand}")

//        if (quoteCommand.isNonGccSpec){
//            return adamjeeRateService.getRate(quoteCommand, isOffline)
//        }


        try{
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE)
            if (!checkEligibility) {
                return null
            }

            AdamjeeQuoteCommand adamjeeQuoteCommand = AdamjeeQuoteCommand.generateQuoteCommand(quoteCommand)

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(
                    false, quote, Provider.load(PROVIDER_ID) , LocalDateTime.now(), CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value()))

            AdamjeeRateCommand adamjeeRateCommand  = null
            adamjeeQuoteCommand = toAdamjeeQuoteCommand(quoteCommand, quote)

            if (carQuoteCovers.size() > 0) {
                CarQuoteCover selectedCover = getCoverByProduct(carQuoteCovers, quoteCommand.productId)
                log.info("Selected Cover is ${selectedCover}")
                adamjeeRateCommand = adamjeeApiService.coverDetailsToRateCommand(JSON.parse(selectedCover.covers), selectedCover.providerQuoteNo)
            }else {
                if (quoteCommand.productId == PRODUCT_DYNA_TRADE_ID){
                    adamjeeRateCommand = adamjeeApiService.callCreateQuoteAPI(adamjeeQuoteCommand, CoverageTypeEnum.COMPREHENSIVE, true)
                }else if (quoteCommand.productId == PRODUCT_COMPREHENSIVE_ID){
                    adamjeeRateCommand = adamjeeApiService.callCreateQuoteAPI(adamjeeQuoteCommand, CoverageTypeEnum.COMPREHENSIVE, false)
                }
            }

            RateCommand rateCommand = null

            rateCommand = toRateCommand(adamjeeQuoteCommand, adamjeeRateCommand)

            rateCommand = populateRatings(ratingService, adamjeeQuoteCommand, rateCommand)

            return rateCommand
        }catch (Exception e){
            log.error("AdamjeeRateV2Service.gateRate - Exception ${e.getMessage()}", e)
            return null
        }

    }


    def getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("Adamjee TPL Rate ${quoteCommand}")

//        if (quoteCommand.isNonGccSpec){
//            return adamjeeRateService.getTplRate(quoteCommand, isOffline)
//        }


        try {
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

            if (!checkEligibility) {
                return null
            }

            AdamjeeQuoteCommand adamjeeQuoteCommand = AdamjeeQuoteCommand.generateQuoteCommand(quoteCommand)
            List<ProviderRateCommand> adamjeeRateCommands = []

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            CarQuoteCover carQuoteCover = CarQuoteCover.
                findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                    Provider.load(PROVIDER_ID), LocalDateTime.now(), CoverageType.load(CoverageTypeEnum.THIRD_PARTY.value()))
            adamjeeQuoteCommand = toAdamjeeQuoteCommand(quoteCommand, quote)

            if (!adamjeeQuoteCommand){
                return null
            }
            if (carQuoteCover){
                log.info("Cover found in db")
               adamjeeRateCommands.add(adamjeeApiService.coverDetailsToRateCommand(JSON.parse(carQuoteCover.covers), carQuoteCover.providerQuoteNo))
            }else {
                log.info("Adamjee Quote Command ${adamjeeQuoteCommand}")
                adamjeeRateCommands = adamjeeApiService.getBaseRateMinPremium(adamjeeQuoteCommand, CoverageTypeEnum.THIRD_PARTY)
            }

            log.info("adamjeeRateV2.getTplRate - adamjeeRateCommands:$adamjeeRateCommands")
            RateCommand rateCommand = null
            adamjeeRateCommands.each {
                if (it.productCode == adamjeeApiService.TPL_PRODUCT_CODE){
                    rateCommand = toRateCommand(adamjeeQuoteCommand, it)
                    if (rateCommand) {
                        rateCommand = populateRatings(ratingService, adamjeeQuoteCommand, rateCommand)
                    } else {
                        log.error("Null rateCommand for $it ")
                    }
                }

            }
            return rateCommand

        }catch (Exception e){
            log.error("adamjeeRateV2.getTplRate - Exception ${e.getMessage()}", e)
            return null
        }

    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (quoteCommand.makeId in [CarMakeEnum.FERRARI.id, CarMakeEnum.MERCURY.id]){
            isEligible = false
        }

//        if (quoteCommand.hasClaim && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
//            return false
//        }

        return isEligible
    }


    private AdamjeeQuoteCommand toAdamjeeQuoteCommand(QuoteCommand quoteCommand, CarQuote quote){
        log.info("Change to Adamjee Quote Command")
        ExternalDataSource adamjeeDataSource = ExternalDataSource.load(ExternalDataSourceEnum.ADAMJEE.id)

        Make make = Make.load(quoteCommand.makeId)
        Model model = Model.load(quoteCommand.modelId)
        ModelMaster modelMaster = ModelMaster.load(model.modelMaster.id)
        Country country = Country.load(quoteCommand.nationalityId)
        City city = City.load(quoteCommand.registrationCityId)

        AdamjeeQuoteCommand adamjeeQuoteCommand = AdamjeeQuoteCommand.generateQuoteCommand(quoteCommand)
        MakeExternal makeExternal = MakeExternal.findByExternalDataSourceAndMake(adamjeeDataSource, make)
        ModelMasterExternal modelMasterExternal =  ModelMasterExternal.findByExternalDataSourceAndModelMaster(adamjeeDataSource, modelMaster)
        String makeCode = getMakeCodeForSpecificModelMasters(model.modelMaster.id) ?: makeExternal?.externalId
        log.info ".toAdamjeeQuoteCommand - makeCode: ${makeCode}"

        if (!makeCode || !modelMasterExternal || !model.adcBodyTypeCode){
            return null
        }

        adamjeeQuoteCommand.vehicleMakeCode = makeCode
        adamjeeQuoteCommand.vehicleModelMasterCode = modelMasterExternal.externalId
        adamjeeQuoteCommand.vehicleModelMasterCode = ModelMasterExternal.findByExternalDataSourceAndModelMaster(adamjeeDataSource, modelMaster)?.externalId
        adamjeeQuoteCommand.nationalityCode = CountryExternal.findByExternalDataSourceAndCountry(adamjeeDataSource, country)?.externalId
        adamjeeQuoteCommand.regEmiratesCode = CityExternal.findByExternalDataSourceAndCity(adamjeeDataSource, city)?.externalId
        adamjeeQuoteCommand.vehicleUseCode = VEHICLE_USE_CODE
        adamjeeQuoteCommand.vehicleBodyTypeCode = model.adcBodyTypeCode
        adamjeeQuoteCommand.noOfPassengers = model.numberOfSeats.toString()
        adamjeeQuoteCommand.numberOfDoors = model.numberOfDoors.toString()
        adamjeeQuoteCommand.mobileNumber = quote.mobile
        adamjeeQuoteCommand.licenseIssueDate = CommonQuoteService.getDrivingLicenseIssueDate(quoteCommand.localDrivingExperienceId).toString("dd/MM/YYYY")

        Integer noClaimYears = calculateNCDYears(quote, quoteCommand)
        adamjeeQuoteCommand.noClaimYears =  noClaimYears == null ? 0 : (noClaimYears <= 3 ? noClaimYears : 3)
        adamjeeQuoteCommand.isInsuranceBreak = "N"
        adamjeeQuoteCommand.isVehicleGCC = quoteCommand.isNonGccSpec ? "N" : "Y"
        adamjeeQuoteCommand.isTplLastYear = quoteCommand.isThirdParty ? "Y" : "N"


        return adamjeeQuoteCommand

    }

    private RateCommand toRateCommand(AdamjeeQuoteCommand quoteCommand, AdamjeeRateCommand providerRateCommand){

        Integer productId = getProductId(providerRateCommand.productCode, providerRateCommand.isDynaTrade)

        log.info("adamjeeV2.toRateCommand - productId:$productId")
        if (!productId) return null

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.productId = productId
        rateCommand.providerId = PROVIDER_ID

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        rateCommand.basePremium = providerRateCommand.netPremium
        rateCommand.premium = rateCommand.basePremium
        rateCommand.minPremium = rateCommand.premium
        rateCommand.currency = quoteCommand.currency
        rateCommand.excess = providerRateCommand.excess

        if (providerRateCommand.isDynaTrade){
            rateCommand.hasPremiumGarage = true
        } else if(!providerRateCommand.isDynaTrade && providerRateCommand.productCode == adamjeeApiService.COMPREHENSIVE_PRODUCT_CODE){
            rateCommand.agencyRepair = false
        }

        rateCommand.dynamicAddons = providerRateCommand.optionalCovers.
            findAll {it.premium != "0" }.
            collect {
                [label: it.name, price: new BigDecimal(it.premium), providerCode: it.code,
                 code: AddonCodeEnum.DYNAMIC_ADDON.code,
                 description: it.name
                 ]
            }

        List allCovers = []
        allCovers.addAll(providerRateCommand.covers)
        allCovers.addAll(providerRateCommand.optionalCovers)

        AdamjeeRateCommand.AdamjeeRateCoverCommand RSA_SILVER = allCovers.find {
            it.code == CarCoversEnum.RSA_SILVER.adcCode}
        rateCommand.breakdownCover = RSA_SILVER ?
            (RSA_SILVER.premium == "0" || RSA_SILVER.coverFlag == "MC"? CarCoversEnum.findByAdcCode(RSA_SILVER.code).shortName : RSA_SILVER.premium) : "none"

        AdamjeeRateCommand.AdamjeeRateCoverCommand pax = allCovers.find {
            it.code == CarCoversEnum.PAB_PASSENGER.adcCode }
        rateCommand.personalAccidentPax = pax ? (pax.premium == "0" || pax.coverFlag == "MC"? "yes" : pax.premium) : "no"

        AdamjeeRateCommand.AdamjeeRateCoverCommand natural_calamity = allCovers.find {
            it.code == CarCoversEnum.NATURAL_CALAMITY.adcCode }
        rateCommand.naturalCalamity = natural_calamity ? (natural_calamity.premium == "0" || natural_calamity.coverFlag == "MC" ? "yes" : natural_calamity.premium) : "no"

        AdamjeeRateCommand.AdamjeeRateCoverCommand pad = allCovers.find {
            it.code == CarCoversEnum.PAB_DRIVER.adcCode }
        rateCommand.paCover = pad ? (pad.premium == "0" || pad.coverFlag == "MC" ? "yes" : pad.premium) : "no"

        AdamjeeRateCommand.AdamjeeRateCoverCommand rac = allCovers.find {
            it.code == CarCoversEnum.REPLACE_CAR.adcCode }
        rateCommand.replacementCar = rac ? (rac.premium == "0" || rac.coverFlag == "MC" ? "yes" : rac.premium) : "no"

        AdamjeeRateCommand.AdamjeeRateCoverCommand windScreenCover = allCovers.find {
            it.code == CarCoversEnum.WIND_SCREEN.adcCode }
        rateCommand.windScreenCover = windScreenCover ? (windScreenCover.premium == "0" || windScreenCover.coverFlag == "MC" ? "3,000" : windScreenCover.premium) : "no"


        return rateCommand
    }

    private Integer getProductId(String productCode, boolean showDynaTrade){
        if (productCode == adamjeeApiService.COMPREHENSIVE_PRODUCT_CODE && showDynaTrade){
            return PRODUCT_DYNA_TRADE_ID
        }else if (productCode == adamjeeApiService.COMPREHENSIVE_PRODUCT_CODE && !showDynaTrade){
            return PRODUCT_COMPREHENSIVE_ID
        }else if (productCode == adamjeeApiService.TPL_PRODUCT_CODE){
            return PRODUCT_TPL_ID
        }
        return null
    }

    CarQuoteCover getCoverByProduct(List<CarQuoteCover> carQuoteCovers, Integer productId){
        log.info("adamjeeRateV2Service - Product ID - ${productId}")
        CarQuoteCover selectedCover = null

        carQuoteCovers.each {
            JSONElement coverDetails = JSON.parse(it.covers)
            log.info("Cover ID ${it.id}")
            log.info("isPremium or Not - ${isPremiumGarage(coverDetails.SchemeDetails[0])}")
            if (productId == PRODUCT_DYNA_TRADE_ID && isPremiumGarage(coverDetails.SchemeDetails[0])){
                selectedCover = it
            }else if (productId == PRODUCT_COMPREHENSIVE_ID && !isPremiumGarage(coverDetails.SchemeDetails[0])){
                selectedCover = it
                return
            } else if (productId == PRODUCT_TPL_ID && !isPremiumGarage(coverDetails.SchemeDetails[0])){
                selectedCover = it
                return
            }
        }
        return selectedCover
    }

    private Boolean isPremiumGarage(JSONElement coverDetails){
        boolean isPremium = false
        for (JSONElement cover in coverDetails?.CoverDetails){
            String coverFlag = cover?.CvrFlag
            if (coverFlag == "IC" && cover?.CdlCode == CarCoversEnum.PREMIUM_GARAGE.adcCode){
                isPremium = true
            }
        }
        return isPremium
    }


    def saveQuoteDetails(CarQuote quote, def selectedDynamicAddons){
        try{
            /*List<String> selectedOptionalAddonsCodes = []
            selectedDynamicAddons.each{
                selectedOptionalAddonsCodes.add(it.providerCode)
            }
            adamjeeApiService.callSaveQuoteApi(quote, selectedOptionalAddonsCodes)
            */
        }catch (Exception e){
            log.error("adamjeeRateV2Service.saveQuoteDetails - Exception ${e.getMessage()}", e)
        }

    }

    Integer calculateNCDYears(CarQuote quote, QuoteCommand command){

        Integer drivingExp = DrivingExperienceEnum.findById(command.localDrivingExperienceId).experienceInYears
        Integer noClaimYears = quote.noClaimDiscount ? quote.noClaimDiscount.years : command.lastClaimPeriod == ClaimPeriodEnum.NEVER ?
            drivingExp : command.lastClaimPeriod.noClaimYears()

        return (noClaimYears > drivingExp) ? drivingExp : noClaimYears
    }

    String getMakeCodeForSpecificModelMasters(Integer modelMasterId) {
        String makeCode = null
        if (modelMasterId == ModelMasterEnum.JAC_J6.id) {
            makeCode = "1009"
        } else if (modelMasterId == ModelMasterEnum.SUZUKI_VITARA.id) {
            makeCode = "159"
        } else if (modelMasterId == ModelMasterEnum.MCLAREN_720.id) {
            makeCode = "167"
        } else if (modelMasterId == ModelMasterEnum.OPEL_ASTRA.id) {
            makeCode = "229"
        } else if (modelMasterId == ModelMasterEnum.MERCEDES_BENZ_CLC_CLASS.id) {
            makeCode = "068"
        } else if (modelMasterId == ModelMasterEnum.MG_350.id) {
            makeCode = "068"
        } else if (modelMasterId == ModelMasterEnum.MERCEDES_BENZ_EQE.id) {
            makeCode = "068"
        }

        makeCode
    }

}


