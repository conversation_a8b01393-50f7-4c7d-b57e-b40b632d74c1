package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.*
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Ratings calculation for <PERSON>can<PERSON>.
 * old name was OMAN
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class AscanaRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 70
    public static final Integer PRODUCT_TPL_ID = 5086

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("Ascana getRates- quoteCommand - ${quoteCommand} - isOffline - ${isOffline}")
        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)
        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = null
            Country country = Country.get(quoteCommand.nationalityId)

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    if ((isMENationality(quoteCommand) || quoteCommand.nationalityId as long == CountryEnum.PAKISTAN.id) && quoteCommand.nationalityId as long != CountryEnum.LBN.id) {
                        return []
                    }
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("ascana getRate - ${quoteCommand} - isOffline - ${isOffline}")
        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = null
            quoteCommand.nationalityCategory = null

            log.info("Product Id - ${quoteCommand.productId}")

            if (model) {
                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, true)
                log.info("Rates - ${applicableRates.size()}")
                if (applicableRates) {
                    rateCommand = populateRatings(quoteCommand, applicableRates.first())
                }
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        //rateCommand = applyAdditionalFees(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        Provider provider = Provider.get(PROVIDER_ID)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        //BigDecimal baseRate = calculateBaseRate(quoteCommand, provider, product)

        rateCommand.agencyRepair = checkAgency(product, quoteCommand)
        rateCommand.productId = product.id

        rateCommand = applyBaseRate(rateCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        if (rateCommand.premium < rateCommand.minPremium) {
            rateCommand.premium = rateCommand.minPremium
        }
        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("ascanaTplRate - ${quoteCommand} - isOffline - ${isOffline}")
        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)
            for (ProductTplRate tplRate : applicableRates) {
                log.info(tplRate.productId.toString() + ":" + tplRate.basePremium.toString() + ":" + tplRate.noOfCyl.toString() + ":" + tplRate.vehicleTypeId.toString())
            }

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand = applyTplBasePremium(rateCommand, quoteCommand, rate)
        rateCommand.premium = rateCommand.basePremium
        rateCommand.providerId = PROVIDER_ID
        rateCommand.productId = rate.productId

        rateCommand = applyTplLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        // rateCommand = applyAdditionalFees(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand applyTplLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {
        // TODO: add TPL loading

        rateCommand
    }

    private boolean checkAgency(Product product, QuoteCommand quoteCommand) {

        boolean isAgency = false

//        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
//            if (GOLD_ID == product.id && quoteCommand.carAge <= 2) {
//                isAgency = true
//            } else if (SILVER_ID == product.id && quoteCommand.carAge == 0) {
//                isAgency = true
//            }
//        }

        isAgency
    }

    /*private BigDecimal calculateBaseRate(QuoteCommand quoteCommand, Provider provider, Product product) {

        Model model = Model.get(quoteCommand.modelId)

        City city = City.get(quoteCommand.registrationCityId)
        Country country = Country.get(quoteCommand.nationalityId)

        FactorCarAge factorCarAge =
            FactorCarAge.findByProviderAndCarAge(provider, quoteCommand.carAge)
        FactorDrivingExperience factorDrivingExperience =
            FactorDrivingExperience.findByProviderAndDrivingExperience(provider,
                DrivingExperience.get(quoteCommand.localDrivingExperienceId))
        FactorUserAge factorUserAge =
            FactorUserAge.findByProviderAndAgeFrom(provider, quoteCommand.customerAge)

        BigDecimal factorClaimsInTheLastYear
        switch (quoteCommand.claimsInTheLastYear) {
            case { it >= 3 }:
                factorClaimsInTheLastYear = 2
                break
            case 2:
                factorClaimsInTheLastYear = 1.2
                break
            case 1:
                factorClaimsInTheLastYear = 1.1
                break
            default:
                factorClaimsInTheLastYear = 1
        }

        BigDecimal baseRate = city.omanFactor * (country.omanFactor) *
            (factorCarAge.factor) *
            (factorDrivingExperience.factor) *
            (factorUserAge.factor) *
            (model.factorOman) *
            factorClaimsInTheLastYear

        // change made as advised by Oman 06/APR/17
        BigDecimal newFactorOman = BigDecimal.ONE
        if (model.omanCategory in ['A1', 'A2']) {
            if (checkAgency(product, quoteCommand)) {
                newFactorOman = BigDecimal.valueOf(1.25)
            }
            baseRate = baseRate * newFactorOman
        }

        // if not agency there is a discount
        if (!checkAgency(product, quoteCommand)) {
            baseRate = baseRate * (0.7)
        }

        // as per their rator base rate is 4.2%
        baseRate = baseRate * 4.2

        // min max conditions for base rate as per their rator
        if (baseRate <= 2.5) {
            baseRate = 2.5
        } else if (baseRate >= 5) {
            baseRate = 5
        }

        baseRate
    }*/

    RateCommand applyAdditionalFees(QuoteCommand quoteCommand, RateCommand rateCommand) {

        Model model = Model.get(quoteCommand.modelId)

        // PA Driver 120
        // PA Pax 30 * number of pax
        // Roadside assistance silver 40 Gold 70
        // Ambulance fee
        BigDecimal roadSide = BigDecimal.valueOf(40) // for silver
        rateCommand.premium = rateCommand.premium.add(120).add((model.numberOfSeats - 1) * 30).add(roadSide).add(50)

        // Flat AED 300 added as requested by Oman
        rateCommand.premium = rateCommand.premium.add(300)

        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)
        /*
            not eligible if
            - current policy is third party
        */
//        if (quoteCommand.isThirdParty) {
//            isEligible = false
//        }

        if (quoteCommand.customerAge < 23) {
            return false
        }

//        if (quoteCommand.lastClaimPeriod != ClaimPeriodEnum.NEVER) {
//            return false
//        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.carAge > 14) {
            return false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.isThirdParty) {
            return false
        }

        // not eligible if uae driving experience is less then 6 month
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId()) {
            isEligible = false
        }

        isEligible
    }

    boolean isTransferableNationality(QuoteCommand quoteCommand) {

        if ((quoteCommand.nationalityId as long) in [CountryEnum.AUSTRALIA.id, CountryEnum.AUSTRIA.id, CountryEnum.BELGIUM.id, CountryEnum.CANADA.id,
                                                     CountryEnum.CHINA.id, CountryEnum.DENMARK.id, CountryEnum.FRANCE.id, CountryEnum.FINLAND.id, CountryEnum.GERMANY.id, CountryEnum.GREECE.id,
                                                     CountryEnum.HONG_KONG.id, CountryEnum.IRELAND.id, CountryEnum.ITALY.id, CountryEnum.JAPAN.id, CountryEnum.SOUTH_KOREA.id, CountryEnum.USA.id,
                                                     CountryEnum.LATVIA.id, CountryEnum.LITHUANIA.id, CountryEnum.LUXEMBOURG.id, CountryEnum.NORWAY.id, CountryEnum.POLAND.id, CountryEnum.PORTUGAL.id,
                                                     CountryEnum.ROMANIA.id, CountryEnum.SERBIA.id, CountryEnum.SINGAPORE.id, CountryEnum.SLOVAKIA.id, CountryEnum.SOUTH_AFRICA.id, CountryEnum.SPAIN.id,
                                                     CountryEnum.SWEDEN.id, CountryEnum.SWITZERLAND.id, CountryEnum.TURKEY.id, CountryEnum.UK.id, CountryEnum.NETHERLANDS.id]) {
            return true
        }

        return false

    }

    boolean isMENationality(QuoteCommand quoteCommand) {

        if ((quoteCommand.nationalityId as long) in [CountryEnum.ALGERIA.id, CountryEnum.BAHRAIN.id, CountryEnum.EGYPT.id, CountryEnum.IRAQ.id,
                                                     CountryEnum.IRAN.id, CountryEnum.ISRAEL.id, CountryEnum.JOR.id, CountryEnum.KWT.id, CountryEnum.LBN.id, CountryEnum.LIBYA.id,
                                                     CountryEnum.MOROCCO.id, CountryEnum.OMAN.id, CountryEnum.PALESTINE.id, CountryEnum.QATAR.id, CountryEnum.KSA.id, CountryEnum.SYRIA.id,
                                                     CountryEnum.TUNISIA.id, CountryEnum.YEMEN.id, CountryEnum.UAE.id]) {
            return true
        }

        return false
    }

    RateCommand applyTplBasePremium(RateCommand rateCommand, QuoteCommand quoteCommand, ProductTplRate productTplRate) {

        BigDecimal basePremium = productTplRate.basePremium
        Integer noOfCyl = Model.findById(quoteCommand.modelId).noOfCyl

        if (quoteCommand.customerAge >= 30) {

            // for nationality = [Transfer, India, PHILIPPINES]
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.INDIA.id, CountryEnum.PHILIPPINES.id] ||
                    isTransferableNationality(quoteCommand))) {
                if (noOfCyl <= 4) {
                    basePremium = 600
                } else if (noOfCyl <= 6) {
                    basePremium = 680
                } else if (noOfCyl <= 8) {
                    basePremium = 760
                } else if (noOfCyl > 8) {
                    basePremium = 1040
                }
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.INDIA.id, CountryEnum.PHILIPPINES.id] ||
                    isTransferableNationality(quoteCommand))) {
                if (noOfCyl <= 4) {
                    basePremium = 800
                } else if (noOfCyl <= 6) {
                    basePremium = 840
                } else if (noOfCyl <= 8) {
                    basePremium = 880
                } else if (noOfCyl > 8) {
                    basePremium = 960
                }
            }

            // for nationality = [UAE, GCC, LEBANON]
            else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.UAE.id, CountryEnum.BAHRAIN.id, CountryEnum.KWT.id,
                                                          CountryEnum.OMAN.id, CountryEnum.QATAR.id, CountryEnum.KSA.id,
                                                          CountryEnum.LBN.id])) {
                if (noOfCyl <= 4) {
                    basePremium = 640
                } else if (noOfCyl <= 6) {
                    basePremium = 725
                } else if (noOfCyl <= 8) {
                    basePremium = 810
                } else if (noOfCyl > 8) {
                    basePremium = 1105
                }
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
                ((quoteCommand.nationalityId as long) in [CountryEnum.UAE.id, CountryEnum.BAHRAIN.id, CountryEnum.KWT.id,
                                                          CountryEnum.OMAN.id, CountryEnum.QATAR.id, CountryEnum.KSA.id,
                                                          CountryEnum.LBN.id])) {
                if (noOfCyl <= 4) {
                    basePremium = 850
                } else if (noOfCyl <= 6) {
                    basePremium = 895
                } else if (noOfCyl <= 8) {
                    basePremium = 935
                } else if (noOfCyl > 8) {
                    basePremium = 1020
                }
            }
        }

        rateCommand.basePremium = basePremium

        return rateCommand
    }

    private RateCommand applyBaseRate(RateCommand rateCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        rateCommand.baseRate = baseRate
        return rateCommand
    }
}
