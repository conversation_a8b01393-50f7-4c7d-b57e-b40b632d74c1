package com.cover.car.lbn

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CountryEnum
import com.safeguard.Product
import com.safeguard.QuoteResultLbn
import com.safeguard.Renewal
import com.safeguard.car.CarQuote
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

@Transactional
class LbnQuoteService {

    def lbnCommercialRateService
    def lbnAssurexRateService
    def lbnCapitalRateService
    def lbnFidelityRateService
    def ratingService
    def quoteService

    @Transactional(readOnly = true)
    List<RateCommand> getRatings(QuoteCommand quoteCommand, boolean isOffline) {
        log.info "lbQuote.getRatings - ${quoteCommand}"
        List<RateCommand> rateList = []

        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.vechileTypeId = model.vehicleType.id
        quoteCommand.productId = null
        quoteCommand.providerId = null

        rateList.addAll(lbnCommercialRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(lbnAssurexRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(lbnCapitalRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(lbnFidelityRateService.getRates(quoteCommand, isOffline))

        RateCommand commInsCmd = lbnCommercialRateService.getTplRate(quoteCommand, isOffline)
        if (commInsCmd) {
            rateList.add(commInsCmd)
        }
        RateCommand assurexInsCmd = lbnAssurexRateService.getTplRate(quoteCommand, isOffline)
        if (assurexInsCmd) {
            rateList.add(assurexInsCmd)
        }
        RateCommand capitalInsCmd = lbnCapitalRateService.getTplRate(quoteCommand, isOffline)
        if (capitalInsCmd) {
            rateList.add(capitalInsCmd)
        }
        RateCommand fidelityInsCmd = lbnFidelityRateService.getTplRate(quoteCommand, isOffline)
        if (fidelityInsCmd) {
            rateList.add(fidelityInsCmd)
        }

        return rateList
    }

    @Transactional(readOnly = true)
    getRatings(Integer quoteId, boolean isOffline = false) {
        log.info "lbQuote.getRatings with quoteId - ${quoteId}, isOffline - ${isOffline}"

        CarQuote carQuote = CarQuote.get(quoteId)
        List<RateCommand> ratings = []
        if (carQuote) {
            QuoteCommand quoteCommand = quoteService.toQuoteCommand(carQuote)

            ratings = getRatings(quoteCommand, isOffline)

            if (carQuote.isRenewal) {

                Renewal renewal = Renewal.findByCarQuote(carQuote)

                if (renewal) {
                    //Remove renewal product and products from same provider
                    ratings = ratings.findAll {
                        Product p = Product.findById(it.productId)

                        (p.providerId != renewal.product.providerId && p.id != renewal.productId)
                    }

                    if (renewal.hasPreviousProvider) {
                        RateCommand renewalRateCommand = new RateCommand(productId: renewal.productId)
                        renewalRateCommand.currency = quoteCommand.currency
                        ratingService.applyCovers(quoteCommand, renewalRateCommand)
                        renewalRateCommand.toRenewal(renewal)
                        renewalRateCommand = ratingService.applyRenewalCover(quoteCommand, renewalRateCommand)

                        //ratingService.applyC4meFees(renewalRateCommand, quoteCommand.policyStartDate)
                        //renewalRateCommand = ratingService.applyExtraDiscount(renewalRateCommand)

                        ratings << renewalRateCommand //Adding renewal product
                    }

                }
            }
        }

        return [ratings, carQuote]
    }

    /**
     * Save all quotes
     * @param ratings
     * @param quoteId
     */
    def saveAllQuotes(List<RateCommand> ratings, CarQuote quote) {
        log.info("lbnQuote.saveAllQuotes - saving quotes for: ${quote.id}")

        ratings.eachWithIndex { rating, idx ->
            QuoteResultLbn quoteResult = new QuoteResultLbn()
            Product product = Product.get(rating.productId)
            quoteResult.position = idx
            quoteResult.provider = product?.provider?.nameEn
            quoteResult.paCover = rating.paCover
            quoteResult.productName = product?.nameEn
            quoteResult.agencyRepair = rating.agencyRepair
            quoteResult.breakdownCover = rating.breakdownCover
            quoteResult.replacementCar = rating.replacementCar
            quoteResult.basePremium = rating.basePremium
            quoteResult.premium = rating.premium
            quoteResult.minPremium = rating.minPremium
            quoteResult.excess = rating.excess
            quoteResult.productId = rating.productId
            quoteResult.quote = quote
            quoteResult.save(failOnError: true)

        }

    }
}
