package com.cover.car.lbn

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.DrivingExperienceEnum
import com.safeguard.RepairTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Calculate premium for Lebanon Capital Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class LbnCapitalRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 15
    public static final Integer PRODUCT_STANDARD_WO_DEDUCTIBLE = 108
    public static final Integer PRODUCT_STANDARD_TPL_TOTAL_LOSS = 109
    public static final Integer PRODUCT_CAPITAL_PLUS = 110
    public static final Integer PRODUCT_HIGH_VALUE = 111
    public static final Integer PRODUCT_TPL = 112

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)

                    if (rateCommand) {
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        boolean isEligible = checkHighValueEligibility(quoteCommand, rate)

        if (!isEligible) { return null }

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        //rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        BigDecimal baseRate = getBaseRate(isAgency, applicableRate)
        BigDecimal minPremium = getMinimumPremium(isAgency, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ProductTplRate.
                    findApplicableRate(PROVIDER_ID, model.vehicleTypeId, null, quoteCommand.customerAge,
                        isOffline).list()

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand
    }


    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate rate) {

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            //Agency repair for first 3 years
            if ((rate.productId == PRODUCT_CAPITAL_PLUS || rate.productId == PRODUCT_HIGH_VALUE)
                && quoteCommand.carAge <= 3) {
                isAgency = true
            }
        }

        return isAgency
    }

    private BigDecimal getBaseRate(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        baseRate
    }

    private BigDecimal getMinimumPremium(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal minPremium  = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        minPremium
    }

    boolean checkEligibility(QuoteCommand quoteCommand) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.TWO_TO_THREE.getId()) {
            isEligible = false
        }

        isEligible
    }

    boolean checkHighValueEligibility(QuoteCommand quoteCommand, ProductBaseRate rate) {
        boolean isEligible = true

        if(rate.productId == PRODUCT_HIGH_VALUE
            && quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.FIVE_ABOVE.getId()) {
            isEligible = false
        }

        return isEligible
    }
}
