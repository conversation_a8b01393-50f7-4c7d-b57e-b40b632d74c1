package com.cover.car.lbn

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Calculate premium for Lebanon Commercial Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class LbnCommercialRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 13
    public static final Integer PRODUCT_AGENCY = 101
    public static final Integer PRODUCT_GARAGE = 102

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    log.info("lbnCommercialRate.getRates - rate:${rate.productId}")

                    RateCommand rateCommand = populateRatings(quoteCommand, rate)

                    if (rateCommand) {
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        if(!checkProduct(quoteCommand, rate)) {
            return null
        }

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        //rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        BigDecimal baseRate = getBaseRate(isAgency, applicableRate)
        BigDecimal minPremium = getMinimumPremium(isAgency, applicableRate)
        log.info("minPremium:${minPremium}, baseRate:${baseRate}")

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ProductTplRate.
                    findApplicableRate(PROVIDER_ID, model.vehicleTypeId, null, quoteCommand.customerAge,
                        isOffline).list()

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand
    }


    private boolean checkProduct(QuoteCommand quoteCommand, ProductBaseRate rate) {
        //check if product is agency
        //Check if car is in given range, if product is agency then use agency rates only
        //Check if product is non agency, then use non agency rates only
        if (rate.productId == PRODUCT_GARAGE || (
                rate.productId == PRODUCT_AGENCY && isAgencyApplicable(quoteCommand))) {
            return true
        }

        return false
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate rate) {

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            if (rate.productId == PRODUCT_AGENCY && isAgencyApplicable(quoteCommand)) {
                isAgency = true
            }
        }

        return isAgency
    }

    /**
     * Check if a car is applicable for agency repair
     * @param quoteCommand
     * @return
     */
    private boolean isAgencyApplicable(QuoteCommand quoteCommand) {

        List carMakes = []
        carMakes.add(151) //Mazada
        carMakes.add(172) //Volvo
        carMakes.add(143) //Kia
        carMakes.add(174) //Citroen
        carMakes.add(158) //Peugeot
        carMakes.add(165) //Seat
        carMakes.add(169) //Suzuki
        carMakes.add(155) //Mitsubishi
        carMakes.add(156) //Nissan
        carMakes.add(139) //Infinity
        carMakes.add(177) //Jac
        carMakes.add(125) //Audi
        carMakes.add(171) //Volkswagen
        carMakes.add(166) //Skoda
        carMakes.add(141) //Jaguar
        carMakes.add(142) //Jeep
        carMakes.add(128) //Cadillac
        carMakes.add(129) //Chevrolet
        carMakes.add(138) //Hyundai

        if (quoteCommand.makeId in carMakes) {
            return true
        }

        return false
    }

    private BigDecimal getBaseRate(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        baseRate
    }

    private BigDecimal getMinimumPremium(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal minPremium  = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        minPremium
    }

    RateCommand applyExcess(QuoteCommand quoteCommand, RateCommand rate) {

        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            rate.excess = "25" //Add USD $25 if driving experience is less than 1 year
        }

        rate
    }

    boolean checkEligibility(QuoteCommand quoteCommand) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)

        // only certain makes allowed if car age more then 7 years.
        if (quoteCommand.carAge > 10 || quoteCommand.insuredValue > 100000) {
            isEligible = false
        }

        isEligible
    }

}
