package com.cover.car.lbn

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.DrivingExperienceEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
/**
 * Calculate premium for Lebanon Fidelity Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class LbnFidelityRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 16
    public static final Integer PRODUCT_ESSENTIAL = 113
    public static final Integer PRODUCT_SELECT = 114
    public static final Integer PRODUCT_INFINITE = 115
    public static final Integer PRODUCT_HIGH_VALUE = 116
    public static final Integer PRODUCT_TOTAL_LOSS = 117
    public static final Integer PRODUCT_COLLISION = 118
    public static final Integer PRODUCT_TPL = 119
    public static final Integer PRODUCT_ESSENTIAL_PLUS = 120


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    log.info("lbnFidelityRate.getRates - rate:${rate.productId}")

                    RateCommand rateCommand = populateRatings(quoteCommand, rate)

                    if (rateCommand) {
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        boolean isEligible = checkLicenseEligibility(quoteCommand, rate.productId)

        if (!isEligible) { return null }

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        //rateCommand = extendCover(quoteCommand, rateCommand)
        rateCommand = applyExcess(quoteCommand, rateCommand, rate)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        BigDecimal baseRate = getBaseRate(isAgency, applicableRate)
        BigDecimal minPremium = getMinimumPremium(isAgency, applicableRate)
        log.info("minPremium:${minPremium}, baseRate:${baseRate}")
        baseRate = applyLoadings(quoteCommand, applicableRate.productId, baseRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ProductTplRate.
                    findApplicableRate(PROVIDER_ID, model.vehicleTypeId, null, quoteCommand.customerAge,
                        isOffline).list()

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {
        boolean isEligible = checkLicenseEligibility(quoteCommand, rate.productId)

        if (!isEligible) { return null }

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand
    }


    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate rate) {

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            if (quoteCommand.isBrandNew && (
                (rate.productId == PRODUCT_ESSENTIAL && quoteCommand.carAge <= 1) ||
                    (rate.productId == PRODUCT_ESSENTIAL_PLUS && quoteCommand.carAge <= 1) ||
                    (rate.productId == PRODUCT_SELECT && quoteCommand.carAge <= 3) ||
                    (rate.productId == PRODUCT_INFINITE && quoteCommand.carAge <= 3) ||
                    (rate.productId == PRODUCT_HIGH_VALUE && quoteCommand.carAge <= 3))) {
                isAgency = true
            }
        }

        return isAgency
    }

    private BigDecimal getBaseRate(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        baseRate
    }

    private BigDecimal getMinimumPremium(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal minPremium  = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        minPremium
    }

    RateCommand applyExcess(QuoteCommand quoteCommand, RateCommand rate, ProductBaseRate baseRate) {
        Integer excess = 0

        if (rate.productId in [PRODUCT_ESSENTIAL, PRODUCT_ESSENTIAL_PLUS, PRODUCT_SELECT, PRODUCT_INFINITE, PRODUCT_COLLISION]
            && (quoteCommand.customerAge < 21 ||
                quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId())) {
            excess += 100
        }
        if (rate.productId == PRODUCT_SELECT && quoteCommand.carAge >= 12 && quoteCommand.carAge <= 16) {
            excess += 100
        }
        if (excess) {
            rate.excess = excess
        }

        rate
    }

    boolean checkEligibility(QuoteCommand quoteCommand) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)

        isEligible
    }

    BigDecimal applyLoadings(QuoteCommand quoteCommand, def productId, BigDecimal baseRate) {

        if (productId in [PRODUCT_SELECT, PRODUCT_INFINITE] && quoteCommand.customerAge < 25) {
            //1% loading on customer age less than 25 years
            baseRate = baseRate + 1
        }

        baseRate
    }

   /* RateCommand extendCover(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (rateCommand.productId == PRODUCT_ESSENTIAL && quoteCommand.insuredValue > 18000) {
            rateCommand.dentRepair = "yes"
        }
        log.info("rateCommand.dentRepair:${rateCommand.dentRepair}")
        return rateCommand
    }*/

    boolean checkLicenseEligibility(QuoteCommand quoteCommand, Integer productId) {
        boolean isEligible = true

        if (productId in [PRODUCT_HIGH_VALUE, PRODUCT_TOTAL_LOSS, PRODUCT_TPL]
            && quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }

        return isEligible
    }

}
