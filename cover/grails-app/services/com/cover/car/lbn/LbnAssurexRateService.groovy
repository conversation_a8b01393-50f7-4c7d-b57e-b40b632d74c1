package com.cover.car.lbn

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Calculate premium for Lebanon Assurex Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class LbnAssurexRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 14
    public static final Integer PRODUCT_COMPREHENSIVE = 104
    public static final Integer PRODUCT_STANDARD = 105

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            quoteCommand.carCategory = null

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    log.info("lbnAssurexRate.getRates - rate:${rate.productId}")

                    RateCommand rateCommand = populateRatings(quoteCommand, rate)

                    if (rateCommand) {
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        //rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyRoadsideAssistance(quoteCommand, rateCommand)
        rateCommand = applyExcess(quoteCommand, rateCommand, rate)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        BigDecimal baseRate = getBaseRate(isAgency, applicableRate)
        BigDecimal minPremium = getMinimumPremium(isAgency, applicableRate)
        log.info("minPremium:${minPremium}, baseRate:${baseRate}")

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ProductTplRate.
                    findApplicableRate(PROVIDER_ID, model.vehicleTypeId, null, quoteCommand.customerAge,
                        isOffline).list()

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyRoadsideAssistance(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand
    }


    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate rate) {

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            if (rate.productId == PRODUCT_COMPREHENSIVE && quoteCommand.carAge <= 3) {
                isAgency =  true
            }
        }

        return isAgency
    }

    private BigDecimal getBaseRate(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        baseRate
    }

    private BigDecimal getMinimumPremium(boolean isAgency, ProductBaseRate applicableRate) {

        BigDecimal minPremium  = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        minPremium
    }

    RateCommand applyExcess(QuoteCommand quoteCommand, RateCommand rate, ProductBaseRate baseRate) {
        Integer excess = 0

        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId() ||
            quoteCommand.customerAge < 21) {
            excess += 50 //Add USD 50 if driving experience is less than 1 year or customer age less than 21
        }

        if (rate.productId == PRODUCT_STANDARD && quoteCommand.carAge >= 13 && quoteCommand.carAge <= 15) {
            excess += 100  // Car Age from 2006-2004
        }

        if (checkAgency(quoteCommand, baseRate) && tGargourCars(quoteCommand)) {
            excess += 300 // Add additional 300 if
        }


        if (excess) {
            rate.excess = excess
        }

        rate
    }

    boolean checkEligibility(QuoteCommand quoteCommand) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)

        // only car with insured value less than 200000.
        if (quoteCommand.insuredValue > 200000) {
            isEligible = false
        }

        isEligible
    }

    /**
     * Apply Roadside Assistance only if applicable to vehicle
     *
     * @param quoteCommand
     * @param rateCommand
     * @return
     */
    RateCommand applyRoadsideAssistance(QuoteCommand quoteCommand, RateCommand rateCommand) {
        Model model = Model.read(quoteCommand.modelId)

        if (model.vehicleType.id != VehicleTypeEnum.SEDAN.value) {
            rateCommand.breakdownCover = "none"
            rateCommand.roadsideAssistances = null
        }
        return rateCommand
    }

    boolean tGargourCars(QuoteCommand quoteCommand) {
        List carMakes = []
        carMakes.add(130) //Chrysler
        carMakes.add(142) //Jeep
        carMakes.add(152) //Mercedes

        if (quoteCommand.makeId in carMakes) {
            return true
        }

        return false
    }

}
