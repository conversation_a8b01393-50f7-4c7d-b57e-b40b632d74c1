package com.cover.car

import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.nia.NewIndiaQuoteCommand
import com.cover.car.nia.NewIndiaRateCommand
import com.cover.car.nia.NewIndiaVehicleMatrix
import com.safeguard.BodyTypeEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.EmirateEnum
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.Provider
import com.safeguard.RepairTypeEnum
import com.safeguard.ValuationSourceEnum
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CarValuation
import com.safeguard.car.CoverageType
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap

import javax.annotation.PostConstruct
import java.math.RoundingMode

@Transactional
class NewIndiaApiService extends ProviderRatingService {

    static final String LOGIN_API = "/ValidateLogin"
    static final String VEHICLE_MATRIX_API = "/VehicleMatrixDetails"
    static final String CREATE_QUOTE_API = "/CreateQuotation"
    static final String SAVE_QUOTE_WITH_PLAN_API = "/SaveQuoteWithPlan"
    static final String SAVE_QUOTE_WITH_ADDITIONAL_INFO = "/SaveAdditionalInfo"
    static final String DOCUMENT_FILE_UPLOAD = "/DocumentFileUpload"
    static final String APPROVE_POLICY = "/ApprovePolicy"


    public static final String AGENCY_NEW_VEHICLES_PRODUCT_CODE = "100129" //Agency Only - newly launced vehicles
    public static final String DYNATRADE_PRODUCT_CODE = "100167" //upto 5 years only
    public static final String NON_AGENCY_PRODUCT_CODE = "100133"

    //public static final String NON_AGENCY_2021_PRODUCT_CODE = "100133"
    //public static final String AGENCY_NIA_COMFORT_PRODUCT_CODE = "1005"
    //public static final String NON_AGENCY_NIA_EXCLUSIVE_PRODUCT_CODE = "1004"

    public static final String SUCCESS_SAVE_QUOTE_RESPONSE_CODE = "1001"
    public static final String SUCCESS_SAVE_QUOTE_WITH_PLAN_RESPONSE_CODE = "1003"
    public static final String SUCCESS_LOGIN_RESPONSE_CODE = "1005"
    public static final String SUCCESS_VEHICLE_MATRIX_RESPONSE_CODE = "1008"

    public static final String INVALID_LOGIN_RESPONSE_CODE = "2019"

    // Error when product isnt applicable
    // "Status":{"Code":"2018","Description":"Base Premium is Invalid. So can't generate the Quotation"}

    private String loginId
    private String password
    private String host

    def grailsApplication
    def providerApiService
    def providerTokenService

    List<Map<String, String>> headers = []

    @PostConstruct
    private void init() {
        loginId = grailsApplication.config.getProperty("newindia.auth.loginId")
        password = grailsApplication.config.getProperty("newindia.auth.password")
        host = grailsApplication.config.getProperty("newindia.auth.host")
        headers = [
            ["provider": "NIA"]
        ]
    }

    @Transactional(readOnly = true)
    List<ProviderRateCommand> getBaseRateMinPremium(NewIndiaQuoteCommand command) {
        log.info("newIndiaApiService.getBaseRateMinPremium - command:${command}")
        if (!command) return []

        List<NewIndiaRateCommand> rateCommands = []

        boolean is4x4 = command.bodyType == BodyTypeEnum.FOURX4.niaCode

        if (command.isBrandNew) {
            NewIndiaRateCommand agencyRateWithOffRoad = callCreateQuoteAPI(command, AGENCY_NEW_VEHICLES_PRODUCT_CODE)
            rateCommands.add(agencyRateWithOffRoad)
            if (is4x4 && agencyRateWithOffRoad == null) {
                command.showOffRoadCover = false
                NewIndiaRateCommand agencyRateWithOutOffRoad = callCreateQuoteAPI(command, AGENCY_NEW_VEHICLES_PRODUCT_CODE)
                rateCommands.add(agencyRateWithOutOffRoad)
            }
        } else {
            command.showOffRoadCover = true
            NewIndiaRateCommand nonAgencyWithOffroad = callCreateQuoteAPI(command, NON_AGENCY_PRODUCT_CODE)
            rateCommands.add(nonAgencyWithOffroad)
            if (is4x4 && nonAgencyWithOffroad == null) {
                command.showOffRoadCover = false
                NewIndiaRateCommand nonAgencyWithOutOffroad = callCreateQuoteAPI(command, NON_AGENCY_PRODUCT_CODE)
                rateCommands.add(nonAgencyWithOutOffroad)
            }
        }

        return rateCommands
    }

    NewIndiaRateCommand callCreateQuoteAPI(QuoteCommand command, String niaProductId) {
        log.info("newIndiaApiService.callCreateQuoteAPI - callCreateQuoteAPI ${command}")

        try {
            JSONElement resp = createQuoteApi(command, niaProductId, providerTokenService.getToken(ExternalDataSourceEnum.NIA))

            if (resp[0].Status.Code && resp[0].Status.Code == INVALID_LOGIN_RESPONSE_CODE) {
                if (callTokenApi()) {
                    resp = createQuoteApi(command, niaProductId, providerTokenService.getToken(ExternalDataSourceEnum.NIA))
                } else {
                    return null
                }
            }

            if (resp[0].Status.Code != SUCCESS_SAVE_QUOTE_RESPONSE_CODE || (!resp[0].QuotationNo || resp[0].QuotationNo == "")) {
                log.error("newIndiaApiService.callCreateQuoteAPI - Error: ${resp} for command:${command.toString()}")
                return null
            }

            log.info("newIndiaApiService.callCreateQuoteAPI - Recieved response:${resp} for command:${command}")

            CarQuoteCover quoteCover = new CarQuoteCover()
            quoteCover.quote = CarQuote.load(command.quoteId)
            quoteCover.provider = Provider.load(command.providerId)
            quoteCover.covers = resp[0]
            quoteCover.expiryDate = LocalDateTime.now().withTime(23, 59, 59, 999)
            quoteCover.providerQuoteNo = resp[0].QuotationNo
            quoteCover.schemeCode = resp[0].Data.ProdCode
            quoteCover.insuredValue = command.insuredValue
            if (quoteCover.schemeCode in [NON_AGENCY_PRODUCT_CODE, AGENCY_NEW_VEHICLES_PRODUCT_CODE]) {
                quoteCover.coverageType = CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value())
            } else {
                quoteCover.coverageType = CoverageType.load(CoverageTypeEnum.THIRD_PARTY.value())
            }
            quoteCover.save(failOnError: true)

            NewIndiaRateCommand rateCommand = coverDetailsToRateCommand(resp[0], command.insuredValue.longValue())
            log.info("Here are Rate Commands ${rateCommand}")

            return rateCommand
        } catch (Exception e) {
            log.error("newIndiaApi.callCreateQuoteAPI - Failed to execute for command:${command}. ", e)
        }
        return null
    }

    private boolean callTokenApi() {
        log.info("newIndiaApiService.callTokenApi - Calling Token API")

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add('pUserId', loginId)
        map.add('pUserPassword', password)

        JSONElement resp = providerApiService.callApi(host + LOGIN_API, headers, map, HttpMethod.POST,
            MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_XML_VALUE)

        log.info("Token  ${resp[0].Data?.Token}")

        if (resp[0].Status[0].Code == SUCCESS_LOGIN_RESPONSE_CODE){
            String token = resp[0].Data[0]?.Token
            providerTokenService.saveProviderToken(ExternalDataSourceEnum.NIA, token)
            return true
        }else {
            return false
        }

    }

    /**
     * Get Vehicle Matrix detail by NewIndia Model Code and year
     *
     * @param niaModelCode
     * @param year
     * @return
     */
    NewIndiaVehicleMatrix getVehicleMatrix(String niaModelCode, Integer year) {
        log.info("newIndiaApi.getVehicleMatrix - niaModelCode:${niaModelCode}, year:$year")

        if (niaModelCode == null || year == null) {
            return null
        }

        JSONElement resp = callVehicleMatrixApi(niaModelCode, year, providerTokenService.getToken(ExternalDataSourceEnum.NIA))

        if (resp[0].Status.Code && resp[0].Status.Code == INVALID_LOGIN_RESPONSE_CODE) {
            if (callTokenApi()) {
                resp = callVehicleMatrixApi(niaModelCode, year, providerTokenService.getToken(ExternalDataSourceEnum.NIA))
            } else {
                return null
            }
        }

        if (resp[0].Status.Code && resp[0].Status.Code == SUCCESS_VEHICLE_MATRIX_RESPONSE_CODE){
            NewIndiaVehicleMatrix vehicleMatrix = new NewIndiaVehicleMatrix()
            vehicleMatrix.make = resp[0].Data.Make
            vehicleMatrix.model = resp[0].Data.Model
            vehicleMatrix.bodyType = resp[0].Data.BodyType
            vehicleMatrix.noOfSeats = resp[0].Data.NoofSeats
            vehicleMatrix.minimumSumInsured = new BigDecimal(resp[0].Data.MinSI).setScale(0, RoundingMode.CEILING).intValue()
            vehicleMatrix.maximumSumInsured = new BigDecimal(resp[0].Data.MaxSI).setScale(0, RoundingMode.FLOOR).intValue()

            return vehicleMatrix
        }

        return null
    }

    private JSONElement callVehicleMatrixApi(String niaModelCode, Integer year, String loginToken) {
        log.info("newIndiaApi.callVehicleMatrixApi - niaModelCode:$niaModelCode, year:$year")

        String requestBody = """
                {
                    "Authentication": {
                        "Token": "${loginToken}",
                        "UserId": "${loginId}"
                    },
                    "ViewVehicelMatrixData": {
                        "VehModel": "${niaModelCode}",
                        "VehManfYear": "${year}"
                    }
                }"""

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add('pData', requestBody)

        JSONElement resp = providerApiService.callApi(host + VEHICLE_MATRIX_API, headers, map, HttpMethod.POST,
            MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_XML_VALUE)

        return resp
    }

    private JSONElement createQuoteApi(NewIndiaQuoteCommand command, String niaProductId, String loginToken) {
        log.info("newIndiaApi.createQuoteApi - command:${command.toString()} for product:$niaProductId")
        String requestBody = """
                {
                    "Authentication": {
                        "Token": "${loginToken}",
                        "UserId": "${loginId}"
                    },
                    "Data": {
                        "Scheme": "${niaProductId}",
                        "AgentCode": "009726",
                        "PolProduct": "${niaProductId}",
                        "PolPymtMode": "103",
                        "InsrType": "001",
                        "InsrName": "${command.name}",
                        "InsrEmail": "${command.email}",
                        "InsrDob": "${command.dob.toString("dd/MM/YYYY")}",
                        "InsrDrvLicenseNo": "",
                        "InsrDrvLicenseDt": "${command.licenseIssueDate}",
                        "InsrDrvLicenseEmirate": "${command.regEmiratesCode}",
                        "InsrMobileNo": "${command.mobileNumber}",
                        "InsrPhoneNo": "",
                        "InsrNationality": "${command.nationalityCode}",

                        "InsrGender": "M",
                        "VehUsage": "${command.vehicleUseCode}",
                        "VehType": "1001",
                        "VehMiscType": "",
                        "VehManfYear": "${command.manufactureYear.toString()}",
                        "VehMake": "${command.vehicleMakeCode}",
                        "VehModel": "${command.vehicleModelCode}",
                        "VehBodyType": "${command.bodyType}",
                        "VehEngineCC": "",
                        "VehLoadingCap": "0",
                        "VehOffRoadCvr": "${command.showOffRoadCover && command.bodyType == BodyTypeEnum.FOURX4.niaCode ? 'Y' : 'N'}",
                        "VehValue": "${command.insuredValue.toInteger()}",
                        "VehNoOfCylinder": "${command.noOfCylCode}",
                        "VehNoOfPassenger": "${command.noOfPassengers ? command.noOfPassengers : ""}",
                        "VehNoOfDoor": "${command.numberOfDoors}",
                        "VehVariant": "${command.vehicleVariant}",
                        "VehBrandNewYn": "${command.isBrandNew ? 'Y' : 'N'}",
                        "VehDof": "${command.firstRegistrationDate.toString('dd/MM/YYYY')}",
                        "VehAgencyRepYn": "${niaProductId == AGENCY_NEW_VEHICLES_PRODUCT_CODE ? 'Y' :
                                            (niaProductId == DYNATRADE_PRODUCT_CODE ? 'D' : 'N')}",
                        "VehNcdYn": "${command.noClaimsDiscountId != null ? 'Y' : 'N'}",
                        "VehRegnEmirate": "${command.regEmiratesCode}",
                        "VehAddlBodyDesc": "",
                        "IsVehGCCYn": "${command.isNonGccSpec ? 'N' : 'Y'}",
                        "VehPrevClaimHisYn": "${command.claimsInTheLastYear ? 'Y' : 'N'}",
                        "InsrProfession": "004",
                        "InsrEmployer": "ABC"
                    }
                }"""
        //"VehRentACarType":"1008",
        //"VehRentACarDays":"5"
        //"VehAgencyRepYn": "${command.isOldAgency ? 'Y' : 'N'}",

        log.info("newIndiaApiService - Calling Create Quote API")

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add('pData', requestBody)

        String encQuoteId = command.quoteId ? AESCryption.encrypt(command.quoteId + "") : null
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add(["encQuoteId": encQuoteId])

        JSONElement resp = providerApiService.callApi(host + CREATE_QUOTE_API, customHeaders, map, HttpMethod.POST,
            MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_XML_VALUE)

        return resp
    }

    private NewIndiaRateCommand coverDetailsToRateCommand(JSONElement schemeDetails, Long vehicleValue) {
        log.info("newIndiaApiService.coverDetailsToRateCommand - ${schemeDetails}")

        JSONElement coverDetails = schemeDetails.Covers

        NewIndiaRateCommand newIndiaRateCommand = new NewIndiaRateCommand()
        newIndiaRateCommand.quoteNo = schemeDetails.QuotationNo
        newIndiaRateCommand.schemeCode = schemeDetails.Data.ProdCode
        newIndiaRateCommand.productCode = schemeDetails.Data.ProdCode
        newIndiaRateCommand.productDesc = schemeDetails.Data.ProdName
        newIndiaRateCommand.excess = schemeDetails.Deductible

        List<NewIndiaRateCommand.NewIndiaRateCoverCommand> covers = []
        List<NewIndiaRateCommand.NewIndiaRateCoverCommand> optionalCovers = []

        BigDecimal netPremium = 0.0

        for (JSONElement cover in coverDetails){

            String coverFlag = cover.CvrType

            NewIndiaRateCommand.NewIndiaRateCoverCommand coverDetail = new NewIndiaRateCommand.NewIndiaRateCoverCommand()
            coverDetail.description = new NewIndiaRateCommand.CoverData()
            coverDetail.description.eng = cover.Description.Eng
            coverDetail.code = cover.Code
            coverDetail.premium = new BigDecimal(cover.Premium)
            coverDetail.cvrType = coverFlag

            if (coverFlag == "IB" || coverDetail.premium == 0) {
                covers.add(coverDetail)
            } else if(coverFlag == "BC" && coverDetail.premium != 0) {
                netPremium = netPremium.add(new BigDecimal(coverDetail.premium))
            } else if(coverFlag == "DC" && coverDetail.premium != 0) {
                netPremium = netPremium.add(new BigDecimal(coverDetail.premium))
            } else if (coverFlag == "MC") {
                log.info("Cover Flag Premium with MC - ${coverDetail.premium}")
                covers.add(coverDetail)

                if (coverDetail.premium != 0 && cover.Code == CarCoversEnum.RENT_A_CAR_5.niaCode) {
                    //Make RAC optional
                    optionalCovers.add(coverDetail)
                } else {
                    netPremium = netPremium.add(new BigDecimal(coverDetail.premium))
                }
            }
        }

        newIndiaRateCommand.netPremium = netPremium
        newIndiaRateCommand.optionalCovers = optionalCovers
        newIndiaRateCommand.covers = covers
        newIndiaRateCommand.insuredValue = vehicleValue

        log.info("newIndiaApiService.coverDetailsToRateCommand - Net Premium ${netPremium}")

        return newIndiaRateCommand
    }

    /**
     * Call Updated Quote With Plans and Covers API
     *
     * @param quote
     * @param dynamicAddons
     */
    void callUpdateQuoteWithPlanApi(CarQuote quote, def dynamicAddons){
        log.info("newIndiaApiService.callUpdateQuoteWithPlanApi - Entering with quote:${quote.id}, dynamicAddons:${dynamicAddons}")

        CarQuoteCover cover = CarQuoteCover.findByProviderQuoteNoAndProvider(quote.providerPolicyReference,
            Provider.load(InsuranceProviderEnum.NEW_INDIA.id))

        if (cover) {
            String providerProductId = cover.schemeCode
            JSONElement resp = updateQuoteWithPlanAPI(cover.providerQuoteNo, JSON.parse(cover.covers), providerProductId,
                dynamicAddons, quote, providerTokenService.getToken(ExternalDataSourceEnum.NIA))

            if (resp[0].Status.Code && resp[0].Status.Code == INVALID_LOGIN_RESPONSE_CODE){
                if (callTokenApi()){
                    callUpdateQuoteWithPlanApi(quote, dynamicAddons)
                } else {
                    return
                }
            }

            if (resp[0].Status.Code != SUCCESS_SAVE_QUOTE_WITH_PLAN_RESPONSE_CODE){
                log.error("newIndiaApiService.callUpdateQuoteWithPlanApi - Error: ${resp} for quote:${quote}")
                return
            }
        }

    }

    /**
     * Save Quote with Plan
     * @param quoteNo
     * @param coverDetails
     * @param providerProductCode
     * @param dynamicAddons
     * @param quote
     * @param token
     * @return
     */
    private def updateQuoteWithPlanAPI(String quoteNo, JSONElement coverDetails, String providerProductCode, def dynamicAddons, CarQuote quote, String token){

        String requestBody = """
                {
                    "Authentication": {
                        "Token": "${token}",
                        "UserId": "${loginId}"
                    },
                    "SelectedCoverData": [{
                        "QuotNo": "${quoteNo}",
                        "ProdCode": "${providerProductCode}",
                        "SelectedCovers" : [
                            ${includeOptionalCovers(coverDetails, dynamicAddons)}
                        ]
                    }]
                }"""
        log.info("Update Quote with Plan Request Body ${requestBody}")

        String encQuoteId = AESCryption.encrypt(quote.id + "")
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add(["encQuoteId": encQuoteId])
        log.info("newIndiaApiService - Calling Save Quote API")

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add('pData', requestBody)

        JSONElement resp
        resp = providerApiService.callApi(host + SAVE_QUOTE_WITH_PLAN_API, customHeaders, map, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_XML_VALUE)

        return resp
    }

    /**
     * Create List of covers to be sent with updateQuoteWithPlan API
     * @param coverDetails
     * @param dynamicAddons
     * @return
     */
    private String includeOptionalCovers(JSONElement coverDetails, def dynamicAddons){
        String allCovers = ""
        for (JSONElement cover in coverDetails?.Covers){
            if (cover?.CvrType in ["BC", "IB", /*"MC", */"DC"] || cover?.Premium == "0" || cover?.CvrType in dynamicAddons) {
                String includedCover = """{
                            "Code": "${cover?.Code}",
                            "CvrType": "${cover?.CvrType}",
                            "Premium": "${cover?.Premium}"
                            },"""

                allCovers = allCovers +  includedCover
            }
        }
        return allCovers.substring(0, allCovers.length() - 1)
    }

}


