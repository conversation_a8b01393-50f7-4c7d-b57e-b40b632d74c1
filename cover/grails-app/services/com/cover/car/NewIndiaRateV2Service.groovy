package com.cover.car

import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.nia.NewIndiaQuoteCommand
import com.cover.car.nia.NewIndiaRateCommand
import com.cover.car.nia.NewIndiaVehicleMatrix
import com.safeguard.AddonCodeEnum
import com.safeguard.BodyTypeEnum
import com.safeguard.CarMakeEnum
import com.safeguard.City
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.Product
import com.safeguard.Provider
import com.safeguard.ValuationSourceEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CarValuation
import com.safeguard.car.CoverageType
import com.safeguard.car.CarValuationDto
import com.safeguard.car.vehicle.CityExternal
import com.safeguard.car.vehicle.CountryExternal
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.MakeExternal
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelExternal
import com.safeguard.car.vehicle.VehicleCylindersNumberExternal
import com.safeguard.car.vehicle.VehicleEngineSizeExternal
import grails.converters.JSON
import grails.transaction.Transactional
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime


@Transactional
class NewIndiaRateV2Service extends BaseRatingService {

    def newIndiaApiService
    def ratingService
    def valuationService

    public static final Integer PROVIDER_ID = 51

    public static final String VEHICLE_USE_CODE = "001" //FOR PRIVATE VEHICLES ONLY

    /*
    public static final Integer AGENCY_REPAIR_PRODUCT_ID = 5103 //100129 -- Mainly newly launched Vehicles
    public static final Integer DYNATRADE_REPAIR_PRODUCT_ID = 5102 //100167
    public static final Integer NON_AGENCY_REPAIR_2023_PRODUCT_ID = 5101  //100169
    */

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("NewIndiaRateV2Service.getRates - QuoteCommand ${quoteCommand}")

        try {
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return []
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE)
            if (!checkEligibility) {
                return []
            }

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            NewIndiaQuoteCommand newIndiaQuoteCommand = toNewIndiaQuoteCommand(quoteCommand, quote)
            if (!newIndiaQuoteCommand){
                return []
            }

            List<ProviderRateCommand> newIndiaRateCommands = []

            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(
                    false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now(),
                    CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value()))


            if (carQuoteCovers.size() > 0) {
                log.info("NewIndiaRateV2Service.getRates - QuoteCommand ${quoteCommand} - Cover found in db")
                carQuoteCovers.each {
                    if (!(it.schemeCode in newIndiaRateCommands*.schemeCode)) {
                        newIndiaRateCommands.add(newIndiaApiService.coverDetailsToRateCommand(JSON.parse(it.covers), it.insuredValue))
                    }
                }
            } else {
                log.info("NewIndiaRateV2Service.getRates - QuoteCommand:$quoteCommand, NewQuoteCommand:$newIndiaQuoteCommand, Calling API")
                if (newIndiaQuoteCommand) {
                    def startTime = System.currentTimeMillis()

                    newIndiaRateCommands = newIndiaApiService.getBaseRateMinPremium(newIndiaQuoteCommand)

                    def endTime = System.currentTimeMillis()
                    def elapsedTime = (endTime - startTime)/1000.0
                    log.info("New India API call took ${elapsedTime} seconds against quoteId: ${quoteCommand.quoteId}")
                }
            }
            newIndiaRateCommands.removeAll([null])

            log.info("newIndiaRateV2Service.getRates - newIndiaRateCommands:$newIndiaRateCommands")

            List<RateCommand> rateCommands = []
            newIndiaRateCommands.each {
                RateCommand rateCommand = toRateCommand(newIndiaQuoteCommand, it)
                if (rateCommand) {
                    log.info("Rate Command ${rateCommand}")
                    RateCommand command = populateRatings(ratingService, newIndiaQuoteCommand, rateCommand)
                    rateCommands.add(command)
                    log.info("After Populating Rate ${command}")
                } else {
                    log.error("Null rateCommand for $it ")
                }

            }

            return rateCommands

        } catch (Exception e) {
            log.error("newIndiaRateV2Service.getRates - Exception while calling getRates for command:${quoteCommand}", e)
            return []
        }

    }


    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("NewIndiaRateV2Service.gateRate - QuoteCommand ${quoteCommand}")

        try{
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE)
            if (!checkEligibility) {
                return null
            }

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            String providerProductCode = Product.load(quoteCommand.productId).providerProductCode
            CarQuoteCover selectedCarQuoteCover = CarQuoteCover.
                findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndSchemeCode(
                    false, quote, Provider.load(PROVIDER_ID) , LocalDateTime.now(), providerProductCode)

            NewIndiaRateCommand newIndiaRateCommand  = null
            NewIndiaQuoteCommand newIndiaQuoteCommand = toNewIndiaQuoteCommand(quoteCommand, quote)

            if (selectedCarQuoteCover) {
                log.info("Selected Cover is ${selectedCarQuoteCover}")
                newIndiaRateCommand = newIndiaApiService.coverDetailsToRateCommand(JSON.parse(selectedCarQuoteCover.covers), selectedCarQuoteCover.insuredValue)
            } else {
                newIndiaRateCommand = newIndiaApiService.callCreateQuoteAPI(newIndiaQuoteCommand, providerProductCode)
            }

            RateCommand rateCommand = toRateCommand(newIndiaQuoteCommand, newIndiaRateCommand)

            rateCommand = populateRatings(ratingService, newIndiaQuoteCommand, rateCommand)

            return rateCommand

        } catch (Exception e) {
            log.error("NewIndiaRateV2Service.gateRate - Exception in getRate for QuoteCommand:${quoteCommand}", e)
            return null
        }

    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (!isEligible) {
            return isEligible
        }

        if (quoteCommand.isNonGccSpec) {
            return false
        }

        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            return false
        }

        if (quoteCommand.hasClaim) {
            return false
        }

        if (quoteCommand.customerAge < 25 || quoteCommand.customerAge >= 65) {
            return false
        }

        if (quoteCommand.isThirdParty ||
            quoteCommand.insuredValue <= 10000 ||
            quoteCommand.carAge > 15 ||
            quoteCommand.nationalityId == CountryEnum.UAE.id.intValue() ||
            quoteCommand.makeId == CarMakeEnum.MAZDA.id) {
            return false
        }

        Model model = Model.get(quoteCommand.modelId)
        if (model.modelMasterId == 496 && quoteCommand.manufactureYear == 2015) {
            //Toyota Camry
            return false
        }

        if (quoteCommand.makeId == CarMakeEnum.MITSUBISHI.id && quoteCommand.isBrandNew) {
            return false
        }

        if (quoteCommand.vechileTypeId in [VehicleTypeEnum.COUPE.value, VehicleTypeEnum.CONVERTIBLE.value]) {
            return false
        }

        if (quoteCommand.name.indexOf('Limousine') != -1 ||
            quoteCommand.name.indexOf('Luxury') != -1 ||
            quoteCommand.name.indexOf('Tour') != -1 ||
            quoteCommand.name.indexOf('Rent') != -1 ||
            quoteCommand.name.indexOf('Desert') != -1 ||
            quoteCommand.name.indexOf('Taxi') != -1 ||
            quoteCommand.name.indexOf('Nuaimi') != -1 ||
            quoteCommand.name.indexOf('Chemical') != -1 ||
            quoteCommand.name.indexOf('Oil') != -1 ||
            quoteCommand.name.indexOf('Driving') != -1 ||
            quoteCommand.name.indexOf('Luxury') != -1 ||
            quoteCommand.name.indexOf('Jazeera') != -1 ||
            quoteCommand.name.indexOf('Marhaba') != -1 ||
            quoteCommand.name.indexOf('Bello') != -1 ||
            quoteCommand.name.indexOf('Warish') != -1 ||
            quoteCommand.name.indexOf('Humaidi') != -1 ||
            quoteCommand.name.indexOf('Samira Mohammed Ilyass Sultan') != -1 ||
            quoteCommand.name.indexOf('Nurul Ameen Noor Hossain') != -1 ||
            quoteCommand.name.indexOf('Mohamad Ahmad Fares') != -1 ||
            quoteCommand.name.indexOf('Luxurious') != -1 ||
            quoteCommand.name.indexOf('RODRIGUES JOYCELINE PERCY') != -1 ||
            quoteCommand.name.indexOf('Galaxy Shipping') != -1 ||
            quoteCommand.name.indexOf('hazardous') != -1 ||
            quoteCommand.name.indexOf('SHIFT GENERAL') != -1 ||
            quoteCommand.name.indexOf('GAS') != -1 ||
            quoteCommand.name.indexOf('TRAINING') != -1 ||
            quoteCommand.name.indexOf('DRIVERS') != -1) {
            return false
        }

        return isEligible
    }


    private NewIndiaQuoteCommand toNewIndiaQuoteCommand(QuoteCommand quoteCommand, CarQuote quote){
        log.info("newIndiaRateV2Service.toNewIndiaQuoteCommand - Change to NewIndia Quote Command")
        ExternalDataSource niaDataSource = ExternalDataSource.load(ExternalDataSourceEnum.NIA.id)

        Make make = Make.load(quoteCommand.makeId)
        Model model = Model.load(quoteCommand.modelId)
        Country country = Country.load(quoteCommand.nationalityId)
        City city = City.load(quoteCommand.registrationCityId)

        NewIndiaQuoteCommand newIndiaQuoteCommand = NewIndiaQuoteCommand.generateQuoteCommand(quoteCommand)
        MakeExternal makeExternal = MakeExternal.findByExternalDataSourceAndMake(niaDataSource, make)
        ModelExternal modelExternal =  ModelExternal.findByExternalDataSourceAndModel(niaDataSource, model)
        VehicleCylindersNumberExternal cylindersExternal = VehicleCylindersNumberExternal.findByExternalDataSourceAndNoOfCyl(niaDataSource, model.noOfCyl)
        VehicleEngineSizeExternal engineSizeExternal = VehicleEngineSizeExternal.findByExternalDataSourceAndEngineSize(niaDataSource, model.engineSize)

        if (!makeExternal || !modelExternal /*|| !model.niaBodyTypeCode*/){
            return null
        }
        newIndiaQuoteCommand.vehicleMakeCode = makeExternal.externalId
        newIndiaQuoteCommand.vehicleModelCode = modelExternal.externalId
        newIndiaQuoteCommand.nationalityCode = CountryExternal.findByExternalDataSourceAndCountry(niaDataSource, country)?.externalId
        newIndiaQuoteCommand.regEmiratesCode = CityExternal.findByExternalDataSourceAndCity(niaDataSource, city)?.externalId
        newIndiaQuoteCommand.vehicleUseCode = VEHICLE_USE_CODE
        newIndiaQuoteCommand.noOfCylCode = cylindersExternal.externalId
        newIndiaQuoteCommand.vehicleVariant = engineSizeExternal.externalId
        newIndiaQuoteCommand.noOfPassengers = model.numberOfSeats.toString()
        newIndiaQuoteCommand.numberOfDoors = model.numberOfDoors ? model.numberOfDoors.toString() : "0"
        newIndiaQuoteCommand.mobileNumber = formatMobileNumber(quote.mobile)
        newIndiaQuoteCommand.licenseIssueDate = calculateLicenseIssueDate(quoteCommand.localDrivingExperienceId)

        NewIndiaVehicleMatrix vehicleMatrix
        CarValuation carValuation = CarValuation.findByQuoteAndValuationSource(CarQuote.load(quoteCommand.quoteId), ValuationSourceEnum.NIA)

        if (carValuation) {
            vehicleMatrix = new NewIndiaVehicleMatrix()
            vehicleMatrix.maximumSumInsured = carValuation.insuredValueHigh
            vehicleMatrix.minimumSumInsured = carValuation.insuredValueMin
            vehicleMatrix.bodyType = carValuation.bodyType
        } else {
            vehicleMatrix = newIndiaApiService.getVehicleMatrix(newIndiaQuoteCommand.vehicleModelCode, quoteCommand.manufactureYear)

            valuationService.saveCarValuation(quote.id, ValuationSourceEnum.NIA, vehicleMatrix.make,
                null, vehicleMatrix.model, vehicleMatrix.bodyType, vehicleMatrix.maximumSumInsured,
                ((vehicleMatrix.minimumSumInsured + vehicleMatrix.maximumSumInsured) / 2).longValue(),
                vehicleMatrix.minimumSumInsured, quoteCommand.manufactureYear,
                model.engineSizeStr, !quoteCommand.isNonGccSpec, quoteCommand.isBrandNew)
        }

        newIndiaQuoteCommand.bodyType = BodyTypeEnum.findByName(vehicleMatrix.bodyType.replaceAll(" ", "")).niaCode

        if (newIndiaQuoteCommand.insuredValue < vehicleMatrix.minimumSumInsured) {
            newIndiaQuoteCommand.insuredValue = vehicleMatrix.minimumSumInsured
        } else if (newIndiaQuoteCommand.insuredValue > vehicleMatrix.maximumSumInsured) {
            newIndiaQuoteCommand.insuredValue = vehicleMatrix.maximumSumInsured
        }

        return newIndiaQuoteCommand
    }

    String calculateLicenseIssueDate(Integer localDriverExp){
        DrivingExperienceEnum drivingExperience = DrivingExperienceEnum.findById(localDriverExp)
        LocalDate issueDate
        if (drivingExperience == DrivingExperienceEnum.ZERO_TO_SIX_MONTHS){
            issueDate = LocalDate.now()
        }else if (drivingExperience == DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS){
            issueDate = LocalDate.now().minusMonths(6)
        }else {
            issueDate = LocalDate.now().minusYears(drivingExperience.getExperienceInYears())
        }
        log.info("Local Date ${issueDate.toString("dd/MM/YYYY")}")
        return issueDate.toString("dd/MM/YYYY")
    }

    private RateCommand toRateCommand(NewIndiaQuoteCommand quoteCommand, NewIndiaRateCommand providerRateCommand){

        Integer productId = getProductId(providerRateCommand.productCode, providerRateCommand.productDesc)

        log.info("newIndiaRateV2Service.toRateCommand - productId:$productId")
        if (!productId) return null

        RateCommand rateCommand = new RateCommand()
        rateCommand.productId = productId
        rateCommand.providerId = PROVIDER_ID

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand.insuredValue = providerRateCommand.insuredValue

        CarValuation carValuation = CarValuation.findByQuoteAndValuationSource(CarQuote.load(quoteCommand.quoteId), ValuationSourceEnum.NIA)
        rateCommand.carValuation = new CarValuationDto()
        rateCommand.carValuation.valuationMin = carValuation.insuredValueMin
        rateCommand.carValuation.valuationAvg = carValuation.insuredValueMedium
        rateCommand.carValuation.valuationMax = carValuation.insuredValueHigh

        rateCommand.basePremium = providerRateCommand.netPremium
        rateCommand.premium = rateCommand.basePremium
        rateCommand.minPremium = rateCommand.premium
        rateCommand.currency = quoteCommand.currency
        rateCommand.excess = providerRateCommand.excess


        //TODO: Check the Agency Product and make agencyRepair true
        if (providerRateCommand.productCode == newIndiaApiService.AGENCY_NEW_VEHICLES_PRODUCT_CODE) {
            rateCommand.agencyRepair = true
        }

        rateCommand.dynamicAddons = providerRateCommand.optionalCovers.
            findAll {it.premium != 0 }.
            collect {
                [label: it.description.eng, price: it.premium, providerCode: it.code,
                 code: AddonCodeEnum.DYNAMIC_ADDON.code,
                 description: it.description.eng
                ]
            }

        List allCovers = []
        allCovers.addAll(providerRateCommand.covers)
        allCovers.addAll(providerRateCommand.optionalCovers)

        NewIndiaRateCommand.NewIndiaRateCoverCommand agencyRepairCover = allCovers.find {
            it.Code == CarCoversEnum.AGENCY_REPAIR.niaCode }
        rateCommand.agencyRepair = agencyRepairCover ? (agencyRepairCover.getPremium() == 0 || agencyRepairCover.getCvrType() == "IB" ? true : agencyRepairCover.getPremium()) : false

        NewIndiaRateCommand.NewIndiaRateCoverCommand tplCover = allCovers.find {
            it.Code == CarCoversEnum.TPPD_LIMIT.niaCode }
        rateCommand.thirdPartyLiabilityAmount = tplCover ? (tplCover.getPremium() == 0 || tplCover.getCvrType() == "IB" ?
            Integer.parseInt(getCoverLimit(tplCover, CarCoversEnum.TPPD_LIMIT)) : tplCover.getPremium()) : 0
        Float tplInMillion = rateCommand.thirdPartyLiabilityAmount / 1_000_000
        rateCommand.tplMaterialDamageLimit = "Up to AED ${tplInMillion} Million"
        rateCommand.thirdPartyDamageLimit = "Up to AED ${tplInMillion} Million"

        NewIndiaRateCommand.NewIndiaRateCoverCommand rsaCover = allCovers.find {
            it.Code == CarCoversEnum.RSA_SILVER.niaCode}
        rateCommand.breakdownCover = rsaCover ? (rsaCover.getPremium() == 0 || rsaCover.getCvrType() == "IB" ? "silver" : rsaCover.getPremium()) : "none"

        NewIndiaRateCommand.NewIndiaRateCoverCommand pad = allCovers.find {
            it.Code == CarCoversEnum.PAB_DRIVER.niaCode }
        rateCommand.paCover = pad ? (pad.getPremium() == 0 || pad.coverFlag == "IB" ? "yes" : pad.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand pax = allCovers.find {
            it.Code == CarCoversEnum.PAB_PASSENGER.niaCode }
        rateCommand.personalAccidentPax = pax ? (pax.getPremium() == 0 || pax.getCvrType() == "IB"? "yes" : pax.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand natural_calamity = allCovers.find {
            it.Code == CarCoversEnum.NATURAL_CALAMITY.niaCode }
        rateCommand.naturalCalamity = natural_calamity ? (natural_calamity.getPremium() == 0 || natural_calamity.getCvrType() == "IB" ? "yes" : natural_calamity.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand rac = allCovers.find {
            it.Code == CarCoversEnum.REPLACE_CAR.niaCode }
        rateCommand.replacementCar = rac ? (rac.getPremium() == 0 || rac.getCvrType() == "IB" ? "yes" : rac.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand windScreenCover = allCovers.find {
            it.Code == CarCoversEnum.WIND_SCREEN.niaCode }
        rateCommand.windScreenCover = windScreenCover ? (windScreenCover.getPremium() == 0 || windScreenCover.getCvrType() == "IB" ?
            getCoverLimit(windScreenCover, CarCoversEnum.WIND_SCREEN) : windScreenCover.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand personalBelongingsCover = allCovers.find {
            it.Code == CarCoversEnum.PBL.niaCode }
        rateCommand.lossOfPersonalBelongings = windScreenCover ? (personalBelongingsCover.getPremium() == 0 || personalBelongingsCover.getCvrType() == "IB" ? "3000" : windScreenCover.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand emergencyMedicalCover = allCovers.find {
            it.Code == CarCoversEnum.MEDICAL_EMERGENCY.niaCode }
        rateCommand.emergencyMedical = emergencyMedicalCover ? (emergencyMedicalCover.getPremium() == 0 || emergencyMedicalCover.getCvrType() == "IB" ? "2000" : emergencyMedicalCover.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand ownDamageOmanCover = allCovers.find {
            it.Code == CarCoversEnum.OMAN_OD.niaCode }
        rateCommand.damageToYourVehicleOman = ownDamageOmanCover ? (ownDamageOmanCover.getPremium() == 0 || ownDamageOmanCover.getCvrType() == "IB" ? "yes" : ownDamageOmanCover.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand carRegistrationCover = allCovers.find {
            it.Code == CarCoversEnum.CAR_REGISTRATION.niaCode }
        rateCommand.carRegService = carRegistrationCover ? (carRegistrationCover.getPremium() == 0 || carRegistrationCover.getCvrType() == "IB" ? "yes" : carRegistrationCover.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand offroadCover = allCovers.find {
            it.Code == CarCoversEnum.OFF_ROAD_COVER.niaCode }
        rateCommand.offRoadDesertRecovery = offroadCover ? (offroadCover.getPremium() == 0 || offroadCover.getCvrType() == "IB" ? "yes" : offroadCover.getPremium()) : "no"

        NewIndiaRateCommand.NewIndiaRateCoverCommand rac5DaysCover = allCovers.find {
            it.Code == CarCoversEnum.RENT_A_CAR_5.niaCode }
        rateCommand.replacementCar = rac5DaysCover ? (rac5DaysCover.getPremium() == 0 || rac5DaysCover.getCvrType() == "IB" ? "yes" : rac5DaysCover.getPremium()) : "no"

        rateCommand.damageToYourVehicle = "UAE" + (rateCommand.damageToYourVehicleOman ? " & Oman" : "")

        return rateCommand
    }

    private String getCoverLimit(NewIndiaRateCommand.NewIndiaRateCoverCommand cover, CarCoversEnum carCoversEnum) {
        String limit;
        String data = cover.getDescription().getEng()

        switch (carCoversEnum) {
            case CarCoversEnum.TPPD_LIMIT:
                limit = data.replaceAll("THIRD PARTY PROPERTY DAMAGE", "")
                    .replaceAll("AED", "")
                    .replaceAll("\\(", "").replaceAll("\\)", "")
                    .replaceAll(",", "").replaceAll(" ", "")
                break;
            case CarCoversEnum.WIND_SCREEN:
                limit = data.replaceAll("EXCESS WAIVER FOR WINDSCREEN DAMAGE UPTO AED", "")
                    .replaceAll("AED", "")
                    .replaceAll("/", "").replaceAll("-", "")
                    .replaceAll(" ", "")
        }

        return limit;
    }

    private Integer getProductId(String productCode, String productDescription) {
        /*Provider provider = Provider.load(PROVIDER_ID)
        Product product = Product.findByProviderAndSlug(provider, "nia-product-" + productCode)
        if (product) {
            return product.id
        }

        product = new Product(
            version: 1,
            active: true,
            slug: "nia-product-" + productCode,
            nameEn: productDescription,
            nameAr: productDescription,
            descriptionEn:productDescription,
            descriptionAr:productDescription,
            providerProductCode: productCode,
            provider: provider,
            productType: ProductType.load(ProductTypeEnum.CAR.value()),
            type: CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value())

        ).save(failOnError: true)

        return product.id
        */

        Product product = Product.findByProviderAndProviderProductCode(Provider.load(PROVIDER_ID), productCode)

        return product?.id
    }

    CarQuoteCover getCoverByProduct(List<CarQuoteCover> carQuoteCovers, Integer productId) {
        log.info("newIndiaRateV2Service - Product ID - ${productId}")

        Product product = Product.load(productId)
        CarQuoteCover selectedCover = carQuoteCovers.find { it.schemeCode == product.providerProductCode }

        return selectedCover
    }

    /**
     * Save Quote with selected plan and Covers
     * @param quote
     * @param selectedDynamicAddons
     */
    void saveQuoteDetails(CarQuote quote, def selectedDynamicAddons) {
        log.info("newIndiaRateV2S.saveQuoteDetails - quote:${quote.id}, selectedDynamicAddons:${selectedDynamicAddons}")
        try {
            List<String> selectedOptionalAddonsCodes = []
            selectedDynamicAddons.each {
                selectedOptionalAddonsCodes.add(it.providerCode)
            }

            newIndiaApiService.callUpdateQuoteWithPlanApi(quote, selectedOptionalAddonsCodes)

        } catch (Exception e) {
            log.error("newIndiaRateV2Service.saveQuoteDetails - Exception ${e}", e)
        }
    }

    String formatMobileNumber(String mobile) {
        if (mobile.startsWith("+971")) {
            mobile = mobile.substring(4)
        } else if (mobile.startsWith("971")) {
            mobile = mobile.substring(3)
        } else if (mobile.startsWith("00971")) {
            mobile = mobile.substring(5)
        } else if (mobile.startsWith("0")) {
            return mobile
        }

        return "0$mobile"
    }
}


