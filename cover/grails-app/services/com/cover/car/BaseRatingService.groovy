package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.Provider
import grails.transaction.Transactional

@Transactional
class BaseRatingService {

    RateCommand populateRatings(def ratingService, QuoteCommand quoteCommand, RateCommand rateCommand) {
        log.info("baseRating.populateRatings - quoteCommand:$quoteCommand, rateCommand:$rateCommand")

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, rateCommand.providerId)
        /*Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }*/

        rateCommand
    }

    /**
     * Can show provider ratings?
     * Check Provider, it should be active and if allowed to show offline ratings
     *
     * @param providerId
     * @param isOffline
     * @return
     */
    boolean showProviderRatings(Integer providerId, boolean isOffline) {
        Provider provider = Provider.read(providerId)
        if (!provider.isActive()) {
            log.info("showProviderRatings provider ${provider.nameEn} is inactive")
            return false
        }

        log.info("showProviderRatings provider ${provider.nameEn} is active")
        return true
    }
}
