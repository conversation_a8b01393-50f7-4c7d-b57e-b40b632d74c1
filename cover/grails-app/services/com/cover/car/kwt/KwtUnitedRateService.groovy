package com.cover.car.kwt

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CarMakeEnum
import com.safeguard.CityEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.RepairTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * United Insurance Company-kuwait
 * Calculate premium from quote
 *
 */
@Transactional(readOnly = true)
class KwtUnitedRateService {

    def grailsApplication
    def messageSource
    def ratingService

    public static final Integer PROVIDER_ID = 20
    public static final Integer COMPREHENSIVE_PRODUCT_ID = 201

    public static final String MAKE_TYPE1 = "T1"
    public static final String MAKE_TYPE2 = "T2"
    public static final String MAKE_TYPE3 = "T3"
    public static final String MAKE_TYPE4 = "T4"

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            Model model = Model.get(quoteCommand.modelId)

            if (model.make.kwtUicCategory) {
                quoteCommand.carCategory = model.make.kwtUicCategory

                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

                if (applicableRates) {
                    for (rate in applicableRates) {
                        RateCommand rateCommand = populateRatings(quoteCommand, rate)

                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.make.kwtUicCategory

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()

                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = checkMinimumPremium(rateCommand)
        rateCommand = applyCovers(quoteCommand, rateCommand)
        rateCommand = applyExcess(quoteCommand, rateCommand)
        rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        rateCommand
    }


    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand)
        //rateCommand = ratingService.applyExtraDiscount(rateCommand)

        rateCommand
    }


    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        BigDecimal baseRate = getBaseRate(isAgency, quoteCommand, applicableRate)
        BigDecimal minPremium = getMinimumPremium(isAgency, quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            isAgency = true
        }

        isAgency
    }

    private BigDecimal getBaseRate(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        if (applicableRate.carCategory == MAKE_TYPE4 && !quoteCommand.isBrandNew) {
            isAgency ? 3.35 : 3.35
        }
        baseRate
    }

    private BigDecimal getMinimumPremium(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        BigDecimal minPremium  = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        minPremium
    }

    RateCommand checkMinimumPremium(RateCommand rate) {
        // after discount if premium is less then minimum premium then use minimum premium
        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium
        }

        rate
    }


    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        if (quoteCommand.isNonGccSpec || quoteCommand.isPolicyExpired) {
            isEligible = false
        }

        if (!(quoteCommand.makeId in [CarMakeEnum.BMW.id, CarMakeEnum.LAND_ROVER.id, CarMakeEnum.MINI_COOPER.id]) &&
            quoteCommand.customerAge < 23 && quoteCommand.customerAge > 65) {
            isEligible = false
        }

        isEligible
    }

    RateCommand applyCovers(QuoteCommand quoteCommand, RateCommand rateCommand) {

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        Make make = Make.read(quoteCommand.makeId)
        Locale locale = new Locale(quoteCommand.locale)

        List specialFeatures = []

        if (make.kwtUicCategory == MAKE_TYPE1 && quoteCommand.carAge <=3) {
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.windshield", [].toArray(), locale))
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.excess15TotalLoss", [].toArray(), locale))
        } else if (make.kwtUicCategory == MAKE_TYPE1 && quoteCommand.carAge >3) {
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.dentsExcluded", [].toArray(), locale))
        } else if (make.kwtUicCategory == MAKE_TYPE2) {
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.windshield", [].toArray(), locale))
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.dentsExcluded", [].toArray(), locale))
        } else if (make.kwtUicCategory == MAKE_TYPE3) {
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.windshield", [].toArray(), locale))
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.excess25TotalLoss", [].toArray(), locale))
        } else if (make.kwtUicCategory == MAKE_TYPE4) {
            specialFeatures
                .add(messageSource.getMessage("kuwait.uic.specialFeature.windshield", [].toArray(), locale))
        }

        rateCommand.specialFeatures = rateCommand.specialFeatures?.concat(specialFeatures.join("; "))

        return rateCommand
    }

    RateCommand applyExcess(QuoteCommand quoteCommand, RateCommand rateCommand) {

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)

        Model model = Model.read(quoteCommand.modelId)

        Integer maccanSTrimId = 38715
        Integer panameraId = 440
        Integer cayenneId = 437
        Integer caymanId = 438
        Integer boxterId = 436
        //Integer carreraId = Lets do whole 911 model
        //Integer targaId = Lets do whole 911 model
        Integer nine11 = 434

        //Nill excess -	Panamera, Cayenne, Maccan - (S, 4S, GTS)
        if (model.id == maccanSTrimId || model.modelMaster.id in [panameraId, cayenneId]) {
            rateCommand.excess = null
        } else if (model.id in [nine11, caymanId, boxterId]) {
            rateCommand.excess = "75 K.D."
        }

        return rateCommand
    }

    RateCommand applyAdditionalFees(RateCommand rateCommand, QuoteCommand quoteCommand) {
        //Policy Fee
        rateCommand.premium = rateCommand.premium.add(3)

        rateCommand
    }


}
