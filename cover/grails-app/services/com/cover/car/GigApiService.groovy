package com.cover.car

import com.cover.ProviderPaymentGatewayCommand
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.gig.GigQuoteCommand
import com.cover.car.gig.GigRateCommand
import com.fasterxml.jackson.databind.ObjectMapper
import com.safeguard.ClaimPeriodEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.JedisConnectionPool
import com.safeguard.NoClaimDiscount
import com.safeguard.ProductTypeEnum
import com.safeguard.Provider
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.ProviderApiToken
import com.safeguard.payment.ProviderPaymentQuoteMapping
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.DateTime
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap

import javax.annotation.PostConstruct
import javax.sql.DataSource
import java.text.MessageFormat


@Transactional
class GigApiService extends ProviderRatingService {

    //APIs EndPoints
    static final String AUTH_API = "/oauth/token"
    static final String GET_PRODUCTS_API = "/products"
    static final String CREATE_QUOTE_API = "/quotes"
    static final String UPDATE_QUOTE_API = "/quotes/{0}" //0 is Provider QuoteId
    static final String PAYMENT_API = "/apis/pnc/v1/payments"
    static final String VEHICLE_VALUATION_API = "/apis/motor/v1/vehicles/valuations"

    static final Integer INVALID_TOKEN_RESPONSE = 900901
    //static final String SCHEME_CODE = "39722A00004"


    def grailsApplication
    def providerApiService
    def providerTokenService

    String authHost
    String host
    String baseHost
    String grantType
    String clientId
    String clientSecret
    String audience
    String schemeCode
    String paymentHost
    String authorizedPaymentReturnUrl

    List<Map<String, String>> headers = []
//    def jedis = JedisConnectionPool.getConnection()

    @PostConstruct
    private void init() {
        authHost = grailsApplication.config.getProperty("gig.auth.authHost")
        host = grailsApplication.config.getProperty("gig.auth.host")
        baseHost = grailsApplication.config.getProperty("gig.auth.baseHost")

        grantType = grailsApplication.config.getProperty("gig.auth.grantType")
        clientId = grailsApplication.config.getProperty("gig.auth.clientId")
        clientSecret = grailsApplication.config.getProperty("gig.auth.clientSecret")
        audience = grailsApplication.config.getProperty("gig.auth.audience")
        schemeCode = grailsApplication.config.getProperty("gig.auth.schemeCode")
        paymentHost = grailsApplication.config.getProperty("gig.payment.host")
        authorizedPaymentReturnUrl = grailsApplication.config.getProperty("gig.payment.returnUrl")

        headers = [
            ["provider": "GIG"],
            ["opCo": "GULF"],
            ["sourceApplication": "YCUAE"]
        ]
    }


    @Transactional(readOnly = true)
    List<ProviderRateCommand> getBaseRateMinPremium(GigQuoteCommand command) {
        log.info("gigApiService - getBaseRateMinPremium ${command}")

        if (!command) return []

        List<GigRateCommand> rateCommands = []

        rateCommands = callGetProductsApi(command)


        return rateCommands
    }

    @Transactional
    List<GigRateCommand> callGetProductsApi(GigQuoteCommand quoteCommand) {
        log.info("gigApiService.callGetProductsApi - ${quoteCommand}")

        //TODO: Yet to Decide Delete or Keep
        JSONElement resp = getProductsApi(quoteCommand)
        log.info("resp:${resp}")

            CarQuoteCover quoteCover = new CarQuoteCover()
            JSONElement coverDetails = resp
            quoteCover.quote = CarQuote.load(quoteCommand.quoteId)
            quoteCover.provider = Provider.load(quoteCommand.providerId)
            quoteCover.covers = coverDetails
            quoteCover.expiryDate = LocalDateTime.now().plusMinutes(10)
            quoteCover.schemeCode = schemeCode

            quoteCover.insuredValue = quoteCommand.gigValuation ? quoteCommand.gigValuation : quoteCommand.insuredValue
            quoteCover.save(failOnError: true)


        return toGigRateCommand(resp.eligiblePlans, quoteCover.insuredValue)

    }

    private String getTokenApi() {
        log.info("getTokenApi - Starting")
        MultiValueMap<String, String> request = new LinkedMultiValueMap<>();

        request.add('grant_type', grantType)
        request.add('client_id', clientId)
        request.add("client_secret", clientSecret)
        request.add("audience", audience)

        JSONElement resp
        resp = providerApiService.callApi(authHost + AUTH_API, headers, request, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED_VALUE)

        if (resp && resp.access_token && resp.expires_in) {
            log.info("Token is ${resp.access_token}")
            providerTokenService.saveProviderToken(ExternalDataSourceEnum.GIG, resp.access_token, LocalDateTime.now().plusSeconds(resp.expires_in))
            return resp.access_token
        } else {
            return null
        }

    }


     JSONElement getProductsApi(GigQuoteCommand quoteCommand) {
        String token = getToken()

//        String token = jedis.get("gigToken")
        log.info("getProductsApi - ${token}")

        def (firstName, lastName) = quoteCommand.name.split(" ")
        log.info("QuoteCommand DOB - ${quoteCommand.dob}")
         Integer drivingExperienceInYears = DrivingExperienceEnum.findById(quoteCommand.localDrivingExperienceId).experienceInYears
         Integer noClaimDiscountInYears = quoteCommand.noClaimsDiscountId ? NoClaimDiscount.load(quoteCommand.noClaimsDiscountId).years : quoteCommand.lastClaimPeriod == ClaimPeriodEnum.NEVER ?
             drivingExperienceInYears.toString() : quoteCommand.lastClaimPeriod.noClaimYears()
         noClaimDiscountInYears = noClaimDiscountInYears > drivingExperienceInYears ? drivingExperienceInYears : noClaimDiscountInYears
         String requestBody = """
                {
                    "policyHolder": {
                        "person": {
                            "givenName": "${firstName}",
                            "surName": "${lastName}",
                            "birthDate": "${quoteCommand.dob.toString("YYYY-MM-dd")}",
                            "nationality": {
                                "code": "${quoteCommand.nationalityCode}"
                            }
                        },
                        "policyHolderDrivingExperience": "${drivingExperienceInYears.toString()}",
                        "firstDrivingLicenseIssueCountry": {
                            "code": "${quoteCommand.firstDrivingLicenseCountryCode}"
                        },
                        "contactMethods": {
                            "postMailContact": [
                                {
                                    "city": {
                                        "code": "${quoteCommand.contactCityCode}",
                                        "value": "${quoteCommand.cityName}"
                                    },
                                    "country": {
                                        "code": "${quoteCommand.uaeCountryCode}",
                                        "value": "UAE"
                                    }
                                }
                            ],
                            "emailContact": [
                                {
                                    "type": "PERSONAL"
                                }
                            ]
                        }
                    },
                    "policySchedule": {
                        "policyType": "NEW",
                        "creationDate": "${DateTime.now()}"
                    },
                    "motorInformation": {
                        "make": {
                            "code": "${quoteCommand.makeCode}"
                        },
                        "model": {
                            "code": "${quoteCommand.modelCode}"
                        },
                        "version": {
                            "code": "${quoteCommand.trimCode}"
                        },
                        "modelYear": "${quoteCommand.manufactureYear.toString()}",
                        "registrationYear": "${quoteCommand.firstRegistrationDate.year.toString()}",
                        "registrationMonth": "${quoteCommand.firstRegistrationDate.monthOfYear.toString()}",
                        "noClaimDiscountInYears": "${noClaimDiscountInYears}",
                        "noClaimSupportingDocuments": "${quoteCommand.noClaimsDiscountId ? true : false}",
                        "vehicleValue": {
                            "amount": "${quoteCommand.gigValuation ? quoteCommand.gigValuation.toString() : quoteCommand.insuredValue.toString()}",
                            "currencyCode": "AED"
                        },
                        "isModified": "${quoteCommand.isNonGccSpec ? 'true' : 'false'}",
                        "isNonGCCSpecs": "${quoteCommand.isNonGccSpec ? 'true' : 'false'}",
                        "placeOfRegistration": {
                            "code": "${quoteCommand.registrationCityCode}",
                            "value": "${quoteCommand.cityName}"
                        }
                    },
                    "schemeId": "${schemeCode}"
                }
            """

        ObjectMapper objectMapper = new ObjectMapper();

        String encQuoteId = quoteCommand.quoteId ? AESCryption.encrypt(quoteCommand.quoteId + "") : null
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": encQuoteId])
        log.info("Request is ${requestBody}")
        JSONElement resp
        resp = providerApiService.callApi(host + GET_PRODUCTS_API, customHeaders, objectMapper.readValue(requestBody, Object.class), HttpMethod.POST)
        log.info("Response is ${resp}")

        return resp
    }


    List<GigRateCommand> toGigRateCommand(JSONElement plans, Integer insuredValue) {
        List<GigRateCommand> rateCommands = []


        plans.each { plan ->
            if (plan.uwApprovalStatus != "N") {
                log.info("In Plan ${plan}")
                GigRateCommand rateCommand = new GigRateCommand()
                rateCommand.excess = plan.excess.amount
                rateCommand.netPremium = new BigDecimal(plan.premium.premium.amount).setScale(2, BigDecimal.ROUND_HALF_UP)
                rateCommand.productCode = plan.product.code
                rateCommand.productDesc = plan.product.value
                rateCommand.schemeCode = schemeCode
                rateCommand.insuredValue = insuredValue
                List<GigRateCommand.GigRateCoverCommand> covers = []
                plan.covers.each { planCover ->
                    GigRateCommand.GigRateCoverCommand cover = new GigRateCommand.GigRateCoverCommand()
                    cover.name = planCover.name
                    cover.code = planCover.id
                    cover.premium = Math.ceil(planCover.coverPremium.amount).toInteger().toString()
                    cover.isIncluded = planCover.isOptional != true
                    if (planCover.sumInsured) {
                        cover.sumValue = planCover.sumInsured.amount.toString()
                    }
                    covers.add(cover)
                }
                rateCommand.covers = covers
                rateCommands.add(rateCommand)
            }
        }

        return rateCommands
    }


    void createQuote(CarQuote carQuote, GigQuoteCommand quoteCommand, JSONElement selectedCover) {
        log.info("gigApiService - QuoteCommand - ${quoteCommand}")

        JSONElement createQuoteResp = callCreateQuoteApi(carQuote, quoteCommand, selectedCover)
        log.info("Policy Reference is ${createQuoteResp.quoteId}")

        if (createQuoteResp.quoteId && createQuoteResp.selectedPlan) {
            CarQuoteCover quoteCover = new CarQuoteCover()
            JSONElement coverDetails = createQuoteResp
            quoteCover.quote = CarQuote.load(quoteCommand.quoteId)
            quoteCover.provider = Provider.load(quoteCommand.providerId)
            quoteCover.covers = coverDetails
            quoteCover.expiryDate = LocalDateTime.now().plusDays(30)
            quoteCover.schemeCode = schemeCode
            quoteCover.providerQuoteNo = createQuoteResp.quoteId
            quoteCover.insuredValue = quoteCommand.gigValuation ? quoteCommand.gigValuation : quoteCommand.insuredValue
            quoteCover.save(failOnError: true)

            carQuote.providerPolicyReference = createQuoteResp.quoteId
            carQuote.save()
        }

    }

     JSONElement callCreateQuoteApi(CarQuote carQuote, GigQuoteCommand quoteCommand, JSONElement selectedCover) {

        String token = getToken()
//        String token = jedis.get("gigToken")
        log.info("getProductsApi - ${token}")
        def (firstName, lastName) = carQuote.name.split(" ")
         Integer drivingExperienceInYears = DrivingExperienceEnum.findById(carQuote.localExperience.id.intValue()).experienceInYears
         Integer noClaimDiscountInYears = carQuote.noClaimDiscount ? carQuote.noClaimDiscount.years : quoteCommand.lastClaimPeriod == ClaimPeriodEnum.NEVER ?
             drivingExperienceInYears.toString() : quoteCommand.lastClaimPeriod.noClaimYears()
         noClaimDiscountInYears = noClaimDiscountInYears > drivingExperienceInYears ? drivingExperienceInYears : noClaimDiscountInYears

         String requestBody = """
                {
                    "policyHolder": {
                        "person": {
                            "givenName": "${firstName}",
                            "surName": "${lastName}",
                            "birthDate": "${carQuote.dob.toString("YYYY-MM-dd")}",
                            "nationality": {
                                "code": "${quoteCommand.nationalityCode}"
                            }
                        },
                        "policyHolderDrivingExperience": "${drivingExperienceInYears.toString()}",
                        "firstDrivingLicenseIssueCountry": {
                            "code": "${quoteCommand.firstDrivingLicenseCountryCode}"
                        },
                        "contactMethods": {
                            "postMailContact": [
                                {
                                    "city": {
                                        "code": "${quoteCommand.contactCityCode}",
                                        "value": "${quoteCommand.cityName}"
                                    },
                                    "country": {
                                        "code": "${quoteCommand.uaeCountryCode}",
                                        "value": "UAE"
                                    }
                                }
                            ],
                            "phoneContacts": [
                                {
                                    "phoneType": "MOBILE",
                                    "prefixCode": "+971",
                                    "operatorCode": "${carQuote.mobile.take(2)}",
                                    "phoneNumber": "${carQuote.mobile.drop(2)}"
                                }
                            ],
                            "emailContact": [
                                {
                                    "type": "PERSONAL",
                                    "url": "${carQuote.email}"
                                }
                            ]
                        }
                    },
                    "policySchedule": {
                        "policyType": "NEW",
                        "creationDate": "${DateTime.now()}"
                    },
                    "motorInformation": {
                        "make": {
                            "code": "${quoteCommand.makeCode}"
                        },
                        "model": {
                            "code": "${quoteCommand.modelCode}"
                        },
                        "version": {
                            "code": "${quoteCommand.trimCode}"
                        },
                        "modelYear": "${quoteCommand.manufactureYear.toString()}",
                        "registrationYear": "${quoteCommand.firstRegistrationDate.year.toString()}",
                        "registrationMonth": "${quoteCommand.firstRegistrationDate.monthOfYear.toString()}",
                        "noClaimDiscountInYears": "${noClaimDiscountInYears}",
                        "noClaimSupportingDocuments": "${carQuote.noClaimDiscount ? true : false}",
                        "vehicleValue": {
                            "amount": "${quoteCommand.gigValuation ? quoteCommand.gigValuation : quoteCommand.insuredValue.toString()}",
                            "currencyCode": "AED"
                        },
                        "isModified": "${quoteCommand.isNonGccSpec ? 'true' : 'false'}",
                        "isNonGCCSpecs": "${quoteCommand.isNonGccSpec ? 'true' : 'false'}",
                        "placeOfRegistration": {
                            "code": "${quoteCommand.registrationCityCode}",
                            "value": "${quoteCommand.cityName}"
                        }
                    },
                    "selectedPlan" : {
                        "product": {
                            "applicationCode": "",
                            "code": "${selectedCover.product.code}",
                            "value": "${selectedCover.product.value}"
                        },
                        "covers": [
                           ${allCovers(selectedCover.covers)}
                        ]
                    },
                    "schemeId": "${schemeCode}"
                }
            """


        ObjectMapper objectMapper = new ObjectMapper();

        String encQuoteId = quoteCommand.quoteId ? AESCryption.encrypt(quoteCommand.quoteId + "") : null
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": encQuoteId])
        log.info("Request is ${requestBody}")
        JSONElement resp
        resp = providerApiService.callApi(host + CREATE_QUOTE_API, customHeaders, objectMapper.readValue(requestBody, Object.class), HttpMethod.POST)
        log.info("Response is ${resp}")
        return resp

    }

    void updateQuoteApi(GigQuoteCommand quoteCommand, CarQuote carQuote, JSONElement selectedCover, def dynamicAddons, String productCode, String productName) {
        log.info("updateQuoteApi - QuoteCommand ${quoteCommand}")

        String token = getToken()
//        String token = jedis.get("gigToken")
        def (firstName, lastName) = carQuote.name.split(" ")
        Integer drivingExperienceInYears = DrivingExperienceEnum.findById(carQuote.localExperience.id.intValue()).experienceInYears
        Integer noClaimDiscountInYears = carQuote.noClaimDiscount ? carQuote.noClaimDiscount.years : quoteCommand.lastClaimPeriod == ClaimPeriodEnum.NEVER ?
            drivingExperienceInYears.toString() : quoteCommand.lastClaimPeriod.noClaimYears()
        noClaimDiscountInYears = noClaimDiscountInYears > drivingExperienceInYears ? drivingExperienceInYears : noClaimDiscountInYears

        String requestBody = """
                {
                    "policyHolder": {
                        "person": {
                            "givenName": "${firstName}",
                            "surName": "${lastName}",
                            "birthDate": "${carQuote.dob.toString("YYYY-MM-dd")}",
                            "nationality": {
                                "code": "${quoteCommand.nationalityCode}"
                            }
                        },
                        "policyHolderDrivingExperience": "${drivingExperienceInYears.toString()}",
                        "firstDrivingLicenseIssueCountry": {
                            "code": "${quoteCommand.firstDrivingLicenseCountryCode}"
                        },
                        "contactMethods": {
                            "postMailContact": [
                                {
                                    "city": {
                                        "code": "${quoteCommand.contactCityCode}",
                                        "value": "${quoteCommand.cityName}"
                                    },
                                    "country": {
                                        "code": "${quoteCommand.uaeCountryCode}",
                                        "value": "UAE"
                                    }
                                }
                            ],
                            "phoneContacts": [
                                {
                                    "phoneType": "MOBILE",
                                    "prefixCode": "+971",
                                    "operatorCode": "${carQuote.mobile.take(2)}",
                                    "phoneNumber": "${carQuote.mobile.drop(2)}"
                                }
                            ],
                            "emailContact": [
                                {
                                    "type": "PERSONAL",
                                    "url": "${carQuote.email}"
                                }
                            ]
                        }
                    },
                    "policySchedule": {
                        "policyType": "NEW",
                        "creationDate": "${DateTime.now()}"
                    },
                    "motorInformation": {
                        "make": {
                            "code": "${quoteCommand.makeCode}"
                        },
                        "model": {
                            "code": "${quoteCommand.modelCode}"
                        },
                        "version": {
                            "code": "${quoteCommand.trimCode}"
                        },
                        "registrationYear": "${quoteCommand.isBrandNew ? quoteCommand.manufactureYear.toString() : quoteCommand.firstRegistrationDate.year.toString()}",
                        "registrationMonth": "${quoteCommand.isBrandNew ? "01" : quoteCommand.firstRegistrationDate.monthOfYear.toString()}",
                        "noClaimDiscountInYears": "${noClaimDiscountInYears}",
                        "noClaimSupportingDocuments": "${carQuote.noClaimDiscount ? true : false}",
                        "vehicleValue": {
                            "amount": "${quoteCommand.gigValuation ? quoteCommand.gigValuation : quoteCommand.insuredValue.toString()}",
                            "currencyCode": "AED"
                        },
                        "isModified": "${quoteCommand.isNonGccSpec ? 'true' : 'false'}",
                        "isNonGCCSpecs": "${quoteCommand.isNonGccSpec ? 'true' : 'false'}",

                        "placeOfRegistration": {
                            "code": "${quoteCommand.registrationCityCode}",
                            "value": "${quoteCommand.cityName}"
                        }
                    },
                    "selectedPlan" : {
                        "product": {
                            "applicationCode": "",
                            "code": "${productCode}",
                            "value": "${productName}"
                        },
                        "covers": [
                           ${allCovers(selectedCover, dynamicAddons)}
                        ]
                    },
                    "schemeId": "${schemeCode}"
                }
            """


        ObjectMapper objectMapper = new ObjectMapper()
        log.info("Request Body For Update ${requestBody}")
        String encQuoteId = quoteCommand.quoteId ? AESCryption.encrypt(quoteCommand.quoteId + "") : null
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": encQuoteId])
        JSONElement resp
        String endPoint = host + MessageFormat.format(UPDATE_QUOTE_API, carQuote.providerPolicyReference)
        log.info("The EndPoint is ${endPoint}")
        resp = providerApiService.callApi(host + MessageFormat.format(UPDATE_QUOTE_API, carQuote.providerPolicyReference), customHeaders, objectMapper.readValue(requestBody, Object.class), HttpMethod.PUT)
        if (!resp || resp.uwApprovalStatus != "Y"){
            throw new Error("updateQuoteApi - Failed");
        }
    }

    ProviderPaymentGatewayCommand getAuthorizedPaymentLink(Long quoteId, String gigProviderQuoteNumber,
                                                           String newReturnUrl = null) {
        String token = getToken()
        log.info(".getAuthorizedPaymentLink - token:${token}")
        String ycBaseUrl = grailsApplication.config.getProperty('server.cover')
        CarQuote quote = CarQuote.read(quoteId)
        WhiteLabelBrandEnum whiteLabelBrand = WhiteLabelBrandEnum.getWhiteLabelBrandEnumFromRequestSource(quote.requestSource)
        if (whiteLabelBrand && whiteLabelBrand != WhiteLabelBrandEnum.YALLACOMPARE){
            ycBaseUrl = grailsApplication.config.getProperty("whitelabel.domain.${quote.requestSource.name().toLowerCase()}")
        }

        String requestBody = """
            {
                "command": "AUTHORIZATION",
                "payeeReferenceData": {
                "referenceType": "quoteId",
                "referenceValue": "${gigProviderQuoteNumber}"
            },
                "country": "UAE",
                "paymentReturnURL": "${newReturnUrl ? newReturnUrl : ycBaseUrl + authorizedPaymentReturnUrl}"
            }
        """

        ObjectMapper objectMapper = new ObjectMapper();

        String encQuoteId = quoteId ? AESCryption.encrypt(quoteId + "") : null
        List<Map<String, String>> customHeaders = []
        //customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": encQuoteId])
        customHeaders.add(["sourceApplication": "YC"])
        customHeaders.add(["brokerId": "60303"])
        customHeaders.add(["opCo": "UAE"])
        customHeaders.add(["provider": "GIG"])

        log.info(".getAuthorizedPaymentLink - Request is ${requestBody}")
        JSONElement jsonResp
        jsonResp = providerApiService.callApi(paymentHost + PAYMENT_API, customHeaders, objectMapper.readValue(requestBody, Object.class), HttpMethod.POST)
        log.info(".getAuthorizedPaymentLink - Response is ${jsonResp}")

        ProviderPaymentGatewayCommand providerPaymentGatewayCommand = new ProviderPaymentGatewayCommand()
        if (jsonResp.paymentRedirectionRequest) {
            ProviderPaymentQuoteMapping paymentQuoteMapping = ProviderPaymentQuoteMapping.findByQuoteIdAndProductType(quoteId, ProductTypeEnum.CAR)
            if (paymentQuoteMapping == null) {
                paymentQuoteMapping = new ProviderPaymentQuoteMapping()
                paymentQuoteMapping.quoteId = quoteId
                paymentQuoteMapping.productType = ProductTypeEnum.CAR
                paymentQuoteMapping.provider = Provider.read(InsuranceProviderEnum.GIG_INSURANCE.id)
            }
            paymentQuoteMapping.providerPaymentReference = jsonResp.paymentRefNumber
            paymentQuoteMapping.save(failOnError: true)

            providerPaymentGatewayCommand.providerEnum = InsuranceProviderEnum.GIG_INSURANCE
            providerPaymentGatewayCommand.paymentPageHtml = jsonResp.paymentRedirectionRequest
            providerPaymentGatewayCommand.paymentRefNumber = jsonResp.paymentRefNumber
        }

        return providerPaymentGatewayCommand
    }

    private String allCovers(JSONElement covers, def dynamicAddons = null) {
//        log.info("addingCovers - SelectedPlan - ${plan}, Optional Addons -${dynamicAddons}")

        String allCoversAdded = ""

        covers.each {
            if ((it.isSelected == true || Math.ceil(it.coverPremium.amount).toInteger().toString() == "0" || (dynamicAddons != null && it.id in dynamicAddons)) && it.id != CarCoversEnum.NO_CLAIM_DISCOUNT_PROTECTION.gigCode){
                it.isSelected = true

                allCoversAdded = allCoversAdded + it.toString() + ","
            }
        }
        log.info("all Covers ${allCoversAdded}")
        return allCoversAdded.substring(0, allCoversAdded.length() - 1)
    }

    /**
     * Get Vehicle Valuation
     *
     * @param quoteCommand
     */
    def getVehicleValuation(GigQuoteCommand quoteCommand) {
        log.info(".getVehicleValuation - QuoteCommand - ${quoteCommand}")

        JSONElement valuationResp = callVehicleValuationApi(quoteCommand)
        log.info(".getVehicleValuation - Valuation Response: ${valuationResp.valuations}")

        def valuation = null

        if (valuationResp.valuations) {
            valuation = [lowValue: valuationResp.valuations[0].valuation.lowerThreshold,
                         highValue: valuationResp.valuations[0].valuation.upperThreshold,
                         avgValue: valuationResp.valuations[0].valuation.vehicleValue.amount]
        }

        return valuation
    }

    JSONElement callVehicleValuationApi(GigQuoteCommand quoteCommand) {
        String token = getToken()
        String requestBody = """
            {
              "vehiclesInformation": [
                {
                  "vehicle": {
                        "make": {
                            "code": "${quoteCommand.makeCode}"
                        },
                        "model": {
                            "code": "${quoteCommand.modelCode}"
                        },
                        "version": {
                            "code": "${quoteCommand.trimCode}"
                        }
                    },
                  "firstRegistrationDate": "${quoteCommand.firstRegistrationDate.toString('YYYY-MM-dd')}",
                  "policyEffectiveDate": "${quoteCommand.policyStartDate.toString('YYYY-MM-dd')}",
                  "vehicleReference": "1"
                }
              ]
            }
        """
        ObjectMapper objectMapper = new ObjectMapper();

        String encQuoteId = quoteCommand.quoteId ? AESCryption.encrypt(quoteCommand.quoteId + "") : null
        List<Map<String, String>> customHeaders = [
            ["provider": "GIG"],
            ["opCo": "UAE"],
            ["sourceApplication": "YCUAE"]
        ]
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": encQuoteId])

        log.info(".callVehicleValuationApi - Request is ${requestBody}")
        JSONElement resp = providerApiService.callApi(baseHost + VEHICLE_VALUATION_API, customHeaders,
            objectMapper.readValue(requestBody, Object.class), HttpMethod.POST)
        log.info(".callVehicleValuationApi - Response is ${resp}")

        return resp
    }

    private String getToken(){

        String token = providerTokenService.getToken(ExternalDataSourceEnum.GIG)

        if (token){
            return token
        } else {
            String newToken = getTokenApi()
            if (!newToken){
                throw new Exception("Exception While Calling Token Api")
            }
            return newToken
        }
    }
}
