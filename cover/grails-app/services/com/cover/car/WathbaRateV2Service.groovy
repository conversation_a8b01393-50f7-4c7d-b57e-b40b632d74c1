package com.cover.car

import com.RoadsideAssistanceEnum
import com.cover.car.Wathba.WathbaQuoteCommand
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.nia.NewIndiaVehicleMatrix
import com.cover.util.UtilService
import com.safeguard.*
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CarValuation
import grails.converters.JSON
import grails.transaction.Transactional
import org.joda.time.LocalDateTime

@Transactional
class WathbaRateV2Service extends BaseRatingService {

    def ratingService
    def wathbaApiService


    public static final Integer PROVIDER_ID = InsuranceProviderEnum.AL_WATHBA.id

    public static final Integer COMPREHENSIVE_PRODUCT_ID = 5109
    public static final Integer TPL_PRODUCT_ID = 5110

    public static final String AGENCY_PRODUCT_NAME = "AWNIC Comprehensive Agency"
    public static final String GARAGE_PRODUCT_NAME = "AWNIC Comprehensive Non Agency"
    public static final String TPL_PRODUCT_NAME = "AWNIC Third Party Limited"


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - QuoteCommand: ${quoteCommand}, isOffline: ${isOffline}")

        try {
            boolean isShowProviderRatings = showProviderRatings(PROVIDER_ID, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - isShowProviderRatings ${isShowProviderRatings}")
            if (!isShowProviderRatings) {
                return []
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - checkEligibility ${checkEligibility} - autoDataSpecDesc: ${quoteCommand.autoDataSpecDesc}")
            if (!checkEligibility || !quoteCommand.autoDataSpecDesc || quoteCommand.autoDataSpecDesc == "") {
                return []
            }

            List<ProviderRateCommand> wathbaRateCommands = []
            WathbaQuoteCommand wathbaQuoteCommand = WathbaQuoteCommand.generateWathbaQuoteCommand(quoteCommand)
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - wathbaQuoteCommand: ${wathbaQuoteCommand}")

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                    false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now())
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - carQuoteCovers: ${carQuoteCovers?.size()}")

            if (carQuoteCovers.size() > 0) {
                wathbaQuoteCommand.adSumInsured = carQuoteCovers.last().insuredValue
                wathbaRateCommands = wathbaApiService.toWathbaRateCommand(JSON.parse(carQuoteCovers.last().covers), quoteCommand)
            } else {
                wathbaQuoteCommand = setAutoDataInsuredValue(wathbaQuoteCommand)
                if (!wathbaQuoteCommand.adSumInsured) {
                    log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - no trim found in valuation")

                    return []
                }

                def startTime = System.currentTimeMillis()
                wathbaRateCommands = wathbaApiService.getBaseRateMinPremium(wathbaQuoteCommand)
                log.info "${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - executionTime: ${((System.currentTimeMillis() - startTime)) / 1000} seconds - provider: ${InsuranceProviderEnum.AL_WATHBA}, ${InsuranceProviderEnum.AL_WATHBA.id}"
            }
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - wathbaRateCommandsSize ${wathbaRateCommands.size()}")

            List<RateCommand> rateCommands = []
            wathbaRateCommands.each {
                RateCommand rateCommand = toRateCommand(wathbaQuoteCommand, it)
                if (rateCommand) {
                    RateCommand command = populateRatings(ratingService, wathbaQuoteCommand, rateCommand)
                    rateCommands.add(command)
                }
            }
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - rateCommandsSize ${rateCommands.size()}")

            return rateCommands

        } catch (Exception e) {
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - exception ${e}")
            return []
        }
    }


    static boolean checkEligibility(QuoteCommand quoteCommand,
                                    CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE, boolean isOffline) {

        return true
    }

    RateCommand toRateCommand(WathbaQuoteCommand quoteCommand, ProviderRateCommand wathbaRateCommand) {

        Integer productId = getProductId(wathbaRateCommand)
        if (!productId) return null

        Product product = Product.read(productId)
        if (!product.isActive()) return null

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = wathbaRateCommand.insuredValue

        rateCommand.productId = productId
        rateCommand.providerId = PROVIDER_ID

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        rateCommand.agencyRepair = wathbaRateCommand.productName == AGENCY_PRODUCT_NAME
        rateCommand.basePremium = wathbaRateCommand.netPremium
        rateCommand.premium = rateCommand.basePremium
        rateCommand.minPremium = rateCommand.premium
        rateCommand.currency = quoteCommand.currency
        rateCommand.excess = wathbaRateCommand.excess

        rateCommand = applyCovers(rateCommand, wathbaRateCommand)
        // rateCommand.insuredValue = new BigDecimal(quoteCommand.adSumInsured).toInteger()
        rateCommand.insuredValue = quoteCommand.adSumInsured.toInteger()
        rateCommand.dynamicAddons = wathbaRateCommand.optionalCovers.findAll {
            it.premium && (it.premium != "0" && it.premium != "") && it.isOpted == "O"
        }.collect {
            [
                label: it.name, price: new BigDecimal(it.premium), providerCode: it.code,
                code : AddonCodeEnum.DYNAMIC_ADDON.code, description: it.benefits ?: it.name
            ]
        }
        return rateCommand
    }

    static Integer getProductId(ProviderRateCommand wathbaRateCommand) {

        if (wathbaRateCommand.productName in [AGENCY_PRODUCT_NAME, GARAGE_PRODUCT_NAME]) {
            return COMPREHENSIVE_PRODUCT_ID
        } else if (wathbaRateCommand.productName == TPL_PRODUCT_NAME) {
            return TPL_PRODUCT_ID
        }

        return null
    }

    RateCommand applyCovers(RateCommand rateCommand, ProviderRateCommand wathbaRateCommand) {

        List<ProviderRateCommand.RateCoverCommand> allCovers = wathbaRateCommand.covers
        allCovers.addAll(wathbaRateCommand.optionalCovers)

        boolean isComprehensive = wathbaRateCommand.productName != TPL_PRODUCT_NAME
        rateCommand.specialFeatures = rateCommand.specialFeatures != null ? rateCommand.specialFeatures : ""

//        ProviderRateCommand.RateCoverCommand agencyCover = allCovers.find{
//            it.code == CarCoversEnum.AGENCY_REPAIR.wathbaCode }
//        rateCommand.agencyRepair = agencyCover ? !agencyCover.premium || agencyCover.premium == "0" || agencyCover.premium == "" : false


        ProviderRateCommand.RateCoverCommand pax = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.PAB_PASSENGER.wathbaCode : it.code == CarCoversEnum.PAB_PASSENGER.wathbaTplCode
        }
        rateCommand.personalAccidentPax = pax && pax.isOpted != "N" ? ((!pax.premium || pax.premium == "0" || pax.premium == "") ? "yes" : pax.premium) : "no"

        ProviderRateCommand.RateCoverCommand offRoadCover = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.OFF_ROAD_COVER.wathbaCode : it.code == CarCoversEnum.OFF_ROAD_COVER.wathbaTplCode
        }
        rateCommand.offRoadDesertRecovery = offRoadCover && offRoadCover.isOpted != "N" ? ((!offRoadCover.premium || offRoadCover.premium == "0" || offRoadCover.premium == "") ? "yes" : offRoadCover.premium) : "no"

        if (rateCommand.offRoadDesertRecovery == "yes") {
            rateCommand.roadsideAssistances.add("Off-Road Assistance")
            rateCommand.specialFeatures = rateCommand.specialFeatures + "Off-Road Assistance;"
        }

        ProviderRateCommand.RateCoverCommand ambulanceCover = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.AMBULANCE_SERVICE.wathbaCode : it.code == CarCoversEnum.AMBULANCE_SERVICE.wathbaTplCode
        }
        boolean ambulanceCoverIncluded = ambulanceCover && ambulanceCover.isOpted != "N" ? (!ambulanceCover.premium || ambulanceCover.premium == "0" || ambulanceCover.premium == "") : false

        if (ambulanceCoverIncluded) {
            rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.AMBULANCE_SERVICE.nameEn)
            rateCommand.specialFeatures = rateCommand.specialFeatures + ambulanceCover.name + ";"
        }

        ProviderRateCommand.RateCoverCommand naturalClimateCover = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.NATURAL_CALAMITY.wathbaCode : it.code == CarCoversEnum.NATURAL_CALAMITY.wathbaTplCode
        }
        rateCommand.naturalCalamity = naturalClimateCover && naturalClimateCover.isOpted != "N" ? ((!naturalClimateCover.premium || naturalClimateCover.premium == "0" || naturalClimateCover.premium == "") ? "yes" : "no") : "no"

        ProviderRateCommand.RateCoverCommand pad = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.PAB_DRIVER.wathbaCode : it.code == CarCoversEnum.PAB_DRIVER.wathbaTplCode
        }
        rateCommand.paCover = pad && pad.isOpted != "N" ? ((!pad.premium || pad.premium == "0" || pad.premium == "") ? "yes" : pad.premium) : "no"

        ProviderRateCommand.RateCoverCommand pbl = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.PBL.wathbaCode : it.code == CarCoversEnum.PBL.wathbaTplCode
        }
        rateCommand.lossOfPersonalBelongings = pbl && pbl.isOpted != "N" ? ((!pbl.premium || pbl.premium == "0" || pbl.premium == "") ? extractAmount(pbl.benefits) : pbl.premium) : "no"

        ProviderRateCommand.RateCoverCommand windScreenCover = allCovers.find {
            it.code == CarCoversEnum.WIND_SCREEN.wathbaCode
        }
        rateCommand.windScreenCover = windScreenCover && windScreenCover.isOpted != "N" ? ((!windScreenCover.premium || windScreenCover.premium == "0" || windScreenCover.premium == "") ? extractAmount(windScreenCover.benefits) : "no") : "no"

        ProviderRateCommand.RateCoverCommand replacementCarCover = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.RENT_A_CAR_7.wathbaCode : it.code == CarCoversEnum.RENT_A_CAR_7.wathbaTplCode
        }
        rateCommand.replacementCar = replacementCarCover && replacementCarCover.isOpted != "N" ? ((!replacementCarCover.premium || replacementCarCover.premium == "0" || replacementCarCover.premium == "") ? "yes" : replacementCarCover.premium) : "no"

        if (replacementCarCover) {
            rateCommand.carHireBenefitTitle = replacementCarCover.name
        }

        ProviderRateCommand.RateCoverCommand ownDamageOman = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.OMAN_OD.wathbaCode : it.code == CarCoversEnum.OMAN_OD.wathbaTplCode
        }
        rateCommand.damageToYourVehicleOman = ownDamageOman && ownDamageOman.isOpted != "N" ? (!ownDamageOman.premium || ownDamageOman.premium == "0" || ownDamageOman.premium == "") : false

        if (ownDamageOman) {
            rateCommand.omanOwnDamageTitle = ownDamageOman.name
        }

        ProviderRateCommand.RateCoverCommand carRegistration = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.CAR_REGISTRATION.wathbaCode : it.code == CarCoversEnum.CAR_REGISTRATION.wathbaTplCode
        }
        rateCommand.carRegService = carRegistration && carRegistration.isOpted != "N" ? ((!carRegistration.premium || carRegistration.premium == "0" || carRegistration.premium == "") ? "yes" : carRegistration.premium) : "no"

        ProviderRateCommand.RateCoverCommand batteryBoost = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.BATTERY_BOOST.wathbaCode : it.code == CarCoversEnum.BATTERY_BOOST.wathbaTplCode
        }
        boolean batteryBoostService = batteryBoost && batteryBoost.isOpted != "N" ? (!batteryBoost.premium || batteryBoost.premium == "0" || batteryBoost.premium == "") : false

        if (batteryBoostService) {
            rateCommand.roadsideAssistances.add(batteryBoost.name)
            rateCommand.specialFeatures = rateCommand.specialFeatures + batteryBoost.name + ";"
        }

        ProviderRateCommand.RateCoverCommand emergencyFuel = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.EMERGENCY_FUEL.wathbaCode : it.code == CarCoversEnum.EMERGENCY_FUEL.wathbaTplCode
        }
        boolean emergencyFuelService = emergencyFuel && emergencyFuel.isOpted != "N" ? (!emergencyFuel.premium || emergencyFuel.premium == "0" || emergencyFuel.premium == "") : false

        if (emergencyFuelService) {
            rateCommand.roadsideAssistances.add(emergencyFuel.name)
            rateCommand.specialFeatures = rateCommand.specialFeatures + emergencyFuel.name + ";"
        }

        ProviderRateCommand.RateCoverCommand lockOut = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.LOCKOUT_SERVICE.wathbaCode : it.code == CarCoversEnum.LOCKOUT_SERVICE.wathbaTplCode
        }
        boolean lockOutService = lockOut && lockOut.isOpted != "N" ? (!lockOut.premium || lockOut.premium == "0" || lockOut.premium == "") : false

        if (lockOutService) {
            rateCommand.roadsideAssistances.add(lockOut.name)
            rateCommand.specialFeatures = rateCommand.specialFeatures + lockOut.name + ";"
        }

        ProviderRateCommand.RateCoverCommand flatTyre = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.FLAT_TYRE.wathbaCode : it.code == CarCoversEnum.FLAT_TYRE.wathbaTplCode
        }
        boolean flatTyreService = batteryBoost && batteryBoost.isOpted != "N" ? (!flatTyre.premium || flatTyre.premium == "0" || flatTyre.premium == "") : false

        if (flatTyreService) {
            rateCommand.roadsideAssistances.add(flatTyre.name)
            rateCommand.specialFeatures = rateCommand.specialFeatures + flatTyre.name + ";"
        }

        ProviderRateCommand.RateCoverCommand internationalDrivingLicense = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.INTERNATIONAL_LICENSE.wathbaCode : it.code == CarCoversEnum.INTERNATIONAL_LICENSE.wathbaTplCode
        }
        boolean internationalLicenseService = internationalDrivingLicense && internationalDrivingLicense.isOpted != "N" ? (!internationalDrivingLicense.premium || internationalDrivingLicense.premium == "0" || internationalDrivingLicense.premium == "") : false

        if (internationalLicenseService) {
            rateCommand.roadsideAssistances.add(internationalDrivingLicense.name)
            rateCommand.specialFeatures = rateCommand.specialFeatures + internationalDrivingLicense.name + ";"
        }

        ProviderRateCommand.RateCoverCommand mechanicalBreakdown = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.MECHANICAL_BREAKDOWN.wathbaCode : it.code == CarCoversEnum.MECHANICAL_BREAKDOWN.wathbaTplCode
        }
        boolean mechanicalBreakdownService = mechanicalBreakdown && mechanicalBreakdown.isOpted != "N" ? (!mechanicalBreakdown.premium || mechanicalBreakdown.premium == "0" || mechanicalBreakdown.premium == "") : false

        if (mechanicalBreakdownService) {
            rateCommand.roadsideAssistances.add(mechanicalBreakdown.name)
            rateCommand.specialFeatures = rateCommand.specialFeatures + mechanicalBreakdown.name + ";"
        }

        ProviderRateCommand.RateCoverCommand accidentTowing = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.ACCIDENT_TOWING.wathbaCode : it.code == CarCoversEnum.ACCIDENT_TOWING.wathbaTplCode
        }
        boolean accidentTowingService = accidentTowing && accidentTowing.isOpted != "N" ? (!accidentTowing.premium || accidentTowing.premium == "0" || accidentTowing.premium == "") : false

        if (accidentTowingService) {
            rateCommand.roadsideAssistances.add(accidentTowing.name + " - Within City Limits")
            rateCommand.specialFeatures = rateCommand.specialFeatures + accidentTowing.name + " - Within City Limits;"
        }


        ProviderRateCommand.RateCoverCommand fireAndTheft = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.FIRE_AND_THEFT.wathbaCode : it.code == CarCoversEnum.FIRE_AND_THEFT.wathbaTplCode
        }
        boolean fireAndTheftService = fireAndTheft && fireAndTheft.isOpted != "N" ? (!fireAndTheft.premium || fireAndTheft.premium == "0" || fireAndTheft.premium == "") : false

        if (fireAndTheftService) {
            rateCommand.roadsideAssistances.add(fireAndTheft.name)
            rateCommand.specialFeatures = rateCommand.specialFeatures + fireAndTheft.name + ";"
        }

        ProviderRateCommand.RateCoverCommand orangeCard = allCovers.find {
            isComprehensive ? it.code == CarCoversEnum.ORANGE_CARD.wathbaCode : it.code == CarCoversEnum.ORANGE_CARD.wathbaTplCode
        }
        boolean orangeCarService = orangeCard && orangeCard.isOpted != "N" ? (!orangeCard.premium || orangeCard.premium == "0" || orangeCard.premium == "") : false

        if (orangeCarService) {
            rateCommand.specialFeatures = rateCommand.specialFeatures + orangeCard.name + ";"
        }

        //rateCommand.specialFeatures = isComprehensive ? rateCommand.specialFeatures : null

        return rateCommand
    }


    RateCommand getRate(QuoteCommand quoteCommand, Boolean isOffline) {
        log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - isOffline ${isOffline} - quoteCommand ${quoteCommand}")

        try {
            boolean isShowProviderRatings = showProviderRatings(PROVIDER_ID, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - isShowProviderRatings ${isShowProviderRatings}")
            if (!isShowProviderRatings) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - checkEligibility ${checkEligibility}")
            if (!checkEligibility) {
                return null
            }

            WathbaQuoteCommand wathbaQuoteCommand = WathbaQuoteCommand.generateWathbaQuoteCommand(quoteCommand)
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - wathbaQuoteCommand: ${wathbaQuoteCommand}")

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                    false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now())
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - carQuoteCovers: ${carQuoteCovers?.size()}")

            ProviderRateCommand wathbaRateCommand = null

            if (carQuoteCovers.size() > 0) {
                wathbaQuoteCommand.adSumInsured = carQuoteCovers.last().insuredValue
                List<ProviderRateCommand> wathbaRateCommands = wathbaApiService.toWathbaRateCommand(JSON.parse(carQuoteCovers.last().covers), quoteCommand)
                wathbaRateCommand = getRateCommandByProductId(wathbaRateCommands, quoteCommand)
            } else {
                wathbaQuoteCommand = setAutoDataInsuredValue(wathbaQuoteCommand)
                if (!wathbaQuoteCommand.adSumInsured) {
                    log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - no trim found in valuation")
                    return null
                }
                List<ProviderRateCommand> wathbaRateCommands = wathbaApiService.getBaseRateMinPremium(wathbaQuoteCommand)
                wathbaRateCommand = getRateCommandByProductId(wathbaRateCommands, quoteCommand)
            }
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - wathbaRateCommand ${wathbaRateCommand}")

            RateCommand rateCommand = null
            rateCommand = toRateCommand(wathbaQuoteCommand, wathbaRateCommand)
            rateCommand = populateRatings(ratingService, wathbaQuoteCommand, rateCommand)

            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - rateCommand ${rateCommand}")
            return rateCommand
        } catch (Exception e) {
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - exception ${e.getMessage()}")
            return null
        }

    }

    def getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - isOffline ${isOffline} - quoteCommand ${quoteCommand}")

        try {
            boolean isShowProviderRatings = showProviderRatings(PROVIDER_ID, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - isShowProviderRatings ${isShowProviderRatings}")
            if (!isShowProviderRatings) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - checkEligibility ${checkEligibility}, autoDataSpecDesc: ${quoteCommand.autoDataSpecDesc}")
            if (!checkEligibility || !quoteCommand.autoDataSpecDesc) {
                return null
            }


            WathbaQuoteCommand wathbaQuoteCommand = WathbaQuoteCommand.generateWathbaQuoteCommand(quoteCommand)
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - wathbaQuoteCommand: ${wathbaQuoteCommand}")

            List<ProviderRateCommand> wathbaRateCommands = []

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                    false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now())
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - carQuoteCovers: ${carQuoteCovers?.size()}")

            if (carQuoteCovers.size() > 0) {
                wathbaQuoteCommand.adSumInsured = carQuoteCovers.last().insuredValue
                wathbaRateCommands = wathbaApiService.toWathbaRateCommand(JSON.parse(carQuoteCovers.last().covers), quoteCommand)
            } else {
                wathbaQuoteCommand = setAutoDataInsuredValue(wathbaQuoteCommand)
                if (!wathbaQuoteCommand.adSumInsured) {
                    log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - no trim found in valuation")
                    return null
                }
                wathbaRateCommands = wathbaApiService.getBaseRateMinPremium(wathbaQuoteCommand)
            }
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - wathbaRateCommandsSize ${wathbaRateCommands.size()}")

            RateCommand rateCommand = null

            wathbaRateCommands.each {
                if (it.productName == TPL_PRODUCT_NAME) {
                    rateCommand = toRateCommand(wathbaQuoteCommand, it)
                    if (rateCommand) {
                        rateCommand = populateRatings(ratingService, wathbaQuoteCommand, rateCommand)
                    }
                }

            }

            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - rateCommand ${rateCommand}")
            return rateCommand

        } catch (Exception e) {
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - exception ${e.getMessage()}")
            return null
        }

    }


    static ProviderRateCommand getRateCommandByProductId(List<ProviderRateCommand> wathbaRateCommands, QuoteCommand command) {

        if (command.productId == COMPREHENSIVE_PRODUCT_ID && command.selectedRepairType == RepairTypeEnum.AGENCY) {
            wathbaRateCommands.find {
                return it.productName == AGENCY_PRODUCT_NAME
            }
        } else if (command.productId == COMPREHENSIVE_PRODUCT_ID && command.selectedRepairType == RepairTypeEnum.GARAGE) {
            wathbaRateCommands.find {
                return it.productName == GARAGE_PRODUCT_NAME
            }
        } else if (command.productId == TPL_PRODUCT_ID) {
            wathbaRateCommands.find {
                return it.productName == TPL_PRODUCT_NAME
            }
        } else {
            return null
        }
    }


    static String extractAmount(String coverLimit) {
        def regexPattern = /(\d+,\d+)/
        def matcher = (coverLimit =~ regexPattern)

        if (matcher.find()) {
            def amountWithComma = matcher.group(0)
            def amountWithoutComma = amountWithComma.replaceAll(',', '')
            return amountWithoutComma
        } else {
            return ""
        }
    }


    WathbaQuoteCommand setAutoDataInsuredValue(WathbaQuoteCommand command) {
        def adValuation = wathbaApiService.getAutoDataValuation(command)
        log.info("adValuation:${adValuation}")
        log.info("${UtilService.getQuoteLoggingPrefix('setAutoDataInsuredValue', command.quoteId, InsuranceProviderEnum.AL_WATHBA)} - commandInsuredValue: ${command.insuredValue}, wathbaValuation: ${adValuation}")
        if (adValuation.lowValue &&
            adValuation.lowValue <= command.insuredValue && command.insuredValue <= adValuation.highValue) {
            command.adSumInsured = command.insuredValue.toInteger().toString()
            log.info("command.adSumInsured1:${command.adSumInsured}")

        } else if (command.insuredValue < adValuation.lowValue) {
            command.adSumInsured = adValuation.lowValue.toString()
            log.info("command.adSumInsured2:${command.adSumInsured}")

        } else if (command.insuredValue > adValuation.highValue) {
            command.adSumInsured = adValuation.highValue.toString()
            log.info("command.adSumInsured3:${command.adSumInsured}")

        } else {
            command.adSumInsured = null
        }
        log.info("command.adSumInsured final:${command.adSumInsured}")

        return command
    }

}
