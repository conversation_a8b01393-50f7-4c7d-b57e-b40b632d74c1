package com.cover.car

import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.EdataVehicles
import com.safeguard.User
import com.safeguard.car.vehicle.CountryModel
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import com.safeguard.car.vehicle.ModelRta
import com.safeguard.car.vehicle.translation.ModelTranslation
import grails.transaction.Transactional

@Transactional
class VehicleService {

    /**
     * To get list of makes
     *
     * @param lang
     * @return
     */
    @Transactional(readOnly = true)
    def getMakeList (Country country = null) {

        if (!country) {
            country = Country.read(CountryEnum.UAE.id)
        }

        List<Make> makes = Make.executeQuery("""
            SELECT distinct mk FROM CountryModel cm
                JOIN cm.model md
                JOIN md.modelMaster mm
                JOIN mm.make mk
                JOIN cm.country c
            WHERE c.id = :countryId
                AND md.active = :active
                AND mk.active = :active
        """, [countryId:country.id, active:true])

        return makes
    }

    /**
     * To get list of model master
     *
     * @param lang
     * @param make
     * @return List of model master
     */
    @Transactional(readOnly = true)
    def getModelMasterList (String lang, int makeId, int year, Country country = null) {
        log.debug(".getModelMasterList - Entering with params [makeId: $makeId, country:${country?.id}, year: ${year}]")

        if (!country) {
            country = Country.read(CountryEnum.UAE.id)
        }

        def namedParams = [countryId:country.id, makeId: makeId, locale: lang]
        boolean isNotForEgypt = country.id != CountryEnum.EGYPT.id
        if (isNotForEgypt && year != 0) {
            namedParams.year = year
        }

        String query = """
            select distinct m.modelMaster from Model m join m.translations t, CountryModel cm
            where
            m.make.id = :makeId
            ${isNotForEgypt == true && year != 0 ? ' and (m.yearTo is null or m.yearTo >= :year) and m.yearFrom <= :year ' : ' '}
            and m.active = true
            and t.locale = :locale
            and cm.model.id = m.id
            and cm.country.id = :countryId
            order by
            ${lang == "ar" ? ' m.modelMaster.nameAr' : ' m.modelMaster.nameEn ' }
        """

        List<ModelMaster> modelMasterList = ModelMaster.executeQuery(query, namedParams)

        modelMasterList
    }

    /**
     * To get list of models
     *
     * @param lang
     * @param year
     * @param make
     * @return List of models
     */
    @Transactional(readOnly = true)
    def getModelList (String lang, int year, int make, int modelMaster, String rtaModelId, Country country = null) {
        log.debug(".getModelList - Entering with params [year:$year, make:$make, modelMaster:$modelMaster, rtaModel:$rtaModelId, country:${country?.id}]")

        if (!country) {
            country = Country.read(CountryEnum.UAE.id)
        }

        List modelList
        Make makeObject
        ModelMaster modelMasterObject

        if (rtaModelId) {
            Model model = ModelRta.findByRtaModelId(rtaModelId)?.model
            if (model) {
                modelMasterObject = model.modelMaster
                modelMaster = modelMasterObject.id
                make = modelMasterObject.makeId
            } else {
                notify AsyncEventConstants.STORE_MISSING_TRIMS_FOR_RTA_MODEL, [rtaModelId: rtaModelId, year: year]
            }
        }

        if (make && modelMaster) {
            makeObject = Make.read(make)
            modelMasterObject = ModelMaster.read(modelMaster)
        }

        Map<String, Object> namedParams = [countryId: country.id, makeId: make, modelMasterId:modelMaster, locale: lang]
        boolean isNotForEgypt = country.id != CountryEnum.EGYPT.id
        if (isNotForEgypt) {
            namedParams.year = year
        }

        def modelListByQuery = ModelTranslation.executeQuery("""
            select mt.model.id, CONCAT(
                IFNULL(CONCAT(mt.trim, ' | '), ''),
                IFNULL(CONCAT(mt.model.engineSizeStr, ' | '), ''),
                IFNULL(CONCAT(mt.model.bodyType, ' | '), ''),
                IFNULL(CONCAT(mt.model.noOfCyl, ' CYLINDERS | '), ''),
                IFNULL(CONCAT(mt.model.numberOfDoors, ' DOORS | '), ''),
                IFNULL(CONCAT(mt.model.numberOfSeats, ' SEATS'), '')
            )
            from ModelTranslation mt, CountryModel cm
            where mt.model.make.id = :makeId
            and mt.model.modelMaster.id = :modelMasterId
            ${isNotForEgypt ? ' and (mt.model.yearTo is null or mt.model.yearTo >= :year) and (mt.model.yearFrom is null or mt.model.yearFrom <= :year )' : '' }
            and mt.model.active = true
            and mt.locale = :locale
            and cm.model.id = mt.model.id
            and cm.country.id = :countryId
            order by mt.name, mt.trim

        """, namedParams)

        modelList = modelListByQuery.collect { model ->
            def modelId = null
            def trim = null
            if (model.size() == 2) {
                modelId = model[0]
                trim = model[1]
            }
            [modelId: modelId, trim: trim]
        }

        [modelList:modelList, make: makeObject, modelMaster: modelMasterObject]
    }

    /**
     * This method will return max year of the trim
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param make [edata make]
     * @param model [edata model]
     * @param trim [edata trim]
     * @return maxYear [maxYear of the trim]
     */
    @Transactional(readOnly = true)
    Integer getEDataMaxYear(String make, String model, String trim) {
        log.info("VehicleService.getEDataMaxYear - make: ${make}, model: ${model}, trim: ${trim}")

        List maxYear = EdataVehicles.executeQuery("""
            SELECT MAX(edv.modelYear)
            FROM EdataVehicles edv
            GROUP BY edv.make, edv.model, edv.trim
            HAVING edv.make = :make AND edv.model = :model AND edv.trim = :trim
        """, [make: make, model: model, trim: trim])

        log.info("VehicleService.getEDataMaxYear - maxYear: ${maxYear}")
        return maxYear.size() > 0 ? maxYear.get(0) as Integer : null
    }

}
