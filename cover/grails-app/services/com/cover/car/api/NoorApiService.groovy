package com.cover.car.api

import com.cover.car.NoorRateV2Service
import com.cover.car.ProviderRatingService
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.noor.NoorQuoteCommand
import com.cover.car.noor.NoorRateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.ExtraFieldCodeEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.Product
import com.safeguard.Provider
import com.safeguard.QuoteExtraField
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelExternal
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.plugins.rest.client.RestBuilder
import grails.plugins.rest.client.RestResponse
import grails.transaction.Transactional
import org.apache.commons.lang3.StringUtils
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap

import javax.annotation.PostConstruct

@Transactional
class NoorApiService extends ProviderRatingService {

    private static String baseURL
    static final String LOGIN_URL_PATH = "generateRef"
    static final String STORE_DETAILS_URL_PATH = "motorAPIs"
    static final String CALCULATE_VEHICLE_AGE_URL_PATH = "motorAPIfetch"
    static final String PLAN_LISTING_URL_PATH = "premium_planList"
    static final String CALCULATE_PREMIUM_URL_PATH = "rateEngineConfigServlet"
    static final String FETCH_VEHICLE_SPECIFICATION_URL_PATH = "motorAPIfetch"

    static final Integer SESSION_EXPIRY_MINUTES = 25 // session expires in 30 minutes, so another session will be created after 25 minutes

    String sessionId, accessToken
    LocalDateTime sessionStartTime
    RestBuilder restBuilder

    def grailsApplication
    def providerApiService

    @PostConstruct
    void init() {
        String apiUsername = grailsApplication.config.noor.auth.apiUsername
        String apiKey = grailsApplication.config.noor.auth.apiKey
        accessToken = "Basic ${(apiUsername + ":" + apiKey).bytes.encodeBase64().toString()}"
        baseURL = grailsApplication.config.noor.baseURL
        restBuilder = new RestBuilder()
    }

    /**
     * Login to Noor's API. This will create a session which will expire after 30 minutes
     * Another session will be created after 25 minutes.
     */
    String login() {
        String username = grailsApplication.config.noor.auth.username
        String password = grailsApplication.config.noor.auth.password

        String loginURL = "${baseURL}${LOGIN_URL_PATH}"

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "loginUser")
        requestBody.add("email_id", username)
        requestBody.add("pass", password)
        requestBody.add("isB2BUser", "True")
        requestBody.add("usertype", "Third Party User")

        String sessionId = callNoorAPIAndGetCookie(loginURL, requestBody)
        sessionId
    }

    /**
     * Get a list of NoorRateCommand objects to show on the quotes listing page
     * @param noorQuoteCommand
     * @param coverageTypeEnum
     * @return
     */
    List getBaseRateMinPremium(NoorQuoteCommand noorQuoteCommand, Model model, CoverageTypeEnum coverageTypeEnum, Boolean isBrandNewCar) {
        String sessionId = login()

        List<NoorRateCommand> noorRateCommands = []
        String encryptedQuoteId = AESCryption.encrypt(noorQuoteCommand.quoteId.toString())
        String referenceNo = generateRefNo(encryptedQuoteId, sessionId)
        storeDetailsForRefNo(referenceNo, encryptedQuoteId, sessionId)
        storeQuickQuoteInformation(referenceNo, coverageTypeEnum, isBrandNewCar, encryptedQuoteId, sessionId)

        // fetching the vehicle's trim ID from Noor's API
        Integer noorModelId
        String admeid
        (noorModelId, admeid) = fetchModelSpecificationId(referenceNo, noorQuoteCommand, noorQuoteCommand.vMake, noorQuoteCommand.vModel, noorQuoteCommand.vManuf, model, encryptedQuoteId, sessionId)
        noorQuoteCommand.vSpeci = noorModelId
        BigDecimal providerInsuredValue
        JSONElement vehicleOtherInformation = fetchVehicleOtherInformation(noorQuoteCommand, referenceNo, encryptedQuoteId, sessionId, admeid)

        if (vehicleOtherInformation) {
            noorQuoteCommand.noOfCylinders = vehicleOtherInformation.cylinders_val
            if (vehicleOtherInformation.deviation) {
                Integer siTo = vehicleOtherInformation.deviation.siTo
                Integer siFrom = vehicleOtherInformation.deviation.siFrom
                if (noorQuoteCommand.insuredValue > siTo || noorQuoteCommand.insuredValue < siFrom) {
                    providerInsuredValue = (siTo + siFrom) / 2
                }
            }
        }

        saveVehicleAndDriverDetails(referenceNo, noorQuoteCommand, (providerInsuredValue ?: noorQuoteCommand.insuredValue), encryptedQuoteId, sessionId)
        calculateVehicleAge(referenceNo, encryptedQuoteId, sessionId)
        //JSONElement planListing = getPlanListing(referenceNo, encryptedQuoteId)

        List<String> plans = []

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            plans.addAll(getComprehensivePlanSchemeCodeList(model))
        } else {
            Product TPLProduct = Product.read(NoorRateV2Service.THIRD_PARTY_ID)
            if (TPLProduct.active) {
                plans += getSchemeCode(TPLProduct, null, VehicleTypeEnum.findById(model.vehicleTypeId.toInteger()))
            } else {
                return []
            }
        }

        plans.each { String planId ->
            NoorRateCommand noorRateCommand = calculatePremium(referenceNo, planId, noorQuoteCommand, (providerInsuredValue ?: noorQuoteCommand.insuredValue), coverageTypeEnum, encryptedQuoteId, sessionId)
            if (noorRateCommand) {
                if (providerInsuredValue) {
                    noorRateCommand.insuredValue = providerInsuredValue
                }
                noorRateCommands += noorRateCommand
            }
        }

        noorRateCommands
    }

    /**
     * Generate a reference no. which will be used in other API calls to Noor.
     * First step in getting quotes from Noor's API.
     * @return
     */
    String generateRefNo(String encryptedQuoteId, sessionId) {
        String generateRefURL = "${baseURL}${LOGIN_URL_PATH}"
        String referenceNo

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "MOTOR_REF_NO")

        JSONElement responseJSON = callNoorAPI(generateRefURL, requestBody, encryptedQuoteId, sessionId)

        referenceNo = responseJSON.ref_number
        referenceNo
    }

    /**
     * Call Noor's API to save the action "register_entry_new_ui"
     * Second step in getting quotes from Noor's API.
     * @param referenceNo
     */
    void storeDetailsForRefNo(String referenceNo, String encryptedQuoteId, String sessionId) {
        String storeDetailsURL = "${baseURL}${STORE_DETAILS_URL_PATH}"

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "register_entry_new_ui")
        requestBody.add("ref_no", referenceNo)

        callNoorAPI(storeDetailsURL, requestBody, encryptedQuoteId, sessionId)
    }

    /**
     * Call Noor's API to save basic information about a quote
     * Third step in getting quotes from Noor's API
     * @param referenceNo
     */
    void storeQuickQuoteInformation(String referenceNo, CoverageTypeEnum coverageTypeEnum, Boolean isBrandNewCar, String encryptedQuoteId, String sessionId) {
        String storeQuickQuoteInfoURL = "${baseURL}${STORE_DETAILS_URL_PATH}"

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "quotationPolicyDetail")
        requestBody.add("ref_no", referenceNo)
        requestBody.add("policy_type", coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE ? "Comprehensive" : "TPL")
        requestBody.add("business_type", isBrandNewCar ? "New" : "Used")
        requestBody.add("ref_no", referenceNo)
        requestBody.add("email_id", grailsApplication.config.noor.auth.username)
        requestBody.add("vehicle_type", "Private")

        callNoorAPI(storeQuickQuoteInfoURL, requestBody, encryptedQuoteId, sessionId)
    }

    /**
     * Call Noor's API to save detailed information about vehicle and driver
     * Fourth step in getting quotes from Noor's API
     * @param referenceNo
     * @param noorQuoteCommand
     */
    void saveVehicleAndDriverDetails(String referenceNo, NoorQuoteCommand noorQuoteCommand, BigDecimal insuredValue, String encryptedQuoteId, String sessionId) {
        String saveVehicleAndDriverDetailsURL = "${baseURL}${STORE_DETAILS_URL_PATH}"

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "basic_details_new_ui")
        requestBody.add("ref_no", referenceNo)
        requestBody.add("v_make", noorQuoteCommand.vMake.toString())
        requestBody.add("v_model", noorQuoteCommand.vModel.toString())
        requestBody.add("v_manuf", noorQuoteCommand.vManuf.toString())
        requestBody.add("v_speci", noorQuoteCommand.vSpeci.toString())
        requestBody.add("v_body", noorQuoteCommand.vBody.toString())
        requestBody.add("no_of_cylinders", noorQuoteCommand.noOfCylinders)
        requestBody.add("coverdPassengers", noorQuoteCommand.coverdpassengers.toString())
        requestBody.add("seats", noorQuoteCommand.seats.toString())
        requestBody.add("sum_insured", insuredValue.toString())
        requestBody.add("ncb_year", noorQuoteCommand.ncbYear.toString())
        requestBody.add("dob", noorQuoteCommand.dob.toString("yyyy/MM/dd"))
        requestBody.add("e_date", noorQuoteCommand.eDate)
        requestBody.add("ihc_licence", noorQuoteCommand.ihcLicence.toString())
        requestBody.add("license_country", noorQuoteCommand.licenseCountry)
        requestBody.add("vehicle_weight_load", noorQuoteCommand.vehicleWeightLoad.toString())
        requestBody.add("vehicle_mileage", noorQuoteCommand.vehicleMileage.toString())

        callNoorAPI(saveVehicleAndDriverDetailsURL, requestBody, encryptedQuoteId, sessionId)
    }

    /**
     * Calculate the vehicle's age on Noor's system.
     * Fifth step in getting quotes from Noor's API.
     * @param referenceNo
     */
    void calculateVehicleAge(String referenceNo, String encryptedQuoteId, String sessionId) {
        String calculateVehicleAgeURL = "${baseURL}${CALCULATE_VEHICLE_AGE_URL_PATH}"

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "getVehicleAge")
        requestBody.add("ref_no", referenceNo)

        callNoorAPI(calculateVehicleAgeURL, requestBody, encryptedQuoteId, sessionId)
    }

    /**
     * Get a list of plans from Noor.
     * Sixth step in getting quotes from Noor's API.
     * @param referenceNo
     * @return
     */
    JSONElement getPlanListing(String referenceNo, String encryptedQuoteId, String sessionId) {
        String planListingURL = "${baseURL}${PLAN_LISTING_URL_PATH}?ref_no=${referenceNo}"
        callNoorAPI(planListingURL, null, encryptedQuoteId, sessionId, HttpMethod.GET)
    }

    /**
     * Get a NoorRateCommand object for the given planId
     * Seventh step in getting quotes from Noor's API
     * @param referenceNo
     * @param planId
     * @param noorQuoteCommand
     * @return
     */
    NoorRateCommand calculatePremium(String referenceNo, String planId, NoorQuoteCommand noorQuoteCommand, BigDecimal insuredValue,
                                     CoverageTypeEnum coverageTypeEnum, String encryptedQuoteId, String sessionId, List<String> providerAddonCodes = []) {
        String calculatePremiumURL = "${baseURL}${CALCULATE_PREMIUM_URL_PATH}"
        log.info("Calculating premium from Noor API for quote: ${noorQuoteCommand?.quoteId} ")
        if (!sessionId) {
            sessionId = login()
        }

        MultiValueMap requestBody = new LinkedMultiValueMap()
        requestBody.add("Method", "RATEAPI")
        requestBody.add("refNo", referenceNo)
        requestBody.add("PlanId", planId)
        if (providerAddonCodes) {
            String providerAddonCodesStr = "[{\"coverID\":${providerAddonCodes.toListString()}}]"
            requestBody.add("CoverId", providerAddonCodesStr)
        } else {
            requestBody.add("CoverId", "[]")
        }

        requestBody.add("data", "[]")
        requestBody.add("Rule", "{}")
        requestBody.add("flag", "Y")

        JSONElement responseJSON = callNoorAPI(calculatePremiumURL, requestBody, encryptedQuoteId, sessionId)

        JSONElement currentScheme = responseJSON.schemes[0]

        CarQuoteCover quoteCover = new CarQuoteCover()
        quoteCover.quote = CarQuote.load(noorQuoteCommand?.quoteId)
        quoteCover.provider = Provider.load(noorQuoteCommand?.providerId)
        quoteCover.schemeCode = planId
        quoteCover.covers = responseJSON
        quoteCover.expiryDate = LocalDateTime.now().plusDays(30)
        quoteCover.providerQuoteNo = referenceNo
        quoteCover.insuredValue = insuredValue
        quoteCover.coverageType = CoverageType.load(coverageTypeEnum.value())
        quoteCover.isDeleted = responseJSON.Error == "1"
        quoteCover.save(failOnError: true)
        log.info("CarQuoteCover created for quote; cover: ${quoteCover.id} , quote: ${noorQuoteCommand?.quoteId}")

        if (responseJSON.Error == "1") {
            return null
        }
        NoorRateCommand noorRateCommand = convertSchemeToRateCommand(currentScheme, insuredValue?.toInteger())

        noorRateCommand
    }

    /**
     * Fetch trim from Noor's API
     * @param referenceNo
     * @param noorMakeId
     * @param noorModelId
     * @param noorYearId
     * @param model
     * @return
     */
    List fetchModelSpecificationId(String referenceNo, NoorQuoteCommand noorQuoteCommand,Integer noorMakeId, Integer noorModelId, Integer noorYearId, Model model, String encryptedQuoteId, String sessionId) {
        String fetchModelSpecificationURL = "${baseURL}${FETCH_VEHICLE_SPECIFICATION_URL_PATH}"

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "vehicleSpecification")
        requestBody.add("mk_id", noorMakeId.toString())
        requestBody.add("model_id", noorModelId.toString())
        requestBody.add("vManf_Id", noorYearId.toString())
        requestBody.add("ref_no", referenceNo)

        JSONElement responseJSON = callNoorAPI(fetchModelSpecificationURL, requestBody, encryptedQuoteId, sessionId)
        QuoteExtraField quoteExtraField = QuoteExtraField.findByInsuranceTypeAndQuoteIdAndExtraFieldCode(InsuranceTypeEnum.CAR, noorQuoteCommand.quoteId, ExtraFieldCodeEnum.AUTODATA_SPEC_ID)
        String trimDescription = quoteExtraField.extraFieldDescription
        Long modelIDFromAPI

        for (JSONElement noorModelJSON : responseJSON.data) {
            if (StringUtils.containsIgnoreCase(trimDescription, noorModelJSON.text)) {
                modelIDFromAPI = noorModelJSON.ID.toInteger()
            }
        }

        [modelIDFromAPI, quoteExtraField.extraFieldValue]
    }

    /**
     * Fetch other vehicle information by passign the trim
     * Other information like no. of cylinders, insured value etc.
     * @param noorQuoteCommand
     * @param referenceNo
     * @param encryptedQuoteId
     * @return
     */
    JSONElement fetchVehicleOtherInformation(NoorQuoteCommand noorQuoteCommand, String referenceNo, String encryptedQuoteId, String sessionId, String admeid) {
        String fetchVehicleOtherInformation = "${baseURL}${FETCH_VEHICLE_SPECIFICATION_URL_PATH}"

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<String, String>()
        requestBody.add("action", "otherVehicleDetailsGet")
        requestBody.add("mk_id", noorQuoteCommand.vMake.toString())
        requestBody.add("model_id", noorQuoteCommand.vModel.toString())
        requestBody.add("vManf_Id", noorQuoteCommand.vManuf.toString())
        requestBody.add("spec_type", noorQuoteCommand.vSpeci.toString())
        requestBody.add("sum_insured", "1")
        requestBody.add("ref_no", referenceNo)

        JSONElement responseJSON = callNoorAPI(fetchVehicleOtherInformation, requestBody, encryptedQuoteId, sessionId)

        JSONElement vehicleInformationJSON

        if (responseJSON.request_status_code == 1) {
            if (responseJSON.data.size() > 1) {

                for (JSONElement vehicleData : responseJSON.data) {
                    if (vehicleData.admeid == admeid) {
                        vehicleInformationJSON = vehicleData
                        break
                    }
                }
            } else if (responseJSON.data.size() == 1) {
                vehicleInformationJSON = responseJSON.data[0]
            }
        }

        vehicleInformationJSON
    }

    /**
     * Call Noor's API and save the cookie received in the response
     * @param url
     * @param requestBody
     * @return
     */
    String callNoorAPIAndGetCookie(String url, MultiValueMap requestBody) {

        String sessionId
        RestResponse restResponse = restBuilder.post(url) {
                contentType("application/x-www-form-urlencoded")
                header("Authorization", accessToken)
                header("Cookie", sessionId)
                body requestBody
        }

        List cookieHeader = restResponse.headers.get("Set-Cookie")
        if (cookieHeader) {
            sessionId = cookieHeader[0].split(";")[0]
        }

        sessionId
    }

    /**
     * Call Noor's API using providerApiService.callApi
     * @param url
     * @param requestBody
     * @return
     */
    JSONElement callNoorAPI(String url, MultiValueMap requestBody, String encryptedQuoteId, String sessionId, HttpMethod httpMethod = HttpMethod.POST) {
        List<Map<String, String>> headers = [
            [   Authorization     :   accessToken                               ],
            [   Cookie            :   sessionId                                 ],
            [   encQuoteId        :   encryptedQuoteId                          ],
            [   provider          :   "NT"                                      ]
        ]

        log.info("NoorApiService.callNoorAPI - calling ${url}, encrypted quote ID: ${encryptedQuoteId}, params: ${requestBody.toMapString()}")
        JSONElement responseJSON = providerApiService.callApi(url, headers, requestBody, httpMethod, MediaType.APPLICATION_FORM_URLENCODED_VALUE)

        responseJSON
        //validateAndParseResponse(responseJSON, url)
    }

    /**
     * Validate and parse the response from Noor's API
     * This will throw an exception if the request failed or if there is an error in the result
     * @param restResponse
     * @param url
     * @return
     */
    JSONElement validateAndParseResponse(RestResponse restResponse, String url) {
        JSONElement responseJSON = JSON.parse(restResponse.responseEntity.body.toString())
        /*if (restResponse.statusCode == HttpStatus.OK) {
            responseJSON = JSON.parse(restResponse.responseEntity.body.toString())
        }
        if (restResponse.statusCode != HttpStatus.OK || responseJSON.request_status_code != 1 || responseJSON.Error == "1") {
            throw new Exception("NoorApiService.validateAndParseResponse - API call failed to ${url} ; status code: ${restResponse.statusCode.value()} ; body : ${restResponse.responseEntity.body.toString()}")
        }*/

        responseJSON
    }

    /**
     * Convert schemes in JSON format to NoorRateCommand objects
     * @param schemes
     * @param providerQuoteNo
     * @param insuredValue
     * @return
     */
    List<NoorRateCommand> convertSchemesToRateCommand(JSONElement schemes, Integer insuredValue) {
        List<NoorRateCommand> noorRateCommands = []

        for (JSONElement noorScheme in schemes) {
            noorRateCommands += convertSchemeToRateCommand(noorScheme, insuredValue)
        }

        noorRateCommands
    }

    /**
     * Convert a scheme in JSON format to NoorRateCommand objects
     * @param currentScheme
     * @param insuredValue
     * @return
     */
    NoorRateCommand convertSchemeToRateCommand(JSONElement currentScheme, Integer insuredValue) {
        if (currentScheme.Error == "1") {
            return null
        }

        NoorRateCommand noorRateCommand = new NoorRateCommand()
        noorRateCommand.schemeName = currentScheme.scheme
        noorRateCommand.schemeCode = currentScheme.planId
        noorRateCommand.netPremium = new BigDecimal(currentScheme.totalAED)
        noorRateCommand.excess = currentScheme.DeductAmount
        noorRateCommand.insuredValue = insuredValue

        List<ProviderRateCommand.RateCoverCommand> covers = []
        for (JSONElement cover in currentScheme.covername) {
            ProviderRateCommand.RateCoverCommand rateCoverCommand = new ProviderRateCommand.RateCoverCommand()
            rateCoverCommand.code = cover.coverId
            rateCoverCommand.name = cover.cover
            rateCoverCommand.premium = cover.netpremium
            covers += rateCoverCommand
        }
        noorRateCommand.covers = covers

        List<ProviderRateCommand.RateCoverCommand> optionalCovers = []
        for (JSONElement cover in currentScheme.covernameAddon) {
            ProviderRateCommand.RateCoverCommand rateCoverCommand = new ProviderRateCommand.RateCoverCommand()
            rateCoverCommand.code = cover.coverId
            rateCoverCommand.name = cover.cover
            rateCoverCommand.premium = cover.netpremium
            CarCoversEnum carCoversEnum = CarCoversEnum.findByNoorCode(cover.coverId)
            rateCoverCommand.help = carCoversEnum.tooltipText
            optionalCovers += rateCoverCommand
        }
        noorRateCommand.optionalCovers = optionalCovers

        noorRateCommand
    }

    /**
     * Get a list of applicable scheme codes (Comprehensive) for a specific car model
     * @param model
     * @return
     */
    List<String> getComprehensivePlanSchemeCodeList(Model model) {
        List<String> schemeCodes = []

        [
            NoorRateV2Service.MUMTAZ_ID,
            NoorRateV2Service.EDGE_ID,
            NoorRateV2Service.ALMASA_ID,
            NoorRateV2Service.ASAS_ID
        ].each { Integer id ->
            Product product = Product.read(id)
            if (!product.active) {
                return
            }

            VehicleTypeEnum vehicleTypeEnum = VehicleTypeEnum.findById(model.vehicleTypeId.toInteger())
            if (id == NoorRateV2Service.ASAS_ID) {
                schemeCodes += getSchemeCode(product, null, vehicleTypeEnum)
            } else {
                schemeCodes += getSchemeCode(product, RepairTypeEnum.AGENCY, vehicleTypeEnum)
                schemeCodes += getSchemeCode(product, RepairTypeEnum.GARAGE, vehicleTypeEnum)
            }
        }
        schemeCodes.removeAll([null])
        schemeCodes
    }

    /**
     * Return Noor's scheme code for the given product, repair type and vehicle type
     * @param product
     * @param repairType
     * @param vehicleType
     * @return
     */
    String getSchemeCode(Product product, RepairTypeEnum repairType, VehicleTypeEnum vehicleType) {
        switch (product.id) {
            case NoorRateV2Service.MUMTAZ_ID:
                switch (vehicleType) {
                    case VehicleTypeEnum.FOURx4:
                        if (repairType == RepairTypeEnum.GARAGE) {
                            return NoorRateV2Service.AG_MUMTAZ_SCHEME_CODES[0]
                        } else {
                            return NoorRateV2Service.AG_MUMTAZ_SCHEME_CODES[1]
                        }
                        break
                    case VehicleTypeEnum.COUPE:
                    case VehicleTypeEnum.SPORTS:
                    case VehicleTypeEnum.CONVERTIBLE:
                        if (repairType == RepairTypeEnum.GARAGE) {
                            return NoorRateV2Service.AG_MUMTAZ_SCHEME_CODES[2]
                        } else {
                            return NoorRateV2Service.AG_MUMTAZ_SCHEME_CODES[3]
                        }
                        break
                    case VehicleTypeEnum.SEDAN:
                        if (repairType == RepairTypeEnum.AGENCY) {
                            return NoorRateV2Service.AG_MUMTAZ_SCHEME_CODES[4]
                        } else {
                            return NoorRateV2Service.AG_MUMTAZ_SCHEME_CODES[5]
                        }
                        break
                }
                break
            case NoorRateV2Service.EDGE_ID:
                switch (vehicleType) {
                    case VehicleTypeEnum.FOURx4:
                        if (repairType == RepairTypeEnum.GARAGE) {
                            return NoorRateV2Service.AG_EDGE_SCHEME_CODES[0]
                        } else {
                            return NoorRateV2Service.AG_EDGE_SCHEME_CODES[1]
                        }
                        break
                }
                break
            case NoorRateV2Service.ALMASA_ID:
                switch (repairType) {
                    case RepairTypeEnum.GARAGE:
                        return NoorRateV2Service.AG_ALMASA_SCHEME_CODES[0]
                        break
                    case RepairTypeEnum.AGENCY:
                        return NoorRateV2Service.AG_ALMASA_SCHEME_CODES[1]
                        break
                }
                break
            case NoorRateV2Service.ASAS_ID:
                return NoorRateV2Service.AG_ASAS_SCHEME_CODES[0]
            case NoorRateV2Service.NW_COMPREHENSIVE_SME_ID:

                break
            case NoorRateV2Service.THIRD_PARTY_ID:
                if (vehicleType == VehicleTypeEnum.SEDAN) {
                    return NoorRateV2Service.AG_THIRD_PARTY_LIABILITY_CODES[0]
                } else if (vehicleType == VehicleTypeEnum.FOURx4) {
                    return NoorRateV2Service.AG_THIRD_PARTY_LIABILITY_CODES[1]
                } else if (vehicleType in [VehicleTypeEnum.COUPE, VehicleTypeEnum.SPORTS, VehicleTypeEnum.CONVERTIBLE]) {
                    return NoorRateV2Service.AG_THIRD_PARTY_LIABILITY_CODES[2]
                }
                break
        }
    }
}
