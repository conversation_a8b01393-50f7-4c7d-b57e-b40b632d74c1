package com.cover.car.api

import com.cover.car.ProviderRatingService
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.rak.RakQuoteCommand
import com.cover.car.rak.RakRateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.Provider
import com.safeguard.RepairTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap

import javax.annotation.PostConstruct
import java.lang.reflect.Field

@Transactional
class RakApiService extends ProviderRatingService {

    private static String baseURL
    static final String LOGIN_URL_PATH = "login/authenticate"
    static final String GET_MOTOR_RATING_URL_PATH = "api/motor/getmotorrating"

    private static final String AGENCY_REPAIR_TYPE = "Agency / Dealer Repair"
    private static final String GARAGE_REPAIR_TYPE = "Approved Workshop-Garage"
    private static final String PREMIUM_GARAGE_REPAIR_TYPE = "Premium Garage-Dyna Trade"

    def grailsApplication
    def providerApiService

    private LocalDateTime tokenExpiryTime
    private String token

    @PostConstruct
    void init() {
        baseURL = grailsApplication.config.rak.baseURL
    }

    /**
     * Login to Rak's API.
     */
    String login(String encryptedQuoteId) {
        if (token && tokenExpiryTime && LocalDateTime.now().isBefore(tokenExpiryTime)) {
            return token
        }

        String username = grailsApplication.config.rak.auth.username
        String password = grailsApplication.config.rak.auth.password

        String loginURL = "${baseURL}${LOGIN_URL_PATH}"

        def requestBody = [
            "username" : username,
            "password" : password
        ]

        List headers = getHeadersForProvider(encryptedQuoteId)
        JSONElement loginResponse = providerApiService.callApi(loginURL, headers, requestBody, HttpMethod.POST)
        token = loginResponse?.token
        if (token) {
            tokenExpiryTime = LocalDateTime.now().plusMinutes(55)
        }
        token
    }

    /**
     * Get a list of RakRateCommand objects to show on the quotes listing page
     * @param noorQuoteCommand
     * @param coverageTypeEnum
     * @return
     */
    List<RakRateCommand> getBaseRateMinPremium(RakQuoteCommand rakQuoteCommand) {
        String encryptedQuoteId = AESCryption.encrypt(rakQuoteCommand.quoteId.toString())
        String token = login(encryptedQuoteId)
        if (!token) {
            return []
        }
        List<RakRateCommand> rakRateCommands = []

        /*List<String> repairTypes = [ AGENCY_REPAIR_TYPE,
                                     GARAGE_REPAIR_TYPE,
                                     PREMIUM_GARAGE_REPAIR_TYPE ]*/
        /*for (String repairType in repairTypes) {
            if (rakQuoteCommand.selectedRepairType && rakQuoteCommand.selectedRepairType != getRepairType(repairType)) {
                continue
            }

            if (repairType == AGENCY_REPAIR_TYPE && rakQuoteCommand.manufactureYear < LocalDate.now().year - 2) {
                continue
            }

            if (repairType == PREMIUM_GARAGE_REPAIR_TYPE && rakQuoteCommand.manufactureYear < LocalDate.now().year - 6) {
                continue
            }*/

            rakQuoteCommand.vehicleRepairs = "" //repairType
            JSONElement ratesResultJSON = getRates(token, rakQuoteCommand, encryptedQuoteId)

            if (ratesResultJSON.error && ratesResultJSON.error[0]) {
                Boolean failedWithError = true
                saveCarQuoteCover(ratesResultJSON, rakQuoteCommand, failedWithError)
                //break
            }

            ratesResultJSON.each { JSONElement rateResultJSON ->
                CoverageTypeEnum coverageType = getCoverageType(rateResultJSON.planName)
                //if (coverageType == CoverageTypeEnum.COMPREHENSIVE || repairType == GARAGE_REPAIR_TYPE) {
                    saveCarQuoteCover(rateResultJSON, rakQuoteCommand)
                    if (!rateResultJSON.nstp) {
                        rakRateCommands += toRakRateCommand(rateResultJSON, rakQuoteCommand)
                    }
                //}
            }
        //}

        rakRateCommands
    }

    /**
     * Fetch rates from RAK's endpoint
     * @param token
     * @param rakQuoteCommand
     * @return
     */
    JSONElement getRates(String token, RakQuoteCommand rakQuoteCommand, String encryptedQuoteId) {
        String getRatesURL = "${baseURL}${GET_MOTOR_RATING_URL_PATH}"

        List<Map<String, String>> headers = getHeadersForProvider(encryptedQuoteId)
        headers += getBearerTokenHeader(token)

        Map<String, String> requestBody = [:]

        RakQuoteCommand.declaredFields.each {   Field field ->
            if (!field.isSynthetic() && field.declaringClass.simpleName == RakQuoteCommand.simpleName) {
                requestBody[field.name] = rakQuoteCommand[field.name]
            }
        }

        JSONElement ratesResultJSON = providerApiService.callApi(getRatesURL, headers, requestBody, HttpMethod.POST)
        if (ratesResultJSON.error[0]) {
            ratesResultJSON.error.each { JSONElement errorObj ->
                String warningMsg = errorObj.message
                List<String> warningMsgSplit = warningMsg.split(" ")
                if (warningMsg.startsWith("sumInsured :")) {
                    Float minInsuredValue = Float.parseFloat(warningMsgSplit[8])
                    Float maxInsuredValue = Float.parseFloat(warningMsgSplit[11])
                    BigDecimal leadInsuredValue = rakQuoteCommand.insuredValue
                    BigDecimal insuredValue = leadInsuredValue
                    if (leadInsuredValue < minInsuredValue) {
                        insuredValue = minInsuredValue
                    } else if (leadInsuredValue > maxInsuredValue) {
                        insuredValue = maxInsuredValue
                    }
                    rakQuoteCommand.sumInsured = insuredValue
                    rakQuoteCommand.insuredValue = insuredValue
                    requestBody.sumInsured = insuredValue.toString()
                } else if (warningMsg.startsWith("tplOnly")) {
                    rakQuoteCommand.tplOnly = "Yes"
                    requestBody.tplOnly = rakQuoteCommand.tplOnly
                }
            }
            ratesResultJSON = providerApiService.callApi(getRatesURL, headers, requestBody, HttpMethod.POST)
        }

        ratesResultJSON
    }

    /**
     * Get authorization header to add in API request
     * @param token
     * @return
     */
    Map<String, String> getBearerTokenHeader(String token) {
        [
            Authorization   :   "Bearer ${token}"
        ]
    }

    /**
     * Get specific headers for RAK insurance
     * @param encryptedQuoteId
     * @return
     */
    List<Map<String, String>> getHeadersForProvider(String encryptedQuoteId) {
        [
            [   "provider"          : "RAK"                 ],
            // [   "Content-Type"    : "application/json"    ],
            // [   "user-agent"      : "Application"         ],
            [   "encQuoteId"        : encryptedQuoteId      ]
        ]
    }

    /**
     * Convert response JSON to CarQuoteCover object and save in DB
     * @param responseJSON
     * @param rakQuoteCommand
     * @return
     */
    CarQuoteCover saveCarQuoteCover(def responseJSON, RakQuoteCommand rakQuoteCommand, Boolean failedWithError = false) {
        CarQuoteCover carQuoteCover = new CarQuoteCover()
        carQuoteCover.insuredValue = rakQuoteCommand.insuredValue
        carQuoteCover.repairType = getRepairType(responseJSON.vehicleRepairs)
        carQuoteCover.covers = responseJSON.toString()
        CoverageTypeEnum coverageTypeEnum = failedWithError ? null : getCoverageType(responseJSON.planName)
        carQuoteCover.coverageType = coverageTypeEnum ? CoverageType.load(coverageTypeEnum.value()) : null
        carQuoteCover.provider = Provider.load(InsuranceProviderEnum.RAK_INSURANCE.id)
        carQuoteCover.providerQuoteNo = failedWithError ? null : responseJSON.id
        carQuoteCover.expiryDate = LocalDateTime.now().withTime(23, 59, 59, 999)
        carQuoteCover.quote = CarQuote.load(rakQuoteCommand.quoteId)
        carQuoteCover.isNstp = failedWithError ? null : responseJSON.nstp

        if (failedWithError) {
            carQuoteCover.isDeleted = true
        }

        carQuoteCover.save()
    }

    /**
     * Convert response JSON to RakRateCommand object
     * @param responseJSON
     * @param rakQuoteCommand
     * @return
     */
    RakRateCommand toRakRateCommand(JSONElement responseJSON, RakQuoteCommand rakQuoteCommand) {
        RakRateCommand rakRateCommand = new RakRateCommand()
        rakRateCommand.schemeName = responseJSON.planName
        rakRateCommand.quoteNo = responseJSON.id
        rakRateCommand.netPremium = responseJSON.total
        rakRateCommand.repairType = getRepairType(responseJSON.vehicleRepairs)
        rakRateCommand.insuredValue = rakQuoteCommand.insuredValue
        rakRateCommand.coverageType = getCoverageType(responseJSON.planName)
        rakRateCommand.excess = responseJSON.deductible
        rakRateCommand.covers = []
        rakRateCommand.optionalCovers = []
        responseJSON.covers.each {
            String value = it.values[0].value
            if (value == "Not Covered" || value == "N/A") {
                return
            }

            ProviderRateCommand.RateCoverCommand rateCoverCommand = new ProviderRateCommand.RateCoverCommand()
            rateCoverCommand.code = it.id
            rateCoverCommand.name = it.name
            if (value.startsWith("AED ")) {
                rateCoverCommand.premium = value.split(" ")[1]
                rateCoverCommand.help = it.name
                rakRateCommand.optionalCovers += rateCoverCommand
            } else {
                rateCoverCommand.help = value
                rateCoverCommand.premium = "0"
                rakRateCommand.covers += rateCoverCommand
            }
        }

        rakRateCommand
    }

    /**
     * Get repair type from the string value in Rak API's response
     * @param vehicleRepairType
     * @return
     */
    RepairTypeEnum getRepairType(String vehicleRepairType) {
        switch (vehicleRepairType) {
            case AGENCY_REPAIR_TYPE:
                return RepairTypeEnum.AGENCY
            case GARAGE_REPAIR_TYPE:
                return RepairTypeEnum.GARAGE
            case PREMIUM_GARAGE_REPAIR_TYPE:
                return RepairTypeEnum.PREMIUM_GARAGE
        }
    }

    /**
     * Get coverage type from the string value in Rak API's response
     * @param productName
     * @return
     */
    CoverageTypeEnum getCoverageType(String productName) {
        switch (productName) {
            case "Comprehensive (All Risk Policy)":
            case "Comprehensive (Garage)":
            case "Comprehensive (DynaTrade)":
                return CoverageTypeEnum.COMPREHENSIVE
            case "Third Party Liability (TPL Only)":
                return CoverageTypeEnum.THIRD_PARTY
        }
    }

}
