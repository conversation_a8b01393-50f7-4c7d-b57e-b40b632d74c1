package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.joda.time.LocalDate

/**
 * Ratings calculation for Aman.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class AmanRateService {

    public static final Integer PROVIDER_ID = 27
    public static final Integer AMAN_STANDARD = 1003
    public static final Integer AMAN_PLUS_DYNATRADE = 1016 //Motor Plus  product
    public static final Integer PRODUCT_TPL_ID = 1031

    def grailsApplication
    def ratingService

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            //QuoteCommand newQuoteCommand = customChecks(quoteCommand)
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    if (checkProductEligibility(rate.productId, quoteCommand)) {
                        RateCommand rateCommand = populateRatings(quoteCommand, rate)
                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList

    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            if (checkProductEligibility(quoteCommand.productId, quoteCommand)) {
                //QuoteCommand newQuoteCommand = customChecks(quoteCommand)
                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

                if (applicableRates) {
                    ProductBaseRate rate = applicableRates.first()
                    rateCommand = populateRatings(quoteCommand, rate)
                }
            }
        }

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = applyMinPremiumDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    /*QuoteCommand customChecks(QuoteCommand quoteCommand) {
        QuoteCommand newQuoteCommand = new QuoteCommand()
        InvokerHelper.setProperties(newQuoteCommand, quoteCommand.properties)

        if (quoteCommand.localDrivingExperienceId <= DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId()) {
            // Clients with less than 1 year old valid UAE license will
            // have the same pricing treatment as clients younger than 25 years old
            newQuoteCommand.dob = LocalDate.now().minusYears(24)
        } else if (quoteCommand.customerAge < 25 && quoteCommand.noClaimsDiscountId) {
            // Clients younger than 25 years old will be offered the same rates of the clients who are
            // 25 years old or above only if they can present no claims declaration for previous year (NCD)
            newQuoteCommand.dob = LocalDate.now().minusYears(25)
        }

        newQuoteCommand
    }*/

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = checkMinimumPremium(rateCommand, quoteCommand)
        log.info("aman.populateRatings - premium:${rateCommand.premium}, minPremium:${rateCommand.minPremium}")
        if (rateCommand.premium == rateCommand.minPremium) {
            rateCommand = applyMinPremiumDiscounts(quoteCommand, rateCommand)
        }
        rateCommand = applyLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        Integer vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId.intValue()
        if (rate.productId == AMAN_STANDARD &&
            !(vehicleTypeId in [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.SEDAN.value])) {
            log.info("no breakdown or replacement car under aman standard")
            rateCommand.showBreakdownAddon = false
            rateCommand.breakdownCover = "none"
            rateCommand.showReplacementCarAddon = false
            rateCommand.replacementCar = "no"
        }

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        if (quoteCommand.customerAge < 25) {
            rateCommand.excess = rateCommand.excess + " + 10% of claim"
        }

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        Product product = applicableRate.product

        boolean isAgency = checkAgency(quoteCommand,product.id)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        BigDecimal baseRate =  isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand,int productID) {

        if (!ratingService.allowAgency()){
            return false
        }

        if (quoteCommand.selectedRepairType != null && quoteCommand.selectedRepairType != RepairTypeEnum.AGENCY) {
            return false
        }

        if (productID == AMAN_PLUS_DYNATRADE) {
            return false
        }

        if (quoteCommand.vechileTypeId in [
            VehicleTypeEnum.SEDAN.value,
            VehicleTypeEnum.FOURx4.value,
            VehicleTypeEnum.COUPE.value,
            VehicleTypeEnum.CONVERTIBLE.value] && !quoteCommand.isPolicyExpired &&
            !quoteCommand.hasClaim && (quoteCommand.isBrandNew || quoteCommand.carAge <= 1)) {

            return true
        }

        return false
    }

    RateCommand checkMinimumPremium(RateCommand rateCommand, QuoteCommand quoteCommand) {
        rateCommand.actualBasePremium = rateCommand.premium
        boolean isAgency = checkAgency(quoteCommand,rateCommand.productId)

        if (!isAgency && quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value && quoteCommand.noOfCyl <= 6 && rateCommand.productId == AMAN_STANDARD) {
            // for non agency sedan upto 6 cyl min premium will be 1300
            rateCommand.minPremium = 1300
        }

        // after discount if premium is less then minimum premium then use minimum premium
        if (rateCommand.premium < rateCommand.minPremium) {
            rateCommand.premium = rateCommand.minPremium
            rateCommand.basePremium = rateCommand.minPremium

            rateCommand.productDiscountAmount = null
            rateCommand.productDiscountPercent = null
        }

        rateCommand
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {

        //Old functionality for aman standard
        if (rateCommand.productId == AMAN_STANDARD){
            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()
                && quoteCommand.internationalDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                // 13000 AED loading if less than 1 year driving experience.gi
                rateCommand.premium = rateCommand.premium.add(1300)
            }
        }
        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {
        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.customerAge < 25) {
            return false
        }

        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            if (quoteCommand.isThirdParty || quoteCommand.isPolicyExpired) {
                isEligible = false
            }

            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId() &&
                quoteCommand.insuredValue <= 200000 && quoteCommand.customerAge < 25) {
                isEligible = false
            }

            //no eligible from 11th year
            if (LocalDate.now().getYear() - quoteCommand.manufactureYear  >= 10) {
                isEligible = false
            }

        } else if (productTypeEnum == CoverageTypeEnum.THIRD_PARTY) {
            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                isEligible = false
            }
        }

        Model model = Model.read(quoteCommand.modelId)
        if (model.vehicleTypeId in [VehicleTypeEnum.CONVERTIBLE.value, VehicleTypeEnum.SPORTS.value, VehicleTypeEnum.COUPE.value] &&
            model.numberOfSeats < 4) {
            isEligible = false
        }

        isEligible
    }

    boolean checkProductEligibility(Integer productId, QuoteCommand quoteCommand) {
        boolean isEligible = true

        if (productId == AMAN_STANDARD && quoteCommand.insuredValue >= 250000 &&
            quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id) {
            isEligible = false
        }

        if (productId == AMAN_PLUS_DYNATRADE) {
            if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                isEligible = false
            }
        }

        return isEligible
    }


    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        Integer vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId.toInteger()

        if (vehicleTypeId in [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value]
            && quoteCommand.customerAge >= 25 && !quoteCommand.isPolicyExpired
            && rate.productId == AMAN_STANDARD
            && LocalDate.now() <= new LocalDate("2020-01-31") && quoteCommand.policyStartDate <= new LocalDate("2020-01-31")) {

            if (quoteCommand.insuredValue < 250000) {
                // 20% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
            } else {
                // 10% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
            }

        }

        rate
    }

    RateCommand applyMinPremiumDiscounts(QuoteCommand quoteCommand, RateCommand rate) {
        //No Discount if customer is below 25 or if Brand new
        if (quoteCommand.customerAge < 25 || quoteCommand.isBrandNew) {
            return rate
        }

        if (quoteCommand.noClaimsDiscountId) {
            if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
                // 30% discount covid
                rate.premium = rate.premium.subtract(rate.basePremium * (0.30))
                rate.noClaimDiscountPercent = 30   //10%
            }
        } else if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
            !quoteCommand.isPolicyExpired &&
            quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
            if (noClaimYears >= 1) {
                rate.premium = rate.premium.subtract(rate.basePremium * (0.30))
                rate.noClaimDiscountPercent = 30   //30%
                rate.requiredSelfDeclarationNumber = 1 //At least 1 year self declaration is required
            }
        }

        rate
    }

}

