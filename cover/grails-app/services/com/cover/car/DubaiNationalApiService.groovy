package com.cover.car


import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.dnirc.DnircQuoteCommand
import com.cover.car.dnirc.DnircRateCommand
import com.cover.util.UtilService
import com.safeguard.*
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.CarQuoteCover
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap

import javax.annotation.PostConstruct

@Transactional
class DubaiNationalApiService extends ProviderRatingService {

    static final String TOKEN_API = "/Api/login"
    static final String QUICK_QUOTE = "/QuickQuote/insertQuickQuotation"
    static final String GET_QUOTE_BENEFITS = "/Product/Motor/GetQuoteBenefits"
    static final String UPDATE_QUOTE_BENEFITS = "/Product/Motor/UpdateQuoteBenefits"
    static final String UPDATE_QUICK_QUOTE = "/QuickQuote/updateQuickQuotation"

    static final String COMPREHENSIVE_PLAN_NAME = "Motor Standard"
    static final String THIRD_PARTY_PLAN_NAME = "THIRD PARTY LIABILITY"
    static final String THIRD_PARTY_PLAN_NAME_PROD = "TPL"

    static final Boolean SHOW_MOKE_DATA = false

    def grailsApplication
    def providerApiService
    def providerTokenService

    private String username
    private String password
    private String clientId
    private String host

    List<Map<String, String>> headers = []

    @PostConstruct
    private void init() {
        username = grailsApplication.config.getProperty("dnirc.auth.username")
        password = grailsApplication.config.getProperty("dnirc.auth.password")
        host = grailsApplication.config.getProperty("dnirc.auth.host")
        clientId = grailsApplication.config.getProperty("dnirc.auth.clientId")
        headers = [
            ["provider": "DNIRC"]
        ]
    }


    @Transactional(readOnly = true)
    List<DnircRateCommand> getBaseRateMinPremium(DnircQuoteCommand command) {
        if (command.policyStartDate < LocalDate.now()) {
            command.policyStartDate = LocalDate.now()
        } else if (command.policyStartDate > LocalDate.now().plusDays(15)) {
            return []
        }

        List<DnircRateCommand> dnircRateCommands = callQuickQuoteApi(command)

        return dnircRateCommands
    }

    List<DnircRateCommand> callQuickQuoteApi(DnircQuoteCommand command) {

        JSONElement resp = quickQuoteApi(command)

        if (resp?.response_code && resp?.response_code == "2") {
            def rangePattern = /(\d+) - (\d+)/
            def matcher = (resp.toString() =~ rangePattern)
            if (matcher.find()) {
                def lowerValue = matcher.group(1) as int
                def higherValue = matcher.group(2) as int
                log.info("${UtilService.getQuoteLoggingPrefix('callQuickQuoteApi', command.quoteId, InsuranceProviderEnum.DNIRC)} - commandInsuredValue: ${command.insuredValue}, higherValue: ${higherValue}, lowerValue: ${lowerValue}")

                if (command.insuredValue > higherValue) {
                    command.insuredValue = higherValue
                } else if (command.insuredValue < lowerValue) {
                    command.insuredValue = lowerValue
                }

                resp = quickQuoteApi(command)
            } else {
                return null
            }

        }

        if (resp?.response_code && resp.response_code == HttpStatus.OK.value().toString() && resp?.quotationNumber && resp?.quotationPremiumDetails) {

            CarQuoteCover quoteCover = new CarQuoteCover()
            quoteCover.quote = CarQuote.load(command.quoteId)
            quoteCover.providerQuoteNo = resp?.quotationNumber
            quoteCover.provider = Provider.load(command.providerId)
            quoteCover.covers = resp
            quoteCover.expiryDate = LocalDateTime.now().plusDays(15)
            quoteCover.insuredValue = command.insuredValue.intValue()
            quoteCover.save(flush: true)

            return toProviderRateCommand(resp, command)
        }

        return []

    }

    private String getToken(Long quoteId) {
        String token = providerTokenService.getToken(ExternalDataSourceEnum.DNIRC)

        if (!token) {
            return callTokenApi(quoteId)
        }
        return token
    }

    private String callTokenApi(Long quoteId) {
        MultiValueMap<String, String> request = new LinkedMultiValueMap<>()
        request.add('username', username)
        request.add('password', password)
        request.add('clientid', clientId)
        String url = host + TOKEN_API

        log.info("${UtilService.getQuoteLoggingPrefix('callTokenApi', quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, headers: ${headers}, request: ${request}")

        JSONElement resp
        resp = providerApiService.callApi(url, headers, request, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.TEXT_HTML_VALUE) as JSONElement

        log.info("${UtilService.getQuoteLoggingPrefix('callTokenApi', quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, response: ${resp}")

        String responseCode = resp.response_code
        if (responseCode == "1") {
            log.info("Access Token ${resp?.response_data?.access_token}")
            providerTokenService.saveProviderToken(ExternalDataSourceEnum.DNIRC, resp?.response_data?.access_token, LocalDateTime.now().plusMinutes(30))
            return resp?.response_data?.access_token
        } else {
            log.info("${UtilService.getQuoteLoggingPrefix('callTokenApi', quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, Error Calling Token API")
            return null
        }
    }


    JSONElement quickQuoteApi(DnircQuoteCommand command) {
        String token = getToken(command.quoteId)

        def requestBody = [
            "client_id"              : clientId,
            "insuredNameEn"          : command.insuredNameEn,
            "license_country"        : command.licenseCountry,
            "nationality"            : command.nationality,
            "dob"                    : command.dob.toString("yyyy-MM-dd"),
            "email"                  : command.email,
            "mobile"                 : command.mobile,
            "inceptionDate"          : command.policyStartDate ? command.policyStartDate.toString("yyyy-MM-dd") : LocalDate.now().toString("yyyy-MM-dd"),
            "ncd"                    : command.ncd,
            "coverType"              : "COMPREHENSIVE",
            "policyType"             : command.policyType,
            "insured_type"           : command.insureType,
            "model_year"             : command.modelYear,
            "vehicleMake"            : command.vehicleMake,
            "vehicleModel"           : command.vehicleModel,
            "bodyType"               : command.bodyType,
            "vehicleTrim"            : command.vehicleTrim,
            "engineCapacity"         : command.engineCapacity,
            "isBrandNew"             : command.isBrandNew ? "1" : "0",
            "seats"                  : command.seats,
            "doors"                  : command.doors,
            "cylinder"               : command.cylinders,
            "sumInsured"             : adjustInsuredValue(command),
            "drivingLicenseIssueDate": command.drivingLicenseIssueDate,
            "registered_date"        : command.registrationDate,
            "isNonGCCSpec"           : command.isNonGccSpec ? "Yes" : "No",
            "repair_type"            : "GARAGE",
            "isModified"             : command.isNonGccSpec ? "Yes" : "No",
            "chassisNumber"          : ""
        ]

        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": AESCryption.encrypt(command.quoteId.toString())])
        String url = host + QUICK_QUOTE

        log.info("${UtilService.getQuoteLoggingPrefix('quickQuoteApi', command.quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, headers: ${customHeaders}, request: ${requestBody}")

        JSONElement response
        if (SHOW_MOKE_DATA) {
            response = MockDataUtil.getDnircQuickQuote()
        } else {
            response = providerApiService.callApi(url, customHeaders, requestBody, HttpMethod.POST, MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_HTML_VALUE) as JSONElement
        }

        log.info("${UtilService.getQuoteLoggingPrefix('quickQuoteApi', command.quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, response: ${response}")
        return response
    }


    JSONElement quoteBenefitsApi(String quoteNumber, Integer quoteId) {
        log.info("${UtilService.getQuoteLoggingPrefix('quoteBenefitsApi', quoteId, InsuranceProviderEnum.DNIRC)} - quoteNumber: ${quoteNumber}")

        String token = getToken(quoteId)
        def requestBody = [
            "quotationNumber": quoteNumber
        ]

        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": AESCryption.encrypt(quoteId.toString())])
        String url = host + GET_QUOTE_BENEFITS

        log.info("${UtilService.getQuoteLoggingPrefix('quoteBenefitsApi', quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, headers: ${customHeaders}, request: ${requestBody}")

        JSONElement response
        response = providerApiService.callApi(url, customHeaders, requestBody, HttpMethod.POST, MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_HTML_VALUE) as JSONElement

        log.info("${UtilService.getQuoteLoggingPrefix('quoteBenefitsApi', quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, response: ${response}")
        return response
    }

    List<DnircRateCommand> toProviderRateCommand(JSONElement coversJson, DnircQuoteCommand command) {
        log.info("${UtilService.getQuoteLoggingPrefix('toProviderRateCommand', command.quoteId, InsuranceProviderEnum.DNIRC)} - coversJson: ${coversJson}")

        List<DnircRateCommand> rateCommands = []
        DnircRateCommand agencyRateCommand = null
        DnircRateCommand garageRateCommand = null
        DnircRateCommand tplRateCommand = null

        coversJson?.quotationPremiumDetails?.each { detail ->
            String excess = detail?.planName?.trim() == THIRD_PARTY_PLAN_NAME || detail?.planName?.trim() == THIRD_PARTY_PLAN_NAME_PROD ? null : detail?.deductible?.toString()
            Integer productId = detail?.planName?.trim() == THIRD_PARTY_PLAN_NAME || detail?.planName?.trim() == THIRD_PARTY_PLAN_NAME_PROD ? DubaiNationalRateV2Service.TPL_PRODUCT_ID : DubaiNationalRateV2Service.STANDARD_PRODUCT_ID
            log.info("${UtilService.getQuoteLoggingPrefix('toProviderRateCommand', command.quoteId, InsuranceProviderEnum.DNIRC)} - productId: ${productId}, plan: ${detail}")

            DnircRateCommand rateCommand = new DnircRateCommand(
                productName: detail?.planName,
                excess: excess,
                netPremium: new BigDecimal(detail?.basePremium?.toString()),
                productId: productId,
                isAgency: detail?.repairType?.toUpperCase() == "AGENCY"
            )

            switch (detail?.repairType?.toUpperCase()) {
                case "AGENCY":
                    agencyRateCommand = rateCommand
                    break
                case "GARAGE":
                    garageRateCommand = rateCommand
                    break
                default:
                    tplRateCommand = rateCommand
                    break
            }
        }

        log.info("${UtilService.getQuoteLoggingPrefix('toProviderRateCommand', command.quoteId, InsuranceProviderEnum.DNIRC)} - agencyDnircRateCommand: ${agencyRateCommand}")
        log.info("${UtilService.getQuoteLoggingPrefix('toProviderRateCommand', command.quoteId, InsuranceProviderEnum.DNIRC)} - garageDnircRateCommand: ${garageRateCommand}")
        log.info("${UtilService.getQuoteLoggingPrefix('toProviderRateCommand', command.quoteId, InsuranceProviderEnum.DNIRC)} - tplDnircRateCommand: ${tplRateCommand}")

        coversJson?.quotationPlanBenefits?.each { benefit ->
            ProviderRateCommand.RateCoverCommand cover = new ProviderRateCommand.RateCoverCommand(
                name: benefit?.BenefitName,
                code: benefit?.BenefitId,
                premium: benefit?.Price?.toString(),
                benefits: !(benefit?.SumCovered?.toString()?.equals("Covered")) ? benefit?.SumCovered?.toString() : null
            )

            switch (benefit?.RepairType?.toUpperCase()) {
                case "AGENCY":
                    if (agencyRateCommand) {
                        if (benefit?.Price == 0 || benefit?.IsIncluded == 1) {
                            agencyRateCommand.covers.add(cover)
                        } else if (benefit?.Price != 0 && benefit?.CoverType == "MC") {
                            agencyRateCommand.netPremium += new BigDecimal(benefit?.Price?.toString())
                            agencyRateCommand.covers.add(cover)
                        } else if (benefit?.Price != 0 && benefit?.CoverType == "OC") {
                            agencyRateCommand.optionalCovers.add(cover)
                        }
                    }
                    break
                case "GARAGE":
                    if (garageRateCommand) {
                        if (benefit?.Price == 0 || benefit?.IsIncluded == 1) {
                            garageRateCommand.covers.add(cover)
                        } else if (benefit?.Price != 0 && benefit?.CoverType == "MC") {
                            garageRateCommand.netPremium += new BigDecimal(benefit?.Price?.toString())
                            garageRateCommand.covers.add(cover)
                        } else if (benefit?.Price != 0 && benefit?.CoverType == "OC") {
                            garageRateCommand.optionalCovers.add(cover)
                        }
                    }
                    break
                case "NA":
                    if (tplRateCommand) {
                        if (benefit?.Price == 0 || benefit?.IsIncluded == 1) {
                            tplRateCommand.covers.add(cover)
                        } else if (benefit?.Price != 0 && benefit?.CoverType == "MC") {
                            tplRateCommand.netPremium += new BigDecimal(benefit?.Price?.toString())
                            tplRateCommand.covers.add(cover)
                        } else if (benefit?.Price != 0 && benefit?.CoverType == "OC") {
                            tplRateCommand.optionalCovers.add(cover)
                        }
                    }
                    break
            }
        }

        return [agencyRateCommand, garageRateCommand, tplRateCommand].findAll { it != null }
    }


    void SaveQuoteApi(CarQuote quote, List<CarQuoteAddon> dynamicAddons) {
        log.info("${UtilService.getQuoteLoggingPrefix('SaveQuoteApi', quote.id, InsuranceProviderEnum.DNIRC)} - providerPolicyReference: ${quote.providerPolicyReference}")

        String token = getToken(quote.id)
        def requestBody = [
            "QuotationNumber" : quote.providerPolicyReference,
            "Optionalbenefits": dynamicAddons.collect {
                [
                    "BenefitId"     : it.providerAddonCode,
                    "BenefitPremium": it.price,
                    "BenefitName"   : it.addonDescription.toString(),
                    "IsSelected"    : 1
                ]
            }
        ]

        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": AESCryption.encrypt(quote.id.toString())])
        String url = host + UPDATE_QUOTE_BENEFITS

        log.info("${UtilService.getQuoteLoggingPrefix('SaveQuoteApi', quote.id, InsuranceProviderEnum.DNIRC)} - url: ${url}, headers: ${customHeaders}, request: ${requestBody}")

        JSONElement response
        response = providerApiService.callApi(url, customHeaders, requestBody, HttpMethod.POST, MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_HTML_VALUE) as JSONElement
        log.info("${UtilService.getQuoteLoggingPrefix('SaveQuoteApi', quote.id, InsuranceProviderEnum.DNIRC)} - url: ${url}, response: ${response}")

        if (response?.Status_Code?.toString() != HttpStatus.OK.toString()) {
            log.info("${UtilService.getQuoteLoggingPrefix('SaveQuoteApi', quote.id, InsuranceProviderEnum.DNIRC)} - url: ${url}, Exception While Calling Update Quote API")
        }
    }


    def callUpdateQuoteApi(DnircQuoteCommand quoteCommand, CarQuote quote) {

        CoverageTypeEnum coverageType = quote.productId == DubaiNationalRateV2Service.STANDARD_PRODUCT_ID ? CoverageTypeEnum.COMPREHENSIVE : CoverageTypeEnum.THIRD_PARTY

        JSONElement response = updateQuickQuoteApi(quoteCommand, quote.providerPolicyReference, coverageType, quote.isAgencyRepair)

        if (response?.response_code && response?.response_code == "2") {
            def rangePattern = /(\d+) - (\d+)/
            def matcher = (response.toString() =~ rangePattern)
            if (matcher.find()) {
                def lowerValue = matcher.group(1) as int
                def higherValue = matcher.group(2) as int
                log.info("${UtilService.getQuoteLoggingPrefix('callUpdateQuoteApi', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - commandInsuredValue: ${quoteCommand.insuredValue}, higherValue: ${higherValue}, lowerValue: ${lowerValue}")

                if (quoteCommand.insuredValue > higherValue) {
                    quoteCommand.insuredValue = higherValue
                } else if (quoteCommand.insuredValue < lowerValue) {
                    quoteCommand.insuredValue = lowerValue
                }

                response = updateQuickQuoteApi(quoteCommand, quote.providerPolicyReference, coverageType, quote.isAgencyRepair)
            } else {
                return null
            }

        }

        if (!(response?.response_code && response.response_code == HttpStatus.OK.value().toString())) {
            throw new Exception("${UtilService.getQuoteLoggingPrefix('callUpdateQuoteApi', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - The Api response is incorrect - Response: ${response}")
        }
    }

    JSONElement updateQuickQuoteApi(DnircQuoteCommand command, String providerQuotationNumber, CoverageTypeEnum coverageType, Boolean isAgency) {
        String token = getToken(command.quoteId)
        def requestBody = [
            "client_id"              : clientId,
            "insuredNameEn"          : command.insuredNameEn,
            "license_country"        : command.licenseCountry,
            "nationality"            : command.nationality,
            "dob"                    : command.dob.toString("yyyy-MM-dd"),
            "email"                  : command.email,
            "mobile"                 : command.mobile,
            "inceptionDate"          : command.policyStartDate ? command.policyStartDate.toString("yyyy-MM-dd") : LocalDate.now().toString("yyyy-MM-dd"),
            "ncd"                    : command.ncd,
            "coverType"              : coverageType == CoverageTypeEnum.COMPREHENSIVE ? "COMPREHENSIVE" : "THIRD PARTY LIABILITY",
            "policyType"             : command.policyType,
            "insured_type"           : command.insureType,
            "model_year"             : command.modelYear,
            "vehicleMake"            : command.vehicleMake,
            "vehicleModel"           : command.vehicleModel,
            "bodyType"               : command.bodyType,
            "vehicleTrim"            : command.vehicleTrim,
            "engineCapacity"         : command.engineCapacity,
            "isBrandNew"             : command.isBrandNew ? "1" : "0",
            "seats"                  : command.seats,
            "doors"                  : command.doors,
            "cylinder"               : command.cylinders,
            "sumInsured"             : coverageType == CoverageTypeEnum.THIRD_PARTY ? 1 : adjustInsuredValue(command),
            "drivingLicenseIssueDate": command.drivingLicenseIssueDate,
            "registered_date"        : command.registrationDate,
            "isNonGCCSpec"           : command.isNonGccSpec ? "Yes" : "No",
            "repair_type"            : coverageType == CoverageTypeEnum.COMPREHENSIVE ? (isAgency ? "AGENCY" : "GARAGE") : "NA",
            "isModified"             : command.isNonGccSpec ? "Yes" : "No",
            "chassisNumber"          : "",
            "IsPlanSelected"         : "YES",
            "QuotationNumber"        : providerQuotationNumber
        ]

        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add("Authorization": "Bearer " + token)
        customHeaders.add(["encQuoteId": AESCryption.encrypt(command.quoteId.toString())])
        String url = host + UPDATE_QUICK_QUOTE

        log.info("${UtilService.getQuoteLoggingPrefix('updateQuickQuoteApi', command.quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, headers: ${customHeaders}, request: ${requestBody}")

        JSONElement response
        response = providerApiService.callApi(url, customHeaders, requestBody, HttpMethod.POST, MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_HTML_VALUE) as JSONElement

        log.info("${UtilService.getQuoteLoggingPrefix('updateQuickQuoteApi', command.quoteId, InsuranceProviderEnum.DNIRC)} - url: ${url}, response: ${response}")
        return response
    }

    Long adjustInsuredValue(QuoteCommand command){
        log.info("${UtilService.getQuoteLoggingPrefix('adjustInsuredValue', command.quoteId, InsuranceProviderEnum.DNIRC)} - commandInsuredValue: ${command.insuredValue}, commandInsuredValueMax: ${command.insuredValueMax}, commandInsuredValueMin: ${command.insuredValueMin}")

        if (command.insuredValue > command.insuredValueMax) {
            command.insuredValue = command.insuredValueMax
        } else if (command.insuredValue < command.insuredValueMin) {
            command.insuredValue = command.insuredValueMin
        }

        command.insuredValue.longValue()
    }

}
