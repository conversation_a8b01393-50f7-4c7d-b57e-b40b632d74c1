package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.tokio.TokioQuoteCommand
import com.cover.car.tokio.TokioRateCommand
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.Product
import com.safeguard.Provider
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.vehicle.*
import grails.converters.JSON
import grails.transaction.Transactional
import org.joda.time.LocalDateTime

/**
 * This service handles rates using TOKIO APIs
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-10-06
 */
@Transactional
class TokioMarineRateV2Service extends BaseRatingService {

    def ratingService
    def tokioMarineApiService
    def vehicleService

    public static final Integer PROVIDER_ID = 42

    public static Integer COMPREHENSIVE_AGENCY_PRODUCT_ID = 5088 // Tokio Agency
    public static final Integer COMPREHENSIVE_GARAGE_PRODUCT_ID = 1056 // Tokio Must
    public static final Integer TPL_PRODUCT_ID = 1057 // Third Party

    public static String COMPREHENSIVE_AGENCY_PRODUCT_CODE = "1001" // Tokio Agency
    public static String COMPREHENSIVE_GARAGE_PRODUCT_CODE = "1002" // Tokio Must
    public static String TPL_PRODUCT_CODE = "1501" // Third Party


    /**
     * This method will return TPL, Comprehensive (Agency, Non-Agency) rates if applicable
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param quoteCommand [YC Criteria of fetching quotes]
     * @param isOffline ["true" if method call from BROKER, "false" if method calls from COVER]
     * @return List<RateCommand> [Have rates if applicable; otherwise, return empty]
     */
    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        quoteCommand.providerId = PROVIDER_ID
        log.info("TokioMarineRateV2Service.getRates - QuoteCommand: ${quoteCommand}, isOffline: ${isOffline}")

        List<RateCommand> rateCommands = []

        Map response = getRatesByTokio(quoteCommand, isOffline)

        List<TokioRateCommand> tokioRateCommands = response.get("tokioMarineRateCommands")
        log.info("TokioMarineRateV2Service.getRates - tokioRateCommandsSize: ${tokioRateCommands.size()}")

        TokioQuoteCommand tokioQuoteCommand = response.get("tokioQuoteCommand") as TokioQuoteCommand
        log.info("TokioMarineRateV2Service.getRates - tokioQuoteCommand: ${tokioQuoteCommand}")

        if (!tokioRateCommands || !tokioQuoteCommand) {
            return rateCommands
        }

        // Convert TokioMarineRateCommands to RateCommands
        tokioRateCommands.each {
            log.info("TokioMarineRateV2Service.getRates.tokioRateCommands - quoteNumber: ${it.quoteNo}, " +
                "productName: ${it.productName}, productCode: ${it.productCode}, premium: ${it.netPremium}")

            RateCommand rateCommand = tokioMarineApiService.toRateCommand(it, tokioQuoteCommand)
            log.info("TokioMarineRateV2Service.getRates.rateCommand - rateCommand: ${rateCommand}")

            if (rateCommand) {
                rateCommand = populateRatings(ratingService, tokioQuoteCommand, rateCommand)
                rateCommands.add(rateCommand)
            }
        }

        log.info("TokioMarineRateV2Service.getRates - rateCommandsSize: ${rateCommands.size()}")
        rateCommands
    }


    /**
     * This method will return Comprehensive (Agency, Non-Agency) rates from the API if applicable
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param quoteCommand [YC Criteria of fetching quotes]
     * @param isOffline ["true" if method call from BROKER, "false" if method calls from COVER]
     * @return RateCommand [Have comprehensive rate if applicable; otherwise, return empty]
     */
    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        quoteCommand.providerId = PROVIDER_ID
        log.info("TokioMarineRateV2Service.getRate - QuoteCommand: ${quoteCommand}, isOffline: ${isOffline}")

        Map response = getRatesByTokio(quoteCommand, isOffline)

        List<TokioRateCommand> tokioRateCommands = response.get("tokioMarineRateCommands")
        log.info("TokioMarineRateV2Service.getRate - tokioRateCommandsSize: ${tokioRateCommands.size()}")

        TokioQuoteCommand tokioQuoteCommand = response.get("tokioQuoteCommand") as TokioQuoteCommand
        log.info("TokioMarineRateV2Service.getRate - tokioQuoteCommand: ${tokioQuoteCommand}")

        if (!tokioRateCommands || !tokioQuoteCommand) {
            return null
        }

        TokioRateCommand tokioRateCommand = getTokioRateCommandByProductId(quoteCommand.productId, tokioRateCommands)

        if (!tokioRateCommand) {
            log.error("TokioMarineRateV2Service.getRate - tokioRateCommand: ${null}, against quoteId: ${quoteCommand.quoteId}")
            return null
        }

        RateCommand rateCommand = tokioMarineApiService.toRateCommand(tokioRateCommand, tokioQuoteCommand)
        log.info("TokioMarineRateV2Service.getRate - rateCommandBeforePopulating: ${rateCommand}")

        if (rateCommand) {
            rateCommand = populateRatings(ratingService, tokioQuoteCommand, rateCommand)
            log.info("TokioMarineRateV2Service.getRate - rateCommandAfterPopulating: ${rateCommand}")
        }

        return rateCommand
    }


    /**
     * This method will return TPL rates from the API if applicable
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param quoteCommand [YC Criteria of fetching quotes]
     * @param isOffline ["true" if method call from BROKER, "false" if method calls from COVER]
     * @return RateCommand [Have TPL rate if applicable; otherwise, return empty]
     */
    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        quoteCommand.providerId = PROVIDER_ID
        log.info("TokioMarineRateV2Service.getTplRate - QuoteCommand: ${quoteCommand}, isOffline: ${isOffline}")

        RateCommand rateCommand = getRate(quoteCommand, isOffline)
        log.info("TokioMarineRateV2Service.getTplRate - rateCommand: ${rateCommand}")

        return rateCommand
    }


    /**
     * This method will return TPL, Comprehensive (Agency, Non-Agency) rates from the API if applicable
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param quoteCommand [YC Criteria of fetching quotes]
     * @param isOffline ["true" if method call from BROKER, "false" if method calls from COVER]
     * @return Map [tokioMarineRateCommands and tokioQuoteCommand]
     */
    private Map getRatesByTokio(QuoteCommand quoteCommand, boolean isOffline) {
        List<TokioRateCommand> tokioMarineRateCommands = []

        boolean isProviderRatings = showProviderRatings(PROVIDER_ID, isOffline)
        log.info("TokioMarineRateV2Service.getRatesByTokio - isProviderRatings: ${isProviderRatings}")
        if (!isProviderRatings) {
            return ["tokioMarineRateCommands": tokioMarineRateCommands, "tokioQuoteCommand": null]
        }

        boolean isEligible = checkEligibility(quoteCommand, isOffline)
        log.info("TokioMarineRateV2Service.getRatesByTokio - isEligible: ${isEligible}")
        if (!isEligible) {
            return ["tokioMarineRateCommands": tokioMarineRateCommands, "tokioQuoteCommand": null]
        }

        TokioQuoteCommand tokioQuoteCommand = TokioQuoteCommand.generateTokioQuoteCommand(quoteCommand)
        log.info("TokioMarineRateV2Service.getRatesByTokio - tokioQuoteCommand: ${tokioQuoteCommand}")
        if (!tokioQuoteCommand) {
            return ["tokioMarineRateCommands": tokioMarineRateCommands, "tokioQuoteCommand": null]
        }

        CarQuote quote = CarQuote.load(quoteCommand.quoteId)
        List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
            findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now())

        if (carQuoteCovers.size() > 0) {
            log.info("TokioMarineRateV2Service.getRatesByTokio - ${carQuoteCovers.size()} covers found in db " +
                "against quoteId: ${quoteCommand.quoteId}")

            carQuoteCovers.each {
                TokioRateCommand tokioRateCommand = TokioRateCommand.generateTokioRateCommand(it)

                tokioMarineRateCommands.add(tokioRateCommand)
            }
        } else {
            log.info("TokioMarineRateV2Service.getRatesByTokio - No covers found in db against quoteId: ${quoteCommand.quoteId}")

            def startTime = System.currentTimeMillis()

            tokioMarineRateCommands = tokioMarineApiService.callCreateQuoteAPI(tokioQuoteCommand)

            def endTime = System.currentTimeMillis()
            def elapsedTime = (endTime - startTime) / 1000.0
            log.info("Tokio API call took ${elapsedTime} seconds against quoteId: ${quoteCommand.quoteId}")
        }

        // Checking whether the product is active/inactive in DB
        tokioMarineRateCommands.removeAll {
            Product product = Product.load(getProductId(it.productCode))
            log.info("TokioMarineRateV2Service.getRatesByTokio - tokioMarineRateCommands.ProductId: ${product.id}, " +
                "tokioMarineRateCommands.ProductActive: ${product.active}")
            !product.active
        }

        return ["tokioMarineRateCommands": tokioMarineRateCommands, "tokioQuoteCommand": tokioQuoteCommand]
    }


    /**
     * This method checks the eligibility criteria
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param quoteCommand [YC Criteria of fetching quotes]
     * @param isOffline ["true" if method call from BROKER, "false" if method calls from COVER]
     * @return boolean ["true" if eligible, otherwise "false"]
     */
    boolean checkEligibility(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("isOffline:$isOffline, hasClaim:${quoteCommand.hasClaim}, age:${quoteCommand.customerAge}")
        // There is no need to check. Blacklisted vehicles are handled by the provider
        // boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, CoverageTypeEnum.COMPREHENSIVE)

        if (quoteCommand.hasClaim) {
            return false
        }

        if (quoteCommand.customerAge < 25 || quoteCommand.customerAge > 60) {
            return false
        }

        // temporary solution to stop generating quotes for incorrect mapping a/c to edata
        Make make = Make.load(quoteCommand.makeId)
        Model model = Model.load(quoteCommand.modelId)
        ModelMaster modelMaster = ModelMaster.load(model.modelMaster.id)

        // We have used ExternalDataSourceEnum.TOKIO only for authentication tokens; for mapping, we have used ExternalDataSourceEnum.EDATA
        ExternalDataSource eDataSource = ExternalDataSource.load(ExternalDataSourceEnum.EDATA.id)
        MakeExternal makeExternal = MakeExternal.findByExternalDataSourceAndMake(eDataSource, make)
        ModelMasterExternal modelMasterExternal = ModelMasterExternal.findByExternalDataSourceAndModelMaster(eDataSource, modelMaster)
        ModelExternal modelExternal = ModelExternal.findByExternalDataSourceAndModel(eDataSource, model)
        log.info("make:${makeExternal?.externalId}, modelMasterExternal: ${modelMasterExternal?.externalId}, modelExternal: " +
            "${modelExternal?.externalId} against quoteId: ${quoteCommand.quoteId}")

        if (makeExternal && modelMasterExternal && modelExternal) {
            Integer maxYear = vehicleService
                .getEDataMaxYear(makeExternal.externalName, modelMasterExternal.externalName, modelExternal.externalId)

            // if maxYear is null then allow for quotes
            return maxYear ? quoteCommand.manufactureYear <= maxYear : true
        } else {
            log.error("TokioMarineRateV2Service.checkEligibility -  not found for vehicle with makeExternal: " +
                "${makeExternal?.externalId}, modelMasterExternal: ${modelMasterExternal?.externalId}, modelExternal: " +
                "${modelExternal?.externalId} against quoteId: ${quoteCommand.quoteId}")
            return false
        }
    }


    /**
     * This method saves dynamic addons against a quote
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param quoteCommand [YC Criteria of fetching quotes]
     * @param selectedDynamicAddons [selected addons]
     * @return boolean ["true" if eligible, otherwise "false"]
     */
    String saveQuoteDetails(QuoteCommand quoteCommand, List selectedDynamicAddons) {
        log.info("TokioMarineRateV2Service.saveQuoteDetails - quoteCommand: ${quoteCommand}, " +
            "selectedDynamicAddons: ${selectedDynamicAddons.toString()}")

        Map response = getRatesByTokio(quoteCommand, false)

        List<TokioRateCommand> tokioRateCommands = response.get("tokioMarineRateCommands")
        log.info("TokioMarineRateV2Service.saveQuoteDetails - tokioRateCommandsSize: ${tokioRateCommands.size()}")

        TokioQuoteCommand tokioQuoteCommand = response.get("tokioQuoteCommand") as TokioQuoteCommand
        log.info("TokioMarineRateV2Service.saveQuoteDetails - tokioQuoteCommand: ${tokioQuoteCommand}")

        if (!tokioRateCommands || !tokioQuoteCommand) {
            return null
        }

        TokioRateCommand tokioRateCommand = getTokioRateCommandByProductId(quoteCommand.productId, tokioRateCommands)

        String providerQuoteNumber = tokioMarineApiService.callSaveQuoteWithPlanAPI(selectedDynamicAddons,
            tokioRateCommand, tokioQuoteCommand)
        log.info("TokioMarineRateV2Service.saveQuoteDetails - providerQuoteNumber: ${providerQuoteNumber}")

        return providerQuoteNumber
    }


    /**
     * This method will return product id based on product code
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param productCode [TOKIO product code]
     * @return productId [TOKIO product id]
     */
    static Integer getProductId(String productCode) {
        Integer productId = null

        switch (productCode) {
            case COMPREHENSIVE_AGENCY_PRODUCT_CODE:
                productId = COMPREHENSIVE_AGENCY_PRODUCT_ID
                break
            case COMPREHENSIVE_GARAGE_PRODUCT_CODE:
                productId = COMPREHENSIVE_GARAGE_PRODUCT_ID
                break
            case TPL_PRODUCT_CODE:
                productId = TPL_PRODUCT_ID
                break
        }

        return productId
    }


    /**
     * This method checks whether the given product is agency or non-agency
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param productCode [TOKIO product code]
     * @return productId [TOKIO product id]
     */
    static Boolean checkAgency(String productCode) {
        Boolean isAgency = false

        switch (productCode) {
            case COMPREHENSIVE_AGENCY_PRODUCT_CODE:
                isAgency = true
                break
            case COMPREHENSIVE_GARAGE_PRODUCT_CODE:
                isAgency = false
                break
            case TPL_PRODUCT_CODE:
                isAgency = false
                break
        }

        return isAgency
    }


    /**
     * This method will filter tokio rate commands by product id
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param productId [TOKIO product id]
     * @param tokioRateCommands [TOKIO rates]
     * @return TokioRateCommand [Have tokio rate if applicable; otherwise, return empty]
     */
    TokioRateCommand getTokioRateCommandByProductId(Integer productId, List<TokioRateCommand> tokioRateCommands) {
        log.info("TokioMarineRateV2Service.getTokioRateCommandByProductId - productId: ${productId}, " +
            "tokioRateCommandsSize: ${tokioRateCommands.size()}")

        TokioRateCommand tokioRateCommand = null
        tokioRateCommands.each {
            Integer tokioRateCommandProductId = getProductId(it.productCode)
            log.info("TokioMarineRateV2Service.getTokioRateCommandByProductId - tokioRateCommandProductId: " +
                "${tokioRateCommandProductId}, tokioRateCommand.productCode: ${it.productCode}")

            if (tokioRateCommandProductId == productId) {
                tokioRateCommand = it
            }
        }

        return tokioRateCommand
    }


    /**
     * This method will filter car quote cover by product id
     *
     * <AUTHOR> Abdullah
     * @version 1.0
     * @since 2023-10-06
     *
     * @param productId [TOKIO product id]
     * @param carQuoteCovers [TOKIO covers/plans]
     * @return CarQuoteCover [Have cover if applicable; otherwise, return empty]
     */
    CarQuoteCover getCarQuoteCoverByProductId(Integer productId, List<CarQuoteCover> carQuoteCovers) {
        log.info("TokioMarineRateV2Service.getCarQuoteCoverByProductId - productId: ${productId}, " +
            "carQuoteCoversSize: ${carQuoteCovers.size()}")

        CarQuoteCover carQuoteCover = null
        carQuoteCovers.each {
            Integer carQuoteCoverProductId = getProductId(JSON.parse(it.covers)?.Code)
            log.info("TokioMarineRateV2Service.getCarQuoteCoverByProductId - carQuoteCoverProductId: " +
                "${carQuoteCoverProductId}, carQuoteCover.covers.productCode: ${JSON.parse(it.covers)?.Code}")

            if (carQuoteCoverProductId == productId) {
                carQuoteCover = it
            }
        }

        return carQuoteCover
    }
}


