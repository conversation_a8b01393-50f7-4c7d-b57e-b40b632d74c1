package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.CarValuationDto
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.joda.time.LocalDate

@Transactional
class OrientalRateService {

    def commonUtilService
    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 25
    public static final Integer COMPREHENSIVE_PRODUCT_ID = 96
    public static final Integer TPL_PRODUCT_ID = 97
    public static final Integer PREMIUM_GARAGES_PRODUCT_ID = 1029

    List<RateCommand> getRates(QuoteCommand command, boolean isOffline) {
        log.info(".getRates - entering with quoteCommand:${command}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        command.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(command)
        log.info(".getRates - checkEligibility:${checkEligibility}")

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(command, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            Model model = Model.get(command.modelId)
            command.carCategory = model.orientalCategory
            command.noOfCyl = model.noOfCyl
            log.info(".getRates - carAge:${command.carAge}, carCategory: ${command.carCategory}, noOfCyl: ${command.noOfCyl}")

            if (!model.orientalCategory) {
                return []
            }

            QuoteCommand quoteCommand = new QuoteCommand()
            quoteCommand = customChecks(command, quoteCommand)

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                log.info(".getRates - applicableRates:${applicableRates.size()}")

                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand command, boolean isOffline) {

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(command)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(command)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(command.modelId)
            command.carCategory = model.orientalCategory
            command.noOfCyl = model.noOfCyl

            QuoteCommand quoteCommand = new QuoteCommand()
            quoteCommand = customChecks(command, quoteCommand)

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        log.info(".populateRatings - rateId:${rate.id}, agencyRate:${rate.baseRateAgency}, garageRate:${rate.baseRateGarage}")

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        log.info(".populateRatings - after calculatePremium - minPremium: ${rateCommand.minPremium}, baseRate:${rateCommand.baseRate}, insuredValue:${quoteCommand.insuredValue}, premium: ${rateCommand.premium}")

        rateCommand = checkMinimumPremium(quoteCommand, rateCommand)
        log.info(".populateRatings - after checkMinimumPremium - premium: ${rateCommand.premium}")

//        rateCommand = applyLoadings(quoteCommand, rateCommand)
        log.info(".populateRatings - after applyLoadings - premium:${rateCommand.premium}")

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        log.info(".populateRatings - after applyCovers - premium: ${rateCommand.premium}, insuredValue: ${rateCommand.insuredValue}")

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = applyExcess(quoteCommand, rateCommand)
        log.info(".populateRatings - after applyExcess - premium: ${rateCommand.premium}, rateCommand: ${rateCommand}")

        //rateCommand = checkMaximumPremium(rateCommand, quoteCommand)
        rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        log.info(".populateRatings - after applyAdditionalFees - premium: ${rateCommand.premium}")

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }
        rateCommand
    }


    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate.productId)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        // Method added to implement the new ratings for premium garages
        //rateCommand.baseRate = getBaseRate(rateCommand, quoteCommand, applicableRate)

        //Brand new rate applies if agency is applicable
        if (rateCommand.agencyRepair && quoteCommand.isBrandNew && applicableRate.baseRateBrandNew) {
            rateCommand.baseRate = applicableRate.baseRateBrandNew
        } else {
            rateCommand.baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        }

        if (rateCommand.agencyRepair && quoteCommand.isBrandNew && applicableRate.minPremiumBrandNew) {
            rateCommand.minPremium = applicableRate.minPremiumBrandNew
        } else {
            rateCommand.minPremium = rateCommand.agencyRepair ?
                applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        }

        log.info(".calculatePremium - minPremium: ${rateCommand.minPremium}, baseRate: ${rateCommand.baseRate}")

        Model model = Model.findById(quoteCommand.modelId)
        rateCommand = applyMakeSpecificRates(quoteCommand, rateCommand)
        log.info(".calculatePremium - after applyMakeSpecificRates - minPremium: ${rateCommand.minPremium}, baseRate: ${rateCommand.baseRate}")

        rateCommand = applyChineseMakeSpecificRates(quoteCommand, rateCommand)
        log.info(".calculatePremium - after applyChineseMakeSpecificRates - minPremium: ${rateCommand.minPremium}, baseRate: ${rateCommand.baseRate}")

        Tuple result = applyModelSpecificRate(quoteCommand, rateCommand)
        boolean isFrom10Makes = result[1] as boolean

        rateCommand = result[0] as RateCommand
        log.info(".calculatePremium - after applyModelSpecificRate - minPremium: ${rateCommand.minPremium}, baseRate: ${rateCommand.baseRate}, isFrom10Makes: ${isFrom10Makes}")


//        if ((model.modelMasterId.intValue() == ModelMasterEnum.TOYOTA_COROLLA.id) || !isFrom10Makes){
        rateCommand = applyNationalitySpecificRate(quoteCommand, rateCommand)
        log.info(".calculatePremium - after applyNationalitySpecificRate - minPremium: ${rateCommand.minPremium}, baseRate: ${rateCommand.baseRate}")
//        }


        /*ModelMaster modelMaster = Model.findById(quoteCommand.modelId).modelMaster

        if (quoteCommand.makeId in [CarMakeEnum.MITSUBISHI.id, CarMakeEnum.HYUNDAI.id, CarMakeEnum.HONDA.id, CarMakeEnum.TOYOTA.id, CarMakeEnum.FORD.id,
                                    CarMakeEnum.MAZDA.id, CarMakeEnum.RENAULT.id, CarMakeEnum.KIA.id, CarMakeEnum.NISSAN.id, CarMakeEnum.CHERY.id] && !rateCommand.agencyRepair
                                    && (quoteCommand.carCategory == '4WD' || quoteCommand.carCategory == 'SUV' || quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.getValue())){
            rateCommand.baseRate = 1.400

            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.getValue()){
                if (modelMaster.id == ModelMasterEnum.TOYOTA_COROLLA.id) {
                    rateCommand.minPremium = 910.00
                } else {
                    rateCommand.minPremium = 780.00
                }

            } else if (quoteCommand.carCategory == 'SUV'){
                rateCommand.minPremium = 975.00
            } else if (quoteCommand.carCategory == '4WD'){
                rateCommand.minPremium = 1200.00
            }

            if (quoteCommand.makeId == CarMakeEnum.CHERY.id && quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.getValue() && quoteCommand.carCategory != 'SUV'){
                rateCommand.baseRate = 1.3
                rateCommand.minPremium = 650.00
            }
            if (quoteCommand.makeId in [CarMakeEnum.MITSUBISHI.id, CarMakeEnum.HYUNDAI.id, CarMakeEnum.HONDA.id, CarMakeEnum.TOYOTA.id,
                                        CarMakeEnum.FORD.id, CarMakeEnum.MAZDA.id, CarMakeEnum.RENAULT.id] &&
                !rateCommand.agencyRepair && quoteCommand.carCategory == 'SUV') {
                rateCommand.minPremium = 780.00
            }
        }

        if (quoteCommand.makeId == CarMakeEnum.MG.id && modelMaster.id in ModelMasterEnum.values()*.id){
            if (modelMaster.id == ModelMasterEnum.MG_HS.getId() && quoteCommand.carCategory == 'SUV'){
                if (rateCommand.agencyRepair){
                    rateCommand.minPremium = 1800.00
                    rateCommand.baseRate = 1.600
                }else {
                    rateCommand.minPremium = 1300.00
                    rateCommand.baseRate = 1.800
                }
            }
            if (modelMaster.id in [ModelMasterEnum.MG_350.getId(), ModelMasterEnum.MG_5.getId(), ModelMasterEnum.MG_6.getId()] && quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.getValue()){
                if (rateCommand.agencyRepair){
                    rateCommand.minPremium = 1500.00
                    rateCommand.baseRate = 2.000
                }else {
                    rateCommand.minPremium = 1000.00
                    rateCommand.baseRate = 1.600
                }
            }

        }*/

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)

        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            quoteCommand.carCategory = model.orientalCategory
            quoteCommand.noOfCyl = model.noOfCyl
            log.info(".getTplRate - carAge:${quoteCommand.carAge}, carCategory: ${quoteCommand.carCategory}, noOfCyl: ${quoteCommand.noOfCyl}")

            if (!model.orientalCategory) {
                return null
            }

            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, model.orientalCategory, true, quoteCommand.requestSource)
            for (ProductTplRate tplRate : applicableRates) {
                log.info(tplRate.productId.toString() + ":" + tplRate.basePremium.toString() + ":" + tplRate.noOfCyl.toString() + ":" + tplRate.vehicleTypeId.toString())
            }
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId.intValue()

        rateCommand = applyTplLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        //rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, Long productId) {

        if (!ratingService.allowAgency()) {
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (productId == COMPREHENSIVE_PRODUCT_ID) {
                if (quoteCommand.isBrandNew) {
                    isAgency = true
                } else if (quoteCommand.carAge <= 1 && quoteCommand.insuredValue > 100000
                    && quoteCommand.noClaimsDiscountId) {
                    isAgency = true
                }

                if (quoteCommand.makeId in [CarMakeEnum.KIA.id, CarMakeEnum.PEUGEOT.id, CarMakeEnum.SUZUKI.id,
                                            CarMakeEnum.CITROEN.id, CarMakeEnum.HYUNDAI.id, CarMakeEnum.HAVAL.id, CarMakeEnum.MAXUS.id]) {

                    isAgency = false
                }
            }

            //no chinese make in 100,000 to 250,000 insured value are not eligible for agency
//            if (!quoteCommand.isBrandNew && quoteCommand.insuredValue >= 100000 && quoteCommand.insuredValue < 250000 &&
//                make.countryId == 36) {
//                isAgency = false
//            }
        }

        isAgency
    }

    RateCommand applyAdditionalFees(RateCommand rateCommand, QuoteCommand quoteCommand) {
        log.info("oriental.applyAdditionalFees - before apply rateCommand:${rateCommand.premium}")


        // if registration city is dubai then additional 10 aed for RTA Fees
//        if (quoteCommand.registrationCityId == CityEnum.DUBAI.value()) {
//            if (quoteCommand.carCategory == 'BLK' ||
//                rateCommand.productId == TPL_PRODUCT_ID ||
//                quoteCommand.insuredValue >= 250000) {
//                rateCommand.premium = rateCommand.premium.add(10)
//            }
//
//        } else if(quoteCommand.registrationCityId != CityEnum.DUBAI.value()) {
//            // if registration city is not Dubai then additional 30 aed for "Emirates Vehicle Gate (EVG)"
//
//            if (quoteCommand.insuredValue >= 250000 && rateCommand.productId == COMPREHENSIVE_PRODUCT_ID) {
//                rateCommand.premium = rateCommand.premium.add(30)
//            } else if (rateCommand.productId == TPL_PRODUCT_ID) {
//                //35 for TPL
//                rateCommand.premium = rateCommand.premium.add(35)
//            }
//        }
//        if (rateCommand.productId == TPL_PRODUCT_ID){
//            rateCommand = addMandatoryPABFees(rateCommand, quoteCommand)
//        }
        log.info("oriental.applyAdditionalFees - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    /**
     * Add mandatory PAB fees
     *
     * @param rateCommand
     * @param quoteCommand
     * @return
     */
    private RateCommand addMandatoryPABFees(RateCommand rateCommand, QuoteCommand quoteCommand) {

        Model model = Model.read(quoteCommand.modelId)
        rateCommand.premium = rateCommand.premium.add(100) //PAB for Driver
        rateCommand.premium = rateCommand.premium.add(20 * (model.numberOfSeats - 1)) // PAB for Passenger
        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.isThirdParty) {
            return false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && (quoteCommand.customerAge < 25 || quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId())) {
            return false
        }

        if (quoteCommand.makeId == CarMakeEnum.ALFA_ROMEO.id && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            return false
        }

        if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY && getCarAgeFromManufactureYear(quoteCommand) >= 15) {
            return false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && getCarAgeFromManufactureYear(quoteCommand) >= 10) {
            return false
        }

        if (quoteCommand.hasClaim) {
            return false
        }

        if (quoteCommand.isNonGccSpec && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            isEligible = false
        }

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && isPortalRatesFollowed(quoteCommand)) {
            isEligible = false
        }


        return isEligible
    }

    /**
     * Apply Driver Age and License loading
     * @param quoteCommand
     * @param rateCommand
     * @return
     */
    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {

        int customerAge = quoteCommand.customerAge
        BigDecimal loadingRate = 0
        log.info("First Loading Rate ${loadingRate}")
        if (quoteCommand.insuredValue < 250000) {
            if (rateCommand.agencyRepair) {
                if (rateCommand.minPremium == rateCommand.premium) {
                    if (customerAge < 25) {
                        rateCommand.premium = rateCommand.premium.add(1650.00)
                    }
                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                        rateCommand.premium = rateCommand.premium.add(1500.00)
                    }
                } else {
                    if (customerAge < 25) {
                        loadingRate = 3 // % excess of base rate
                    }
                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                        loadingRate += 1.5 // 1% excess of base rate
                    }
                }
            } else if (!rateCommand.agencyRepair) {
                if (rateCommand.minPremium == rateCommand.premium) {
                    if (customerAge < 25) {
                        rateCommand.premium = rateCommand.premium.add(1500)
                    }
                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                        rateCommand.premium = rateCommand.premium.add(1000)
                    }
                } else {
                    if (customerAge < 25) {
                        loadingRate = 3 // 1% excess of base rate
                    }
                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                        loadingRate += 1 // 1% excess of base rate
                    }
                }
            }
//            if (rateCommand.agencyRepair && quoteCommand.carCategory != 'BLK'){
//                if (rateCommand.minPremium == rateCommand.premium){
//                    if (customerAge < 25) {
//                        rateCommand.premium = rateCommand.premium.add(750)
//                    }
//                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
//                        rateCommand.premium = rateCommand.premium.add(1500)
//                    }
//                }else {
//                    if (customerAge < 25) {
//                        loadingRate = 2 // 2% excess of base rate
//                    }
//                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
//                        loadingRate+= 1.5 // 1% excess of base rate
//                    }
//                }
//            } else if (!rateCommand.agencyRepair && quoteCommand.carCategory != 'BLK'){
//                if (rateCommand.minPremium == rateCommand.premium){
//                    if (customerAge < 25) {
//                        rateCommand.premium = rateCommand.premium.add(500)
//                    }
//                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
//                        rateCommand.premium = rateCommand.premium.add(1000)
//                    }
//                }else {
//                    if (customerAge < 25) {
//                        loadingRate = 2 // 2% excess of base rate
//                    }
//                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
//                        loadingRate+= 1 // 1% excess of base rate
//                    }
//                }
//            }
        } else if (quoteCommand.insuredValue > 250000) {
            if (rateCommand.agencyRepair) {
                if (rateCommand.minPremium == rateCommand.premium) {
                    if (customerAge < 25) {
                        rateCommand.premium = rateCommand.premium.add(1650)
                    }

                } else {
                    if (customerAge < 25) {
                        loadingRate = 3 // % excess of base rate
                    }
                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                        loadingRate += 1.5 // 1% excess of base rate
                    }
                }
            } else if (!rateCommand.agencyRepair) {
                if (rateCommand.minPremium == rateCommand.premium) {
                    if (customerAge < 25) {
                        rateCommand.premium = rateCommand.premium.add(1500)
                    }

                } else {
                    if (customerAge < 25) {
                        loadingRate = 3 // 1% excess of base rate
                    }
                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                        loadingRate += 1 // 1% excess of base rate
                    }
                }
            }
//            if (rateCommand.agencyRepair ){
//                if (rateCommand.minPremium == rateCommand.premium){
//                    if (customerAge < 25) {
//                        rateCommand.premium = rateCommand.premium.add(750)
//                    }
//
//                }else {
//                    if (customerAge < 25) {
//                        loadingRate = 2 // 2% excess of base rate
//                    }
//                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
//                        loadingRate+= 1.5 // 1% excess of base rate
//                    }
//                }
//            } else if (!rateCommand.agencyRepair && quoteCommand.carCategory != 'BLK'){
//                if (rateCommand.minPremium == rateCommand.premium){
//                    if (customerAge < 25) {
//                        rateCommand.premium = rateCommand.premium.add(500)
//                    }
//                }else {
//                    if (customerAge < 25) {
//                        loadingRate = 2 // 2% excess of base rate
//                    }
//                    if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
//                        loadingRate+= 1 // 1% excess of base rate
//                    }
//                }
//            }

        }


        if (loadingRate) {
            if (rateCommand.agencyRepair && (rateCommand.baseRate + loadingRate) > 7 && quoteCommand.carCategory == 'BLK') {
                loadingRate = new BigDecimal(7).subtract(rateCommand.baseRate)
            } else if (!rateCommand.agencyRepair && (rateCommand.baseRate + loadingRate) > 5 && quoteCommand.carCategory == 'BLK') {
                loadingRate = new BigDecimal(5).subtract(rateCommand.baseRate)
            }
            BigDecimal loading = ratingService.calculate(loadingRate, quoteCommand.insuredValue)

            // Minimum AED 3,000 loading
//            if (loading < 3000) {
//                loading = 3000
//            }

            rateCommand.premium = rateCommand.premium.add(loading)
            rateCommand
        }

        rateCommand
    }

    /**
     * Apply Driver Age and License loading
     * @param quoteCommand
     * @param rateCommand
     * @return
     */
    RateCommand applyTplLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {
        int customerAge = quoteCommand.customerAge
        BigDecimal loadingRate = 0
//        if (quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.getValue(), VehicleTypeEnum.COUPE.getValue(), VehicleTypeEnum.SPORTS.getValue()]
//            || quoteCommand.carCategory in ['SUV', '4WD']) {
//        }
        if (customerAge < 25) {
            loadingRate = 1000
        }
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            loadingRate += 1000
        }

//
//        if (customerAge < 25 || quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
//            Model model = Model.read(quoteCommand.modelId)
//            Integer vehicleTypeId = model.vehicleTypeId.intValue()
//            Integer noOfCyl = model.noOfCyl
//
//            if (vehicleTypeId == VehicleTypeEnum.SEDAN.value) {
//                if (noOfCyl == 4) {
//                    premium = 1300
//                } else if (noOfCyl == 6) {
//                    premium = 1400
//                } else if (noOfCyl == 8) {
//                    premium = 1600
//                } else if (noOfCyl > 8) {
//                    premium = 2100
//                }
//            } else if (vehicleTypeId == VehicleTypeEnum.FOURx4.value) {
//                if (noOfCyl == 4) {
//                    premium = 1750
//                } else if (noOfCyl == 6) {
//                    premium = 1900
//                } else if (noOfCyl == 8) {
//                    premium = 1950
//                } else if (noOfCyl > 8) {
//                    premium = 2150
//                }
//            } else if (vehicleTypeId in [VehicleTypeEnum.CONVERTIBLE.value, VehicleTypeEnum.COUPE.value]) {
//                log.info("vehicleTypeId is coupe")
//
//                if (noOfCyl == 4) {
//                    premium = 1750
//                } else if (noOfCyl == 6) {
//                    premium = 1900
//                } else if (noOfCyl == 8) {
//                    premium = 1950
//                } else if (noOfCyl > 8) {
//                    premium = 2150
//                }
//            }
//        }

        if (loadingRate) {
            rateCommand.premium = rateCommand.premium.add(loadingRate)
        }

        rateCommand
    }

    RateCommand checkMaximumPremium(RateCommand rateCommand, QuoteCommand quoteCommand) {

        if (quoteCommand.vechileTypeId in
            [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.MPV.value, VehicleTypeEnum.SEDAN.value]) {

            BigDecimal maxPremium = quoteCommand.vechileTypeId in
                [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.MPV.value] ?
                ratingService.calculate(7, quoteCommand.insuredValue) : ratingService.calculate(5, quoteCommand.insuredValue)

            if (maxPremium > rateCommand.minPremium && rateCommand.premium > maxPremium) {
                rateCommand.premium = maxPremium
                rateCommand.basePremium = maxPremium
            }
        }

        rateCommand
    }

    //TODO: Will remove once all the results are live.
    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {
        log.info("oriental.applyDiscounts - rate.premium:${rate.premium}, product:${rate.productId}")

        //No NCD Discount on Brand New or 10 years old car. Yet to confirm from Oriental, but their portal isn't allowing
        if (quoteCommand.isBrandNew || quoteCommand.manufactureYear <= (LocalDate.now().year - 10)) {
            return rate
        }

        if (quoteCommand.customerAge >= 25 &&
            quoteCommand.localDrivingExperienceId > DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId() &&
            quoteCommand.carCategory != 'BLK' &&
            quoteCommand.vechileTypeId in [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.SEDAN.value]
            && !rate.agencyRepair) {

            return rate
        }

        // NCD Discount
        if (quoteCommand.noClaimsDiscountId) {
            if (!quoteCommand.hasClaim && quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                // 10% discount for 1 year old car with 1 year NCD or Self declaration
                rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
            } else if (!quoteCommand.hasClaim && quoteCommand.noClaimsDiscountId == NcdEnum.YEAR2.value() &&
                !rate.isAgencyRepair()) {
                // 15% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.15))
            } else if (!quoteCommand.hasClaim && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value() &&
                !rate.isAgencyRepair()) {
                // 20% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
            }
        } else if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
            quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {
            // 10% discount for 1 year old car with 1 year self declaration
            rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
            rate.noClaimDiscountPercent = 10   //10%
            rate.requiredSelfDeclarationNumber = 1  //At least 1 year self declaration is required
        }

        log.info("oriental.applyDiscounts - After discount rate.premium:${rate.premium}")

        rate
    }

    RateCommand checkMinimumPremium(QuoteCommand quoteCommand, RateCommand rate) {

        rate.actualBasePremium = rate.premium

//        if (quoteCommand.makeId == CarMakeEnum.ALFA_ROMEO.id) {
//            rate.minPremium = 2500
//        }
//
//        if (quoteCommand.makeId == CarMakeEnum.MINI_COOPER.id) {
//            rate.minPremium = rate.agencyRepair ? 2000 : 1800
//        }

        //In any case, premium cannot go below 2%
        BigDecimal minimum2PercentPremium = ratingService.calculate(2, quoteCommand.insuredValue)
//        if (rate.minPremium < minimum2PercentPremium) {
//            rate.minPremium = minimum2PercentPremium
//        }

//        if (quoteCommand.customerAge >= 25 &&
//            quoteCommand.localDrivingExperienceId > DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId() &&
//            quoteCommand.carCategory != 'BLK') {
//            if (!rate.agencyRepair) {
//                if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
//                    rate.minPremium = 910
//                }
//
//                if (quoteCommand.carCategory == '4WD') {
//                    rate.minPremium = 1400
//                }
//
//                if (quoteCommand.carCategory == 'SUV' && quoteCommand.noOfCyl == 4) {
//                    rate.minPremium = 1250
//                }
//            } else {
//                if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
//                    rate.minPremium = 1500
//                }
//
//                if (quoteCommand.carCategory == '4WD' || quoteCommand.carCategory == 'SUV') {
//                    rate.minPremium = 1800
//                }
//            }
//
//            if (!quoteCommand.noClaimsDiscountId) {
//                Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
//                rate.requiredSelfDeclarationNumber = noClaimYears >= 3 ? 3 : noClaimYears
//            }
//        }

        // after discount if premium is less then minimum premium then use minimum premium
        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium

            rate.productDiscountAmount = null
            rate.productDiscountPercent = null
        }

        rate
    }

    Integer getCarAgeForAgency(QuoteCommand quoteCommand) {
        int regYear = (quoteCommand.isBrandNew) ? (new LocalDate()).getYear() : quoteCommand.firstRegistrationDate.getYear()

        int yearGap = regYear - quoteCommand.manufactureYear

        // if reg year is just 1 year more then man year then use reg year
        boolean useReg = yearGap == 1 || regYear <= quoteCommand.manufactureYear

        if (!useReg) {
            return (quoteCommand.policyStartDate.getYear() + 1) - quoteCommand.manufactureYear
        }

        return (quoteCommand.policyStartDate.getYear() + 1) - regYear
    }

    Tuple applyModelSpecificRate(QuoteCommand quoteCommand, RateCommand rateCommand) {

        boolean isFrom10Makes = false

        if (rateCommand.isAgencyRepair()) {
            return new Tuple(rateCommand, false)
        }

        Model model = Model.read(quoteCommand.modelId)
        ModelMaster modelMaster = model.modelMaster

        if (modelMaster.id in [
            10431, 1259, 377, 385, 372, 386, 384, 10432, // Mitsubishi ZINGER is missing
            191, 203, 205, 10221, 206, 208, 1193, 1091, // Hyundai SANTA-FE-2.4-2WD is missing
            175, 179, 1184, 10433, 181, // Honda
            495, 1197, 500, 502, 1065, 1066, 509, 507, // Toyota ALPHARD, C-HR, GRANVIA, PRADO, RAV 4, VENZA, HARRIER, SIENNA, FJ CRUISER (GSJ10)) are missing
            256, 255, 1183, 266, 268, 268, 260, 1209, // Kia SEDONA is missing
            1179, 394, 403, 391, 393, 1168, 397, 408, 399, 407, //Nissan QUEST is missing
            148, 151, 157, 152, 150, 151, 149, //Ford EDGE SPORT, FREESTAR, S MAX, EXCURSION, WINDSTAR are missing
            317, 318, 319, 320, 1249, //Mazda MPV, TRIBUTE are missing
            445, 441, 443 //Renault SCENIC is missing
        ]) {

            //Apply SUV Rate / Min premium
            log.info(".applyModelSpecificRate - applying SUV rates")

            rateCommand.baseRate = 1.6
            rateCommand.minPremium = 1400
            isFrom10Makes = true

            if (quoteCommand.makeId in [CarMakeEnum.HONDA.id, CarMakeEnum.FORD.id, CarMakeEnum.MAZDA.id, CarMakeEnum.RENAULT.id]) {
                rateCommand.minPremium = 1600
            }


        } else if (modelMaster.id in [
            374, 373, 380, 383, 381, 376, //Mitsubishi TRADIA, MAGNA EI, FORTIS are missing
            204, 188, 202, 198, 201, 1170, 195, 194, 185, 192, 200, 199, 10005, 187, //Hyundai ATOS, CLICK, AVANTE, XG 350, EXCEL, STELLER are missing
            171, 176, 177, 173, 172, //Honda VIGOR, QUINTENT, ACCORD CROSSTOUR are missing
            1049, 510, 1180, 493, 494, 498, // Toyota MARK 2, CAMRY, CRESSIDA, TERCEL, YARIS, MATRIX, CARINA, IQ, SCION XA, CORONA, CROWN are missing
            1239, 1040, 1199, 1252, 257, 262, 264, 263, 257, 267, 254, 261, 1247, 265, // Kia SPECTRA, CREDOS, AMANTI, JOICE, SEPHIA, CLARUS, SHUMA, SHUMA, OPIRUS are missing
            1042, //Nissan ALTIMA, MICRA, SENTRA, TIIDA, BLUE BIRD, MAXIMA, GLORIA, LAUREL, PRIMERA, SUNNY, CEDRIC, TENA are missing
            156, 147, 161, 154, 160,// Ford 500, FIGO, ESCORT, MERCURY SABLE, TAURUS GT, TAURUS, TEMPO, MERCURY, MERCURY MILAN, MERCURY MONTEGO, TOPAZ, CONTOUR, TOWN CAR MKX, MYSTIQUE, FOCUS, GRAND MARQUIS are missing
            312, 313, // Mazda 6 V, SENTIA, ULTRA, MX 6, 323 LX, 6 'S', 6 'R', 6, XEDOS, 626 ORYX, 626, LANTIS, LX 323, 929, 323, 626LX, RX 8 are missing
            450, 452, 444, 447, 449, 448, 451, 442, // Renault SFRAN, ZOE are missing
            // cherry is missing
        ] || model.id in [
            //Toyota
        ]) {

            //Apply Saloon Rate / Min premium
            log.info(".applyModelSpecificRate - applying SALOON rates")

            isFrom10Makes = true
            rateCommand.baseRate = 1.6
            rateCommand.minPremium = 1170

            if (quoteCommand.makeId in [CarMakeEnum.HONDA.id, CarMakeEnum.TOYOTA.id, CarMakeEnum.FORD.id, CarMakeEnum.MAZDA.id, CarMakeEnum.RENAULT.id]) {
                rateCommand.minPremium = 1300
            }


        } else if (modelMaster.id in [
            512, 503, 499, //Toyota
            178 // Honda
            // Hyundai is missing
        ] || model.id in [
            //Toyota
        ]) {

            //Apply 4WD Rate / Min premium
            log.info(".applyModelSpecificRate - applying 4WD rates")

            isFrom10Makes = true
            if (quoteCommand.makeId in [
                CarMakeEnum.MITSUBISHI.id, CarMakeEnum.TOYOTA.id, CarMakeEnum.NISSAN.id, CarMakeEnum.FORD.id
            ]) {
                rateCommand.baseRate = 1.8
                rateCommand.minPremium = 1800
            }
        }

        return new Tuple(rateCommand, isFrom10Makes)
    }

    RateCommand applyNationalitySpecificRate(QuoteCommand quoteCommand, RateCommand rateCommand) {

        if (!applyNationalityRate(quoteCommand, rateCommand)) {
            return rateCommand
        }

        long nationalityId = quoteCommand.nationalityId as long

        if (nationalityId in [CountryEnum.UAE.id, CountryEnum.PALESTINE.id,
                              CountryEnum.JOR.id, CountryEnum.SYRIA.id, CountryEnum.EGYPT.id]) {

            def premium = 0
            def baseRate = 0

            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value && quoteCommand.carCategory != 'BLK') {

                premium = rateCommand.isAgencyRepair() ? (quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? 250 : 500) : (quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? 125 : 250)
                baseRate = rateCommand.isAgencyRepair() ? (quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? 0.5 : 0.75) : (quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? 0.25 : 0.5)

            } else if (quoteCommand.carCategory == 'SUV') {

                premium = quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? (rateCommand.isAgencyRepair() ? 250 : 125) : (rateCommand.isAgencyRepair() ? 500 : 250)
                baseRate = quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? (rateCommand.isAgencyRepair() ? 0.5 : 0.25) : (rateCommand.isAgencyRepair() ? 0.75 : 0.5)

            } else if (quoteCommand.carCategory == '4WD') {

                premium = quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? (rateCommand.isAgencyRepair() ? 500 : 250) : (rateCommand.isAgencyRepair() ? 1000 : 500)
                baseRate = quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? (rateCommand.isAgencyRepair() ? 0.5 : 0.25) : (rateCommand.isAgencyRepair() ? 0.75 : 0.5)

            } else if (quoteCommand.vechileTypeId in [VehicleTypeEnum.CONVERTIBLE.value, VehicleTypeEnum.COUPE.value] || quoteCommand.carCategory == 'BLK') {

                premium = quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? (rateCommand.isAgencyRepair() ? 500 : 250) : (rateCommand.isAgencyRepair() ? 1000 : 500)
                baseRate = quoteCommand.nationalityId == CountryEnum.EGYPT.id.intValue() ? (rateCommand.isAgencyRepair() ? 0.5 : 0.25) : (rateCommand.isAgencyRepair() ? 0.75 : 0.5)

            }


            rateCommand.minPremium += premium
            rateCommand.baseRate += baseRate

//            log.info(" orientalRateService.applyNationalitySpecificRate MinPremium -  ${rateCommand.minPremium}")
//            log.info("orientalRateService.applyNationalitySpecificRate baseRate -  ${rateCommand.baseRate}")

        }

        return rateCommand
    }

    RateCommand applyMakeSpecificRates(QuoteCommand quoteCommand, RateCommand rateCommand) {

        Model model = Model.findById(quoteCommand.modelId)
        Integer modelMasterId = model.modelMasterId as int
        if (quoteCommand.makeId == CarMakeEnum.TESLA.id && quoteCommand.insuredValue < 250000) {
            rateCommand.minPremium = 3500
            if (modelMasterId in [ModelMasterEnum.TESLA_MODEL_Y.id, ModelMasterEnum.TESLA_MODEL_S.id]) {
                rateCommand.baseRate = 3.0
                rateCommand.minPremium = 4000
            } else if (modelMasterId in [ModelMasterEnum.TESLA_MODEL_X.id]) {
                rateCommand.baseRate = 2.30
            } else if (modelMasterId in [ModelMasterEnum.TESLA_MODEL_S.id]) {
                rateCommand.baseRate = 2.59
            } else {
                rateCommand.baseRate = 2.0 //since there is no other model, but for safety, lets keep a good value here
            }
        } else if (rateCommand.agencyRepair && modelMasterId in [ModelMasterEnum.SUZUKI_SWIFT.id, ModelMasterEnum.SUZUKI_GIMNY.id, ModelMasterEnum.SUZUKI_ERTIGA.id]) {
            rateCommand.minPremium = 2000
            rateCommand.baseRate = 3.0
        } else if (rateCommand.agencyRepair && quoteCommand.makeId == CarMakeEnum.CITROEN.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.minPremium = 2100
            rateCommand.baseRate = 3.0
        } else if (quoteCommand.makeId == CarMakeEnum.CHERY.id && !rateCommand.agencyRepair) {
            //Apply Non agency Cherry Rate / Min premium
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.getValue()) {
                rateCommand.baseRate = 1.6
                rateCommand.minPremium = 1300
            } else if (quoteCommand.carCategory == 'SUV') {
                rateCommand.baseRate = 1.6
                rateCommand.minPremium = 1600
            }
        } else if (quoteCommand.makeId == CarMakeEnum.TOYOTA.id && modelMasterId in [
            511, 1062, 1280, 1236, 495
        ]) {
            // Rav4, Rush, Corolla Cross, C-HR, Avanza
            rateCommand.baseRate = rateCommand.agencyRepair ? 2.50 : 1.60
            rateCommand.minPremium = rateCommand.agencyRepair ? 2750 : 1500
        } else if ((modelMasterId in [
            514, 496, 1180, // TOYOTA
            390, 395, 396, 404, 405, 406, // NISSAN
            164, 159, 1222, 155 // FORD
        ] || model.id in [
            44041, 44007
        ]) && rateCommand.agencyRepair) {
            rateCommand.baseRate = 2.0
            rateCommand.minPremium = 2500
        } else if ((modelMasterId in [
            // 514, 496, 1180, // TOYOTA
            390, 404, 405, // NISSAN
            // 159, 1222, 1203 // FORD
        ] || model.id in [
            44041, 44007
        ]) && !rateCommand.agencyRepair) {
            rateCommand.baseRate = 1.80
            rateCommand.minPremium = 1170
        }


        return rateCommand
    }

    RateCommand applyChineseMakeSpecificRates(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (quoteCommand.makeId == CarMakeEnum.CHANGAN.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.CHANGAN.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.GEELY.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.GEELY.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.JAC.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.JAC.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.GAC.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.GAC.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.JETOUR.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.JETOUR.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.BAIC.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.BAIC.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.BAIC.id && quoteCommand.carCategory == '4WD') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 4 : 3.5
            rateCommand.minPremium = rateCommand.agencyRepair ? 4000 : 3500
        } else if (quoteCommand.makeId == CarMakeEnum.MG.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.MG.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 3.5 : 3.0
            rateCommand.minPremium = rateCommand.agencyRepair ? 3500 : 2500
        } else if (quoteCommand.makeId == CarMakeEnum.HONGQI.id && quoteCommand.carCategory == 'A') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 4 : 3.25
            rateCommand.minPremium = rateCommand.agencyRepair ? 5000 : 3000
        } else if (quoteCommand.makeId == CarMakeEnum.HONGQI.id && quoteCommand.carCategory == 'SUV') {
            rateCommand.baseRate = rateCommand.agencyRepair ? 4 : 3.25
            rateCommand.minPremium = rateCommand.agencyRepair ? 5000 : 3000
        }

        return rateCommand
    }

    Integer getCarAgeFromManufactureYear(QuoteCommand quoteCommand) {

        return ((new LocalDate()).getYear() - quoteCommand.manufactureYear)
    }

    boolean applyNationalityRate(QuoteCommand quoteCommand, RateCommand rateCommand) {

        Model model = Model.findById(quoteCommand.modelId)
        if (quoteCommand.makeId == CarMakeEnum.GAC.id && rateCommand.agencyRepair ||
            quoteCommand.makeId == CarMakeEnum.MG.id && rateCommand.agencyRepair) {
            return false
        }

        return true
    }

    RateCommand applyExcess(QuoteCommand quoteCommand, RateCommand rateCommand) {

        Model model = Model.load(quoteCommand.modelId)

        if ((model.modelMasterId.intValue() in [514, 496, 1180, // TOYOTA
                                                390, 395, 396, 404, 405, 406, // NISSAN
                                                164, 159, 1222, 155 // FORD
        ] || model.id in [44041, 44007]) && rateCommand.agencyRepair) {
            rateCommand.excess = "500"
        }

        return rateCommand
    }

    Boolean isPortalRatesFollowed(QuoteCommand quoteCommand) {


        if (quoteCommand.makeId in [
            CarMakeEnum.BORGWARD.id, CarMakeEnum.HAVAL.id
        ]) {
            return true
        } else if (quoteCommand.makeId == CarMakeEnum.GEELY.id && quoteCommand.insuredValue > 80000) {
            return true
        }


        return false
    }

    QuoteCommand customChecks(QuoteCommand quoteCommand, QuoteCommand clonedQuoteCommand) {

        InvokerHelper.setProperties(clonedQuoteCommand, quoteCommand.properties)

        CarValuationDto providerValuation = commonUtilService.getProviderValuation(InsuranceProviderEnum.ORIENTAL,
            !quoteCommand.isNonGccSpec,  quoteCommand.carValuation)

        BigDecimal newInsuredValue = quoteCommand.insuredValue
        if (newInsuredValue > providerValuation.valuationMax) {
            newInsuredValue = providerValuation.valuationMax
        } else if (newInsuredValue < providerValuation.valuationMin) {
            newInsuredValue = providerValuation.valuationMin
        }

        clonedQuoteCommand.carValuation = providerValuation
        log.info(".customChecks - providerValuation:${providerValuation}")
        clonedQuoteCommand.insuredValue = newInsuredValue

        clonedQuoteCommand
    }
}
