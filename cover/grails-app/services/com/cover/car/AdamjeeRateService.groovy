package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CityEnum
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.CarValuationDto
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper

/**
 * Ratings calculation for Al-Sagr.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class AdamjeeRateService {

    public static final Integer PROVIDER_ID = 12
    public static final Integer PRODUCT_COMPREHENSIVE_ID = 65
    public static final Integer PRODUCT_DYNA_TRADE_ID = 1011 //Offline now
    public static final Integer PRODUCT_TPL_ID = 66

    def commonUtilService
    def grailsApplication
    def ratingService


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = getCarCategory(quoteCommand.isNonGccSpec)
            quoteCommand.nationalityCategory = getNationalityCategory(quoteCommand.nationalityId)
            QuoteCommand clonedQuoteCommand = new QuoteCommand()
            clonedQuoteCommand = customChecks(quoteCommand, clonedQuoteCommand)

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(clonedQuoteCommand, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(clonedQuoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.carCategory = getCarCategory(quoteCommand.isNonGccSpec)
            quoteCommand.nationalityCategory = getNationalityCategory(quoteCommand.nationalityId)

            QuoteCommand clonedQuoteCommand = new QuoteCommand()
            clonedQuoteCommand = customChecks(quoteCommand, clonedQuoteCommand)

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(clonedQuoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(clonedQuoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)
        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            quoteCommand.carCategory = getCarCategory(quoteCommand.isNonGccSpec)
            quoteCommand.nationalityCategory = getNationalityCategory(quoteCommand.nationalityId)
            log.info("getTplRate - nationalityCategory:${quoteCommand.nationalityCategory}")
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true,
                        quoteCommand.requestSource, quoteCommand.nationalityCategory)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId

        log.info("getTplRate - premium:${rateCommand.premium}")

        rateCommand = applyTplDiscounts(quoteCommand, rateCommand)
        rateCommand = applyTplLoading(quoteCommand, rateCommand)
        log.info("getTplRate - after loading/discount premium:${rateCommand.premium}")

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)

        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        log.info("Adamjee Before Discount - premium:${rateCommand.premium}, min:${rateCommand.minPremium}")

//        if (rateCommand.premium == rateCommand.minPremium) {
//            rateCommand = applyMinPremiumDiscounts(quoteCommand, rateCommand)
//        }
        log.info("Adamjee after Discount - premium:${rateCommand.premium}, min:${rateCommand.minPremium}")

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = quoteCommand.isNonGccSpec ? applyCovers(rateCommand) : ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        log.info("Adamjee Excess - ${rateCommand.excess}")
        log.info("adamjeeRate.populateRatings - originalPre:${rateCommand.premium}")
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }


    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate.productId)
        log.info("quoteCommand.insuredValue:${quoteCommand.insuredValue}")
        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.carValuation = quoteCommand.carValuation
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        BigDecimal baseRate = getBaseRate(isAgency, quoteCommand, applicableRate)
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage

        rateCommand.baseRate = baseRate
        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private BigDecimal getBaseRate(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        BigDecimal minPremium = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        BigDecimal calculatedPremium = (baseRate * quoteCommand.insuredValue)/100
        log.info("adamjee.getBaseRate - baseRate:${baseRate}, minPremium:${minPremium}, calculatedPremium:${calculatedPremium}")

        baseRate
    }

    private boolean checkAgency(QuoteCommand quoteCommand, Integer productId) {

        boolean isAgency = false

//        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
//
//            /*if (quoteCommand.isBrandNew) {
//                isAgency = true
//            }*/
//
//            if (productId == PRODUCT_DYNA_TRADE_ID || ratingService.agencyBlacklisted(quoteCommand)) {
//                isAgency = false
//            }
//
//        }

        isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {
//        if (!quoteCommand.isNonGccSpec){
//            return false
//        }
//
//        if (quoteCommand.productId != PRODUCT_COMPREHENSIVE_ID){
//            return false
//        }

        if (quoteCommand.productId == PRODUCT_DYNA_TRADE_ID){
            return false
        }

        if (quoteCommand.customerAge < 25) {
            return false
        }

        // not eligible if uae driving experience is less then 1 year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            return false
        }

        if (quoteCommand.isNonGccSpec) {
            return false
        }

        if (quoteCommand.registrationCityId == CityEnum.ABU_DHABI.value() && productTypeEnum == CoverageTypeEnum.THIRD_PARTY){
            return false
        }

        /*if (quoteCommand.lastClaimPeriod != ClaimPeriodEnum.NEVER){
              return false
        }*/

//        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.isThirdParty) {
//            return false
//        }

        return ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)
    }

    RateCommand applyCovers(RateCommand rateCommand) {
//        if (!rateCommand.isAgencyRepair() && rateCommand.productId == PRODUCT_COMPREHENSIVE_ID) {
//            rateCommand.paCover = "yes" //Covered in case of Non Agency
//            rateCommand.showPaCoverAddon = false
//        }


        rateCommand.replacementCar = 'no'
        rateCommand.breakdownCover = CarCoversEnum.RSA_SILVER.shortName
        rateCommand.personalAccidentPax = 'no'
        rateCommand.paCover = 'no'
        rateCommand.carRegService = 'no'
        rateCommand.naturalCalamity = 'no'
        rateCommand.windScreenCover = 'no'

        return rateCommand
    }

    /*RateCommand applyMinPremiumDiscounts(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (!quoteCommand.hasClaim &&
            !quoteCommand.isFirstCar &&
            !quoteCommand.isPolicyExpired &&
            !rateCommand.agencyRepair &&
            isDiscountedMake(quoteCommand.makeId, quoteCommand.modelId)
            ) {

            int customerAge = quoteCommand.customerAge
            int minimumPremium = rateCommand.minPremium

            if (customerAge >= 30 && isDiscountedNationality(quoteCommand.nationalityId)) {
                if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value && quoteCommand.insuredValue <= 70000) {
                    minimumPremium = 1000
                } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value && quoteCommand.insuredValue <= 65000) {
                    minimumPremium = 800
                }

            } else if (customerAge >= 25 && customerAge < 30 ||
                (customerAge >= 30 && !isDiscountedNationality(quoteCommand.nationalityId))) {
                if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value && quoteCommand.insuredValue <= 70000) {
                    minimumPremium = 1100
                } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value && quoteCommand.insuredValue <= 65000) {
                    minimumPremium = 1100
                }
            }
            log.info("new minimumPremium:$minimumPremium")
            rateCommand.minPremium = minimumPremium
            rateCommand.premium = rateCommand.minPremium
        }

        return rateCommand
    }*/

    /**
     * Apply TPL discount
     * @param quoteCommand
     * @param rateCommand
     * @return
     */
    RateCommand applyTplDiscounts(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (!quoteCommand.isPolicyExpired && !quoteCommand.hasClaim
                && quoteCommand.customerAge >= 30 && quoteCommand.nationalityCategory == "PC") {
            Model model = Model.load(quoteCommand.modelId)
            Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndNcd(quoteCommand)
            log.info("noOfCyl:${model.noOfCyl}, noClaimYears:$noClaimYears, nationalityCategory:${quoteCommand.nationalityCategory}")

            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                if (model.noOfCyl <= 4) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 675
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 638
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 600
                    }
                } else if (model.noOfCyl <= 6) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 765
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 723
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 680
                    }
                } else if (model.noOfCyl == 8) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 855
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 808
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 760
                    }
                }  else if (model.noOfCyl > 8) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 1170
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 1105
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 1040
                    }
                }
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                if (model.noOfCyl <= 4) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 900
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 850
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 800
                    }
                } else if (model.noOfCyl <= 6) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 945
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 893
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 840
                    }
                } else if (model.noOfCyl == 8) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 990
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 935
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 880
                    }
                }  else if (model.noOfCyl > 8) {
                    if (noClaimYears == NcdEnum.YEAR1.value()) {
                        rateCommand.premium = 1080
                    } else if (noClaimYears == NcdEnum.YEAR2.value()) {
                        rateCommand.premium = 1020
                    } else if (noClaimYears >= NcdEnum.YEAR3.value()) {
                        rateCommand.premium = 960
                    }
                }
            }
        }

        return rateCommand
    }

    /**
     * Apply TPL Loading
     * @param quoteCommand
     * @param rateCommand
     * @return
     */
    RateCommand applyTplLoading(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (quoteCommand.customerAge < 30) {
            rateCommand.premium = rateCommand.premium.add(rateCommand.premium * 0.30)
        }

        return rateCommand
    }


    /*boolean isDiscountedNationality(Integer nationalityId) {
        Country nationality = Country.load(nationalityId)
        boolean isDiscountedNationality = !nationality.isArabic &&
            !(nationality.id in [CountryEnum.DOMINICA.id.intValue(), CountryEnum.DOMINICA_REPUBLIC.id.intValue()])
        log.info("isDiscountedNationality:$isDiscountedNationality")
        return isDiscountedNationality
    }*/

    //Get Nationality Category
    private String getNationalityCategory(Long nationalityId) {
        if (nationalityId in [CountryEnum.COMOROS.id, CountryEnum.EGYPT.id, CountryEnum.MOROCCO.id,
                              CountryEnum.KSA.id, CountryEnum.SUDAN.id, CountryEnum.SYRIA.id, CountryEnum.YEMEN.id,
                              CountryEnum.BANGLADESH.id, CountryEnum.BAHRAIN.id, CountryEnum.LIBYA.id]) {
            log.info("getNationalityCategory - LC")
            return "LC" //Loading Category
        } else if (nationalityId in [CountryEnum.AFGHANISTAN.id, CountryEnum.IRAN.id, CountryEnum.IRAQ.id,
                                     CountryEnum.JORDAN.id, CountryEnum.KUWAIT.id, CountryEnum.OMAN.id,
                                     CountryEnum.PAKISTAN.id, CountryEnum.QATAR.id, CountryEnum.SRILANKA.id,
                                     CountryEnum.PALESTINE.id, CountryEnum.UAE.id]) {
            log.info("getNationalityCategory - MC")
            return "MC" //Medium category
        }

        log.info("getNationalityCategory - PC")
        return "PC" //Preferred Category
    }

    private String getCarCategory(boolean isNonGccSpec) {
        return isNonGccSpec ? "NGCC" : "GCC"
    }

    private QuoteCommand customChecks(QuoteCommand quoteCommand, QuoteCommand clonedQuoteCommand) {
        log.info(".customChecks - quoteCommand.valuationDetail:${quoteCommand.carValuation}")
        InvokerHelper.setProperties(clonedQuoteCommand, quoteCommand.properties)

        CarValuationDto providerValuation = commonUtilService.getProviderValuation(InsuranceProviderEnum.ADAMJEE,
            !quoteCommand.isNonGccSpec,  quoteCommand.carValuation)

        BigDecimal newInsuredValue = quoteCommand.insuredValue
        if (newInsuredValue > providerValuation.valuationMax) {
            newInsuredValue = providerValuation.valuationMax
        } else if (newInsuredValue < providerValuation.valuationMin) {
            newInsuredValue = providerValuation.valuationMin
        }

        clonedQuoteCommand.carValuation = providerValuation
        log.info(".customChecks - providerValuation:${providerValuation}")

        clonedQuoteCommand.insuredValue = newInsuredValue

        clonedQuoteCommand
    }

}
