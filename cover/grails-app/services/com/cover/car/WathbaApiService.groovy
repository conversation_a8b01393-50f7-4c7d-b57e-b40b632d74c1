package com.cover.car

import com.cover.car.Wathba.WathbaQuoteCommand
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.util.UtilService
import com.safeguard.AutoDataTrimDto
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.Provider
import com.safeguard.ProviderApiService
import com.safeguard.ValuationSourceEnum
import com.safeguard.car.AutoDataTrim
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.vehicle.MakeExternal
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMasterExternal
import com.safeguard.car.vehicle.ModelYearExternal
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod

import javax.annotation.PostConstruct

@Transactional
class WathbaApiService extends ProviderApiService {

    def grailsApplication
    def providerApiService

    static final String CALCULATE_PREMIUM_API = "/motor/motorCalcPremiumAmwaj"
    static final String GET_AUTODATA_VEHICLE_DESC = "/motor/motorADVechicleDescription"

    String baseUrl
    String partnerId
    String apiKey

    List<Map<String, String>> headers = []


    @PostConstruct
    private void init() {
        baseUrl = grailsApplication.config.getProperty("wathba.host.baseUrl")
        partnerId = grailsApplication.config.getProperty("wathba.host.partnerId")
        apiKey = grailsApplication.config.getProperty("wathba.host.apiKey")
        headers = [
            ["provider": InsuranceProviderEnum.AL_WATHBA.code],
            ["Partner-Id": partnerId], ["Api-Key": apiKey]
        ]
    }


    List<ProviderRateCommand> getBaseRateMinPremium(WathbaQuoteCommand command) {
        if (!command) return []

        List<ProviderRateCommand> rateCommands = []
        rateCommands = getProducts(command)

        return rateCommands
    }


    List<ProviderRateCommand> getProducts(WathbaQuoteCommand command) {
        JSONElement response = callCalculatePremiumApi(command)

        if (response?.isSuccess == "Y" && response?.errorList == null) {
            CarQuoteCover quoteCover = new CarQuoteCover()
            JSONElement coverDetails = response
            quoteCover.quote = CarQuote.load(command.quoteId)
            quoteCover.provider = Provider.load(command.providerId)
            quoteCover.covers = coverDetails
            quoteCover.expiryDate = LocalDateTime.now().plusDays(7)
            quoteCover.insuredValue = command.adSumInsured.toInteger()
            quoteCover.save(failOnError: true)

            return toWathbaRateCommand(response, command)
        } else {
            return []
        }
    }

    JSONElement callCalculatePremiumApi(WathbaQuoteCommand command) {
        def requestBody = [
            "makeCode"          : command.makeCode,
            "modelCode"         : command.modelCode,
            "specDesc"          : command.autoDataSpecDesc,
            "sumInsured"        : command.adSumInsured,
            "mfgYear"           : command.manufactureYear.toString(),
            "isBrandNewYN"      : command.isBrandNew ? "Y" : "N",
            "noClaimYear"       : command.ncd.toInteger(),
            "insurerType"       : command.insurerType,
            "dob"               : command.dateOfBirth,
            "licenseDate"       : command.drivingLicenseIssueDate,
            "nationality"       : command.nationalityCode,
            "usageType"         : command.usageType,
            "registeredDate"    : command.registrationDate,
            "registeredLocation": command.registrationLocation,
            "isGccSpecYN"       : command.isNonGccSpec ? "N" : "Y",
            "promoCode"         : ""
        ]

        String encQuoteId = command.quoteId ? AESCryption.encrypt(command.quoteId + "") : null
        String url = baseUrl + CALCULATE_PREMIUM_API
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add(["encQuoteId": encQuoteId])
        log.info("${UtilService.getQuoteLoggingPrefix('callCalculatePremiumApi', command.quoteId, InsuranceProviderEnum.AL_WATHBA)} - url: ${url}, headers: ${customHeaders}, request: ${requestBody.toString()}")

        JSONElement response
        response = providerApiService.callApi(url, customHeaders, requestBody, HttpMethod.POST)
        log.info("${UtilService.getQuoteLoggingPrefix('callCalculatePremiumApi', command.quoteId, InsuranceProviderEnum.AL_WATHBA)} - url: ${url}, response: ${response}}")
        return response
    }

    List<ProviderRateCommand> toWathbaRateCommand(JSONElement response, QuoteCommand quoteCommand) {
        log.info("${UtilService.getQuoteLoggingPrefix('toWathbaRateCommand', quoteCommand.quoteId, InsuranceProviderEnum.AL_WATHBA)} - response: ${response}}")

        List<ProviderRateCommand> rateCommands = []

        response?.premiumInfo?.each { plan ->

            ProviderRateCommand rateCommand = new ProviderRateCommand()
            rateCommand.excess = plan?.deductibleAmt
            rateCommand.netPremium = new BigDecimal(plan?.finalPremiumAmt)
            rateCommand.productName = plan?.planName

            List<ProviderRateCommand.RateCoverCommand> includingCovers = []
            List<ProviderRateCommand.RateCoverCommand> optionalCovers = []


            plan?.covers?.each { cover ->
//                log.info("Response Covers ${plan?.covers}")
//                log.info("Current Cover ${cover}")
                ProviderRateCommand.RateCoverCommand coverCommand = new ProviderRateCommand.RateCoverCommand()
                coverCommand.code = cover?.coverCode
                coverCommand.name = cover?.coverDesc
                coverCommand.premium = cover?.premium
                coverCommand.benefits = cover?.sumInsured
                coverCommand.isOpted = cover?.Type

                if (cover?.Type == "N") {
                    log.info("Not Covered ${cover?.coverDesc}")
                }

                if (cover?.Type == "C") {
                    includingCovers.add(coverCommand)
                } else {
                    optionalCovers.add(coverCommand)
                }

            }

            rateCommand.covers = includingCovers
            rateCommand.optionalCovers = optionalCovers
            rateCommands.add(rateCommand)
        }

        return rateCommands
    }

    /**
     * Get Autodata Trim valuation only  by AD Make, Model, Year and trim
     * @param command
     * @return
     */
    def getAutoDataValuation(WathbaQuoteCommand command) {
        List<AutoDataTrimDto> autoDataTrimDtoList = getAutoDataTrims(command)
        def valuation = null
        autoDataTrimDtoList.each { trim ->
            if (command.autoDataSpecDesc.toUpperCase() == trim.description?.toUpperCase()) {
                valuation = [lowValue: trim.minPrice, highValue: trim.maxPrice, avgValue: trim.avgPrice]
            }
        }

        return valuation
    }

    /**
     * Get Autodata vehicle details by AD Make, Model and Year
     * @param command
     * @return
     */
    List<AutoDataTrimDto> getAutoDataTrims(Integer year, Model ycModel, String encQuoteId) {
        ExternalDataSource adDataSource = ExternalDataSource.load(ExternalDataSourceEnum.AUTODATA.id)

        ModelYearExternal adYear = ModelYearExternal.findByExternalDataSourceAndYear(adDataSource, year)
        MakeExternal adVehicleMake = MakeExternal.findByExternalDataSourceAndMake(adDataSource, ycModel.make)
        ModelMasterExternal adVehicleModelMaster = ModelMasterExternal.findByExternalDataSourceAndModelMasterAndModel(adDataSource, ycModel.modelMaster, ycModel)
        if (!adVehicleModelMaster) {
            adVehicleModelMaster = ModelMasterExternal.findByExternalDataSourceAndModelMaster(adDataSource, ycModel.modelMaster)
        }

        if (adYear == null || adVehicleMake == null || adVehicleModelMaster == null) {
            log.info("wathbaApi.getAutoDataTrims - missing data - year:$adYear, make:$adVehicleMake, model:$adVehicleModelMaster")
            return []
        }
        WathbaQuoteCommand command = new WathbaQuoteCommand()
        if (encQuoteId && encQuoteId != "null") {
            command.quoteId = AESCryption.decrypt(encQuoteId)?.toLong()
        } else {

            command.quoteId = null
        }
        command.makeCode = "AD" + adVehicleMake.externalId
        command.modelCode = "AD" + adVehicleModelMaster.externalId
        command.adModelYear = "AD" + adYear.externalId

        return getAutoDataTrims(command)
    }

    /**
     * Get AutoData Trim valuation details by AdmeId and Year
     * @param admeId
     * @param year
     * @return
     */
    AutoDataTrimDto getAutoDataTrimByAdmeIdAndYear(String admeId, Integer year) {
        ExternalDataSource adDataSource = ExternalDataSource.read(ExternalDataSourceEnum.AUTODATA.getId())
        ModelYearExternal adYearExternal = ModelYearExternal.findByExternalDataSourceAndYear(adDataSource, year)
        String adModelYear = "AD" + adYearExternal?.externalId
        LocalDateTime date = LocalDateTime.now()

        return AutoDataTrim.findByAdmeIdAndModelYearCodeAndDateCreatedBetween(admeId, adModelYear,
            date.minusDays(7).withTime(0, 0, 0, 0), date)
    }

    List<AutoDataTrimDto> getAutoDataTrims(WathbaQuoteCommand command) {
        try {
            HashSet<AutoDataTrimDto> uniqueADTrims = new HashSet<>()

            LocalDateTime date = LocalDateTime.now()
            List<AutoDataTrim> autoDataTrims = AutoDataTrim.findAllByMakeCodeAndModelYearCodeAndModelCodeAndDateCreatedBetween(
                command.makeCode, command.adModelYear, command.modelCode, date.minusDays(7).withTime(0, 0, 0, 0), date
            )

            if (autoDataTrims.size()) {
                autoDataTrims.each { trim ->
                    AutoDataTrimDto trimDto = new AutoDataTrimDto()
                    trimDto.admeId = trim.admeId
                    trimDto.description = trim.trimDescription
                    trimDto.minPrice = trim.minimumPrice
                    trimDto.avgPrice = trim.averagePrice
                    trimDto.maxPrice = trim.maximumPrice
                    trimDto.engineSize = trim.engineSize
                    trimDto.spec = trim.spec
                    trimDto.bodyType = trim.bodyType
                    trimDto.doors = trim.doors
                    trimDto.seats = trim.seats
                    trimDto.valuationSource = ValuationSourceEnum.AWNIC
                    uniqueADTrims.add(trimDto)
                }

                return new ArrayList<>(uniqueADTrims)
            }

            List<AutoDataTrimDto> autoDataTrimDtoList = []
            JSONElement response = callGetAutoDataVehicleDescApi(command)

            if (response?.isSuccess == "Y") {
                response?.vehicleDetails?.each { trim ->
                    AutoDataTrimDto trimDto = new AutoDataTrimDto()
                    trimDto.admeId = trim.admeid
                    trimDto.makeCode = trim.makeCode
                    trimDto.modelYearCode = trim.modelYearCode
                    trimDto.modelCode = trim.modelCode
                    trimDto.description = trim.description
                    trimDto.minPrice = Integer.parseInt(trim.minPrice + "")
                    trimDto.avgPrice = Integer.parseInt(trim.avgPrice + "")
                    trimDto.maxPrice = Integer.parseInt(trim.maxPrice + "")
                    trimDto.engineSize = trim.engineSize
                    trimDto.spec = trim.spec
                    trimDto.bodyType = trim.bodyType
                    trimDto.doors = trim.doors
                    trimDto.seats = trim.seats
                    trimDto.noOfCyl = trim.noOfCyls
                    trimDto.finalDrive = trim.finalDrive
                    trimDto.vehicleType = trim.vehicleType
                    trimDto.valuationSource = ValuationSourceEnum.AWNIC
                    autoDataTrimDtoList.add(trimDto)
                }
            }

            saveAutoDataTrims(autoDataTrimDtoList)

            return autoDataTrimDtoList
        } catch (Exception e) {
            log.error("Exception while calling getAutoDataTrims", e)
            return []
        }
    }

    void saveAutoDataTrims(List<AutoDataTrimDto> autoDataTrimDtos) {
        autoDataTrimDtos.each {
            it.toAutoDataTrim().save(failOnError: true)
        }
    }

    private JSONElement callGetAutoDataVehicleDescApi(WathbaQuoteCommand command) {
        log.info("wathbaApiService.callGetAutoDataVehicleDescApi - [year:${command.adModelYear}, make:${command.makeCode}, model:${command.modelCode}]")

        def requestBody = [
            "region"        : "ALL",
            "modelYearCode" : command.adModelYear,
            "makeCode"      : command.makeCode,
            "modelCode"     : command.modelCode,
            "requestedValue": command.insuredValue ? command.insuredValue.toInteger().toString() : "0"
        ]

        String encQuoteId = command.quoteId ? AESCryption.encrypt(command.quoteId + "") : null
        String url = baseUrl + GET_AUTODATA_VEHICLE_DESC
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add(["encQuoteId": encQuoteId])
        log.info("${UtilService.getQuoteLoggingPrefix('callGetAutoDataVehicleDescApi', command.quoteId, InsuranceProviderEnum.AL_WATHBA)} - url: ${url}, headers: ${customHeaders}, request: ${requestBody.toString()}")

        JSONElement response
        response = providerApiService.callApi(url, customHeaders, requestBody, HttpMethod.POST)
        log.info("${UtilService.getQuoteLoggingPrefix('callGetAutoDataVehicleDescApi', command.quoteId, InsuranceProviderEnum.AL_WATHBA)} - url: ${url}, response: ${response}}")

        return response
    }

}
