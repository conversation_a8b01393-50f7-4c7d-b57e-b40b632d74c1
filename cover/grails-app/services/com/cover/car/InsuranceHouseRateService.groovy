package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CarMakeEnum
import com.safeguard.ClaimPeriodEnum
import com.safeguard.CountTypeEnum
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import grails.transaction.Transactional
import org.joda.time.LocalDate

@Transactional
class InsuranceHouseRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 36
    public static final Integer COMPREHENSIVE_PRODUCT_ID = 1033
    public static final Integer COMPREHENSIVE_HVV_PRODUCT_ID = 1035 //Offline
    public static final Integer TPL_PRODUCT_ID = 1034
    public static final Integer DYNATRADE_PRODUCT_ID = 1036 //Offline

    List<RateCommand> getRates(QuoteCommand command, boolean isOffline) {
        log.info("insuranceHouseRate.getRates - entering with quoteCommand:${command}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        command.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(command)
        log.info("insuranceHouseRate.getRates - carAge:${command.carAge}")

        if (checkEligibility) {
            log.info("insuranceHouseRate.getRates - checkEligibility:${checkEligibility}")

            QuoteCommand quoteCommand = checkQuoteCommand(ratingService.cloneQuoteCommand(command))
            quoteCommand.carCategory = null

            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("insuranceHouseRate.getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                log.info("insuranceHouseRate.getRates - applicableRates:${applicableRates.size()}")

                for (rate in applicableRates) {
                    if (checkProductEligibility(rate.productId.intValue(), quoteCommand)) {
                        RateCommand rateCommand = populateRatings(quoteCommand, rate)
                        if (rateCommand != null) {
                            rateList.add(rateCommand)
                        }
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand command, boolean isOffline) {

        QuoteCommand quoteCommand = ratingService.cloneQuoteCommand(command)
        quoteCommand = checkQuoteCommand(quoteCommand)

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {

            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("insuranceHouseRate.getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.carCategory = null
            if (checkProductEligibility(quoteCommand.productId, quoteCommand)) {

                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

                if (applicableRates) {
                    ProductBaseRate rate = applicableRates.first()
                    rateCommand = populateRatings(quoteCommand, rate)
                }
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        log.info("insuranceHouseRate.populateRatings - rate:${rate.id}, agency:${rate.baseRateAgency}, garage:${rate.baseRateGarage}")

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        if (rateCommand == null) {
            return null
        }

        rateCommand = ratingService.checkMinimumPremium(rateCommand)

        rateCommand = applyDiscounts(quoteCommand, rateCommand)

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        if (quoteCommand.carAge == 1 && quoteCommand.insuredValue > 250000 && isAgency) {
            applicableRate.baseRateAgency = 1.4
        }
        if (applicableRate.productId == COMPREHENSIVE_HVV_PRODUCT_ID && !isAgency) {
            return null
        }

        if (applicableRate.productId == COMPREHENSIVE_HVV_PRODUCT_ID && !isAgency && quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value){
            applicableRate.minPremiumGarage = 1040
        } else if (applicableRate.productId == COMPREHENSIVE_HVV_PRODUCT_ID && !isAgency && quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value){
            applicableRate.minPremiumGarage = 1400
        }

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue

        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        if (quoteCommand.isBrandNew && applicableRate.baseRateBrandNew) {
            rateCommand.baseRate = applicableRate.baseRateBrandNew
        } else {
            rateCommand.baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        }
        rateCommand.premium = ratingService.calculate(rateCommand.baseRate , quoteCommand.insuredValue)

        if (quoteCommand.isBrandNew && applicableRate.minPremiumBrandNew) {
            rateCommand.minPremium = applicableRate.minPremiumBrandNew
        } else {
            rateCommand.minPremium = rateCommand.agencyRepair ?
                applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        }

        rateCommand.basePremium = rateCommand.premium

        log.info("insuranceHouseRate.calculatePremium - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand command, boolean isOffline) {

        QuoteCommand quoteCommand = ratingService.cloneQuoteCommand(command)
        quoteCommand = checkQuoteCommand(quoteCommand)

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("insuranceHouseRate.getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId

        rateCommand = applyDiscounts(quoteCommand, rateCommand)

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyAdditionalTplFees(rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate rate) {

        if (!ratingService.allowAgency()){
            return false
        }

        int carAge = quoteCommand.carAge

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (quoteCommand.isBrandNew || carAge <= 1) {
                // if car age is <= 1 then agency for all products
                isAgency = true

            } else if (carAge == 2 && quoteCommand.carAgeInMonthsByRegistrationDate <= 13 &&
                quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value() && quoteCommand.isOldAgency) {
                // if car age is 2 and at-least 1 years NCD
                isAgency = true
            }

            if (rate.productId == DYNATRADE_PRODUCT_ID || quoteCommand.hasClaim) {
                isAgency = false
            }
        }
        return isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            if (quoteCommand.isThirdParty) {
                isEligible = false
            }
        }

        if (quoteCommand.isPolicyExpired) {
            isEligible = false
        }

        if (quoteCommand.isNonGccSpec && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            isEligible = false
        }

        //Only Eligible for DL at least 1 year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id &&
            quoteCommand.internationalDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id) {
            isEligible = false
        }

        if (quoteCommand.customerAge >= 23 && quoteCommand.customerAge < 25
            && quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.THREE_TO_FOUR.id) {
            isEligible = false
        }

        isEligible
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rateCommand) {

        int discountPercentage = 30

        if (quoteCommand.noClaimsDiscountId &&
            quoteCommand.vechileTypeId in [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.SEDAN.value] &&
            !quoteCommand.isPolicyExpired &&
            rateCommand.productId in [COMPREHENSIVE_PRODUCT_ID, TPL_PRODUCT_ID] &&
            !rateCommand.agencyRepair) {

            // COVID discount
            rateCommand.premium = rateCommand.premium
                .subtract(rateCommand.basePremium * (discountPercentage / 100))

            if (rateCommand.premium < rateCommand.minPremium) {
                rateCommand.premium = rateCommand.minPremium
            }
            return rateCommand
        }

        boolean isEligibleForSelfDecDiscount = ratingService.isEligibleForSelfDecDiscount(quoteCommand)
        if (isEligibleForSelfDecDiscount &&
            quoteCommand.vechileTypeId in [VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.SEDAN.value] &&
            !quoteCommand.isPolicyExpired &&
            !rateCommand.agencyRepair && rateCommand.productId in [COMPREHENSIVE_PRODUCT_ID, TPL_PRODUCT_ID]) {

            Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)

            // COVID discount
            rateCommand.premium = rateCommand.premium.subtract(rateCommand.basePremium * (discountPercentage / 100))
            rateCommand.noClaimDiscountPercent = discountPercentage   //40%
            rateCommand.requiredSelfDeclarationNumber = noClaimYears  //At least 3 year self declaration is required

            if (rateCommand.premium < rateCommand.minPremium) {
                rateCommand.premium = rateCommand.minPremium
            }
            return rateCommand
        }

        return rateCommand
    }

    /**
     * Check if product is Eligible to be quoted
     * @param quoteCommand
     * @param product
     * @return
     */
    private boolean checkProductEligibility(Integer productId, QuoteCommand quoteCommand) {
        boolean isEligible = true

        if (productId == DYNATRADE_PRODUCT_ID) {
            //Not Eligible if No NCD or NCD is less than the car age
            if (!quoteCommand.noClaimsDiscountId ||
                    quoteCommand.noClaimsDiscountId < (quoteCommand.carAge - 1)) {
                isEligible = false
            }
        }

        //Chinese cars not eligible for Comprehensive
        Make make = Make.read(quoteCommand.makeId)
        if (make.countryId == CountryEnum.CHINA.id && productId != TPL_PRODUCT_ID) {
            isEligible = false
        }

        return isEligible
    }

    RateCommand applyAdditionalTplFees(RateCommand rateCommand) {
        // additional fee 10 AED for RSA
        rateCommand.premium = rateCommand.premium.add(10)
        rateCommand
    }

    QuoteCommand checkQuoteCommand(QuoteCommand quoteCommand){
        Model model = Model.findById(quoteCommand.modelId)
        if (model.modelMaster.id in [416, 421, 441, 443, 445, 191, 1091, 206, 202, 268, 255, 1209, 267, 317, 1249, 394, 393, 403, 480, 490, 489, 485]){
            quoteCommand.vechileTypeId = 6
        }
        return quoteCommand
    }

}
