package com.cover.car

import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.qic.QicQuoteCommand
import com.cover.car.qic.QicRateCommand
import com.safeguard.AddonCodeEnum
import com.safeguard.Country
import com.safeguard.CoverageTypeEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.ExtraFieldCodeEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.Product
import com.safeguard.Provider
import com.safeguard.QuoteExtraField
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.car.NoOfCylEnum
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.MakeExternal
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMasterExternal
import com.safeguard.car.vehicle.ModelYearExternal
import grails.converters.JSON
import grails.transaction.Transactional
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime

@Transactional
class QatarRateV2Service extends BaseRatingService {

    def commonUtilService
    def qicApiService
    def ratingService

    public static final Integer PROVIDER_ID = 3
    public static final Integer PRODUCT_BASIC_ID = 80
    public static final Integer PRODUCT_PRESTIGE_ID = 8
    public static final Integer PRODUCT_PRESTIGE_PLUS_ID = 81
    public static final Integer PRODUCT_TPL_ID = 59

    public static final String COMPREHENSIVE_PRODUCT_CODE = "0110"
    public static final String TPL_PRODUCT_CODE = "0100"

    public static final def BASIC_PRODUCT_SCHEME_CODES = ["2150"] //non agency, there is no agency in it
    public static final String[] PRESTIGE_SCHEME_CODES = ["2151", "2152"] //2151 - agency, 2152 - non agency
    public static final String[] PRESTIGE_PLUS_SCHEME_CODES = ["2153", "2154"] //2153 - agency, 2154 - non agency
    public static final String[] TPL_SCHEME_CODES = ["0221", "0224"]

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        try {
            quoteCommand.providerId = PROVIDER_ID
            QicQuoteCommand qicQuoteCommand = QicQuoteCommand.generateQuoteCommand(quoteCommand)
            List<ProviderRateCommand> qicRateCommands = []

            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)

            if (!checkEligibility) {
                return []
            }

            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return []
            }

            if (!qicQuoteCommand.autoDataSpecId || qicQuoteCommand.autoDataSpecId == "0") {
                return []
            }

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            CarQuoteCover carQuoteCover = CarQuoteCover.
                findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                    Provider.load(PROVIDER_ID), LocalDateTime.now(), CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value()))
            if (carQuoteCover) {
                log.info("Cover found in db")
                qicRateCommands = qicApiService.convertSchemesToRateCommand(JSON.parse(carQuoteCover.covers),
                    carQuoteCover.providerQuoteNo, carQuoteCover.insuredValue)
            } else {
                log.info("Calling QIC API")
                qicQuoteCommand = toQicQuoteCommand(quoteCommand,  CoverageTypeEnum.COMPREHENSIVE)

                def startTime = System.currentTimeMillis()

                qicRateCommands = qicApiService.getBaseRateMinPremium(qicQuoteCommand, CoverageTypeEnum.COMPREHENSIVE)

                def endTime = System.currentTimeMillis()
                def elapsedTime = (endTime - startTime)/1000.0
                log.info("QIC API call took ${elapsedTime} seconds against quoteId: ${quoteCommand.quoteId}")
            }
            log.info("qatarRateV2.getRates - qicRateCommands:$qicRateCommands")

            List<RateCommand> rateCommands = []
            qicRateCommands.each {
                if (it.productCode != TPL_PRODUCT_CODE) {
                    RateCommand rateCommand = toRateCommand(qicQuoteCommand, it, isOffline)
                    if (rateCommand) {
                        rateCommands.add(populateRatings(ratingService, qicQuoteCommand, rateCommand))
                    } else {
                        log.error("Null rateCommand for $it ")
                    }
                }
            }

            return rateCommands
        } catch (Exception e){
            log.error("getRates - Exception: ${e.getMessage()}", e)
            return []
        }
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        try {
            QicQuoteCommand qicQuoteCommand = QicQuoteCommand.generateQuoteCommand(quoteCommand)

            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)

            if (!checkEligibility) {
                return []
            }

            if (!qicQuoteCommand.autoDataSpecId || qicQuoteCommand.autoDataSpecId == "0") {
                return []
            }

            RateCommand rateCommand = null

            List<ProviderRateCommand> qicRateCommands = []
            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            CarQuoteCover carQuoteCover = CarQuoteCover.
                findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                    Provider.load(PROVIDER_ID), LocalDateTime.now(), CoverageType.load(CoverageTypeEnum.COMPREHENSIVE.value()))

            if (carQuoteCover) {
                qicRateCommands = qicApiService.convertSchemesToRateCommand(JSON.parse(carQuoteCover.covers),
                    carQuoteCover.providerQuoteNo, carQuoteCover.insuredValue)
            } else {
                log.info("Calling QIC API")
                qicQuoteCommand = toQicQuoteCommand(quoteCommand,  CoverageTypeEnum.COMPREHENSIVE)
                qicRateCommands = qicApiService.getBaseRateMinPremium(qicQuoteCommand, CoverageTypeEnum.COMPREHENSIVE)
            }

            String schemeCode = getSchemeCode(quoteCommand.productId, quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY)
            log.info("qatarRateV2.getRates - qicRateCommands:$qicRateCommands, selected schemeCode:$schemeCode")

            qicRateCommands.each {
                if (it.productCode != TPL_PRODUCT_CODE && it.schemeCode == schemeCode) {
                    rateCommand = toRateCommand(qicQuoteCommand, it, isOffline)
                    if (rateCommand) {
                        rateCommand = populateRatings(ratingService, qicQuoteCommand, rateCommand)
                    } else {
                        log.error("Null rateCommand for $it ")
                    }
                }
            }

            return rateCommand
        } catch (Exception e){
            log.error("getRate Exception: ${e.getMessage()}", e)
            return []
        }
    }

    def getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        try{
            quoteCommand.providerId = PROVIDER_ID
            QicQuoteCommand qicQuoteCommand = QicQuoteCommand.generateQuoteCommand(quoteCommand)

            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY, isOffline)

            if (!checkEligibility) {
                return null
            }

            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return null
            }

            List<ProviderRateCommand> qicRateCommands = []

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            CarQuoteCover carQuoteCover = CarQuoteCover.
                findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageType(false, quote,
                    Provider.load(PROVIDER_ID), LocalDateTime.now(), CoverageType.load(CoverageTypeEnum.THIRD_PARTY.value()))

            if (carQuoteCover) {
                log.info("Cover found in db")
                qicRateCommands = qicApiService.convertSchemesToRateCommand(JSON.parse(carQuoteCover.covers),
                    carQuoteCover.providerQuoteNo, null)
            } else {
                log.info("Calling QIC API")
                qicQuoteCommand = toQicQuoteCommand(quoteCommand, CoverageTypeEnum.THIRD_PARTY)
                qicRateCommands = qicApiService.getBaseRateMinPremium(qicQuoteCommand, CoverageTypeEnum.THIRD_PARTY)
            }

            log.info("qatarRateV2.getTplRate - qicRateCommands:$qicRateCommands")

            RateCommand rateCommand = null
            qicRateCommands.each {
                if (it.productCode == TPL_PRODUCT_CODE) {
                    rateCommand = toRateCommand(qicQuoteCommand, it, isOffline)
                    if (rateCommand) {
                        rateCommand = populateRatings(ratingService, qicQuoteCommand, rateCommand)
                    }
                }
            }

            return rateCommand
        } catch (Exception e){
            log.error("getTplRate - Exception: ${e.getMessage()}", e)
        }
    }

    private QicQuoteCommand toQicQuoteCommand(QuoteCommand quoteCommand, CoverageTypeEnum coverageTypeEnum) {
        if (quoteCommand.autoDataSpecId == null) return

        ExternalDataSource qicDataSource = ExternalDataSource.load(ExternalDataSourceEnum.QIC.id)
        ExternalDataSource adDataSource = ExternalDataSource.load(ExternalDataSourceEnum.AUTODATA.id)

        Make make = Make.load(quoteCommand.makeId)
        Model model = Model.read(quoteCommand.modelId)

        QicQuoteCommand qicQuoteCommand = QicQuoteCommand.generateQuoteCommand(quoteCommand)
        if (qicQuoteCommand.policyStartDate < LocalDate.now()) {
            qicQuoteCommand.policyStartDate = LocalDate.now().plusDays(1)
        }

        ModelMasterExternal qicModelMasterExternal = ModelMasterExternal.findByExternalDataSourceAndModelMasterAndModel(qicDataSource, model.modelMaster, model)
        if (!qicModelMasterExternal) {
            qicModelMasterExternal = ModelMasterExternal.findByExternalDataSourceAndModelMaster(qicDataSource, model.modelMaster)
        }

        ModelMasterExternal adModelMasterExternal = ModelMasterExternal.findByExternalDataSourceAndModelMasterAndModel(adDataSource, model.modelMaster, model)
        if (!adModelMasterExternal) {
            adModelMasterExternal = ModelMasterExternal.findByExternalDataSourceAndModelMaster(adDataSource, model.modelMaster)
        }

        qicQuoteCommand.makeCode = MakeExternal.findByExternalDataSourceAndMake(qicDataSource, make)?.externalId
        qicQuoteCommand.modelCode = qicModelMasterExternal?.externalId
        qicQuoteCommand.adMakeCode = MakeExternal.findByExternalDataSourceAndMake(adDataSource, make)?.externalId
        qicQuoteCommand.adModelCode = adModelMasterExternal?.externalId
        qicQuoteCommand.adModelYear =  ModelYearExternal.findByExternalDataSourceAndYear(adDataSource, qicQuoteCommand.manufactureYear)?.externalId
        log.info("qicQuoteCommand.modelCode: ${qicQuoteCommand.modelCode}, qicQuoteCommand.adModelCode: ${qicQuoteCommand.adModelCode}")
        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            def adValuation = qicApiService.getAutoDataValuation(qicQuoteCommand.adModelYear,
                qicQuoteCommand.adMakeCode, qicQuoteCommand.adModelCode, qicQuoteCommand.autoDataSpecId)
            if (adValuation.lowValue &&
                adValuation.lowValue <= quoteCommand.insuredValue && quoteCommand.insuredValue <= adValuation.highValue) {
                //Customer provided price is between the limit
                qicQuoteCommand.adSumInsured = quoteCommand.insuredValue
            } else if (quoteCommand.insuredValue < adValuation.lowValue) {
                qicQuoteCommand.adSumInsured = adValuation.lowValue
            } else if (quoteCommand.insuredValue > adValuation.highValue) {
                qicQuoteCommand.adSumInsured = adValuation.highValue
            } else {
                qicQuoteCommand.adSumInsured = null
            }
        } else {
            qicQuoteCommand.adSumInsured = null
        }
        /*
        //For now, no need to round off, as we are using average value
        if (qicQuoteCommand.adSumInsured) {
            //Rounding down to 1000
            Integer adSumInsured = new BigDecimal(qicQuoteCommand.adSumInsured).toInteger()
            adSumInsured = adSumInsured / 1000
            adSumInsured = adSumInsured * 1000
            qicQuoteCommand.adSumInsured = adSumInsured + ""
        }*/
        qicQuoteCommand.vehicleTypeCode = VehicleTypeEnum.findById(quoteCommand.vechileTypeId).qicCode
        qicQuoteCommand.noOfCylCode = NoOfCylEnum.findById(model.noOfCyl).qicCode
        qicQuoteCommand.nationalityCode = Country.load(quoteCommand.nationalityId).qicCode
        qicQuoteCommand.seatingCapacity = model.numberOfSeats
        qicQuoteCommand.noClaimYear = quoteCommand.noClaimsDiscountId
        qicQuoteCommand.selfDeclarationYear = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
        qicQuoteCommand.driverExp = DrivingExperienceEnum.findById(quoteCommand.localDrivingExperienceId).experienceInYears

        return qicQuoteCommand
    }

    private Integer getProductId(String schemeCode) {
        log.info("qatarRateV2.getProductId - schemeCode:$schemeCode")

        if (schemeCode in BASIC_PRODUCT_SCHEME_CODES) {
           return PRODUCT_BASIC_ID
        } else if (schemeCode in PRESTIGE_SCHEME_CODES) {
            return PRODUCT_PRESTIGE_ID
        } else if (schemeCode in PRESTIGE_PLUS_SCHEME_CODES) {
            return PRODUCT_PRESTIGE_PLUS_ID
        } else if (schemeCode in TPL_SCHEME_CODES) {
            return PRODUCT_TPL_ID
        }
        return null
    }

     String getSchemeCode(Integer productId, Boolean isAgency){
        log.info("qatarRateV2.getSchemeCode - productId:$productId")
        if (productId == PRODUCT_BASIC_ID){
            return BASIC_PRODUCT_SCHEME_CODES.first()
        } else if (productId == PRODUCT_PRESTIGE_ID && isAgency){
            return PRESTIGE_SCHEME_CODES[0]
        } else if (productId == PRODUCT_PRESTIGE_ID && !isAgency){
            return PRESTIGE_SCHEME_CODES[1]
        } else if (productId == PRODUCT_PRESTIGE_PLUS_ID && isAgency){
            return PRESTIGE_PLUS_SCHEME_CODES[0]
        } else if (productId == PRODUCT_PRESTIGE_PLUS_ID && !isAgency){
            return PRESTIGE_PLUS_SCHEME_CODES[1]
        } else if (productId == PRODUCT_TPL_ID) {
            return TPL_SCHEME_CODES[1]
        }
        return null
    }

    private RateCommand toRateCommand(QicQuoteCommand quoteCommand, QicRateCommand providerRateCommand,
                                      boolean isOffline) {
        Integer productId = getProductId(providerRateCommand.schemeCode)
        log.info("qatarRateV2.toRateCommand - productId:$productId")
        if (!productId) return null

        Product product = Product.read(productId)
        if (!product || !product.isActive() || product.isOffline != isOffline) {
            return null
        }

        RateCommand rateCommand = new RateCommand()
        rateCommand.productId = productId

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand.insuredValue = providerRateCommand.insuredValue

        rateCommand.basePremium = providerRateCommand.netPremium
        rateCommand.premium = rateCommand.basePremium
        rateCommand.minPremium = rateCommand.premium
        rateCommand.currency = quoteCommand.currency

        rateCommand.dynamicAddons = providerRateCommand.optionalCovers.
            findAll {it.premium != "0" }.
            collect {
                [label: it.name, price: new BigDecimal(it.premium), providerCode: it.code,
                 code: AddonCodeEnum.DYNAMIC_ADDON.code,
                 description: it.help ?: (it.code == CarCoversEnum.ORANGE_CARD.qicCode ? 'Orange Card - Oman' : '')]
            }
        log.info("rateCommand.dynamicAddons:${rateCommand.dynamicAddons}")

        //Find RSA Cover which is free
        List allCovers = []
        allCovers.addAll(providerRateCommand.covers)
        allCovers.addAll(providerRateCommand.optionalCovers)

        rateCommand.agencyRepair = allCovers.find {
            it.premium == "0" && it.code == CarCoversEnum.AGENCY_REPAIR.qicCode } != null

        def rsaCover = allCovers.findAll {
            it.code in [CarCoversEnum.RSA_BRONZE.qicCode, CarCoversEnum.RSA_GOLD.qicCode,
                            CarCoversEnum.RSA_SILVER.qicCode, CarCoversEnum.RSA_PLATINUM.qicCode] }.sort {new BigDecimal(it.premium)}
        def firstRsaCover = rsaCover?.first()
        rateCommand.breakdownCover = firstRsaCover ?
            (firstRsaCover.premium == "0" ? CarCoversEnum.findByQicCode(firstRsaCover.code).shortName : firstRsaCover.premium) : "no"
        if (firstRsaCover && firstRsaCover.help) {
            //Override roadside services with API data
            String roadSideServies = firstRsaCover.help.replace("Help : ", "").replace("Help: ", "")
            rateCommand.roadsideAssistances = roadSideServies.split(",")
        }

        rateCommand.excess = providerRateCommand.excess

        ProviderRateCommand.RateCoverCommand pax = allCovers.find {
            it.code == CarCoversEnum.PAB_PASSENGER.qicCode }?.subMap(['code', 'name', 'premium', 'help'])
        rateCommand.personalAccidentPax = pax ? (pax.premium == "0" ? "yes" : pax.premium) : "no"

        ProviderRateCommand.RateCoverCommand pad = allCovers.find {
            it.code == CarCoversEnum.PAB_DRIVER.qicCode }?.subMap(['code', 'name', 'premium', 'help'])
        rateCommand.paCover = pad ? (pad.premium == "0" ? "yes" : pad.premium) : "no"

        ProviderRateCommand.RateCoverCommand rac = allCovers.find {
            it.code == CarCoversEnum.RENT_A_CAR.qicCode }?.subMap(['code', 'name', 'premium', 'help'])
        rateCommand.replacementCar = rac ? (rac.premium == "0" ? "yes" : rac.premium) : "no"

        ProviderRateCommand.RateCoverCommand offroadCover = allCovers.find {
            it.code == CarCoversEnum.OFF_ROAD_COVER.qicCode }?.subMap(['code', 'name', 'premium', 'help'])
        rateCommand.offRoadDesertRecovery = offroadCover ? (offroadCover.premium == "0" ? "yes" : offroadCover.premium) : "no"

        return rateCommand
    }

    BigDecimal getGrossPremium(CarQuote carQuote, String providerQuoteNo, List<String> providerAddonCodes) {

        Product product = carQuote.product
        String schemeCode = getSchemeCode(carQuote.productId.intValue(), carQuote.isAgencyRepair)
        String productCode = COMPREHENSIVE_PRODUCT_CODE
        if (product.type.id == CoverageTypeEnum.THIRD_PARTY.value()) {
            productCode = TPL_PRODUCT_CODE
        }

        BigDecimal netPremium = qicApiService.getNetPremium(providerQuoteNo, schemeCode, productCode, providerAddonCodes)
        BigDecimal grossPremium = commonUtilService.removeVAT(netPremium)

        return grossPremium
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE, boolean isOffline) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (quoteCommand.hasClaim && coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE) {
            return false
        }
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            return false
        }


        isEligible
    }

}
