package com.cover.car.egy

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.health.commands.HealthRateCommand
import com.safeguard.Product
import com.safeguard.ProductType
import com.safeguard.QuoteResultEgy

//import com.safeguard.QuoteResultEgy
import com.safeguard.Renewal
import com.safeguard.car.CarQuote
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

@Transactional
class EgyQuoteService {

    def quoteService
    def ratingService
    def egyGigRateService
    def egyAropeRateService
    def egyIskanRateService
    def egyOrientRateService
    def egyMisrRateService
    def additionalChargesService
    def egyMohandesRateService
    def egyTokyoMarineRateService

    @Transactional(readOnly = true)
    List<RateCommand> getRatings(QuoteCommand quoteCommand, boolean isOffline) {
        log.info "egyQuote.getRatings - ${quoteCommand}"
        List<RateCommand> rateList = []

        Model model = Model.get(quoteCommand.modelId)
        quoteCommand.vechileTypeId = model.vehicleType.id
        quoteCommand.productId = null
        quoteCommand.providerId = null

        rateList.addAll(egyGigRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(egyAropeRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(egyIskanRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(egyOrientRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(egyMisrRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(egyMohandesRateService.getRates(quoteCommand, isOffline))
        rateList.addAll(egyTokyoMarineRateService.getRates(quoteCommand, isOffline))

        List<RateCommand> rateCommandsAfterAdditionCharges = additionalChargesService
                                    .addAdditionalChargesToPremiums(rateList, ProductType.findByName('CAR'))

        return rateCommandsAfterAdditionCharges
    }

    @Transactional(readOnly = true)
    getRatings(Integer quoteId, boolean isOffline = false) {
        log.info "egyQuote.getRatings with quoteId - ${quoteId}, isOffline - ${isOffline}"

        CarQuote carQuote = CarQuote.get(quoteId)
        List<RateCommand> ratings = []
        if (carQuote) {
            QuoteCommand quoteCommand = quoteService.toQuoteCommand(carQuote)

            ratings = getRatings(quoteCommand, isOffline)

            if (carQuote.isRenewal) {

                Renewal renewal = Renewal.findByCarQuote(carQuote)

                if (renewal) {
                    //Remove renewal product and products from same provider
                    ratings = ratings.findAll {
                        Product p = Product.findById(it.productId)

                        (p.providerId != renewal.product.providerId && p.id != renewal.productId)
                    }

                    if (renewal.hasPreviousProvider) {
                        RateCommand renewalRateCommand = new RateCommand(productId: renewal.productId)
                        renewalRateCommand.currency = quoteCommand.currency
                        ratingService.applyCovers(quoteCommand, renewalRateCommand)
                        renewalRateCommand.toRenewal(renewal)
                        renewalRateCommand = ratingService.applyRenewalCover(quoteCommand, renewalRateCommand)

                        //ratingService.applyC4meFees(renewalRateCommand, quoteCommand.policyStartDate)
                        //renewalRateCommand = ratingService.applyExtraDiscount(renewalRateCommand)

                        ratings << renewalRateCommand //Adding renewal product
                    }

                }
            }
        }

        return [ratings, carQuote]
    }

    /**
     * Save all quotes
     * @param ratings
     * @param quoteId
     */
    def saveAllQuotes(List<RateCommand> ratings, CarQuote quote) {
        log.info("egyQuote.saveAllQuotes - saving quotes for: ${quote.id}")

        ratings.eachWithIndex { rating, idx ->
            QuoteResultEgy quoteResult = new QuoteResultEgy()
            Product product = Product.get(rating.productId)

            quoteResult.position = idx
            quoteResult.provider = product?.provider?.nameEn
            quoteResult.paCover = rating.paCover
            quoteResult.productName = product?.nameEn
            quoteResult.agencyRepair = rating.agencyRepair
            quoteResult.breakdownCover = rating.breakdownCover
            quoteResult.replacementCar = rating.replacementCar
            quoteResult.basePremium = rating.basePremium
            quoteResult.premium = rating.premium
            quoteResult.minPremium = rating.minPremium
            quoteResult.excess = rating.excess
            quoteResult.productId = rating.productId
            quoteResult.quote = quote
            quoteResult.save(failOnError: true)

        }

    }
}
