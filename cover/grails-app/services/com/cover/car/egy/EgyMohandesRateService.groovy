package com.cover.car.egy

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.RepairTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

/**
 * Mohandes Insurance Company- Egypt
 * Calculate premium from quote
 *
 */
@Transactional(readOnly = true)
class EgyMohandesRateService {

    def grailsApplication
    def messageSource
    def ratingService

    public static final Integer PROVIDER_ID = 54
    public static final Integer COMPREHENSIVE_PRODUCT_ID = 1077
    public static final Integer COMPREHENSIVE_NO_COPAY_PRODUCT_ID = 1177

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            Make carMake = Make.get(quoteCommand.makeId)
            // Exclude chinese cars
            if(carMake.countryId != CountryEnum.CHINA.id) {
                quoteCommand.carCategory = null
                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

                if (applicableRates) {
                    for (rate in applicableRates) {
                        RateCommand rateCommand = populateRatings(quoteCommand, rate)

                        rateList.add(rateCommand)
                    }
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            Make carMake = Make.get(quoteCommand.makeId)
            // Exclude chinese cars
            if(carMake.countryId != CountryEnum.CHINA.id) {
                quoteCommand.carCategory = null

                List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)

                if (applicableRates) {
                    ProductBaseRate rate = applicableRates.first()

                    rateCommand = populateRatings(quoteCommand, rate)
                }
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = checkMinimumPremium(rateCommand)
        //rateCommand = applyAdditionalFees(rateCommand)
        rateCommand = applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        //rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        BigDecimal baseRate = getBaseRate(isAgency, quoteCommand, applicableRate)
        BigDecimal minPremium = getMinimumPremium(isAgency, quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (quoteCommand.carAge <= 10 && quoteCommand.insuredValue < 700000) {
                isAgency = true
            }
        }

        isAgency
    }

    private BigDecimal getBaseRate(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        if (applicableRate.productId == COMPREHENSIVE_NO_COPAY_PRODUCT_ID) {
            baseRate = baseRate * 1.25
        }

        baseRate
    }

    private BigDecimal getMinimumPremium(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        BigDecimal minPremium  = isAgency ? applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        minPremium
    }

    RateCommand checkMinimumPremium(RateCommand rate) {
        // after discount if premium is less then minimum premium then use minimum premium
        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium
        }

        rate
    }


    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        if (quoteCommand.isNonGccSpec) {
            isEligible = false
        }

        isEligible
    }

    RateCommand applyCovers(QuoteCommand quoteCommand, RateCommand rateCommand) {
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)

        if (rateCommand.productId == COMPREHENSIVE_NO_COPAY_PRODUCT_ID) {
            rateCommand.agencyCoPay = "Nil"

        } else if (rateCommand.productId == COMPREHENSIVE_PRODUCT_ID) {
            if (quoteCommand.carAge <= 5) {
                rateCommand.agencyCoPay = "10%"
            } else {
                rateCommand.agencyCoPay = "25%"
            }
        }
        return rateCommand
    }
}
