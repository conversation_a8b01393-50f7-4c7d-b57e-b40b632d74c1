package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional

import java.time.LocalDate

@Transactional
class AdnicRateService {

    def grailsApplication
    def ratingService
    def adnicRatingCalculatorService

    public static final Integer PROVIDER_ID = 62
    public static final Integer STANDARD_ID = 139
    public static final Integer GOLD_PRODUCT_ID = 138

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("adnic.getRates - entering with quoteCommand:${quoteCommand}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)
        log.info("adnic.getRates - carAge:${quoteCommand.carAge}")

        if (!checkEligibility) {
            return rateList
        }

        List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
        if (offlineQuotes) {
            log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
            rateList.addAll(offlineQuotes)
        }

        log.info("adnic.getRates - checkEligibility:${checkEligibility}")
        Model model = Model.read(quoteCommand.modelId)
        quoteCommand.carCategory = null

        Integer noClaimYears = ratingService.getNoClaimYears(quoteCommand)
        List<ProductBaseRate> applicableRates = new ArrayList<>()
        applicableRates.add(adnicRatingCalculatorService
            .getRates(STANDARD_ID, quoteCommand.customerAge, noClaimYears, model, quoteCommand.manufactureYear, model.vehicleTypeId))

        if (quoteCommand.insuredValue > 50000) {
            applicableRates.add(adnicRatingCalculatorService
                .getRates(GOLD_PRODUCT_ID, quoteCommand.customerAge, noClaimYears, model, quoteCommand.manufactureYear, model.vehicleTypeId))
        }

        for (rate in applicableRates) {
            if (rate != null) {
                RateCommand rateCommand = populateRatings(quoteCommand, rate, noClaimYears)
                rateList.add(rateCommand)
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        quoteCommand.providerId = PROVIDER_ID

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (!checkEligibility) {
            return null
        }

        RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
        log.info("getRate - offlineQuote: ${offlineQuote}")
        if (offlineQuote) {
            return offlineQuote
        }

        Model model = Model.get(quoteCommand.modelId)

        quoteCommand.carCategory = null
        quoteCommand.noOfCyl = model.noOfCyl

        Integer noClaimYears = ratingService.getNoClaimYears(quoteCommand)
        ProductBaseRate productBaseRate = adnicRatingCalculatorService.getRates(
            quoteCommand.productId, quoteCommand.customerAge, noClaimYears, model, quoteCommand.manufactureYear, model.vehicleTypeId)

        return populateRatings(quoteCommand, productBaseRate, noClaimYears)
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate, Integer noClaimYears) {
        log.info("adnic.populateRatings - rate:${rate.id}, agency:${rate.baseRateAgency}, garage:${rate.baseRateGarage}")

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = checkMinimumPremium(rateCommand)

        BigDecimal baseRate = rateCommand.agencyRepair ? rate.baseRateAgency : rate.baseRateGarage

        if (noClaimYears > 2 && baseRate > adnicRatingCalculatorService.MIN_RATE && rateCommand.premium > rateCommand.minPremium) {
            rateCommand.premium = rateCommand.premium * 0.9
        }

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        if (rateCommand.productId == GOLD_PRODUCT_ID) {
            Integer vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId.intValue()
            if (vehicleTypeId == VehicleTypeEnum.FOURx4.value) {
                rateCommand.offRoadDesertRecovery = "yes"
            }
        }

        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        log.info("adnic.populateRatings - after applyExcess, rateCommand:${rateCommand.premium}")

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency

        rateCommand.productId = applicableRate.productId.intValue()
        if (rateCommand.productId == STANDARD_ID && quoteCommand.manufactureYear < 2021) {
            rateCommand.agencyRepair = false
        }

        if (applicableRate.baseRateAgency == null || applicableRate.baseRateAgency == BigDecimal.ZERO) {
            rateCommand.agencyRepair = false
        }

        rateCommand.baseRate = getBaseRate(rateCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        log.info("adnic.calculatePremium - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {
        boolean isAgency = false

        if (!ratingService.allowAgency()){
            return false
        }

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if ((quoteCommand.isBrandNew || (LocalDate.now().getYear() - quoteCommand.manufactureYear) < 3)
                && !quoteCommand.isThirdParty) {
                // if car age is <= 1 then agency for all products
                isAgency = true
            }

            if (quoteCommand.manufactureYear < LocalDate.now().getYear() &&
                applicableRate.productId == STANDARD_ID) {
                isAgency = false
            }
        }
        return isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand, CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        if (quoteCommand.hasClaim) {
                return false
        }
        if (quoteCommand.carAge > 9) {
            return false
        }

        return ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)
    }

    /**
     * Get base rate based on repair type and other conditions
     * @param rateCommand
     * @param quoteCommand
     * @param applicableRate
     * @return
     */
    BigDecimal getBaseRate(RateCommand rateCommand, ProductBaseRate applicableRate) {
        return rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
    }

    RateCommand checkMinimumPremium(RateCommand rate) {
        rate.actualBasePremium = rate.premium

        if (rate.premium < rate.minPremium) {
            rate.premium = rate.minPremium
            rate.basePremium = rate.minPremium

            rate.productDiscountAmount = null
            rate.productDiscountPercent = null
        }

        rate
    }
}
