package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelMaster
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.joda.time.LocalDate
/**
 * Calculate premium for Watania Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class WataniaRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 6
    public static final Integer STANDARD = 12   //Profile Rater
    public static final Integer PREMIUM_GARAGE = 13 //Profile Rater
    public static final Integer STANDARD_PLUS = 91
    public static final Integer PREMIUM_GARAGE_PLUS = 92
    public static final Integer DYNA_TRADE = 77 //Profile Rater
    public static final Integer DYNA_TRADE_PLUS = 93

    public static final Integer WATANIA_EDGE = 1024
    public static final Integer WATANIA_EDGE_PLUS = 1030

    public static final Integer WATANIA_HIGH_VALUE = 1032

    public static final Integer MOTOR_COMPREHENSIVE_IV = 1080
    public static final Integer TPL_PRODUCT_ID = 23

    public static final String EDGE_NON_AGENCY_OFFER_DATE = "2020-09-30"

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null
            List<WataniaFactorOne> wataniaFactorOneList = getApplicableProducts(quoteCommand)
            if (wataniaFactorOneList) {
                for (factorOne in wataniaFactorOneList) {

                    if (checkProductEligibility(quoteCommand, factorOne.productId)) {
                        RateCommand rateCommand = populateRatings(quoteCommand, factorOne)

                        if (rateCommand) {
                            rateList.add(rateCommand)
                        }
                    }
                }
            }

            //Find Nissan 4x4 Rate
            QuoteCommand edgeQuoteCommand = new QuoteCommand()
            InvokerHelper.setProperties(edgeQuoteCommand, quoteCommand.properties)

            Integer noClaimYears = null
            if (!quoteCommand.isBrandNew &&
                quoteCommand.manufactureYear != (LocalDate.now().year + 1)) {
                noClaimYears = ratingService.getNoClaimYears(quoteCommand)
            }

            List<ProductBaseRate> applicableRates = ratingService.getProductBaseRate(quoteCommand, noClaimYears, isOffline)

            if (applicableRates) {
                for (rate in applicableRates) {
                    if (rate.productId in [WATANIA_EDGE, WATANIA_EDGE_PLUS, WATANIA_HIGH_VALUE, MOTOR_COMPREHENSIVE_IV]) {
                        if (checkProductEligibility(quoteCommand, rate.productId)
                                && checkWataniaNonProfileProductsEligibility(quoteCommand, rate.productId)) {
                            RateCommand rateCommand = populateRatings(edgeQuoteCommand, null, rate)
                            rateList.add(rateCommand)
                        }
                    }
                }
            }
        }

        log.info("Total rates returned (#getRates): " + rateList.toString())
        rateList
    }

    RateCommand calculateNonProfileRatingPremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate.productId)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.carAge = quoteCommand.carAge
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand.baseRate = getBaseRate(quoteCommand, rateCommand, applicableRate)
        log.info("rateCommand.productId:${rateCommand.productId}, baserate:${rateCommand.baseRate}")

        if (quoteCommand.insuredValue >= 300000 && quoteCommand.makeId == 180) {
            rateCommand.premium = ratingService.calculate(3, quoteCommand.insuredValue)
        } else {
            rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        }

        if (rateCommand.productId == WATANIA_EDGE_PLUS) {
            rateCommand.premium = rateCommand.premium.add(1000) //Additional AED 1,000 for Edge Plus product
        }

        if (quoteCommand.isBrandNew && applicableRate.minPremiumBrandNew) {
            rateCommand.minPremium = applicableRate.minPremiumBrandNew
        } else {
            rateCommand.minPremium = rateCommand.agencyRepair ?
                applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        }
        rateCommand.basePremium = rateCommand.premium

        log.info("wataniaRateService.calculatePremium - rateCommand:${rateCommand.premium}, product:${rateCommand.productId}")

        rateCommand
    }


    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            if (quoteCommand.productId in [WATANIA_EDGE, WATANIA_EDGE_PLUS, WATANIA_HIGH_VALUE, MOTOR_COMPREHENSIVE_IV]) {
                Integer noClaimYears = null
                if (!quoteCommand.isBrandNew &&
                    quoteCommand.manufactureYear != (LocalDate.now().year + 1)) {
                    noClaimYears = ratingService.getNoClaimYears(quoteCommand)
                }

                List<ProductBaseRate> applicableRates = ratingService.getProductBaseRate(quoteCommand, noClaimYears, isOffline)

                if (applicableRates) {
                    ProductBaseRate rate = applicableRates.first()
                    if (checkWataniaNonProfileProductsEligibility(quoteCommand, rate.productId)) {
                        rateCommand = populateRatings(quoteCommand, null, rate)
                    }
                }

            } else {

                List<WataniaFactorOne> wataniaFactorOneList = getApplicableProducts(quoteCommand)

                if (wataniaFactorOneList) {
                    WataniaFactorOne factorOne = wataniaFactorOneList.first()

                    rateCommand = populateRatings(quoteCommand, factorOne)

                }
            }

        }

        log.info("Total rates returned (#getRate): " + rateCommand.toString())
        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)
            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        log.info("Total rates returned (#getTplRate): " + rateCommand.toString())
        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {
        RateCommand rateCommand = new RateCommand()
        rateCommand.carAge = quoteCommand.carAge
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId

        rateCommand = applyTplLoadings(quoteCommand, rateCommand)
        rateCommand = applyTplDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, WataniaFactorOne factorOne,
                                ProductBaseRate productBaseRate = null) {

        RateCommand rateCommand
        if (productBaseRate && productBaseRate.productId in [WATANIA_EDGE, WATANIA_EDGE_PLUS,
                                                             WATANIA_HIGH_VALUE, MOTOR_COMPREHENSIVE_IV]) {
            rateCommand = calculateNonProfileRatingPremium(quoteCommand, productBaseRate)
        } else {
            rateCommand = calculatePremium(quoteCommand, factorOne)
        }

        if (!rateCommand) {
            return null
        }

        rateCommand = checkMinimumPremium(rateCommand)

        if (rateCommand.premium == rateCommand.minPremium) {
            // apply discount only when minimum premium
            rateCommand = applyMinPremiumDiscounts(quoteCommand, rateCommand)

        } else {
            rateCommand = applyDiscounts(quoteCommand, rateCommand)
        }

        rateCommand = applyLoadings(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = applyExcess(quoteCommand, rateCommand)
        rateCommand = applyAdditionalFees(rateCommand, quoteCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = c4meFees(quoteCommand, rateCommand)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, WataniaFactorOne wataniaFactorOne) {

        boolean isAgency = checkAgency(quoteCommand, wataniaFactorOne.productId)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.carAge = quoteCommand.carAge
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = wataniaFactorOne.product
        WataniaFactorTwo wataniaFactorTwo = getWataniaFactorTwo(quoteCommand)
        WataniaFactorThree wataniaFactorThree = getWataniaFactorThree(quoteCommand)

        if (!wataniaFactorTwo || !wataniaFactorThree) {
            return null
        }

        BigDecimal baseRate
        BigDecimal minPremium
        BigDecimal factorOne
        BigDecimal factorTwo
        BigDecimal factorThree = wataniaFactorThree.factor

        if (isAgency) {
            baseRate = wataniaFactorOne.agencyRate
            minPremium = wataniaFactorOne.agencyMinPremium
            factorOne = wataniaFactorOne.agencyFactor
            factorTwo = wataniaFactorTwo.agencyFactor
        } else {
            baseRate = wataniaFactorOne.garageRate
            minPremium = wataniaFactorOne.garageMinPremium
            factorOne = wataniaFactorOne.garageFactor
            factorTwo = wataniaFactorTwo.garageFactor
        }

        log.info("isAgency:$isAgency, factorOne:${factorOne}, factorTwo:${factorTwo}, factorThree:${factorThree}, baseRate:$baseRate")

        BigDecimal loadingFactor = (factorOne/100) * (factorTwo/100) * (factorThree/100) * 100
        loadingFactor = loadingFactor //.setScale(2, BigDecimal.ROUND_UP)

        baseRate = ((loadingFactor * baseRate)/100) //.setScale(2, BigDecimal.ROUND_UP)

        //baseRate = baseRate > 2 ? baseRate : 2 //Minimum 2 percent

        minPremium = (loadingFactor * minPremium/100).setScale(0, BigDecimal.ROUND_UP)

        log.info("loadingFactor:${loadingFactor}, baseRate:${baseRate}, minPremium:$minPremium")

        if (!quoteCommand.noClaimsDiscountId) {
            if (quoteCommand.vechileTypeId in [VehicleTypeEnum.FOURx4.getValue(), VehicleTypeEnum.MPV.getValue()]) {
                minPremium = minPremium > 2000 ? minPremium : 2000
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN) {
                minPremium = minPremium > 1300 ? minPremium : 1300
            }
        } else if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
            if (quoteCommand.vechileTypeId in [VehicleTypeEnum.FOURx4.getValue(), VehicleTypeEnum.MPV.getValue()]) {
                minPremium = minPremium > 1800 ? minPremium : 1800
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN) {
                minPremium = minPremium > 1170 ? minPremium : 1170
            }
        } else {
            //For 2 or 3 years ncd, min premium cannot be less than below  defined min premium
            //In any case, premium cannot be less than the below defined min premium
            if (quoteCommand.vechileTypeId in [VehicleTypeEnum.FOURx4.getValue(), VehicleTypeEnum.MPV.getValue()]) {
                minPremium = minPremium > 1700 ? minPremium : 1700
            } else if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN) {
                minPremium = minPremium > 1105 ? minPremium : 1105
            }
        }

        BigDecimal premium = (baseRate * quoteCommand.insuredValue/100).setScale(0, BigDecimal.ROUND_UP)

        rateCommand.agencyRepair = isAgency
        rateCommand.productName = product.name
        rateCommand.productId = product.id
        rateCommand.premium = premium
        rateCommand.baseRate = baseRate
        rateCommand.minPremium = minPremium
        rateCommand.basePremium = premium

        log.info("rateCommand.premium:${rateCommand.premium}, minpremium:${rateCommand.minPremium}, baseRate:${baseRate}")

        rateCommand
    }

    RateCommand checkMinimumPremium(RateCommand rateCommand) {
        rateCommand.actualBasePremium = rateCommand.premium
        if (rateCommand.premium < rateCommand.minPremium) {
            rateCommand.premium = rateCommand.minPremium
            rateCommand.basePremium = rateCommand.minPremium
        }

        log.info("watania.calculatePremium - premium:${rateCommand.premium}, minPremium:${rateCommand.minPremium}, basePremium:${rateCommand.basePremium}")

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, Long productId) {
        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (productId == WATANIA_HIGH_VALUE) {
                if ((quoteCommand.isBrandNew || quoteCommand.carAge <= 1) && !quoteCommand.hasClaim) {
                    isAgency = true
                } else if (quoteCommand.carAge == 2 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()
                    && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                    isAgency = true
                } else if (quoteCommand.carAge == 3 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()
                    && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                    isAgency = true
                } else if (quoteCommand.carAge == 4
                    && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()
                    && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                    isAgency = true
                } else if (quoteCommand.carAge == 5
                    && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR4.value()
                    && quoteCommand.isOldAgency && !quoteCommand.hasClaim) {
                    isAgency = true
                }
            } else {

                if ((quoteCommand.isBrandNew || quoteCommand.carAge <= 1) && !quoteCommand.hasClaim) {
                    isAgency = true
                } else if (quoteCommand.carAge == 2 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()
                    && !quoteCommand.hasClaim) {
                    isAgency = true
                } else if (productId != WATANIA_EDGE && productId != WATANIA_EDGE_PLUS
                    && productId != MOTOR_COMPREHENSIVE_IV
                    && quoteCommand.carAge == 3
                    && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value() && !quoteCommand.hasClaim) {
                    isAgency = true
                }

                if (productId == MOTOR_COMPREHENSIVE_IV &&
                    quoteCommand.makeId in [CarMakeEnum.CHEVROLET.id, CarMakeEnum.FORD.id]) {
                    isAgency = false
                }
            }

            if (productId == PREMIUM_GARAGE || productId == PREMIUM_GARAGE_PLUS ||
                productId == DYNA_TRADE || productId == DYNA_TRADE_PLUS) {
                isAgency = false
            }

        }

        isAgency
    }

    RateCommand applyTplDiscounts(QuoteCommand quoteCommand, RateCommand rateCommand) {
        if (quoteCommand.isBrandNew) {
            return rateCommand
        }

        if (quoteCommand.isBrandNew || quoteCommand.manufactureYear == (LocalDate.now().year + 1)) {
            return rateCommand
        }

        if (!quoteCommand.isPolicyExpired) {
            int maxNcdYear = 4
            Map<Integer, Double> minPremiumDiscountsPerYearMap = new HashMap<>()
            minPremiumDiscountsPerYearMap.put(1, 0.10)
            minPremiumDiscountsPerYearMap.put(2, 0.15)
            minPremiumDiscountsPerYearMap.put(3, 0.20)
            minPremiumDiscountsPerYearMap.put(4, 0.30)

            if (quoteCommand.noClaimsDiscountId) {
                int ncdYear = quoteCommand.noClaimsDiscountId > maxNcdYear ? maxNcdYear : quoteCommand.noClaimsDiscountId
                rateCommand.premium = rateCommand.premium
                    .subtract(rateCommand.basePremium * (minPremiumDiscountsPerYearMap.get(ncdYear)) as BigDecimal)

            } else if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
                quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

                Integer applicableDiscountsYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
                int ncdYear = applicableDiscountsYears > maxNcdYear ? maxNcdYear : applicableDiscountsYears

                rateCommand.premium = rateCommand.premium
                    .subtract(rateCommand.basePremium * (minPremiumDiscountsPerYearMap.get(ncdYear)) as BigDecimal)
                rateCommand.noClaimDiscountPercent = minPremiumDiscountsPerYearMap.get(ncdYear) * 100
                rateCommand.requiredSelfDeclarationNumber = ncdYear
            }

        }

        rateCommand
    }

    RateCommand applyTplLoadings(QuoteCommand quoteCommand, RateCommand rate) {

        int customerAge = quoteCommand.customerAge

        // 10% loading for age
        if ((customerAge >= 23 && customerAge <= 24)) {
            rate.premium = rate.premium.add(rate.basePremium * (0.15))
        }

        // loading for new license
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.TWO_TO_THREE.getId()) {
            rate.premium = rate.premium.add(rate.basePremium * (0.10))
        }

        rate
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rateCommand) {

        int customerAge = quoteCommand.customerAge
        int carAge = quoteCommand.carAge

        boolean ageLoading = false
        boolean experienceLoading = false

        List<BigDecimal> loadings = []

        //Age Loading only for below 300K
        if (rateCommand.productId in [STANDARD, PREMIUM_GARAGE, DYNA_TRADE]) {
            BigDecimal ageAndDriverLoading = 0

            if ((customerAge >= 25 && customerAge <= 29) || (customerAge >= 65 && customerAge <= 75)) {
                ageAndDriverLoading += 0.10
                ageLoading = true
            } else if ((customerAge >= 22 && customerAge <= 24)) {
                ageAndDriverLoading += 0.20
                ageLoading = true
            }

            // loading for new license
            // if uae driving exp is less then 1 year
            // international driving exp is also less then 2 year then apply 10% loading
            if (quoteCommand.localDrivingExperienceId <= DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId() &&
                quoteCommand.internationalDrivingExperienceId <= DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                ageAndDriverLoading += 0.10
                experienceLoading = true
            }

            // if both age and experience criteria are met then add 5% more loading (25%)
            if (ageLoading && experienceLoading) {
                ageAndDriverLoading = ageAndDriverLoading + 0.05
            }

            if (ageAndDriverLoading) {
                loadings.add(1 + ageAndDriverLoading)
            }
        }

        if (rateCommand.productId == WATANIA_HIGH_VALUE) {
            if (carAge == 4 && rateCommand.agencyRepair) {
                loadings.add(1.35) //3rd year rate * 135%
            } else if (carAge == 5 && rateCommand.agencyRepair) {
                loadings.add(1.35) //3rd year rate * 135%
            }
        } else if (rateCommand.productId == DYNA_TRADE || rateCommand.productId == DYNA_TRADE_PLUS ||
            rateCommand.productId == PREMIUM_GARAGE || rateCommand.productId == PREMIUM_GARAGE_PLUS) {
            if (carAge in [4, 5, 6, 7]) {
                loadings.add(1.10) // 4th and 5th year loading
            }
        }
        if (rateCommand.productId in [STANDARD, PREMIUM_GARAGE, DYNA_TRADE]) {
            if (carAge == 2 && rateCommand.agencyRepair) {
                loadings.add(1.20) //Rate * 120% // 2nd year agency
            } else if (carAge == 3 && rateCommand.agencyRepair) {
                loadings.add(1.25) //Rate * 125% //3rd year agency
            }
        }

        log.info("wataniaRateService.applyLoadings for product:${rateCommand.productId} - carAge:$carAge, loading:${loadings}, rateCommand.premium:${rateCommand.premium}")

        BigDecimal premiumAfterLoading = rateCommand.premium
        if (loadings.size()) {
            BigDecimal totalLoading = 1
            loadings.each {
                totalLoading = totalLoading * it
            }
            premiumAfterLoading = rateCommand.premium * totalLoading
        }

        rateCommand.premium = premiumAfterLoading //rateCommand.premium.add(additionalLoadingPremium)

        rateCommand
    }

    RateCommand applyAdditionalFees(RateCommand rateCommand, QuoteCommand quoteCommand) {

        // additional fee 50 AED ambulance fee 25 AED certificate fee
        // rateCommand.premium = rateCommand.premium.add(50).add(25)

        // if registration city not dubai then additional 30 aed for "Emirates vehicle gate"
        if (quoteCommand.registrationCityId != CityEnum.DUBAI.value()) {
            rateCommand.premium = rateCommand.premium.add(30)
        }

        //Additional AED 700 above rate,
        if (isWataniaPlusNonEdgeProduct(rateCommand.productId))  {
            rateCommand.premium = rateCommand.premium.add(700) //Additional AED 799 for All Plus products
        }

        rateCommand
    }

    RateCommand c4meFees(QuoteCommand quoteCommand, RateCommand rateCommand) {

        // added credit card fee as part of c4me fee for wathba.
        BigDecimal ccFee = 0 //rateCommand.premium * (0.01955) //Not Applicable anymore
        rateCommand.premium = rateCommand.premium + ccFee
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)
        rateCommand.c4meFee = rateCommand.c4meFee + ccFee

        rateCommand
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)

        if (!isEligible) {
            return false
        }

        if (quoteCommand.customerAge < 23 || quoteCommand.customerAge > 74) {
            return false
        }

        if (productTypeEnum != CoverageTypeEnum.COMPREHENSIVE &&
            (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id ||
                quoteCommand.carAge > 20)) {

            return false
        }

        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            (quoteCommand.isThirdParty ||
                quoteCommand.insuredValue < 20000 ||
                (quoteCommand.carAge >= 7 && quoteCommand.insuredValue < BigDecimal.valueOf(30000)) ||
                quoteCommand.carAge > 9)) {

            return false
        }

        if (quoteCommand.localDrivingExperienceId.intValue() < DrivingExperienceEnum.ONE_TO_TWO.id) {
            return false
        }

        return isEligible
    }

    boolean checkProductEligibility(QuoteCommand quoteCommand, Integer productId) {

        boolean isEligible = true
        if (productId != MOTOR_COMPREHENSIVE_IV) {

            //Sedan below 100,000 insured value with claim is not eligible
            int vehicleTypeId = Model.read(quoteCommand.modelId).vehicleTypeId.intValue()
            if (vehicleTypeId == VehicleTypeEnum.SEDAN.value && quoteCommand.insuredValue < 100000
                && quoteCommand.hasClaim) {
                isEligible = false
            }
        }

        return isEligible
    }

    boolean checkWataniaNonProfileProductsEligibility(QuoteCommand quoteCommand, Integer productId) {

        //Nissan, Toyota, Lexus, Mitsubishi Pajero, Infiniti QX56 & QX80
        ModelMaster modelMaster = Model.get(quoteCommand.modelId).modelMaster
        if (productId in [WATANIA_EDGE, WATANIA_EDGE_PLUS] &&
            quoteCommand.makeId in [CarMakeEnum.TOYOTA.id, CarMakeEnum.NISSAN.id, CarMakeEnum.INFINITI.id,
                                    CarMakeEnum.MAZDA.id, CarMakeEnum.LEXUS.id, CarMakeEnum.HONDA.id,
                                    CarMakeEnum.MITSUBISHI.id] &&
            quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value &&
            quoteCommand.customerAge >= 30 && quoteCommand.customerAge <= 65 &&
            (quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.TWO_TO_THREE.getId() ||
                quoteCommand.internationalDrivingExperienceId >= DrivingExperienceEnum.FIVE_ABOVE.getId()) &&
            ( (quoteCommand.insuredValue > 30000 && quoteCommand.carAge <= 9) ||
                (quoteCommand.insuredValue <= 30000 && quoteCommand.carAge <= 6)) &&
            quoteCommand.insuredValue <= 300000) {

            return true

        } else if (productId == WATANIA_HIGH_VALUE) {
            //More than 1 year driving license is required
            if (quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                return true
            }

        } else if (productId == MOTOR_COMPREHENSIVE_IV &&
                quoteCommand.makeId in [CarMakeEnum.LEXUS.id, CarMakeEnum.TOYOTA.id, CarMakeEnum.HONDA.id,
                                        CarMakeEnum.NISSAN.id, CarMakeEnum.INFINITI.id, CarMakeEnum.HYUNDAI.id,
                                        CarMakeEnum.MITSUBISHI.id, CarMakeEnum.KIA.id, CarMakeEnum.MAZDA.id,
                                        CarMakeEnum.CHEVROLET.id, CarMakeEnum.FORD.id] &&
                quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value &&
                quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.TWO_TO_THREE.getId()) {

            return true
        }

        return false
    }

    List<WataniaFactorOne> getApplicableProducts (QuoteCommand quoteCommand) {

        List products = WataniaFactorOne.createCriteria().list {
            lte "valueFrom", quoteCommand.insuredValue
            gte "valueTo", quoteCommand.insuredValue
            eq "vehicleType.id", quoteCommand.vechileTypeId
            if (quoteCommand.productId) {
                eq "product.id", quoteCommand.productId
            }
            product {
                if (quoteCommand.requestSource == RequestSourceEnum.SMARTDUBAI) {
                    eq "activeForSmartDubai", true
                }
                eq "active", true
                provider {
                    eq 'active', true
                }
            }

        }

        // Agency repair is allowed only for first 3 years, Premier/ Dynatrade is allowed for first 5 years and
        // Non-Agency repair is allowed for first 9 years of vehicle model year.

        List applicableProducts = []

        products.each { WataniaFactorOne factorOne ->

            //DynaTrade only applicable to Japanese makes
            if ((factorOne.product.id == DYNA_TRADE || factorOne.product.id == DYNA_TRADE_PLUS) &&
                (quoteCommand.carAge <= 5 || (quoteCommand.carAge in [6, 7] && quoteCommand.noClaimsDiscountId)) &&
                Make.get(quoteCommand.makeId).countryId == CountryEnum.JAPAN.id) {

                applicableProducts.add(factorOne)

            } else if ((factorOne.product.id == PREMIUM_GARAGE || factorOne.product.id == PREMIUM_GARAGE_PLUS) &&
                (quoteCommand.carAge <= 5 || (quoteCommand.carAge in [6, 7] && quoteCommand.noClaimsDiscountId))) {

                applicableProducts.add(factorOne)

            } else if ((factorOne.product.id == STANDARD || factorOne.product.id == STANDARD_PLUS ) &&
                quoteCommand.carAge <= 9) {

                applicableProducts.add(factorOne)
            }

        }

        applicableProducts
    }

    WataniaFactorTwo getWataniaFactorTwo(QuoteCommand quoteCommand) {

        Integer makeId = quoteCommand.makeId

        if (quoteCommand.makeId == CarMakeEnum.LAND_ROVER.id) {
            Model model = Model.read(quoteCommand.modelId)
            //If model is of Range Rover type, use range rover rate instead
            if (model.modelMaster.id in [280, 281, 282, 1037]) {
                makeId = CarMakeEnum.RANGE_ROVER.id
            }
        }
        WataniaFactorTwo factor = WataniaFactorTwo.createCriteria().get {
            lt "valueFrom", quoteCommand.insuredValue
            gt "valueTo", quoteCommand.insuredValue
            eq "make.id", makeId
            eq "vehicleType.id", quoteCommand.vechileTypeId
        }

        factor
    }

    WataniaFactorThree getWataniaFactorThree(QuoteCommand quoteCommand) {
        Country country = Country.read(quoteCommand.nationalityId)

        Long noClaimDiscountId = (quoteCommand.noClaimsDiscountId ?: 0)

        WataniaFactorThree factor = WataniaFactorThree.createCriteria().get {
            if (quoteCommand.localDrivingExperienceId <= DrivingExperienceEnum.ONE_TO_TWO.getId() &&
                quoteCommand.internationalDrivingExperienceId <= DrivingExperienceEnum.ONE_TO_TWO.getId()) {
                eq "drivingExperience.id", 2L
            } else {
                eq "drivingExperience.id", 3L
            }
            if (noClaimDiscountId) {
                eq "noClaimDiscount.id", noClaimDiscountId
            } else {
                isNull("noClaimDiscount")
            }
            eq "nationalityCategory", country.wataniaCategory
        }

        factor
    }

    boolean isWataniaPlusNonEdgeProduct(Integer productId) {
        return productId == STANDARD_PLUS || productId == PREMIUM_GARAGE_PLUS ||
            productId == DYNA_TRADE_PLUS
    }

    RateCommand applyMinPremiumDiscounts(QuoteCommand quoteCommand, RateCommand rate) {
        if (quoteCommand.isBrandNew) {
            return rate
        }

        if (quoteCommand.isBrandNew || quoteCommand.manufactureYear == (LocalDate.now().year + 1)) {
            return rate
        }

        // NCD Discount
        if (rate.productId == WATANIA_EDGE_PLUS && !quoteCommand.isPolicyExpired) {
            if (quoteCommand.noClaimsDiscountId) {
                if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
                } else if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR2.value()) {
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.15))
                } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
                }

            } else if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
                quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

                Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)

                if (noClaimYears == 1) {
                    // 10% discount for 1 year old car with 1 year self declaration
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
                    rate.noClaimDiscountPercent = 10   //10%
                    rate.requiredSelfDeclarationNumber = 1  //At least 1 year self declaration is required

                } else if (noClaimYears == 2) {
                    // 15% discount
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.15))
                    rate.noClaimDiscountPercent = 15   //15%
                    rate.requiredSelfDeclarationNumber = 2  //At least 2 year self declaration is required

                } else if (noClaimYears >= 3) {
                    // 20% discount
                    rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
                    rate.noClaimDiscountPercent = 20   //20%
                    rate.requiredSelfDeclarationNumber = 3  //At least 3 year self declaration is required
                }
            }

        } else if (rate.productId == MOTOR_COMPREHENSIVE_IV
            && !quoteCommand.isPolicyExpired
            && ratingService.isEligibleForSelfDecDiscount(quoteCommand)
            && quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
            rate.requiredSelfDeclarationNumber = noClaimYears
        } else if (rate.productId == WATANIA_EDGE
            && !quoteCommand.isPolicyExpired
            && quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            int maxNcdYear = 4
            Map<Integer, Integer> minPremiumDiscountsPerYearMap = new HashMap<>()
            Integer noClaimYears

            if (rate.isAgencyRepair()) {
                minPremiumDiscountsPerYearMap.put(1, 1800)
                minPremiumDiscountsPerYearMap.put(2, 1700)
                minPremiumDiscountsPerYearMap.put(3, 1700)
                minPremiumDiscountsPerYearMap.put(4, 1700)
            } else {
                minPremiumDiscountsPerYearMap.put(1, 1800)
                minPremiumDiscountsPerYearMap.put(2, 1700)
                minPremiumDiscountsPerYearMap.put(3, 1600)
                minPremiumDiscountsPerYearMap.put(4, 1400)
            }

            if (ratingService.isEligibleForSelfDecDiscount(quoteCommand)) {
                noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
                if (noClaimYears != null || noClaimYears != 0) {
                    noClaimYears = noClaimYears > maxNcdYear ? maxNcdYear : noClaimYears
                    rate.premium = minPremiumDiscountsPerYearMap.get(noClaimYears)
                    rate.requiredSelfDeclarationNumber = noClaimYears
                }
            } else if (quoteCommand.noClaimsDiscountId) {
                noClaimYears = quoteCommand.noClaimsDiscountId > maxNcdYear ? maxNcdYear : quoteCommand.noClaimsDiscountId
                if (noClaimYears != null || noClaimYears != 0) {
                    rate.premium = minPremiumDiscountsPerYearMap.get(noClaimYears)
                }
            }
        }

        rate
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {
        if (quoteCommand.isBrandNew) {
            return rate
        }

        if (quoteCommand.isBrandNew || quoteCommand.manufactureYear == (LocalDate.now().year + 1)) {
            return rate
        }

        if (rate.productId == MOTOR_COMPREHENSIVE_IV
            && !quoteCommand.isPolicyExpired
            && ratingService.isEligibleForSelfDecDiscount(quoteCommand)
            && quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS) {

            Integer noClaimYears = ratingService.getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
            rate.requiredSelfDeclarationNumber = noClaimYears
        }

        rate
    }

    BigDecimal getBaseRate(QuoteCommand quoteCommand, RateCommand rateCommand, ProductBaseRate applicableRate) {

        BigDecimal bigDecimal
        if (quoteCommand.isBrandNew && applicableRate.baseRateBrandNew) {
            bigDecimal = applicableRate.baseRateBrandNew
        } else {
            bigDecimal = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        }

        if (applicableRate.productId == WATANIA_HIGH_VALUE &&
            quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.FIVE_ABOVE.getId() &&
            rateCommand.agencyRepair && quoteCommand.customerAge >= 30 && quoteCommand.customerAge <= 65) {

            if (quoteCommand.isBrandNew || quoteCommand.carAge <= 1) {
                bigDecimal = 1.9
            } else if (quoteCommand.carAge in [2, 3]) {
                bigDecimal = 1.8
            }

        } else if (applicableRate.productId in [WATANIA_EDGE, WATANIA_EDGE_PLUS]) {
            if (!rateCommand.agencyRepair) {
                LocalDate discountDate = new LocalDate(EDGE_NON_AGENCY_OFFER_DATE);
                if (LocalDate.now() <= discountDate && quoteCommand.policyStartDate <= discountDate) {
                    bigDecimal = 1.50
                }
            }
        }

        log.info("watania.baseRate - product:${applicableRate.productId}, rate:${bigDecimal}")

        return bigDecimal
    }

    RateCommand applyExcess(QuoteCommand quoteCommand, RateCommand rateCommand) {

        if (rateCommand.productId == WataniaRateService.MOTOR_COMPREHENSIVE_IV &&
            quoteCommand.insuredValue <= 50000 && LocalDate.now() <= new LocalDate("2020-05-31") &&
            quoteCommand.policyStartDate <= new LocalDate("2020-05-31")) {

            rateCommand.excess = '200'
        }

        return rateCommand
    }
}
