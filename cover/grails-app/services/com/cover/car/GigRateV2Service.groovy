package com.cover.car

import com.RoadsideAssistanceEnum
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.gig.GigQuoteCommand
import com.cover.car.gig.GigRateCommand
import com.safeguard.*
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.car.vehicle.*
import grails.converters.JSON
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime

import java.text.NumberFormat
import java.time.Year


@Transactional
class GigRateV2Service extends BaseRatingService{

    def ratingService
    def gigApiService

    public static final Integer PROVIDER_ID = 64

    public static final Integer PRODUCT_PERFECT_ID = 1196
    public static final Integer PRODUCT_PRESTIGE_ID = 5076
    public static final Integer PRODUCT_TPL_ID = 5053

    static final String PRODUCT_PRESTIGE_CODE = "VA29"
    static final String PRODUCT_PERFECT_AGENCY_CODE = "VA11"
    static final String PRODUCT_PERFECT_NON_AGENCY_CODE = "VA19"
    static final String PRODUCT_TPL_CODE = "VP10"

    static final String PRODUCT_PRESTIGE_NAME = "Motor Prestige"
    static final String PRODUCT_PERFECT_AGENCY_NAME = "Motor Perfect Plus"
    static final String PRODUCT_PERFECT_NON_AGENCY_NAME = "Motor Perfect"
    static final String PRODUCT_TPL_NAME = "Third Party Liability"


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("gigRateV2Service.getRates - quoteCommand: ${quoteCommand} -isOffline - ${isOffline}")

        try{
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                log.info("Provider is not eligible")
                return []
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)
            log.info("checkEligibility:$checkEligibility")
            if (!checkEligibility) {
                return []
            }

            List<ProviderRateCommand> gigRateCommands = []
            GigQuoteCommand gigQuoteCommand = GigQuoteCommand.generateGigQuoteCommand(quoteCommand)

            CarQuote quote = CarQuote.findById(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndProviderQuoteNoIsNull(
                    false, quote, Provider.findById(PROVIDER_ID) , LocalDateTime.now())

            gigQuoteCommand = toGigQuoteCommand(quoteCommand, quote)
            log.info("GigQuoteCommand - ${gigQuoteCommand}")

            if (!gigQuoteCommand){
                return []
            }

            if (carQuoteCovers.size() > 0){
                log.info("Cover found in db")
                gigRateCommands.addAll(gigApiService.toGigRateCommand(JSON.parse(carQuoteCovers.last().covers).eligiblePlans, carQuoteCovers.last().insuredValue))
            } else {
                log.info("Not Found in Db")
                log.info("GigQuoteCommand - ${gigQuoteCommand}")
                if (gigQuoteCommand){
                    def startTime = System.currentTimeMillis()
                    gigRateCommands = gigApiService.getBaseRateMinPremium(gigQuoteCommand)
                    def endTime = System.currentTimeMillis()
                    def elapsedTime = (endTime - startTime)/1000.0
                    log.info("Gif API call took ${elapsedTime} seconds against quoteId: ${quoteCommand.quoteId}")
                }
            }

            List<RateCommand> rateCommands = []
            gigRateCommands.each {
//                if (it.productCode != PRODUCT_TPL_CODE){
                    RateCommand rateCommand = toRateCommand(gigQuoteCommand, it)
                    if (rateCommand) {
                        log.info("gigRateCommand -  ${rateCommand}")
                        RateCommand command = populateRatings(ratingService, gigQuoteCommand, rateCommand)
                        rateCommands.add(command)
                        log.info("Command - ${command}")
                    } else {
                        log.error("Null rateCommand for $it ")
                    }
               // }

            }

            return rateCommands

        } catch (Exception e){
            log.info("Exception on getRates - Exception: ${e.printStackTrace()}")
            return []
        }

    }


    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE, boolean isOffline) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)
        Model model = Model.findById(quoteCommand.modelId)

/*        Country nationality = Country.load(quoteCommand.nationalityId)
        if (nationality.isArabic){
            return false
        }*/

        //TODO: ENABLE ONLY THESE MAKES
//        if (!(quoteCommand.makeId in [CarMakeEnum.ALFA_ROMEO.id, CarMakeEnum.TOYOTA.id, CarMakeEnum.NISSAN.id, CarMakeEnum.MITSUBISHI.id, CarMakeEnum.MAZDA.id,
//            CarMakeEnum.KIA.id, CarMakeEnum.HONDA.id, CarMakeEnum.HYUNDAI.id, CarMakeEnum.AUDI.id, CarMakeEnum.CHEVROLET.id, CarMakeEnum.FORD.id, CarMakeEnum.BMW.id, CarMakeEnum.MERCEDES.id,
//        CarMakeEnum.LEXUS.id])){
//            isEligible = false
//        } else
        /*if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE
            && model.modelMasterId.intValue() in [58, 60, 61, 62, 63, 64, 65, 76, 77, 284, 285, 286, 287, 288, 289, 290, 291, 292, 324, 325, 326, 327, 329, 331, 333, 334, 335, 336, 337, 338, 339
                                                      ,340, 342, 343, 346, 347, 348, 351, 1175 ]) {
            isEligible = false
        }*/


        //TODO: Mismatch Of the Model Masters Between GIG AND YC DATA


        return isEligible
    }



    private GigQuoteCommand toGigQuoteCommand(QuoteCommand quoteCommand, CarQuote quote){
        log.info("toGigQuoteCommand - quoteCommand: ${quoteCommand}")

        ExternalDataSource gigDataSource = ExternalDataSource.load(ExternalDataSourceEnum.GIG.id)

        Make make = Make.load(quoteCommand.makeId)
        Model model = Model.load(quoteCommand.modelId)
        ModelMaster modelMaster = ModelMaster.load(model.modelMaster.id)
        Country country = Country.load(quoteCommand.nationalityId)
        City city = City.load(quoteCommand.registrationCityId)
        Country firstDrivingLicenseCountry = Country.load(quoteCommand.firstLicenseCountryId)
        Country uaeCountry = Country.load(CountryEnum.UAE.id)
        //VehicleValuationExternal gigValuation = VehicleValuationExternal.findByModelAndExternalDataSource(model, gigDataSource)

        GigQuoteCommand gigQuoteCommand = GigQuoteCommand.generateGigQuoteCommand(quoteCommand)

        MakeExternal makeExternal = MakeExternal.findByExternalDataSourceAndMake(gigDataSource, make)
        ModelMasterExternal modelMasterExternal =  ModelMasterExternal.findByExternalDataSourceAndModelMaster(gigDataSource, modelMaster)
        ModelExternal modelExternal = ModelExternal.findByExternalDataSourceAndModel(gigDataSource, model)
        CountryExternal countryExternal = CountryExternal.findByExternalDataSourceAndCountry(gigDataSource, country)
        CityExternal cityExternal = CityExternal.findByExternalDataSourceAndCity(gigDataSource, city)
        CountryExternal firstDrivingLicenseCountryExternal = CountryExternal.findByExternalDataSourceAndCountry(gigDataSource, firstDrivingLicenseCountry)
        CountryExternal uaeCountryExternal = CountryExternal.findByExternalDataSourceAndCountry(gigDataSource, uaeCountry)

        log.info("makeExternal:$makeExternal, modelMasterExternal:$modelMasterExternal, modelExternal:$modelExternal, " +
            "countryExternal:$countryExternal, cityExternal:$cityExternal, firstDrivingLicenseCountryExternal:$firstDrivingLicenseCountryExternal")
        if (!makeExternal || !modelMasterExternal || !modelExternal || !countryExternal || !cityExternal || !firstDrivingLicenseCountryExternal){
            return null
        }

        gigQuoteCommand.makeCode = makeExternal.externalId
        gigQuoteCommand.modelCode = modelMasterExternal.externalId
        gigQuoteCommand.trimCode = modelExternal.externalId
        gigQuoteCommand.contactCityCode = cityExternal.externalId
        gigQuoteCommand.nationalityCode = countryExternal.externalId
        gigQuoteCommand.registrationCityCode = EmirateEnum.getByGigContactCode(cityExternal.externalId).gigCode
        gigQuoteCommand.firstDrivingLicenseCountryCode = firstDrivingLicenseCountryExternal.externalId
        gigQuoteCommand.cityName = city.name
        gigQuoteCommand.uaeCountryCode = uaeCountryExternal.externalId

        def valuationsByApi = gigApiService.getVehicleValuation(gigQuoteCommand)
        if (valuationsByApi) {
            if (quoteCommand.insuredValue > valuationsByApi.highValue) {
                gigQuoteCommand.gigValuation = valuationsByApi.highValue
            } else if (quoteCommand.insuredValue < valuationsByApi.lowValue) {
                gigQuoteCommand.gigValuation = valuationsByApi.lowValue
            } else {
                //Customer provided value is between the range, then pick customer provided value
                gigQuoteCommand.gigValuation = quoteCommand.insuredValue.toInteger()
            }
        } else {
            VehicleValuationExternal gigValuation = VehicleValuationExternal.findByModelAndExternalDataSource(model, gigDataSource)
            gigQuoteCommand.gigValuation = gigValuation ? calculateCarValuation(quoteCommand.insuredValue.intValue(), gigValuation.insuredValue, quoteCommand.isBrandNew ? quoteCommand.manufactureYear : Integer.parseInt(quoteCommand.firstRegistrationDate.year.toString()), gigValuation.year) : null
        }

        return gigQuoteCommand

    }


    RateCommand toRateCommand(QuoteCommand gigQuoteCommand, GigRateCommand gigRateCommand) {
        log.info("toRateCommand - gigRateCommand - ${gigRateCommand}")

        Integer productId = getProductId(gigRateCommand)
        if (!productId) return null

        Product product = Product.read(productId)
        if (!product.isActive()) return null

        Model model = Model.read(gigQuoteCommand.modelId)
        if (product.type.id == CoverageTypeEnum.COMPREHENSIVE.value()
                && model.modelMasterId.intValue() in [58, 60, 61, 62, 63, 64, 65, 76, 77, 284, 285, 286, 287, 288, 289,
                                                      290, 291, 292, 324, 325, 326, 327, 329, 331, 333, 334, 335, 336,
                                                      337, 338, 339, 340, 342, 343, 346, 347, 348, 351, 1175 ]) {
            log.info("gigQuoteCommand:${gigQuoteCommand} eligiblity failed")
            return null
        }

        if (productId == PRODUCT_TPL_ID && gigQuoteCommand.customerAge < 30) {
            //Dont show GIG TPL below 30
            return null
        }

        RateCommand rateCommand = new RateCommand()
        rateCommand.productId = productId
        rateCommand.providerId = PROVIDER_ID

        log.info("Rate Command Insured Value - ${gigRateCommand.insuredValue}")
        rateCommand = ratingService.applyCovers(gigQuoteCommand, rateCommand)

        rateCommand.basePremium = gigRateCommand.netPremium
        rateCommand.premium = rateCommand.basePremium
        rateCommand.minPremium = rateCommand.premium
        rateCommand.currency = gigQuoteCommand.currency
        rateCommand.excess = gigRateCommand.excess
        rateCommand.insuredValue = gigRateCommand.insuredValue
        rateCommand = applyCovers(rateCommand, gigRateCommand)

        rateCommand.dynamicAddons = gigRateCommand.covers.findAll{
            it.isIncluded == false && it.premium != "0" && it.code != CarCoversEnum.NO_CLAIM_DISCOUNT_PROTECTION.gigCode
        }.collect{[
            label: it.name, price: new BigDecimal(it.premium), providerCode: it.code,
            code: AddonCodeEnum.DYNAMIC_ADDON.code, description: it.sumValue ? "Covered upto AED " + formatAmountWithCommas(it.sumValue.toString()) : it.name
        ]}

        return rateCommand
    }

    private Integer getProductId(GigRateCommand gigRateCommand){

        if (gigRateCommand.productCode in [PRODUCT_PERFECT_AGENCY_CODE, PRODUCT_PERFECT_NON_AGENCY_CODE]){
            return PRODUCT_PERFECT_ID
        } else if (gigRateCommand.productCode == PRODUCT_PRESTIGE_CODE){
            return PRODUCT_PRESTIGE_ID
        } else if (gigRateCommand.productCode == PRODUCT_TPL_CODE){
            return PRODUCT_TPL_ID
        } else {
            return null
        }
    }

    RateCommand applyCovers(RateCommand rateCommand, GigRateCommand gigRateCommand) {

            GigRateCommand.GigRateCoverCommand agencyCover = gigRateCommand.covers.find{
                it.code == CarCoversEnum.AGENCY_REPAIR.gigCode }
            rateCommand.agencyRepair = agencyCover ? agencyCover.isIncluded : false

            GigRateCommand.GigRateCoverCommand pax = gigRateCommand.covers.find{
                it.code == CarCoversEnum.PAB_PASSENGER.gigCode }
            rateCommand.personalAccidentPax = pax ? (pax.isIncluded || pax.premium == "0" ? "yes" : pax.premium) : "no"

            GigRateCommand.GigRateCoverCommand offRoad = gigRateCommand.covers.find{
                it.code == CarCoversEnum.OFF_ROAD_COVER.gigCode }
            rateCommand.offRoadDesertRecovery = offRoad ? (offRoad.isIncluded || offRoad.premium == "0" ? "yes" : offRoad.premium) : "no"

            if (!offRoad || !offRoad.isIncluded || offRoad.premium != "0"){
                rateCommand.roadsideAssistances.remove(RoadsideAssistanceEnum.OFFROAD_RECOVERY.nameEn)
            }

            GigRateCommand.GigRateCoverCommand ambulanceService = gigRateCommand.covers.find{
                it.code == CarCoversEnum.AMBULANCE_SERVICE.gigCode }

            if (ambulanceService && (ambulanceService.isIncluded || ambulanceService.premium == "0")){
                rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.AMBULANCE_SERVICE.nameEn)
            }

            GigRateCommand.GigRateCoverCommand naturalClimate = gigRateCommand.covers.find{
                it.code == CarCoversEnum.NATURAL_CALAMITY.gigCode }
            rateCommand.naturalCalamity = naturalClimate ? (naturalClimate.isIncluded || naturalClimate.premium == "0" ? "yes" : naturalClimate.premium) : "no"

            GigRateCommand.GigRateCoverCommand roadSide = gigRateCommand.covers.find{
                it.code == CarCoversEnum.RSA_GOLD.gigCode }
            rateCommand.breakdownCover = roadSide ?
            (roadSide.isIncluded || roadSide.premium == "0" ? CarCoversEnum.findByGigCode(roadSide.code).shortName : roadSide.premium) : "no"

            GigRateCommand.GigRateCoverCommand pad = gigRateCommand.covers.find{
                it.code == CarCoversEnum.PAB_DRIVER.gigCode }
            rateCommand.paCover = pad ? (pad.isIncluded || pad.premium == "0" ? "yes" : pad.premium) : "no"

            GigRateCommand.GigRateCoverCommand lockReplacement = gigRateCommand.covers.find{
                it.code == CarCoversEnum.REPLACEMENT_LOCKS.gigCode }
            rateCommand.replacementLocks = lockReplacement ? (lockReplacement.isIncluded || lockReplacement.premium == "0" ? "yes" : lockReplacement.premium) : "no"

            GigRateCommand.GigRateCoverCommand personalBelonging = gigRateCommand.covers.find{
                it.code == CarCoversEnum.PBL.gigCode }
            rateCommand.lossOfPersonalBelongings = personalBelonging ? (personalBelonging.isIncluded || personalBelonging.premium == "0" ? personalBelonging.sumValue.toString() : personalBelonging.premium) : "no"

            GigRateCommand.GigRateCoverCommand windScreen = gigRateCommand.covers.find{
                it.code == CarCoversEnum.WIND_SCREEN.gigCode }
            rateCommand.windScreenCover = windScreen ? (windScreen.isIncluded || windScreen.premium == "0" ? "5000" : windScreen.premium) : "no"

            GigRateCommand.GigRateCoverCommand medicalEmergency = gigRateCommand.covers.find{
                it.code == CarCoversEnum.MEDICAL_EMERGENCY.gigCode }
            rateCommand.emergencyMedical = medicalEmergency ? (medicalEmergency.isIncluded || medicalEmergency.premium == "0" ? medicalEmergency.sumValue.toString() : medicalEmergency.premium) : "no"

            GigRateCommand.GigRateCoverCommand rentCar = gigRateCommand.covers.find{
                it.code == CarCoversEnum.RENT_A_CAR_7.gigCode }
            rateCommand.replacementCar = rentCar ? (rentCar.isIncluded || rentCar.premium == "0" ? "yes" : rentCar.premium) : "no"

            GigRateCommand.GigRateCoverCommand omanCover = gigRateCommand.covers.find{
                it.code == CarCoversEnum.OMAN_OD.gigCode }
            rateCommand.damageToYourVehicleOman = omanCover ? omanCover.isIncluded || omanCover.premium == "0" : false

            GigRateCommand.GigRateCoverCommand freeCarRegistration = gigRateCommand.covers.find{
                it.code == CarCoversEnum.CAR_REGISTRATION.gigCode }
            rateCommand.carRegService = freeCarRegistration ? (freeCarRegistration.isIncluded || freeCarRegistration.premium == "0" ? "yes" : freeCarRegistration.premium) : "no"

        return rateCommand
    }

    RateCommand getRate(QuoteCommand quoteCommand, Boolean isOffline){
        log.info("gigRateV2Service.gateRate - QuoteCommand ${quoteCommand} isOffline - ${isOffline}")

        try{
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                log.info("Provider is not eligible")
                return []
            }

            quoteCommand.providerId = PROVIDER_ID

            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE, isOffline)
            log.info("checkEligibility:${checkEligibility}")
            if (!checkEligibility) {
                return []
            }

            GigQuoteCommand gigQuoteCommand = GigQuoteCommand.generateGigQuoteCommand(quoteCommand)

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndProviderQuoteNoIsNull(
                    false, quote, Provider.load(PROVIDER_ID) , LocalDateTime.now())

            GigRateCommand gigRateCommand  = null
            gigQuoteCommand = toGigQuoteCommand(quoteCommand, quote)

            if (carQuoteCovers.size() > 0) {
                JSONElement selectedCover = getCoverByProduct(JSON.parse(carQuoteCovers.last().covers), quoteCommand)
                log.info("Selected Cover is ${selectedCover}")
                gigRateCommand = gigApiService.toGigRateCommand([selectedCover] as JSONElement, carQuoteCovers.last().insuredValue).first()
            } else {
                log.info("Not Found in Db Calling API")
                def startTime = System.currentTimeMillis()

                List<GigRateCommand> rateCommands = gigApiService.getBaseRateMinPremium(gigQuoteCommand)

                def endTime = System.currentTimeMillis()
                def elapsedTime = (endTime - startTime)/1000.0
                log.info("GIG API call took ${elapsedTime} seconds against quoteId: ${quoteCommand.quoteId}")

                gigRateCommand = getSelectedRateCommand(rateCommands, quoteCommand)
            }

            RateCommand rateCommand = null

            rateCommand = toRateCommand(gigQuoteCommand, gigRateCommand)

            rateCommand = populateRatings(ratingService, gigQuoteCommand, rateCommand)

            return rateCommand
        }catch (Exception e){
            log.info("gigRateV2Service.gateRate - Exception ${e}")
            return []
        }

    }

    void createQuote(QuoteCommand quoteCommand){
        log.info("CreateQuote - ${quoteCommand}")

        quoteCommand.providerId = PROVIDER_ID
        try {
            CarQuote carQuote = CarQuote.load(quoteCommand.quoteId)
            GigQuoteCommand gigQuoteCommand = toGigQuoteCommand(quoteCommand, carQuote)

            List<CarQuoteCover> covers = CarQuoteCover.findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndProviderQuoteNoIsNull(
                false, carQuote, Provider.load(PROVIDER_ID) , LocalDateTime.now())

            List<CarQuoteCover> quotationCovers = CarQuoteCover.findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndProviderQuoteNoIsNotNull(
                false, carQuote, Provider.load(PROVIDER_ID) , LocalDateTime.now())
            JSONElement selectedCover
            if (covers.size() > 0){
                selectedCover = getCoverByProduct(JSON.parse(covers.last().covers), quoteCommand)
            } else {
                JSONElement gigCovers = gigApiService.getProductsApi(gigQuoteCommand)
                selectedCover = getCoverByProduct(gigCovers, quoteCommand)
            }

            if (quotationCovers.isEmpty()){
                gigApiService.createQuote(carQuote, gigQuoteCommand, selectedCover)
            } else {
                carQuote.providerPolicyReference = quotationCovers.last().providerQuoteNo
                carQuote.save()
                gigApiService.updateQuoteApi(gigQuoteCommand, carQuote, selectedCover.covers, [], selectedCover.product.code, selectedCover.product.value)
            }

        } catch (Exception e){
            log.error("Exception on createQuote: ${e.getMessage()}")
        }


    }

    Tuple getProductCodeAndProductName(QuoteCommand quoteCommand){
        if (quoteCommand.productId == PRODUCT_PRESTIGE_ID){
            return new Tuple(PRODUCT_PRESTIGE_CODE, PRODUCT_PRESTIGE_NAME)
        } else if (quoteCommand.productId == PRODUCT_PERFECT_ID && quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY){
            return new Tuple(PRODUCT_PERFECT_AGENCY_CODE, PRODUCT_PERFECT_AGENCY_NAME)
        } else if (quoteCommand.productId == PRODUCT_PERFECT_ID && quoteCommand.selectedRepairType == RepairTypeEnum.GARAGE){
            return new Tuple(PRODUCT_PERFECT_NON_AGENCY_CODE, PRODUCT_PERFECT_NON_AGENCY_NAME)
        } else if (quoteCommand.productId == PRODUCT_TPL_ID){
            return new Tuple(PRODUCT_TPL_CODE, PRODUCT_TPL_NAME)
        } else {
            return null
        }
    }


    private JSONElement getCoverByProduct(JSONElement cover, QuoteCommand quoteCommand) {
        log.info("Cover is ${cover}")
        String productCode = getProductCodeAndProductName(quoteCommand)[0]
        JSONElement selectedPlan
        cover.eligiblePlans.forEach({ plan ->
            if (plan.product.code == productCode){
                selectedPlan = plan
                log.info("Selecte Plan ${selectedPlan}")
            }
        })

        return selectedPlan
    }

    private GigRateCommand getSelectedRateCommand(List<GigRateCommand> rateCommands, QuoteCommand command){

        String productCode = getProductCodeAndProductName(command)[0]

        GigRateCommand selectedRateCommand = rateCommands.find {
            it.productCode == productCode
        }

        return selectedRateCommand
    }

    def getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
       log.info("getTplRate - quoteCommand - ${quoteCommand} isOffline - ${isOffline}")

        try{
            if (!showProviderRatings(PROVIDER_ID, isOffline)) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY, isOffline)

            if (!checkEligibility) {
                return null
            }

            GigQuoteCommand gigQuoteCommand = GigQuoteCommand.generateGigQuoteCommand(quoteCommand)
            List<GigRateCommand> gigRateCommands = []

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndProviderQuoteNoIsNull(
                    false, quote, Provider.load(PROVIDER_ID) , LocalDateTime.now())

            gigQuoteCommand = toGigQuoteCommand(quoteCommand, quote)

            if (!gigQuoteCommand){
                return null
            }

            if (carQuoteCovers.size() > 0){
                log.info("Cover found in db")
                gigRateCommands.addAll(gigApiService.toGigRateCommand(JSON.parse(carQuoteCovers.last().covers).eligiblePlans, carQuoteCovers.last().insuredValue))
            }else {
                log.info("Not Found in Db Calling API")
                gigRateCommands = gigApiService.getBaseRateMinPremium(gigQuoteCommand)
            }

            RateCommand rateCommand = null

            gigRateCommands.each {
                if (it.productCode == PRODUCT_TPL_CODE){
                    rateCommand = toRateCommand(gigQuoteCommand, it)
                    if (rateCommand) {
                        rateCommand = populateRatings(ratingService, gigQuoteCommand, rateCommand)
                    } else {
                        log.error("Null rateCommand for $it ")
                    }
                }

            }

            return rateCommand

        } catch (Exception e){
            log.info("Exception on getTplRate - Exception: ${e.message}")
            return null
        }

    }

    def updateQuoteWithAddons(QuoteCommand command, CarQuote quote, def selectedDynamicAddons){
        log.info("updateQuoteWithAddons - QuoteCommand - ${command} - SelectedAddons - ${selectedDynamicAddons}")

        List<String> optionalAddonCodes = []

        GigQuoteCommand gigQuoteCommand = toGigQuoteCommand(command, quote)
        List<CarQuoteCover> covers = CarQuoteCover.findAllByIsDeletedAndQuoteAndProviderAndProviderQuoteNoIsNull(
            false, quote, Provider.load(PROVIDER_ID))

        JSONElement selectedCover
        if (covers.size() > 0){
            selectedCover = getCoverByProduct(JSON.parse(covers.last().covers), command)
        } else {
            JSONElement gigCovers = gigApiService.getProductsApi(command)
            selectedCover = getCoverByProduct(gigCovers, gigQuoteCommand)
        }

        selectedDynamicAddons.each{
            optionalAddonCodes.add(it.providerCode)
        }

        gigApiService.updateQuoteApi(gigQuoteCommand, quote, selectedCover.covers, optionalAddonCodes, selectedCover.product.code, selectedCover.product.value)

    }

    String formatAmountWithCommas(String amountString) {
        def amount = amountString.toBigDecimal()
        def numberFormat = NumberFormat.getNumberInstance()
        numberFormat.setGroupingUsed(true)
        numberFormat.format(amount)
    }

    /**
     * Calculate current valuation by depreciating the value by year
     *
     * @param insuredValue
     * @param gigInsuredValue
     * @param year
     * @param gigValuationYear
     * @return
     */
    def calculateCarValuation(Integer insuredValue, Integer gigInsuredValue, Integer year, Integer gigValuationYear) {
        log.info("calculateCarValuation - ${gigInsuredValue} - Year - ${year}")
        Integer currentYear = Year.now().getValue()
        Integer yearDifference = currentYear - year
        Double valuation = gigInsuredValue
        Integer minRange
        Integer maxRange

        if (yearDifference > 0) {
            for (int i = 1; i <= yearDifference; i++) {
                if (i == 1) {
                    valuation = gigInsuredValue * 0.85
                } else {
                    valuation = valuation * 0.9
                }
            }
        }

        log.info("Deprecian Value - ${valuation}")
        minRange = Math.ceil(valuation * 0.8).intValue()
        maxRange = yearDifference <= 0 ? gigInsuredValue : (valuation + (valuation * 0.1)).intValue()

        log.info("Min Range - ${minRange}")
        log.info("Max Range - ${maxRange}")

        if (insuredValue < minRange) {
            return minRange
        } else if (insuredValue > maxRange) {
            return maxRange
        } else {
            return insuredValue
        }
    }

     boolean quoteExists(QuoteCommand quoteCommand, List<CarQuoteCover> carQuoteCovers) {
        def productCodeAndProductName = getProductCodeAndProductName(quoteCommand)
         boolean isQuotePresent = false
        carQuoteCovers.forEach({ cover ->
            JSONElement jsonCover = JSON.parse(cover.covers)
            if (jsonCover.selectedPlan.product.code == productCodeAndProductName[0].toString()){
                isQuotePresent = true
            }
        })

        return isQuotePresent
    }
}
