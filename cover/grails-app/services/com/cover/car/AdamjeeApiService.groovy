package com.cover.car

import com.cover.car.adamjee.AdamjeeQuoteCommand
import com.cover.car.adamjee.AdamjeeRateCommand
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.JedisConnectionPool
import com.safeguard.Provider
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.car.ProviderApiToken
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.transaction.Transactional
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import redis.clients.jedis.JedisPool

import javax.annotation.PostConstruct



@Transactional
class AdamjeeApiService extends ProviderRatingService{

    static final String LOGIN_API = "/ValidateLogin"
    static final String CREATE_QUOTE_API = "/CreateQuotation"
    static final String SAVE_QUOTE_API = "/SaveQuoteWithPlan"

    public static final String COMPREHENSIVE_PRODUCT_CODE = "10001"
    public static final String TPL_PRODUCT_CODE = "10005"

    public static final String AGENCY_REPAIR = "N"
    public static final String INSURANCE_BREAK = "N"

    public static final String POLICY_DIV_CODE = "10"
    public static final String POLICY_DEP_CODE = "10"
    public static final String LOCATION_CODE = "01" //Constant FROM ADAMJEE

    public static final String SUCCESS_SAVE_QUOTE_RESPONSE_CODE = "10004"

    public static final String SCHEME_CODE = "10001"
    public static final String OK_RESPONSE_STATUS = "True"
    public static final String INVALID_TOKEN_RESPONSE_CODE = "1001"
    public static final String EXPIRE_TOKEN_RESPONSE_CODE = "1002"

    public static final String TOKEN_API_SUCCESSFUL = "10001"


    public static final String CUSTOMER_TYPE_CODE = "I"

    private String loginId
    private String password
    private String host
//    def jedis = JedisConnectionPool.getConnection()

    def grailsApplication
    def providerApiService
    def providerTokenService

    List<Map<String, String>> headers = []

    @PostConstruct
    private void init() {
        loginId = grailsApplication.config.getProperty("adamjee.auth.loginId")
        password = grailsApplication.config.getProperty("adamjee.auth.password")
        host = grailsApplication.config.getProperty("adamjee.auth.host")
        headers = [
            ["provider": "ADC"]
        ]
    }

    @Transactional(readOnly = true)
    List<ProviderRateCommand> getBaseRateMinPremium(QuoteCommand command, CoverageTypeEnum coverageTypeEnum) {
        log.info("AdamjeeApiService - getBaseRateMinPremium ${command}")
        if (!command) return []

        List<AdamjeeRateCommand> rateCommands = []

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE){
            AdamjeeRateCommand nonAgencyRateCommand = callCreateQuoteAPI(command, coverageTypeEnum, false)
            AdamjeeRateCommand dynaTradeRateCommand = callCreateQuoteAPI(command, coverageTypeEnum, true)
            rateCommands.add(nonAgencyRateCommand)
            rateCommands.add(dynaTradeRateCommand)
        }else if (coverageTypeEnum == CoverageTypeEnum.THIRD_PARTY){
            AdamjeeRateCommand tplRateCommand = callCreateQuoteAPI(command, coverageTypeEnum, false)
            rateCommands.add(tplRateCommand)

        }

        return rateCommands
    }


     AdamjeeRateCommand callCreateQuoteAPI(QuoteCommand command, CoverageTypeEnum coverageTypeEnum, boolean showDynaTrade){

         log.info("AdamjeeAPIService - callCreateQuoteAPI ${command}")

        JSONElement resp = createQuoteApi(command, coverageTypeEnum, showDynaTrade, providerTokenService.getToken(ExternalDataSourceEnum.ADAMJEE))


        if (resp[0].Code && resp[0].Code in [INVALID_TOKEN_RESPONSE_CODE, EXPIRE_TOKEN_RESPONSE_CODE]){
            if (callTokenApi()){
                resp = createQuoteApi(command, coverageTypeEnum, showDynaTrade, providerTokenService.getToken(ExternalDataSourceEnum.ADAMJEE))
            }else {
                return []
            }
        }

        if (resp[0].Status != OK_RESPONSE_STATUS || (!resp[0].QuotNo || resp[0].QuotNo == "") || !resp[0].Excess){
            log.error("adamjee.getBaseRateMinPremium - Error: ${resp} for command:${command.toString()}")
            return []
        }

        log.info("response: ${resp[0]}")

        JSONElement coverDetails = resp[0]
        CarQuoteCover quoteCover = new CarQuoteCover()
        quoteCover.quote = CarQuote.load(command.quoteId)
        quoteCover.provider = Provider.load(command.providerId)
        quoteCover.covers = coverDetails
        quoteCover.expiryDate = LocalDateTime.now().withTime(23, 59, 59, 999)
         quoteCover.providerQuoteNo = resp[0].QuotNo
        quoteCover.schemeCode = SCHEME_CODE
        quoteCover.insuredValue = command.insuredValue
        quoteCover.coverageType = CoverageType.load(coverageTypeEnum.value())
        quoteCover.save(failOnError: true)

        AdamjeeRateCommand rateCommand = coverDetailsToRateCommand(coverDetails, resp[0].QuotNo)
        log.info("Here are Rate Commands ${rateCommand}")

        return rateCommand
    }

    private boolean callTokenApi() {
        log.info("AdamjeeApiService - Calling Token API")

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add('LoginId', loginId)
        map.add('Password', password)

        JSONElement resp
        resp = providerApiService.callApi(host + LOGIN_API, headers, map, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_XML_VALUE)

        log.info("Token  ${resp?.Data[0][0]?.Token}")

        if (resp?.Status[0] == OK_RESPONSE_STATUS && resp?.Code[0] == TOKEN_API_SUCCESSFUL){
            def token = resp?.Data[0][0]?.Token
            providerTokenService.saveProviderToken(ExternalDataSourceEnum.ADAMJEE, token)
            return true
        }else {
            return false
        }

    }


     private JSONElement createQuoteApi(AdamjeeQuoteCommand command, CoverageTypeEnum coverageTypeEnum, boolean showDynaTrade, String token){

//         jedis = JedisConnectionPool.checkConnection(jedis)
//         String token = jedis.get("ADC_TOKEN")
         log.info("Quote Id is ${command.quoteId}")
         String requestBody = """
                {
                "Authentication": {
                "Token": "${token}",
                "UserId": "${loginId}"
                },
                "Data": {
                "SchemeCode": "${SCHEME_CODE}",
                "ProdCode": "${coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE ? COMPREHENSIVE_PRODUCT_CODE : TPL_PRODUCT_CODE}",
                "PolDivn": "${POLICY_DIV_CODE}",
                "PolDept": "${POLICY_DEP_CODE}",
                "CustType": "${CUSTOMER_TYPE_CODE}",
                "InsrName": "${command.name}",
                "InsrDob": "${command.dob.toString("dd/MM/YYYY")}",
                "InsrNameAr": "${command.name}",
                "InsrEmail": "${command.email}",
                "InsrPOBox": "",
                "DrvrName": "${command.name}",
                "InsrMobile": "${command.mobileNumber}",
                "Location": "${LOCATION_CODE}",
                "InsrNationality": "${command.nationalityCode}",
                "LicNo": "",
                "LicIssueDt" : "${command.licenseIssueDate}",
                "PolFmDt": "${command.policyStartDate.toString("dd/MM/YYYY")}",
                "VehUse": "${command.vehicleUseCode}",
                "VehMake": "${command.vehicleMakeCode}",
                "VehModel": "${command.vehicleModelMasterCode}",
                "VehBodyType": "${command.vehicleBodyTypeCode}",
                "VehCylinder": "${command.noOfCyl.toString()}",
                "NoOfPassenger": "${command.noOfPassengers ? command.noOfPassengers : ""}",
                "VehManfYear": "${command.manufactureYear.toString()}",
                "ChassisNo": "",
                "RegEmirate": "${command.regEmiratesCode}",
                "NoofDoors": "${command.numberOfDoors}",
                "VehValue": "${command.insuredValue.toString()}",
                "VehBrandNewYn": "${command.isBrandNew ? "Y" : "N"}",
                "VehEngineNo": "",
                "VehAgencyRepYn": "${AGENCY_REPAIR}",
                "VehDynaTradeYn": "${showDynaTrade ? "Y" : "N"}",
                "VehPlateColor": "",
                "VehPlateRegNo": "",
                "VehRentACarYN": "Y",
                "DiscountYN": "${showDynaTrade ? "N" : "Y"}",
                "MortgageYn": "",
                "Bank": "",
                "SurveyYn": "",
                "IsTPLlastYrYn": "${command.isTplLastYear}",
                "IsVehGCCYn": "${command.isVehicleGCC}",
                "VehxtTcfNo": "",
                "SurveyYN" : "${command.quoteId}-${coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE ? (showDynaTrade ? "D" : "C") : "T"}",
                "ClmFreeYear": "${command.noClaimYears}",
                "VehRegion": "${command.isNonGccSpec ? "NON-GCC" : "GCC"}"
                }
                }"""

        String encQuoteId = command.quoteId ? AESCryption.encrypt(command.quoteId + "") : null
         List<Map<String, String>> customHeaders = []
         customHeaders.addAll(headers)
         customHeaders.add(["encQuoteId": encQuoteId])
        log.info("AdamjeeApiService - Calling Create Quote API")

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add('pData', requestBody)

        JSONElement resp

        resp = providerApiService.callApi(host + CREATE_QUOTE_API, customHeaders, map, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_XML_VALUE)

        return resp
}

    private AdamjeeRateCommand coverDetailsToRateCommand(JSONElement schemeDetails, String quoteNo){
        log.info("AdamjeeApi-coverDetailsToRateCommand - ${schemeDetails}")

        JSONElement coverDetails = schemeDetails.SchemeDetails[0]
        JSONElement ncdDiscount = schemeDetails.NcdDiscount
        AdamjeeRateCommand adamjeeRateCommand = new AdamjeeRateCommand()
        adamjeeRateCommand.quoteNo = quoteNo
        adamjeeRateCommand.schemeCode = SCHEME_CODE
        log.info("Cover Details Product Code ${coverDetails[0]?.ProductCode}")
        adamjeeRateCommand.productCode = coverDetails?.ProductCode
        adamjeeRateCommand.productDesc = coverDetails?.ProductDesc
        adamjeeRateCommand.excess = schemeDetails.Excess?.ExcessAmnt

        List<AdamjeeRateCommand.AdamjeeRateCoverCommand> covers = []
        List<AdamjeeRateCommand.AdamjeeRateCoverCommand> optionalCovers = []
        AdamjeeRateCommand.AdamjeeRateCoverCommand discountCover
        AdamjeeRateCommand.AdamjeeRateCoverCommand loadingCover
        AdamjeeRateCommand.AdamjeeRateCoverCommand basicCover

        BigDecimal netPremium = 0.0

        for (JSONElement cover in coverDetails?.CoverDetails){

            String coverFlag = cover?.CvrFlag

            AdamjeeRateCommand.AdamjeeRateCoverCommand coverDetail = new AdamjeeRateCommand.AdamjeeRateCoverCommand()
            coverDetail.name = cover?.CdlDesc
            coverDetail.code = cover?.CdlCode
            coverDetail.premium = cover?.CdlPremFc
            coverDetail.coverFlag = cover?.CvrFlag

            if (coverFlag == "IC" && coverDetail.code == CarCoversEnum.PREMIUM_GARAGE.adcCode){
                adamjeeRateCommand.isDynaTrade = true
            }

            if (coverFlag == "IC" || coverDetail.premium == "0"){
                covers.add(coverDetail)
            }else if (coverFlag == "OC" && coverDetail.premium != "0"){
                optionalCovers.add(coverDetail)
            }else if(coverFlag == "BC" && coverDetail.premium != "0"){
                basicCover = coverDetail
                netPremium = netPremium.add(new BigDecimal(coverDetail.premium))
            }else if (coverFlag == "L"){
                log.info("AdamjeeApiService - applying Loading Of ${coverDetail.premium}")
                loadingCover = coverDetail
                netPremium = netPremium.add(new BigDecimal(coverDetail.premium))
            } else if(coverFlag == "D"){
                log.info("AdamjeeApiService - applying Discount Of ${coverDetail.premium}")
                discountCover = coverDetail
            }else if (coverFlag == "MC"){
                log.info("Cover Flag Premium with MC - ${coverDetail.coverFlag}")
                covers.add(coverDetail)
                netPremium = netPremium.add(new BigDecimal(coverDetail.premium))
            }
        }

        if (!basicCover){
            log.info("AdamjeeApi-coverDetailsToRateCommand - Error - No Basic Covers - ${coverDetails}")
            return []
        }

        if (discountCover){
            netPremium = netPremium - new BigDecimal(discountCover.premium)
        }

        if (ncdDiscount && ncdDiscount.DiscReqYn == "Y" && ncdDiscount.NcdAmnt != "0"){
            log.info("Applying NCD Discount")
            netPremium = netPremium - new BigDecimal(ncdDiscount.NcdAmnt)
        }

        adamjeeRateCommand.netPremium = netPremium
        adamjeeRateCommand.optionalCovers = optionalCovers
        adamjeeRateCommand.covers = covers
        adamjeeRateCommand.discountCover = discountCover
        adamjeeRateCommand.loadingCover = loadingCover

        log.info("Here is Net Premium ${netPremium}")

        return adamjeeRateCommand
    }

     def callSaveQuoteApi(CarQuote quote, def dynamicAddons){

        CarQuoteCover cover = CarQuoteCover.findByProviderQuoteNoAndProvider(quote.providerPolicyReference, Provider.load(InsuranceProviderEnum.ADAMJEE.id))

         if (cover){
            String providerProductId = cover.coverageType.id == CoverageTypeEnum.COMPREHENSIVE.value() ? COMPREHENSIVE_PRODUCT_CODE : TPL_PRODUCT_CODE
            JSONElement resp = saveQuoteAPI(cover.providerQuoteNo, JSON.parse(cover.covers), providerProductId, dynamicAddons, quote, providerTokenService.getToken(ExternalDataSourceEnum.ADAMJEE))

            if (resp[0].Code && resp[0].Code in [INVALID_TOKEN_RESPONSE_CODE, EXPIRE_TOKEN_RESPONSE_CODE]){
                if (callTokenApi()){
                    callSaveQuoteApi(quote, dynamicAddons)
                }else {
                    return
                }
            }

            if (resp[0].Status != OK_RESPONSE_STATUS && resp[0]?.Code != SUCCESS_SAVE_QUOTE_RESPONSE_CODE){
                log.error("adamjee.saveQuoteAPI - Error: ${resp} for quote:${quote}")
                return
            }
        }

    }

    private def saveQuoteAPI(String quoteNo, JSONElement coverDetails, String providerProductCode, def dynamicAddons, CarQuote quote, String token){

        JSONElement schemeDetails = coverDetails.SchemeDetails[0]
//        jedis = JedisConnectionPool.checkConnection(jedis)
//        String token = jedis.get("ADC_TOKEN")
        log.info("Token is ${token}")
        String requestBody = """
                {
                "Authentication": {
                "Token": "${token}",
                "UserId": "${loginId}"
                },
                "SelectedCoverData": [{
                "QuotNo": "${quoteNo}",
                "ProdCode": "${providerProductCode}",
                "SurveyYN": "${quote.id}-S",
                "SelectedCovers" : [
                        ${includeOptionalCovers(schemeDetails, dynamicAddons)}
                                    ]
                                    }]
                }"""

        log.info("Save Quote Request Body ${requestBody}")

        String encQuoteId = AESCryption.encrypt(quote.id + "")
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add(["encQuoteId": encQuoteId])
        log.info("AdamjeeApiService - Calling Save Quote API")

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add('pData', requestBody)

        JSONElement resp
        resp = providerApiService.callApi(host + SAVE_QUOTE_API, customHeaders, map, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.APPLICATION_XML_VALUE)

        return resp
    }

    private String includeOptionalCovers(JSONElement coverDetails, def dynamicAddons){

        String allCovers = ""

        for (JSONElement cover in coverDetails?.CoverDetails){
            if (cover?.CvrFlag in ["BC", "IC", "MC", "D", "L"] || cover?.CdlPremFc == "0" || cover?.CdlCode in dynamicAddons){
                String includedCover = """{
                            "CdlCode": "${cover?.CdlCode}",
                            "CdlDesc": "${cover?.CdlDesc}",
                            "ProdCode": "${cover?.ProdCode}",
                            "ProdDesc": "${cover.ProdDesc}",
                            "RecEditYn": "${cover?.RecEditYn}",
                            "CdlPremFc": "${cover?.CdlPremFc}",
                            "CdlPremLc": "${cover?.CdlPremLc}",
                            "CvrFlag": "${cover?.CvrFlag}",
                            "RecSelectedYn": "${cover?.RecSelectedYn}",
                            "RecCommYn": "${cover?.RecCommYn}",
                            "CdlPremRateNr": "${cover?.CdlPremRateNr}",
                            "CdlPremRateDr": "${cover?.CdlPremRateDr}",
                            "ValMinPrem": "${cover?.ValMinPrem}",
                            "CdlIaMinRate": "${cover?.CdlIaMinRate}",
                            "CdlIaMaxRate": "${cover?.CdlIaMaxRate}",
                            "CdlIaMinPrem": "${cover?.CdlIaMinPrem}",
                            "CdlIaMaxPrem": "${cover?.CdlIaMaxPrem}",
                            "ValMaxPrem": "${cover?.ValMaxPrem}"
                            },"""

                allCovers = allCovers +  includedCover
            }

            }

            return allCovers.substring(0, allCovers.length() - 1)
    }



    }


