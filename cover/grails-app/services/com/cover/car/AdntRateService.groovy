package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
/**
 * Abu Dhabi National takaful Insurance rating service
 *
 */
@Transactional
class AdntRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 29
    public static final Integer BASIC_PRODUCT_ID = 1100
    public static final Integer ELITE_PRODUCT_ID = 1004
    public static final Integer EXCELLENCE_PRODUCT_ID = 1005
    public static final Integer PRODUCT_TPL_ID = 1006

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("adntRate.getRates - entering with quoteCommand:${quoteCommand}, isOffline:$isOffline")

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID


        boolean checkEligibility = checkEligibility(quoteCommand)
        log.info("adntRate.getRates - carAge:${quoteCommand.carAge}")

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null

            Integer noClaimYears = ratingService.getNoClaimYears(quoteCommand)
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline, noClaimYears)

            if (applicableRates) {
                log.info("adntRate.getRates - applicableRates:${applicableRates.size()}")

                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(quoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null

        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            quoteCommand.carCategory = null

            Integer noClaimYears = ratingService.getNoClaimYears(quoteCommand)
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline, noClaimYears)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {
        log.info("adntRate.populateRatings - rate:${rate.id}, agency:${rate.baseRateAgency}, garage:${rate.baseRateGarage}")

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        log.info("adntRate.populateRatings - after min premium, rateCommand:${rateCommand.premium}")

        if (rateCommand.premium == rateCommand.minPremium) {
            rateCommand = applyMinPremiumDiscount(quoteCommand, rateCommand)
        }

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)

        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id

        rateCommand.baseRate = getBaseRate(rateCommand, quoteCommand, applicableRate)

        rateCommand.premium = ratingService.calculate(rateCommand.baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        log.info("adntRate.calculatePremium - rateCommand:${rateCommand.premium}")

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId
        rateCommand = applyTplDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (!ratingService.allowAgency()){
            return false
        }

        Make carMake = Make.get(quoteCommand.makeId)

        int carAge = quoteCommand.carAge

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.PICKUP.value &&
                    !quoteCommand.isBrandNew) {
                return false;
            }

            if ((quoteCommand.isBrandNew || carAge <= 1) && !quoteCommand.isThirdParty) {
                // if car age is <= 1 then agency for all products
                isAgency = true

            } else if (carAge == 2 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value() &&
                quoteCommand.isOldAgency) {
                // if car age is 2 and at-least 1 years NCD
                isAgency = true

            } else if (applicableRate.productId != BASIC_PRODUCT_ID &&
                carAge == 3 && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value() &&
                quoteCommand.isOldAgency) {
                isAgency = true
            }

            //Exclude All Chinese cars from Agency
            if (carMake.countryId == CountryEnum.CHINA.id) {
                isAgency = false
            }
        }
        return isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum productTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, productTypeEnum)
        if (!isEligible) {
            return false
        }

        if (quoteCommand.hasClaim) {
            return false
        }

        if (productTypeEnum == CoverageTypeEnum.COMPREHENSIVE &&
            (quoteCommand.isNonGccSpec ||
                quoteCommand.customerAge < 25 ||
                quoteCommand.customerAge > 70 ||
                quoteCommand.lastClaimPeriod == ClaimPeriodEnum.TWELVE_MONTHS)) {

                return false
        }

        if (productTypeEnum == CoverageTypeEnum.THIRD_PARTY && quoteCommand.customerAge > 70) {
            return false
        }

        return quoteCommand.localDrivingExperienceId >= DrivingExperienceEnum.ONE_TO_TWO.getId()
    }


    RateCommand applyTplDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        if (!(quoteCommand.vechileTypeId in [VehicleTypeEnum.SEDAN.value,
                                           VehicleTypeEnum.FOURx4.value,
                                           VehicleTypeEnum.PICKUP.value,
                                           VehicleTypeEnum.COUPE.value,
                                           VehicleTypeEnum.SPORTS.value])) {
            return rate
        }

        Map<Integer, Map<Integer, Integer>> vehicleToMinPremiumMap = new HashMap<>();

        Map<Integer, Integer> cylinderToMinPremiumSaloon = new HashMap<>();
        cylinderToMinPremiumSaloon.put(4, 750)
        cylinderToMinPremiumSaloon.put(6, 850)
        cylinderToMinPremiumSaloon.put(8, 950)
        cylinderToMinPremiumSaloon.put(10, 1300)
        cylinderToMinPremiumSaloon.put(12, 1300)
        vehicleToMinPremiumMap.put(VehicleTypeEnum.SEDAN.value, cylinderToMinPremiumSaloon)

        Map<Integer, Integer> cylinderToMinPremiumSUV = new HashMap<>();
        cylinderToMinPremiumSaloon.put(4, 1000)
        cylinderToMinPremiumSaloon.put(6, 1050)
        cylinderToMinPremiumSaloon.put(8, 1100)
        cylinderToMinPremiumSaloon.put(10, 1200)
        cylinderToMinPremiumSaloon.put(12, 1200)
        vehicleToMinPremiumMap.put(VehicleTypeEnum.FOURx4.value, cylinderToMinPremiumSUV)

        Map<Integer, Integer> cylinderToMinPremiumTruck = new HashMap<>();
        cylinderToMinPremiumSaloon.put(4, 1000)
        cylinderToMinPremiumSaloon.put(6, 1100)
        cylinderToMinPremiumSaloon.put(8, 1200)
        cylinderToMinPremiumSaloon.put(10, 1300)
        cylinderToMinPremiumSaloon.put(12, 1300)
        vehicleToMinPremiumMap.put(VehicleTypeEnum.PICKUP.value, cylinderToMinPremiumTruck)

        Map<Integer, Integer> cylinderToMinPremiumCoupe = new HashMap<>();
        cylinderToMinPremiumSaloon.put(4, 800)
        cylinderToMinPremiumSaloon.put(6, 850)
        cylinderToMinPremiumSaloon.put(8, 900)
        vehicleToMinPremiumMap.put(VehicleTypeEnum.COUPE.value, cylinderToMinPremiumCoupe)

        Map<Integer, Integer> cylinderToMinPremiumSports = new HashMap<>();
        cylinderToMinPremiumSaloon.put(4, 800)
        cylinderToMinPremiumSaloon.put(6, 850)
        cylinderToMinPremiumSaloon.put(8, 900)
        vehicleToMinPremiumMap.put(VehicleTypeEnum.SPORTS.value, cylinderToMinPremiumSports)

        if (quoteCommand.hasClaim) {
            rate.premium = vehicleToMinPremiumMap.get(quoteCommand.vechileTypeId).get(quoteCommand.noOfCyl)
        }

        return rate
    }

    /**
     * Get base rate based
     * @param rateCommand
     * @param quoteCommand
     * @param applicableRate
     * @return
     */
    BigDecimal getBaseRate(RateCommand rateCommand, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate
        if (quoteCommand.isBrandNew && applicableRate.baseRateBrandNew) {
            baseRate = applicableRate.baseRateBrandNew
        } else {
            baseRate = rateCommand.agencyRepair ? applicableRate.baseRateAgency : applicableRate.baseRateGarage
        }

        log.info("adnt.getBaseRate - baseRate:${baseRate}, product:${applicableRate.productId}")
        baseRate
    }

    RateCommand applyMinPremiumDiscount(QuoteCommand quoteCommand, RateCommand rate) {

        if (!quoteCommand.hasClaim && !rate.agencyRepair) {
            if (quoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value) {
                // 30% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.30))
            }

            if (quoteCommand.vechileTypeId == VehicleTypeEnum.SEDAN.value) {
                // 20% discount
                rate.premium = rate.premium.subtract(rate.basePremium * (0.20))
            }
        }
        rate
    }

}
