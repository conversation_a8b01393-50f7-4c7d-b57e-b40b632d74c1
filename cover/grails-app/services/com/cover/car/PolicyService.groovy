package com.cover.car

import com.safeguard.*
import com.safeguard.car.CarPcr
import com.safeguard.car.CarPcrDocumentGroup
import com.safeguard.car.CarQuote
import com.safeguard.docs.DrivingLicenseDocument
import com.safeguard.docs.EmiratesIdentityDocument
import com.safeguard.docs.VehicleRegistrationDocument
import com.safeguard.util.AESCryption
import com.safeguard.util.ResultWrapper
import grails.transaction.Transactional
import org.joda.time.DateTime
import org.springframework.web.multipart.MultipartFile

import java.util.concurrent.ExecutionException
import java.util.concurrent.ExecutorCompletionService
import java.util.concurrent.Executors
import java.util.concurrent.Future

@Transactional
class PolicyService {

    def documentUploadService
    def policySgService
    def grailsApplication
    def paymentService
    def messageSource
    def quoteService
    def lbnQuoteService
    def kwtQuoteService
    def egyQuoteService

    void processPolicyCancellationDocuments(CarPcr cancellationRequest, CarPcrReasonEnum reason, List<FileAndDocTypeId> fileAndDocTypeIdList) {
        CarQuote carQuote = cancellationRequest.quote
        carQuote.product.provider // just loading the provider info in order for it to be available in email sending threads
        carQuote.user // just loading the user info in order for it to be available in email sending threads

        CarPcrDocumentGroup carPcrDocumentGroup = new CarPcrDocumentGroup()
        carPcrDocumentGroup.carPcr = cancellationRequest
        carPcrDocumentGroup.save(failOnError: true)

        if (fileAndDocTypeIdList.size() == 1) {
            FileAndDocTypeId fileAndDocTypeId = fileAndDocTypeIdList.get(0)

            PolicyDocument document = new PolicyDocument(
                filename: fileAndDocTypeId.file.originalFilename,
                documentType: DocumentType.get(fileAndDocTypeId.docTypeId),
                carQuote: carQuote,
                isUploadedByCustomer: true,
                isCancellationDoc: true,
                carPcrDocumentGroup: carPcrDocumentGroup)

            document.save(failOnError: true)

            DocumentUploadService.S3UploadResult uploadResult = documentUploadService.uploadToS3(
                fileAndDocTypeId.file.inputStream,
                fileAndDocTypeId.file.originalFilename,
                carQuote.id,
                document.id,
                ProductTypeEnum.CAR
            )

            document.fullPath = uploadResult.fileUrl
            document.save(failOnError: true)

        } else {

            ExecutorCompletionService<DocumentUploadService.S3UploadResult> executorCompletionService =
                new ExecutorCompletionService(Executors.newFixedThreadPool(Math.min(fileAndDocTypeIdList.size(), 5)))

            fileAndDocTypeIdList.each { FileAndDocTypeId fileAndDocTypeId ->

                PolicyDocument document = new PolicyDocument(
                    filename: fileAndDocTypeId.file.originalFilename,
                    documentType: DocumentType.get(fileAndDocTypeId.docTypeId),
                    carQuote: carQuote,
                    isUploadedByCustomer: true,
                    isCancellationDoc: true,
                    carPcrDocumentGroup: carPcrDocumentGroup)

                document.save(failOnError: true)

                executorCompletionService.submit {
                    documentUploadService.uploadToS3(
                        fileAndDocTypeId.file.inputStream,
                        fileAndDocTypeId.file.originalFilename,
                        carQuote.id,
                        document.id,
                        ProductTypeEnum.CAR
                    )
                }
            }

            for(int i = 0; i < fileAndDocTypeIdList.size(); ++i) {
                Future<DocumentUploadService.S3UploadResult> future = executorCompletionService.take()
                try {
                    DocumentUploadService.S3UploadResult uploadResult = future.get()
                    PolicyDocument document = PolicyDocument.get(uploadResult.docId)
                    document.fullPath = uploadResult.fileUrl
                    document.save(failOnError: true)
                } catch (ExecutionException e) {
                    throw new RuntimeException("Error while uploading cancellation document to s3. Cancellation Request id " +
                        "= $cancellationRequest.id", e.getCause())
                }
            }
        }

        // send an email to customer telling him that the documents have been successfully uploaded
        notify AsyncEventConstants.CAR_PCR_EMAIL_DOCS_RECEIVED_TO_CUSTOMER, [
            quote: carQuote, cancelReqId: cancellationRequest.id, productType: ProductTypeEnum.CAR
        ]

        String domain = grailsApplication.config.getProperty("cover.domain")
        def encryptedUuid = AESCryption.encrypt(cancellationRequest.uuid)
        def reviewPageLink = "$domain//uae/en/car/policy/policy-cancellation-insurer-review?encryptedUuid=$encryptedUuid"

        List<ProviderContactToRole> providerContactToRoleList =
            ProviderContactToRole.findByRoleAndProviderIdForCurrentEnv(ProviderContactRoleEnum.POLICY_CANCELLATION, carQuote.product.provider.id)

        if (!providerContactToRoleList.isEmpty()) {
            policySgService.updateCrmStatus(carQuote, CrmStatusEnum.CANCELLATION_INSURER_REVIEW, "Cancellation Request #: PCR-$cancellationRequest.id")
            cancellationRequest.statusId = CarPcrStatusEnum.INSURER_REVIEW.id

            // send an email with docs to insurer for review
            notify AsyncEventConstants.CAR_PCR_EMAIL_REVIEW_TO_INSURER, [
                policyNo: carQuote.policyNo,
                insuranceProvider: carQuote.product.provider,
                reviewPageLink: reviewPageLink,
                providerContactToRoleList: providerContactToRoleList,
                cancelReqId: cancellationRequest.id
            ]
        } else {
            policySgService.updateCrmStatus(carQuote, CrmStatusEnum.CANCELLATION_DOCS_RECEIVED, "Cancellation Request #: PCR-$cancellationRequest.id")
            cancellationRequest.statusId = CarPcrStatusEnum.INTERNAL_REVIEW.id

            log.error("Provider contacts were not found for Provider ID = $carQuote.product.provider.id and " +
                "Provider Contact Role = POLICY_CANCELLATION. Policy Cancellation Insurer Review emails can't be sent.")
        }

        cancellationRequest.statusUpdatedDate = DateTime.now()
        cancellationRequest.reasonId = reason.id
        cancellationRequest.save(failOnError: true)
    }


    /**
     * Approves policy cancellation request.
     *
     * @param encryptedUuid - encrypted uuid of corresponding cancellation request
     * @param refundAmount - refund amount
     * @param debitNote - debit note file
     * @param creditNote - credit note file
     * @param providerContact - provider contact who has approved the cancellation request
     * @param currency - currency of the refund amount
     */
    void approvePolicyCancellationRequest(String encryptedUuid, int refundAmount, MultipartFile debitNote, MultipartFile creditNote,
                                          ProviderContact providerContact, String currency) {

        String uuid = AESCryption.decrypt(encryptedUuid)
        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)
        CarQuote carQuote = cancellationRequest.quote

        documentUploadService.savePolicyDocument(debitNote, DocumentType.get(DocumentTypeEnum.DEBIT_NOTE.id), carQuote,
            ProductTypeEnum.CAR, false, true, null)
        documentUploadService.savePolicyDocument(creditNote, DocumentType.get(DocumentTypeEnum.CREDIT_NOTE.id), carQuote,
            ProductTypeEnum.CAR, false, true, null)

        cancellationRequest.statusId = CarPcrStatusEnum.APPROVED.id
        cancellationRequest.statusUpdatedDate = DateTime.now()
        cancellationRequest.refundAmountByInsurer = refundAmount
        cancellationRequest.insurerLastReviewDate = DateTime.now()
        cancellationRequest.save(failOnError: true)

        String remarks = "Insurer employee: $providerContact.emailAddress. Refund amount by insurer: $refundAmount $currency"

        policySgService.updateCrmStatus(cancellationRequest.quote, CrmStatusEnum.CANCELLATION_APPROVED, remarks)
    }

    /**
     * Validate that we have all the required data and change status to Received
     * Most importantly, it also validates if no further documents are required then proceed to ISSUE the policy
     *
     * @param carQuote
     */
    void validateAndChangeStatusToReceived (CarQuote carQuote) {
        log.info("quote.validateAndChangeStatusToReceived - entering with car quote:${carQuote.id}")

        log.info("quote.validateAndChangeStatusToReceived - quote:${carQuote.id}, " +
            "paymentStatus:${carQuote.paymentStatus}, expired:${carQuote.isExpiredPolicy}, tpl:${carQuote.isThirdParty}," +
            " ncd:${carQuote.noClaimDiscountId}, ncd:${carQuote.noClaimDiscountId}, brandNew:${carQuote.isBrandNew}, " +
            " boughtYet: ${carQuote.isNotBoughtYet}")

        //As per our flow, change status to RECEIVED only if the current payment status is PAID
        if (carQuote.paymentStatus == PaymentStatusEnum.PAID &&
            !carQuote.isExpiredPolicy &&    //Policy Not Expired otherwise Pictures with Current date and Passing Certificate required
            !carQuote.isThirdParty &&       //Policy is not TPL otherwise Pictures with Current date and Passing Certificate required
            !carQuote.noClaimDiscountId &&  //No NCD defined otherwise NCD Certificates or Self Declaration Form (only for Watania Takaful) required
            !carQuote.isBrandNew &&         //Not brand new, otherwise Vehicle Clearance certificate or Dealer Quotation required
            !carQuote.isBuyingUsedCar &&      //In current user name, if No Mulkiya (not bought yet), Hayaza (Transfer Certificate) required
            !carQuote.financeInstitution) {

            EmiratesIdentityDocument eidDocument = EmiratesIdentityDocument.findByCarQuote(carQuote)
            DrivingLicenseDocument dlDocument = DrivingLicenseDocument.findByCarQuote(carQuote)
            VehicleRegistrationDocument vrDocument = VehicleRegistrationDocument.findByCarQuote(carQuote)

            //All Documents Available
            if (eidDocument && dlDocument && vrDocument) {
                log.info("quote.validateAndChangeStatusToReceived - all data found, marking as RECEIVED, " +
                    "car quote:${carQuote.id}")

                //if (policySgService.validateCarPolicyDocuments(carQuote))

                //All Documents are recevied, update status to RECEIED
                paymentService.changePaymentStatus(carQuote, PaymentStatusEnum.RECEIVED, null, null)

                policySgService.updateCrmStatus(carQuote, CrmStatusEnum.RECEIVED, "Digital Documents Received, after purchase")

                boolean isUnionInstantIssuanceEnabled = grailsApplication.config.instantIssue.union.enabled

                //Should Start Instant Issuance?
                if (isUnionInstantIssuanceEnabled &&
                    carQuote.valuationSource == ValuationSourceEnum.EDATA &&
                    carQuote.insuredValue >= carQuote.insuredValueMin &&
                    carQuote.insuredValue <= carQuote.insuredValueMax &&
                    carQuote.insuredValue < 150000) {
                        log.info("quote.validateAndChangeStatusToReceived - calling instance issue for union, quote:$carQuote.id ")

                        notify AsyncEventConstants.INSTANT_ISSUE_UNION, [
                            quoteId: carQuote.id
                        ]
                }
            }
        }
    }

    /**
     * Sending car quote created email using the new email template
     * @param carQuote
     * @return
     */
    ResultWrapper sendQuoteCreatedV2Email(CarQuote carQuote) {
        List ratings

        switch (carQuote.quoteCountryId) {
            case CountryEnum.UAE.id:
                (ratings, carQuote) = quoteService.getRatings(carQuote.id.toInteger(), true)
                break
            case CountryEnum.LBN.id:
                (ratings, carQuote) = lbnQuoteService.getRatings(carQuote.id.toInteger(), true)
                break
            case CountryEnum.KWT.id:
                (ratings, carQuote) = kwtQuoteService.getRatings(carQuote.id.toInteger(), true)
                break
            case CountryEnum.EGYPT.id:
                (ratings, carQuote) = egyQuoteService.getRatings(carQuote.id.toInteger(), true)
                break
        }

        ResultWrapper resultWrapper = new ResultWrapper()
        if (!ratings) {
            resultWrapper.message = messageSource.getMessage("common.noQuotesFound", [].toArray(), Locale.ENGLISH)
            return resultWrapper
        }

        notify AsyncEvents.QUOTE_CREATED, [quoteId: carQuote.id]
        resultWrapper.success = true
        resultWrapper
    }

}
