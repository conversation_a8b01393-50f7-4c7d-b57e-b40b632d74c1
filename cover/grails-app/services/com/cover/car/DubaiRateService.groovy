package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.*
import com.safeguard.car.CarValuationDto
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.ProductTplRate
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.joda.time.LocalDate

/**
 * Ratings calculation for Dubai Insurance.
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class DubaiRateService {

    def commonUtilService
    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 7
    //public static final Integer SILVER_ID = 14
    public static final Integer GOLD_ID = 15
    //public static final Integer PLATINUM_ID = 16
    public static final Integer NISSAN_MAKES_PRODUCT_ID = 5199

    public static final Long MAKE_GERMAN = 61
    public static final Long MAKE_JAPANESE = 80

    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            quoteCommand.carCategory = null

            QuoteCommand cloneQuoteCommand = new QuoteCommand()
            cloneQuoteCommand = customChecks(quoteCommand, cloneQuoteCommand)

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(cloneQuoteCommand, isOffline)
            log.info("applicableRates:${applicableRates}")
            if (applicableRates) {
                for (rate in applicableRates) {
                    RateCommand rateCommand = populateRatings(cloneQuoteCommand, rate)
                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            QuoteCommand cloneQuoteCommand = new QuoteCommand()
            cloneQuoteCommand = customChecks(quoteCommand, cloneQuoteCommand)

            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(cloneQuoteCommand, isOffline)

            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()
                rateCommand = populateRatings(cloneQuoteCommand, rate)

            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        log.info("dubai.populateRating - premium:${rateCommand.premium}, product:${rate.productId}")

        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        log.info("dubai.populateRating - after min premium - premium:${rateCommand.premium}")

        rateCommand = applyDiscounts(quoteCommand, rateCommand)

        rateCommand = applyAdditionalFees(rateCommand)

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand, true)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        log.info("dubai.populateRating - final premium:${rateCommand.premium}")

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)
        log.info("dubai.calculatePremium - applicableRate:${applicableRate}")
        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        Product product = applicableRate.product

        rateCommand.agencyRepair = isAgency
        rateCommand.productId = product.id
        rateCommand.premium = rateCommand.agencyRepair ?
            ratingService.calculate(applicableRate.baseRateAgency, quoteCommand.insuredValue) :
            ratingService.calculate(applicableRate.baseRateGarage, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getTplRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.read(quoteCommand.modelId)
            List<ProductTplRate> applicableRates =
                ratingService.
                    findTplApplicableRates(PROVIDER_ID, model.vehicleTypeId,
                        model.noOfCyl, quoteCommand.customerAge, isOffline, null, true, quoteCommand.requestSource)

            if (applicableRates) {
                ProductTplRate rate = applicableRates.first()
                rateCommand = populateTplRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateTplRatings(QuoteCommand quoteCommand, ProductTplRate rate) {

        RateCommand rateCommand = new RateCommand()
        rateCommand.currency = quoteCommand.currency
        rateCommand.basePremium = rate.basePremium
        rateCommand.premium = rate.basePremium
        rateCommand.productId = rate.productId

        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (!ratingService.allowAgency()){
            return false
        }

        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (quoteCommand.isBrandNew) {
                isAgency = true
            } else if (quoteCommand.carAge <= 1 && quoteCommand.isOldAgency) {
                isAgency = true
            } else if (quoteCommand.carAge <= 2 && quoteCommand.isOldAgency &&
                quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) {
                isAgency = true
            } else if (quoteCommand.carAge <= 3 && quoteCommand.isOldAgency &&
                quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()) {
                isAgency = true
            } else if (quoteCommand.carAge <= 4 && quoteCommand.isOldAgency &&
                quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
                isAgency = true
            }
            log.info("checkAgency - isAgency:${isAgency}")

            /*if (ratingService.agencyBlacklisted(quoteCommand)) {
                isAgency = false
            }*/

            if (applicableRate.productId == GOLD_ID) {
                isAgency = false
            }
        }
        log.info("checkAgency - returning isAgency:${isAgency}")

        isAgency
    }

    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {

        Make vehicleMake = Make.get(quoteCommand.makeId)
        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (quoteCommand.hasClaim || quoteCommand.lastClaimPeriod == ClaimPeriodEnum.TWENTY_FOUR_MONTHS ||
            quoteCommand.lastClaimPeriod == ClaimPeriodEnum.TWELVE_MONTHS) {
            isEligible = false
        }

        if (quoteCommand.isNonGccSpec) {
            isEligible = false
        }

        if (quoteCommand.policyStartDate >= LocalDate.now().plusDays(60)) {
            isEligible = false
        }

        if (quoteCommand.isPolicyExpired) {
            isEligible = false
        }


        // not eligible if uae driving experience is less then 1 year
        if (quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.getId()) {
            isEligible = false
        }

        /*//If above 200K and license less than 2 year or non german or non japanese
        if (quoteCommand.insuredValue > 200000 ) {
            Make make = Make.read(quoteCommand.makeId)
            if(quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.TWO_TO_THREE
                || (make.countryId != MAKE_GERMAN && make.countryId != MAKE_JAPANESE)) {
                isEligible = false
            }
        }*/

        /*
            not eligible if
            - current policy is third party
        */
        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && quoteCommand.isThirdParty) {
            isEligible = false
        }

        if ((vehicleMake.country.id as long) == CountryEnum.CHINA.id) {
            isEligible = false
        }

        log.info("isEligible - ${isEligible}")

        isEligible
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        boolean isAgency = rate.agencyRepair

        // NCD Discount for comprehensive non agency only
        /*if (rate.productId != SILVER_ID && rate.productId != PLATINUM_ID
            && !isAgency && quoteCommand.noClaimsDiscountId) {

            BigDecimal discountPercentage = 0

            if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
                discountPercentage = 0.10 //10% Discount
            } else if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR2.value()) {
                discountPercentage = 0.15 //15% Discount
            } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
                discountPercentage = 0.20 //20% Discount
            }

            rate.premium = rate.premium.subtract(rate.basePremium * discountPercentage)

            // Discount is not less than min premium
            if (rate.premium < rate.minPremium) {
                rate.premium = rate.minPremium
            }
        }*/

        log.info("dubai.applyDiscounts - rate.premium:${rate.premium}")

        rate
    }


    RateCommand applyAdditionalFees(RateCommand rateCommand) {

        if (rateCommand.productId == GOLD_ID) {
            // additional RSA fee of 35 AED
            rateCommand.premium = rateCommand.premium.add(35)
        }

        rateCommand
    }

    QuoteCommand customChecks(QuoteCommand quoteCommand, QuoteCommand clonedQuoteCommand) {

        InvokerHelper.setProperties(clonedQuoteCommand, quoteCommand.properties)

        CarValuationDto providerValuation = commonUtilService.getProviderValuation(InsuranceProviderEnum.DUBAI,
            !quoteCommand.isNonGccSpec,  quoteCommand.carValuation)

        BigDecimal newInsuredValue = quoteCommand.insuredValue
        if (newInsuredValue > providerValuation.valuationMax) {
            newInsuredValue = providerValuation.valuationMax
        } else if (newInsuredValue < providerValuation.valuationMin) {
            newInsuredValue = providerValuation.valuationMin
        }

        clonedQuoteCommand.carValuation = providerValuation
        log.info(".customChecks - providerValuation:${providerValuation}")
        clonedQuoteCommand.insuredValue = newInsuredValue

        clonedQuoteCommand
    }

}
