package com.cover.car

import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.car.ProviderApiToken
import grails.transaction.Transactional
import org.joda.time.LocalDateTime


@Transactional
class ProviderTokenService {

    def saveProviderToken(ExternalDataSourceEnum externalDataSource, String token, LocalDateTime expiryTime = null){

        ExternalDataSource dataSource = ExternalDataSource.findById(externalDataSource.id)
        ProviderApiToken.removeToken(dataSource)

        ProviderApiToken newToken = new ProviderApiToken()
        newToken.externalDataSource = dataSource
        newToken.token = token
        newToken.tokenExpiryTime = expiryTime
        newToken.save(failOnError: true)
    }



    String getToken(ExternalDataSourceEnum externalDataSource){

        List<ProviderApiToken> tokens = ProviderApiToken.findAllByExternalDataSource(ExternalDataSource.load(externalDataSource.id))

        if (tokens.isEmpty() || (tokens.last().tokenExpiryTime && LocalDateTime.now().isAfter(tokens.last().tokenExpiryTime))){
            return null
        }
         return tokens.last().token
    }




}
