package com.cover.car

import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.safeguard.ClaimPeriodEnum
import com.safeguard.DrivingExperienceEnum
import com.safeguard.NcdEnum
import com.safeguard.Product
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.ProductBaseRate
import com.safeguard.car.vehicle.Model
import grails.transaction.Transactional
import org.joda.time.LocalDate

/**
 * Calculate premium for Union Insurance
 * <AUTHOR>
 */
@Transactional(readOnly = true)
class UnionRateService {

    def grailsApplication
    def ratingService

    public static final Integer PROVIDER_ID = 1
    public static final Integer PRODUCT_SILVER_ID = 1 // repair type for this product is 'garage'
    public static final Integer PRODUCT_GOLD_ID = 2 // repair type for this product is 'garage'
    public static final Integer PRODUCT_GOLD_AGENCY_ID = 3 // repair type for this product is 'agency'
    public static final Integer PRODUCT_SILVER_AGENCY_ID = 89 // repair type for this product is 'agency'
    public static final Integer PRODUCT_PREM_SILVER = 67 // repair type for this product is 'premium garage'
    public static final Integer PRODUCT_PREM_GOLD = 68 // repair type for this product is 'premium garage'

    public static final String ONE_MONTH_DISCOUNT_DATE = "2020-11-30"
    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {

        List<RateCommand> rateList = []
        quoteCommand.providerId = PROVIDER_ID
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            List<RateCommand> offlineQuotes = ratingService.getOfflineQuotes(quoteCommand, PROVIDER_ID)
            if (offlineQuotes) {
                log.info("getRates - offlineQuotes: ${offlineQuotes.size()}")
                rateList.addAll(offlineQuotes)
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.unionCategoryId+""
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                for (rate in applicableRates) {
                    Product product = rate.product

                    if (product.id in [PRODUCT_GOLD_AGENCY_ID, PRODUCT_SILVER_AGENCY_ID]) {
                        // gold agency product is only eligible if
                        // - 3rd year subject to 1 year No Claim letter and
                        // last year repairing condition to be Dealers Repair.
                        // - 4th & 5th year subject to 2 year No Claim letter and
                        // last year repairing condition to be Dealers Repair.
                        if (!quoteCommand.isOldAgency || quoteCommand.hasClaim) {
                            continue
                        } else if (!(quoteCommand.carAge == 3 &&
                            quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR1.value()) &&
                            !(quoteCommand.carAge in [4, 5] &&
                                quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value()) &&
                                quoteCommand.carAge !=2)  {

                            continue
                        }
                    }

                    RateCommand rateCommand = populateRatings(quoteCommand, rate)

                    rateList.add(rateCommand)
                }
            }
        }

        rateList
    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {

        RateCommand rateCommand = null
        boolean checkEligibility = checkEligibility(quoteCommand)

        if (checkEligibility) {
            RateCommand offlineQuote = ratingService.getOfflineQuote(quoteCommand)
            log.info("getRate - offlineQuote: ${offlineQuote}")
            if (offlineQuote) {
                return offlineQuote
            }

            Model model = Model.get(quoteCommand.modelId)
            quoteCommand.carCategory = model.unionCategoryId+""
            List<ProductBaseRate> applicableRates = ratingService.getBaseRateMinPremium(quoteCommand, isOffline)
            if (applicableRates) {
                ProductBaseRate rate = applicableRates.first()

                rateCommand = populateRatings(quoteCommand, rate)
            }
        }

        rateCommand
    }

    RateCommand populateRatings(QuoteCommand quoteCommand, ProductBaseRate rate) {

        RateCommand rateCommand = calculatePremium(quoteCommand, rate)
        rateCommand = applyDiscounts(quoteCommand, rateCommand)
        rateCommand = ratingService.checkMinimumPremium(rateCommand)
        rateCommand = applyMinimumPremiumDiscount(quoteCommand, rateCommand)
        rateCommand = applyLoadings(quoteCommand, rateCommand)
        rateCommand = applyFreeMonthDiscount(quoteCommand, rateCommand)
        rateCommand = ratingService.applyCovers(quoteCommand, rateCommand)
        rateCommand = ratingService.applyExcess(quoteCommand, rateCommand)
        rateCommand = ratingService.storeOriginalPremiumBeforeC4meFee(rateCommand)
        rateCommand = ratingService.applyC4meFees(rateCommand, quoteCommand.policyStartDate, quoteCommand.vechileTypeId)

        rateCommand = ratingService.applyExtraDiscount(quoteCommand, rateCommand, PROVIDER_ID)

        Boolean pricesInclusiveVat = Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))
        if (pricesInclusiveVat) {
            rateCommand = ratingService.applyVAT(rateCommand)
        }

        rateCommand
    }

    RateCommand calculatePremium(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        boolean isAgency = checkAgency(quoteCommand, applicableRate)

        BigDecimal baseRate = getBaseRate(isAgency, quoteCommand, applicableRate)

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = quoteCommand.insuredValue
        rateCommand.leadType = quoteCommand.leadType
        rateCommand.currency = quoteCommand.currency
        rateCommand.agencyRepair = isAgency
        rateCommand.productId = applicableRate.productId
        rateCommand.premium = ratingService.calculate(baseRate, quoteCommand.insuredValue)
        rateCommand.minPremium = rateCommand.agencyRepair ?
            applicableRate.minPremiumAgency : applicableRate.minPremiumGarage
        rateCommand.basePremium = rateCommand.premium

        rateCommand
    }

    private boolean checkAgency(QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        if (!ratingService.allowAgency()){
            return false
        }

        int carAge = quoteCommand.carAge
        boolean isAgency = false

        if (!quoteCommand.selectedRepairType || quoteCommand.selectedRepairType == RepairTypeEnum.AGENCY) {

            if (applicableRate.productId in [PRODUCT_PREM_GOLD, PRODUCT_PREM_SILVER]) {
                isAgency = false
            } else if (quoteCommand.hasClaim) {

                isAgency = false
            } else if (carAge == 1) {

                isAgency = true
            } else if (carAge == 2 && quoteCommand.insuredValue > 70000) {

                isAgency = true
            } else if (applicableRate.productId in [PRODUCT_GOLD_AGENCY_ID, PRODUCT_SILVER_AGENCY_ID]) {

                isAgency = true
            }
            // if current insurance is TPL agency not applicable.
            if (quoteCommand.isThirdParty) {
                isAgency = false
            }

        }

        isAgency
    }

    private BigDecimal getBaseRate(boolean isAgency, QuoteCommand quoteCommand, ProductBaseRate applicableRate) {

        BigDecimal baseRate = isAgency ? applicableRate.baseRateAgency : applicableRate.baseRateGarage

        // if customer age is less then 25 then baseRate will be 5%
        if (quoteCommand.customerAge < 25) {

            baseRate = BigDecimal.valueOf(5)
        } else if (quoteCommand.localDrivingExperienceId <= DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId() &&
            quoteCommand.internationalDrivingExperienceId <= DrivingExperienceEnum.SIX_TO_TWELVE_MONTHS.getId()) {

            // customer driving exp is less then 1 year local and international
            // then base rate will be 5%
            baseRate = BigDecimal.valueOf(5)
        }

        baseRate
    }

    RateCommand applyDiscounts(QuoteCommand quoteCommand, RateCommand rate) {

        if (quoteCommand.isThirdParty) {
            return rate
        }

        // NCD Discount
        if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR1.value()) {
            // 5% discount
            rate.premium = rate.premium.subtract(rate.basePremium * (0.05))
        } else if (quoteCommand.noClaimsDiscountId == NcdEnum.YEAR2.value()) {
            // 7.5% discount
            rate.premium = rate.premium.subtract(rate.basePremium * (0.075))
        } else if (quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR3.value()) {
            // 10% discount
            rate.premium = rate.premium.subtract(rate.basePremium * (0.10))
        }

        rate
    }

    RateCommand applyLoadings(QuoteCommand quoteCommand, RateCommand rate) {

        int carAge = quoteCommand.carAge
        log.info("union.applyLoading - rate.basePremium:${rate.basePremium}, rate.premium:${rate.premium}")
        // loading for gold agency
        if (rate.productId in [PRODUCT_GOLD_AGENCY_ID, PRODUCT_SILVER_AGENCY_ID]) {
            if (carAge == 2 && quoteCommand.insuredValue <= 70000){
                rate.premium = rate.premium.add(rate.basePremium * (0.35)) // 35% of base premium
            } else if (carAge == 3 &&
                quoteCommand.insuredValue > 70000 &&
                quoteCommand.insuredValue <= 150000) {
                rate.premium = rate.premium.add(rate.basePremium * (0.25)) // 35% of base premium
            } else if (carAge == 3 &&
                quoteCommand.insuredValue > 150000) {
                rate.premium = rate.premium.add(rate.basePremium * (0.10)) // 10% of base premium
            } else if (carAge == 4 &&
                quoteCommand.insuredValue > 70000 &&
                quoteCommand.insuredValue <= 150000) {
                rate.premium = rate.premium.add(rate.basePremium * (0.40)) // 40% of base premium
            } else if (carAge == 4 &&
                quoteCommand.insuredValue > 150000) {
                rate.premium = rate.premium.add(rate.basePremium * (0.30)) // 30% of base premium
            } else if (carAge == 5 &&
                quoteCommand.insuredValue > 70000 &&
                quoteCommand.insuredValue <= 150000) {
                rate.premium = rate.premium.add(rate.basePremium * (0.60)) // 60% of base premium
            } else if (carAge == 5 &&
                quoteCommand.insuredValue > 150000) {
                rate.premium = rate.premium.add(rate.basePremium * (0.55)) // 55% of base premium
            }
        }

        if (rate.productId in [PRODUCT_PREM_SILVER, PRODUCT_PREM_GOLD]) {
            if (!(quoteCommand.isOldAgency && quoteCommand.noClaimsDiscountId >= NcdEnum.YEAR2.value())) {

                if (quoteCommand.carAge == 4) {
                    rate.premium = rate.premium.add(rate.basePremium * (0.15)) // 15% of base premium
                } else if (quoteCommand.carAge == 5) {
                    rate.premium = rate.premium.add(rate.basePremium * (0.25)) // 25% of base premium
                }
            }
        }
        log.info("union.applyLoading - after loading rate.premium:${rate.premium}")

        rate
    }

    boolean checkEligibility(QuoteCommand quoteCommand) {

        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)

        // only certain makes allowed if car age more then 7 years.
        /*if (quoteCommand.carAge > 7) {
            if (!(quoteCommand.makeId in eligibleOldMakes())) {
                isEligible = false
            } else if (quoteCommand.isPolicyExpired) {
                isEligible = false
            } else if (quoteCommand.isThirdParty) {
                isEligible = false
            } else if(!(quoteCommand.vechileTypeId in
                [VehicleTypeEnum.SEDAN.value, VehicleTypeEnum.FOURx4.value, VehicleTypeEnum.MPV.value])) {
                isEligible = false
            }

        }*/

        isEligible
    }

    private List eligibleOldMakes() {
        int lexus = 146
        int nissan = 156
        int honda = 136
        int toyota = 170
        int mitsubishi = 155
        [lexus, nissan, honda, toyota, mitsubishi]
    }

    RateCommand applyMinimumPremiumDiscount(QuoteCommand quoteCommand, RateCommand rate) {

        Model model = Model.read(quoteCommand.modelId)
        if (!rate.agencyRepair && model.unionCategory.id == VehicleTypeEnum.FOURx4.value
            && rate.premium == rate.minPremium &&
            (quoteCommand.noClaimsDiscountId ||
                (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
                    quoteCommand.lastClaimPeriod != ClaimPeriodEnum.TWELVE_MONTHS))) {

            //10% discount for Station Wagon minium premium on Non Agency
            rate.premium = rate.premium.subtract(rate.basePremium * (0.10))

            if (!quoteCommand.noClaimsDiscountId) {
                rate.noClaimDiscountPercent = 10   //10%
                rate.requiredSelfDeclarationNumber = 1  //At least 1 year self declaration is required
            }
        }

        rate
    }

    RateCommand applyFreeMonthDiscount(QuoteCommand quoteCommand, RateCommand rate) {
        if (quoteCommand.isThirdParty || quoteCommand.customerAge < 22 ||
            quoteCommand.localDrivingExperienceId < DrivingExperienceEnum.ONE_TO_TWO.id) {
            return rate
        }

        LocalDate discountDate = new LocalDate(ONE_MONTH_DISCOUNT_DATE);
        if (LocalDate.now() > discountDate || quoteCommand.policyStartDate > discountDate) {
            return rate;
        }

        Integer noClaimYearsByNCD = ratingService
            .getApplicableDiscountYearsByDrivingLicenseAndNcd(quoteCommand)
        Integer noClaimYears = getNoClaimYears(quoteCommand, noClaimYearsByNCD)

        if (noClaimYears < 1) return rate

        //One Month Free Insurance (Which is around 8%)
        return applyOneMonthPremium(rate, noClaimYearsByNCD)
    }

    private Integer getNoClaimYears(QuoteCommand quoteCommand, Integer noClaimYearsByNCD) {
        Integer noClaimYearsBySelfDec = 0

        if (ratingService.isEligibleForSelfDecDiscount(quoteCommand) &&
            !quoteCommand.isPolicyExpired) {
            noClaimYearsBySelfDec = ratingService
                .getApplicableDiscountYearsByDrivingLicenseAndClaimPeriod(quoteCommand)
        }

        return noClaimYearsByNCD ? noClaimYearsByNCD : noClaimYearsBySelfDec
    }

    private RateCommand applyOneMonthPremium(RateCommand rate, Integer noClaimYearsByNCD) {
        BigDecimal oneMonthPremium = (rate.premium / 13).setScale(0, BigDecimal.ROUND_UP)
        log.info("union.applyFreeMonthDiscount - rate.premium:${rate.premium}, oneMonthPremium:${oneMonthPremium}")
        rate.premium = rate.premium.subtract(oneMonthPremium)

        if (!noClaimYearsByNCD) {
            rate.requiredSelfDeclarationNumber = rate.requiredSelfDeclarationNumber ?: 1
        }

        return rate
    }

}
