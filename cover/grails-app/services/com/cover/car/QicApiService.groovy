package com.cover.car

import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.qic.QicQuoteCommand
import com.cover.car.qic.QicRateCommand
import com.safeguard.CoverageTypeEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.ExtraFieldCodeEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.MockDataUtil
import com.safeguard.Provider
import com.safeguard.QuoteExtraField
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CoverageType
import com.safeguard.car.vehicle.MakeExternal
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelExternal
import com.safeguard.car.vehicle.ModelMasterExternal
import com.safeguard.car.vehicle.ModelYearExternal
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType

import javax.annotation.PostConstruct

@Transactional
class QicApiService extends ProviderRatingService {

    //EndPoint: "https://www.devapi.anoudapps.com/qicservices/aggregator/"
    static final String GET_AUTODATA_SPEC_ADMEID = "/admeSpecification"
    static final String GET_AUTODATA_SUM_INSURED = "/admeQuoteInfo"
    static final String GET_TARIFF = "/motor/tariff"
    static final String GET_NET_PREMIUM = "/motor/netPremium"

    static final boolean SHOW_MOCK_DATA = false

    static final String OK_RESPONSE_CODE = "2000"

    def grailsApplication
    def providerApiService

    def qicAuth = [:]
    List<Map<String, String>> headers = []

    @PostConstruct
    void init() {
        qicAuth.companyCode = grailsApplication.config.getProperty("qic.auth.companyCode")
        qicAuth.endpoint = grailsApplication.config.getProperty("qic.auth.endpoint")
        qicAuth.username = grailsApplication.config.getProperty("qic.auth.username")
        qicAuth.password = grailsApplication.config.getProperty("qic.auth.password")
        headers = [
            ["provider": "QIC"],
            ["Authorization": "Basic ${(qicAuth.username + ":" + qicAuth.password).bytes.encodeBase64().toString()}"],
            ["company": qicAuth.companyCode]
        ]
    }

    @Transactional(readOnly = true)
    List getAutoDataTrims(Integer year, Model ycModel, String encQuoteId = null) {
        log.info("qic.getAutoDataTrims - entering with data [year:$year, ycModel:$ycModel]")
        List autoDataTrims = []

        ExternalDataSource adDataSource = ExternalDataSource.load(ExternalDataSourceEnum.AUTODATA.id)
        List modelExternals = []
        // Avoiding during initial stage.
        // modelExternals = ModelExternal.findAllByExternalDataSourceAndModel(adDataSource, ycModel)

        if (modelExternals.size() == 1) {
            log.info("qic.getAutoDataTrims - found exact autodata trim:${modelExternals} for $year $ycModel")
            def autoDataTrim = [
                "description": modelExternals.get(0).externalIdDescription,
                "admeId": modelExternals.get(0).externalId
            ]
            autoDataTrims.add(autoDataTrim)

            return autoDataTrims
        }

        ModelYearExternal adYear = ModelYearExternal.findByExternalDataSourceAndYear(adDataSource, year)
        MakeExternal adVehicleMake = MakeExternal.findByExternalDataSourceAndMake(adDataSource, ycModel.make)
        ModelMasterExternal adVehicleModelMaster = ModelMasterExternal.findByExternalDataSourceAndModelMasterAndModel(adDataSource, ycModel.modelMaster, ycModel)
        if (!adVehicleModelMaster) {
            adVehicleModelMaster = ModelMasterExternal.findByExternalDataSourceAndModelMaster(adDataSource, ycModel.modelMaster)
        }

        if (adYear == null || adVehicleMake == null || adVehicleModelMaster == null) {
            log.info("qic.getAutoDataTrims - missing data - year:$adYear, make:$adVehicleMake, model:$adVehicleModelMaster")
            return autoDataTrims
        }

        JSONElement autoDataTrimsResponse = getAutoDataAdmeId(adYear.externalId,
            adVehicleMake.externalId, adVehicleModelMaster.externalId, encQuoteId)

        JSONArray adSpecs = autoDataTrimsResponse.specification

        if (adSpecs == null) {
            //No specfications returns from AutoData Specs API. Return empty list
            return []
        }

        ycModel.loadTransients('en')
        List filteredAdSpecs = matchTrims(ycModel, adSpecs.toList())

        if (filteredAdSpecs.size() == 0) {
            //When nothing match, return whole list of specifications
            return adSpecs.toList()
        }

        //return only filtered specifications
        return filteredAdSpecs
    }

    /**
     * Still has issues, like 1.6 is converted to 16
     * @param ycTrim
     * @param autodataTrims
     * @return
     */
    private matchTrims(Model ycModel, List autodataTrims) {
        log.info("qic.matchTrims - ycModel:${ycModel}, autodataTrims:$autodataTrims")
        log.info("ycModel:[${ycModel.getName("en")}, ${ycModel.noOfCyl}, ${ycModel.engineSize}]")

        autodataTrims = autodataTrims.findAll { adTrim ->
            log.info("ycModel.engineSize:${ycModel.engineSize.toString()}")
            adTrim.description.contains(ycModel.engineSize.toString() + " L") &&
                adTrim.description.contains(ycModel.noOfCyl + " cyls")
        }

        /*ycTrim = ycTrim.replaceAll("[^a-zA-Z0-9 ]+", "")

        String[] tokens = ycTrim.split(" ")
        tokens.each { token ->
            log.info("filtering for token:$token")

            if (token == "AWD") {
                token = "ALL WHEEL DRIVE"
            } else if (token == "FWD") {
                token = "FRONT WHEEL DRIVE"
            } else if (token == "M/T") {
                token = "MANUAL"
            }
            autodataTrims = autodataTrims.findAll { trim ->
                log.info("filtering trims:$trim")
                //TODO:// this should also be converted to tokens and then compared otherwise 16 is found in 168
                trim.description.toLowerCase().contains(token.toLowerCase())
            }
            log.info("autodataTrims:$autodataTrims")
        }*/



        log.info("qic.matchTrims - matched autodata trims:" + autodataTrims)

        return autodataTrims
    }

    /**
     * Call AutoData Specification API
     *
     * @param adVehicleYear
     * @param adVehicleMake
     * @param adVehModel
     * @return
     */
    private JSONElement getAutoDataAdmeId(String adVehicleYear, String adVehicleMake, String adVehModel,
                                          String encQuoteId = null) {
        log.info("qicService.getAutoDataAdmeId - [ year:$adVehicleYear, make:$adVehicleMake, model:$adVehModel, encQuoteId:$encQuoteId]")

        def requestBody = [
            "withoutChassisNoFlag": true,
            "vehMake": adVehicleMake,
            "vehModel": adVehModel,
            "vehModelYear": adVehicleYear.toInteger()
        ]

        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add(["encQuoteId": encQuoteId])

        JSONElement respJson
        if (SHOW_MOCK_DATA) {
            respJson = MockDataUtil.getAutodataSpecAdmeId()
        } else {
            respJson = providerApiService.callApi(qicAuth.endpoint + GET_AUTODATA_SPEC_ADMEID, customHeaders, requestBody, HttpMethod.POST)
        }
        return respJson
    }

    /**
     * Get AutoData Valuation
     * @param adVehicleYear
     * @param adVehicleMake
     * @param adVehModel
     * @param admeId
     * @return
     */
    @Transactional(readOnly = true)
    def getAutoDataValuation(String adVehicleYear, String adVehicleMake,
                             String adVehModel, String admeId) {

        JSONElement respJson = getAutoDataSumInsured(adVehicleYear, adVehicleMake, adVehModel, admeId)


        if (respJson.respCode != OK_RESPONSE_CODE) {
            log.error("qic.getAutoDataValuation - Error: ${respJson.toString} for " +
                "Year:$adVehicleYear, Make:$adVehicleMake, Modle:$adVehModel, admeId:$admeId")
            return [lowValue:0, highValue: 0]
        }

        def valuation = [lowValue: respJson.vehicleLowValue, highValue: respJson.vehicleHighValue,
                         avgValue: respJson.vehicleValue ]
        return valuation
    }

    @Transactional(readOnly = true)
    List<ProviderRateCommand> getBaseRateMinPremium(QuoteCommand command, CoverageTypeEnum coverageTypeEnum) {
        if (!command) return []

        if (coverageTypeEnum == CoverageTypeEnum.COMPREHENSIVE && !command.adSumInsured) {
            //Shouldnt call tariff api of comprehensive product is required and we dont have AD Sum Insured value
            return []
        }

        JSONElement jsonResp = getTariff(command)

        if (jsonResp.respCode != OK_RESPONSE_CODE) {
            log.error("qic.getBaseRateMinPremium - Error: ${jsonResp.toString} for command:${command.toString()}")
            return []
        }

        JSONElement jsonSchemes = jsonResp.schemes
        CarQuoteCover quoteCover = new CarQuoteCover()
        quoteCover.quote = CarQuote.load(command.quoteId)
        quoteCover.provider = Provider.load(command.providerId)
        quoteCover.covers = jsonSchemes
        quoteCover.expiryDate = LocalDateTime.now().withTime(23, 59, 59, 999)
        quoteCover.providerQuoteNo = jsonResp.quoteNo
        quoteCover.insuredValue = command.adSumInsured ? new BigDecimal(command.adSumInsured).toInteger() : null
        quoteCover.coverageType = CoverageType.load(coverageTypeEnum.value())
        quoteCover.save(failOnError: true)

        return convertSchemesToRateCommand(jsonSchemes, jsonResp.quoteNo + "", quoteCover.insuredValue)
    }

    List<QicRateCommand> convertSchemesToRateCommand(def schemes, String quoteNo, Integer insuredValue) {
        log.info("qicApi-convertSchemesToRateCommand - $schemes")
        List<QicRateCommand> qicRateCommands = []

        for (JSONElement qicScheme in schemes) {
            log.info("qicScheme:$qicScheme")
            log.info("qicScheme.schemeCode: ${qicScheme.schemeCode}")
            QicRateCommand rateCommand = new QicRateCommand(quoteNo: quoteNo)
            rateCommand.schemeName = qicScheme.schemeName
            rateCommand.schemeCode = qicScheme.schemeCode
            rateCommand.productCode = qicScheme.productCode
            rateCommand.netPremium = new BigDecimal(qicScheme.netPremium ?: 0)
            rateCommand.excess = qicScheme.excessCovers.size() > 0 ? qicScheme.excessCovers[0].premium : ""
            rateCommand.insuredValue = insuredValue

            List<ProviderRateCommand.RateCoverCommand> qicCovers = []
            qicCovers.addAll(qicScheme.basicCovers)
            qicCovers.addAll(qicScheme.inclusiveCovers)
            qicCovers.addAll(qicScheme.discountCovers)
            rateCommand.covers = qicCovers
            rateCommand.optionalCovers = qicScheme.optionalCovers

            qicRateCommands.add(rateCommand)
        }

        return qicRateCommands
    }

    BigDecimal getNetPremium(String quoteNo, String schemeCode, String productCode, List<String> addonCodes) {
        JSONElement jsonResp = checkForNetPremium(quoteNo, schemeCode, productCode, addonCodes)

        if (jsonResp.respCode != OK_RESPONSE_CODE) {
            log.error("qic.getNetPremium - Error: ${jsonResp.toString} for quoteNo:$quoteNo, scheme:$schemeCode, product:$productCode, addonCodes:$addonCodes")
            return 0
        }

        return jsonResp.netPremium
    }

    /**
     * Call Autodata Sum Insured API
     *
     * @param adVehicleYear
     * @param adVehicleMake
     * @param adVehModel
     * @param admeId
     * @return
     */
    private JSONElement getAutoDataSumInsured(String adVehicleYear, String adVehicleMake,
                                              String adVehModel, String admeId) {
        log.info("qicService.getAutoDataSumInsured - [year:$adVehicleYear, make:$adVehicleMake, model:$adVehModel, admeId:$admeId]")

        def requestBody = [
            "withoutChassisNoFlag": true,
            "vehMake": adVehicleMake,
            "vehModel": adVehModel,
            "vehModelYear": adVehicleYear.toInteger(),
            "admeId": admeId
        ]

        JSONElement respJson
        if (SHOW_MOCK_DATA) {
            respJson = MockDataUtil.getAutodataSumInsured()
        } else {
            respJson = providerApiService.callApi(qicAuth.endpoint + GET_AUTODATA_SUM_INSURED, headers, requestBody, HttpMethod.POST)
        }
        return respJson
    }

    private JSONElement getTariff(QicQuoteCommand command) {
        log.info("qicService.getTariff - [command:$command, ${command.quoteId}]")

        def requestBody = [
            "insuredName": command.name,
            "makeCode": command.makeCode,
            "modelCode": command.modelCode,
            "modelYear": command.manufactureYear + "",
            "sumInsured": command.adSumInsured,
            "admeId": command.autoDataSpecId,
            "vehicleType": command.vehicleTypeCode,
            "vehicleUsage": command.vehicleUsageCode,
            "noOfCylinder": command.noOfCylCode,
            "nationality": command.nationalityCode,
            "seatingCapacity": command.seatingCapacity + "",
            "firstRegDate": command.firstRegistrationDate.toString("dd/MM/YYYY"),
            "policyFromDate": command.policyStartDate.toString("yyyy-MM-dd'T'00:00:00'Z'"),
            "gccSpec": command.isNonGccSpec ? "0" : "1",
            "previousInsuranceValid": command.isPolicyExpired ? "0" : "1",
            "totalLoss": "0",
            "driverDOB": command.dob.toString("dd/MM/YYYY"),
            "noClaimYear": command.noClaimYear + "",
            "selfDeclarationYear": command.selfDeclarationYear + "",
            "chassisNo": "",
            "driverExp": command.driverExp + ""
        ]

        String encQuoteId = command.quoteId ? AESCryption.encrypt(command.quoteId + "") : null
        List<Map<String, String>> customHeaders = []
        customHeaders.addAll(headers)
        customHeaders.add(["encQuoteId": encQuoteId])

        JSONElement respJson
        if (SHOW_MOCK_DATA) {
            respJson = MockDataUtil.getQicTariff()
        } else {
            respJson = providerApiService.callApi(qicAuth.endpoint + GET_TARIFF, customHeaders, requestBody, HttpMethod.POST)
        }

        return respJson
    }

    private JSONElement checkForNetPremium(String quoteNo, String schemeCode, String productCode, List<String> addonCodes) {
        log.info("qicService.checkForNetPremium - [quoteNo:$quoteNo, schemeCode:$schemeCode, " +
            "productCode:$productCode, addonCodes:$addonCodes]")

        def requestBody = [
            quoteNo : "${quoteNo}",
            schemes : [[
                           "schemeCode": "${schemeCode}" ,
                           "productCode": "${productCode}"
                       ]]
        ]

        if (addonCodes && addonCodes.size() > 0) {
            requestBody.schemes[0].optionalCovers = []
            addonCodes.each {
                requestBody.schemes[0].optionalCovers.add(["code":it])
            }
        }

        JSONElement respJson
        if (SHOW_MOCK_DATA) {
            respJson = MockDataUtil.getQicNetPremium()
        } else {
            respJson = providerApiService.callApi(qicAuth.endpoint + "/motor/netPremium", headers, requestBody, HttpMethod.POST)
        }
        return respJson

    }

}
