package com.cover.car

import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.rak.RakQuoteCommand
import com.cover.car.rak.RakRateCommand
import com.safeguard.AddonCodeEnum
import com.safeguard.ClaimPeriodEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.Product
import com.safeguard.Provider
import com.safeguard.RepairTypeEnum
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.CarValuationDto
import com.safeguard.car.CoverageType
import com.safeguard.car.vehicle.Model
import com.safeguard.exception.VehicleNotFoundException
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.transaction.Transactional
import org.codehaus.groovy.runtime.InvokerHelper
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime

@Transactional(readOnly = true)
class RakRateV2Service extends BaseRatingService {

    def commonUtilService
    def ratingService
    def rakApiService

    public static Integer COMPREHENSIVE_PRODUCT_ID = 5054
    public static Integer THIRD_PARTY_PRODUCT_ID = 5055
    public static Integer PROVIDER_ID = InsuranceProviderEnum.RAK_INSURANCE.id

    /**
     * Get Rates from RAK insurance API
     * @param quoteCommand
     * @param isOffline
     * @return
     */
    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        InsuranceProviderEnum rakProvider = InsuranceProviderEnum.RAK_INSURANCE
        Boolean showRatings = showProviderRatings(rakProvider.id, isOffline)

        if (!showRatings || quoteCommand.lastClaimPeriod == ClaimPeriodEnum.TWELVE_MONTHS) {
            return []
        }

        CarQuote carQuote = CarQuote.load(quoteCommand.quoteId)
        List<RakRateCommand> rakRateCommands = []

        List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
            findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(false, carQuote,
                Provider.load(rakProvider.id), LocalDateTime.now())

        QuoteCommand clonedQuoteCommand = new QuoteCommand()
        clonedQuoteCommand = customChecks(quoteCommand, clonedQuoteCommand)

        RakQuoteCommand rakQuoteCommand
        try {
            rakQuoteCommand = RakQuoteCommand.generateRakQuoteCommand(clonedQuoteCommand)
        } catch (VehicleNotFoundException e) {
            log.error("RakRateV2Service.getRates - Exception occurred while fetching rates for Rak Insurance", e)
            return []
        }

        if (carQuoteCovers) {
            if (carQuoteCovers[0].isNstp) {
                return []
            }

            carQuoteCovers.each {
                rakQuoteCommand.insuredValue = it.insuredValue
                rakRateCommands += rakApiService.toRakRateCommand(JSON.parse(it.covers), rakQuoteCommand)
            }
        } else {
            try {
                def startTime = System.currentTimeMillis()

                rakRateCommands = rakApiService.getBaseRateMinPremium(rakQuoteCommand)

                def endTime = System.currentTimeMillis()
                def elapsedTime = (endTime - startTime)/1000.0
                log.info("RAK API call took ${elapsedTime} seconds against quoteId: ${clonedQuoteCommand.quoteId}")
            } catch (Exception e) {
                log.error("RakRateV2Service.getRates - Exception occurred while fetching rates for Rak Insurance", e)
            }
        }

        List rateCommands = []
        rakRateCommands.each { RakRateCommand rakRateCommand ->
            RateCommand rateCommand = toRateCommand(rakQuoteCommand, rakRateCommand)
            if (rateCommand) {
                rateCommand = populateRatings(ratingService, rakQuoteCommand, rateCommand)
                rateCommands += rateCommand
            }
        }

        rateCommands
    }

    /**
     * Convert RakRateCommand object to RateCommand
     * @param rakRateCommand
     * @return
     */
    RateCommand toRateCommand(RakQuoteCommand rakQuoteCommand, RakRateCommand rakRateCommand) {
        RateCommand rateCommand = new RateCommand()
        rateCommand.productId = rakRateCommand.coverageType == CoverageTypeEnum.COMPREHENSIVE ? COMPREHENSIVE_PRODUCT_ID : THIRD_PARTY_PRODUCT_ID
        rateCommand = ratingService.applyCovers(rakQuoteCommand, rateCommand)
        rateCommand.agencyRepair =  rateCommand.productId == THIRD_PARTY_PRODUCT_ID ? false : rakRateCommand.repairType == RepairTypeEnum.AGENCY
        rateCommand.hasPremiumGarage = rakRateCommand.repairType == RepairTypeEnum.PREMIUM_GARAGE
        rateCommand.premium = rakRateCommand.netPremium
        rateCommand.providerId = InsuranceProviderEnum.RAK_INSURANCE.id
        rateCommand.insuredValue = rakRateCommand.insuredValue
        rateCommand.currency = rakQuoteCommand.currency
        rateCommand.excess = rakRateCommand.excess

        rateCommand.dynamicAddons = rakRateCommand.optionalCovers.
            findAll {it.premium != "0" }.
            collect {
                [label: it.name, price: new BigDecimal(it.premium), providerCode: it.code,
                 code: AddonCodeEnum.DYNAMIC_ADDON.code,
                 description: it.help]
            }

        List allCovers = []
        allCovers.addAll(rakRateCommand.optionalCovers)
        allCovers.addAll(rakRateCommand.covers)
        ProviderRateCommand.RateCoverCommand pax = allCovers.find {
            it.code == CarCoversEnum.PAB_PASSENGER.rakCode }
        rateCommand.personalAccidentPax = pax ? (pax.premium == "0" ? "yes" : pax.premium) : "no"

        ProviderRateCommand.RateCoverCommand pad = allCovers.find {
            it.code == CarCoversEnum.PAB_DRIVER.rakCode }
        rateCommand.paCover = pad ? (pad.premium == "0" ? "yes" : pad.premium) : "no"

        ProviderRateCommand.RateCoverCommand rac = allCovers.find {
            it.code == CarCoversEnum.RENT_A_CAR_5.rakCode }
        rateCommand.replacementCar = rac ? (rac.premium == "0" ? "yes" : rac.premium) : "no"

        ProviderRateCommand.RateCoverCommand offroadCover = allCovers.find {
            it.code == CarCoversEnum.OFF_ROAD_COVER_4WD_ONLY.rakCode }
        rateCommand.offRoadDesertRecovery = offroadCover ? (offroadCover.premium == "0" ? "yes" : offroadCover.premium) : "no"

        ProviderRateCommand.RateCoverCommand carRegService = allCovers.find {
            it.code == CarCoversEnum.CAR_REGISTRATION.rakCode }
        rateCommand.carRegService = carRegService ? (carRegService.premium == "0" ? "yes" : carRegService.premium) : "no"

        ProviderRateCommand.RateCoverCommand windScreenCover = allCovers.find {
            it.code == CarCoversEnum.WIND_SCREEN.rakCode }
        rateCommand.windScreenCover = windScreenCover ? (windScreenCover.premium == "0" ? "yes" : windScreenCover.premium) : "no"

        ProviderRateCommand.RateCoverCommand naturalCalamityCover = allCovers.find {
            it.code == CarCoversEnum.NATURAL_CALAMITY.rakCode }
        rateCommand.naturalCalamity = naturalCalamityCover ? (naturalCalamityCover.premium == "0" ? "yes" : naturalCalamityCover.premium) : "no"

        if (rateCommand.productId == COMPREHENSIVE_PRODUCT_ID) {
            rateCommand.breakdownCover = 'gold'
        } else {
            rateCommand.breakdownCover = 'silver'
        }

        rateCommand
    }

    /**
     * Fetch rate for TPL coverage type and the selected repair type
     * @param quoteCommand
     * @param isOffline
     * @return
     */
    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        getRate(quoteCommand, isOffline, CoverageTypeEnum.THIRD_PARTY)
    }

    /**
     * Fetch rate for the selected coverage type and repair type
     * @param quoteCommand
     * @param isOffline
     * @param coverageType
     * @return
     */
    RateCommand getRate(QuoteCommand quoteCommand, Boolean isOffline, CoverageTypeEnum coverageType = CoverageTypeEnum.COMPREHENSIVE) {
        boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand)
        log.info(".getRate - generalEligibilityCheck.isEligible:${isEligible}")
        if (!isEligible) {
            return null
        }

        QuoteCommand clonedQuoteCommand = new QuoteCommand()
        clonedQuoteCommand = customChecks(quoteCommand, clonedQuoteCommand)

        RakQuoteCommand rakQuoteCommand = RakQuoteCommand.generateRakQuoteCommand(clonedQuoteCommand)
        Integer providerId = InsuranceProviderEnum.RAK_INSURANCE.id
        RakRateCommand rakRateCommand
        RateCommand rateCommand

        CarQuote quote = CarQuote.load(clonedQuoteCommand.quoteId)
        CarQuoteCover carQuoteCover = CarQuoteCover.
            findByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThanAndCoverageTypeAndRepairType(false, quote,
                Provider.load(providerId), LocalDateTime.now(), CoverageType.load(coverageType.value()), clonedQuoteCommand.selectedRepairType)

        log.info(".getRate - carQuoteCover:${carQuoteCover}")

        if (carQuoteCover) {
            JSONElement covers = JSON.parse(carQuoteCover.covers)
            rakQuoteCommand.insuredValue = carQuoteCover.insuredValue
            rakRateCommand = rakApiService.toRakRateCommand(covers, rakQuoteCommand)
            rateCommand = toRateCommand(rakQuoteCommand, rakRateCommand)
            log.info(".getRate - rateCommand:${rateCommand}")

            if (rateCommand) {
                rateCommand = populateRatings(ratingService, rakQuoteCommand, rateCommand)
            }
        }

        rateCommand
    }

    /**
     * Get Premium without VAT
     * @param carQuote
     * @param providerQuoteNo
     * @param providerAddonCodes
     * @return
     */
    BigDecimal getGrossPremium(CarQuote carQuote, String providerQuoteNo, List<String> providerAddonCodes) {

        Product product = carQuote.product
        String encryptedQuoteId = AESCryption.encrypt(carQuote.id.toString())
        CarQuoteCover carQuoteCover = CarQuoteCover.findByProviderAndProviderQuoteNoAndQuoteAndRepairType(Provider.load(InsuranceProviderEnum.RAK_INSURANCE.id), providerQuoteNo, carQuote, carQuote.repairType)

        RakQuoteCommand rakQuoteCommand = new RakQuoteCommand()
        rakQuoteCommand.insuredValue = carQuote.insuredValue
        RakRateCommand rakRateCommand = rakApiService.toRakRateCommand(carQuoteCover.covers, rakQuoteCommand)
        providerAddonCodes.each { String providerAddonCode ->
            rakRateCommand.covers.each { ProviderRateCommand.RateCoverCommand rateCoverCommand ->
                if (rateCoverCommand.code == providerAddonCode) {
                    rakRateCommand.netPremium += rateCoverCommand.premium
                }
            }
        }

        rakRateCommand.netPremium
    }

    private QuoteCommand customChecks(QuoteCommand quoteCommand, QuoteCommand clonedQuoteCommand) {
        log.info(".customChecks - quoteCommand.valuationDetail:${quoteCommand.carValuation}")
        InvokerHelper.setProperties(clonedQuoteCommand, quoteCommand.properties)

        CarValuationDto providerValuation = commonUtilService.getProviderValuation(InsuranceProviderEnum.RAK_INSURANCE,
            !quoteCommand.isNonGccSpec,  quoteCommand.carValuation)

        BigDecimal newInsuredValue = quoteCommand.insuredValue
        if (newInsuredValue > providerValuation.valuationMax) {
            newInsuredValue = providerValuation.valuationMax
        } else if (newInsuredValue < providerValuation.valuationMin) {
            newInsuredValue = providerValuation.valuationMin
        }

        clonedQuoteCommand.carValuation = providerValuation
        log.info(".customChecks - providerValuation:${providerValuation}")

        clonedQuoteCommand.insuredValue = newInsuredValue

        clonedQuoteCommand
    }
}
