package com.cover.car

import com.RoadsideAssistanceEnum
import com.cover.car.commands.ProviderRateCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.dnirc.DnircQuoteCommand
import com.cover.car.dnirc.DnircRateCommand
import com.cover.util.UtilService
import com.safeguard.*
import com.safeguard.car.CarCoversEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.CarQuoteCover
import grails.converters.JSON
import grails.transaction.Transactional
import org.joda.time.LocalDateTime

@Transactional
class DubaiNationalRateV2Service extends BaseRatingService {

    def ratingService
    def dubaiNationalApiService
    def quoteService

    public static final Integer PROVIDER_ID = InsuranceProviderEnum.DNIRC.id
    public static final Integer STANDARD_PRODUCT_ID = 5111
    public static final Integer TPL_PRODUCT_ID = 1042


    List<RateCommand> getRates(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - isOffline ${isOffline} - quoteCommand ${quoteCommand}")

        try {
            boolean isShowProviderRatings = showProviderRatings(PROVIDER_ID, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - isShowProviderRatings ${isShowProviderRatings}")
            if (!isShowProviderRatings) {
                return []
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE)
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - checkEligibility ${checkEligibility}")
            if (!checkEligibility) {
                return []
            }

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<DnircRateCommand> dnircRateCommands = []
            DnircQuoteCommand dnircQuoteCommand = DnircQuoteCommand.generateDnircQuoteCommand(quoteCommand)

            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                    false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now())
            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - carQuoteCovers: ${carQuoteCovers?.size()}")

            if (carQuoteCovers.size() > 0) {
                dnircRateCommands = dubaiNationalApiService.toProviderRateCommand(JSON.parse(carQuoteCovers.last().covers), dnircQuoteCommand)
                dnircQuoteCommand.insuredValue = carQuoteCovers.last().insuredValue
            } else {
                def startTime = System.currentTimeMillis()
                dnircRateCommands = dubaiNationalApiService.getBaseRateMinPremium(dnircQuoteCommand)
                log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - executionTime: ${((System.currentTimeMillis() - startTime)) / 1000} seconds")
            }

            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - dnircRateCommandsSize ${dnircRateCommands ? dnircRateCommands.size() : null}")
            if (dnircRateCommands && dnircRateCommands.size() == 0) {
                return []
            }

            List<RateCommand> rateCommands = []
            dnircRateCommands.each {
//                if (it.productId != TPL_PRODUCT_ID) {
                RateCommand rateCommand = toRateCommand(dnircQuoteCommand, it)
                log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - rateCommand ${rateCommand}")
                if (rateCommand) {
                    RateCommand command = populateRatings(ratingService, dnircQuoteCommand, rateCommand)
                    rateCommands.add(command)
                }
                //               }

            }

            log.info("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - rateCommandsSize ${rateCommands.size()}")
            return rateCommands

        } catch (Exception e) {
            log.error("${UtilService.getQuoteLoggingPrefix('getRates', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - exception: ${e.getMessage()}", e)
            return []
        }

    }

    RateCommand getRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - isOffline ${isOffline} - quoteCommand ${quoteCommand}")

        try {
            boolean isShowProviderRatings = showProviderRatings(PROVIDER_ID, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - isShowProviderRatings ${isShowProviderRatings}")
            if (!isShowProviderRatings) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.COMPREHENSIVE)
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - checkEligibility ${checkEligibility}")
            if (!checkEligibility) {
                return null
            }

            DnircQuoteCommand dnircQuoteCommand = DnircQuoteCommand.generateDnircQuoteCommand(quoteCommand)

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                    false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now())
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - carQuoteCovers: ${carQuoteCovers?.size()}")

            DnircRateCommand dnircRateCommand = null
            if (carQuoteCovers.size() > 0) {
                dnircRateCommand = getSelectedRateCommand(dubaiNationalApiService.toProviderRateCommand(JSON.parse(carQuoteCovers.last().covers), dnircQuoteCommand), quoteCommand)
                dnircQuoteCommand.insuredValue = carQuoteCovers.last().insuredValue
            } else {
                List<DnircRateCommand> rateCommands = dubaiNationalApiService.getBaseRateMinPremium(dnircQuoteCommand)
                dnircRateCommand = getSelectedRateCommand(rateCommands, quoteCommand)
            }
            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - dnircRateCommand ${dnircRateCommand}")

            RateCommand rateCommand = null
            rateCommand = toRateCommand(dnircQuoteCommand, dnircRateCommand)

            rateCommand = populateRatings(ratingService, dnircQuoteCommand, rateCommand)

            log.info("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - rateCommand ${rateCommand}")
            return rateCommand
        } catch (Exception e) {
            log.error("${UtilService.getQuoteLoggingPrefix('getRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - exception: ${e.getMessage()}", e)
            return null
        }

    }

    RateCommand getTplRate(QuoteCommand quoteCommand, boolean isOffline) {
        log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - QuoteCommand ${quoteCommand}")

        try {
            boolean isShowProviderRatings = showProviderRatings(PROVIDER_ID, isOffline)
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - isShowProviderRatings ${isShowProviderRatings}")
            if (!isShowProviderRatings) {
                return null
            }

            quoteCommand.providerId = PROVIDER_ID
            boolean checkEligibility = checkEligibility(quoteCommand, CoverageTypeEnum.THIRD_PARTY)
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - checkEligibility ${checkEligibility}")
            if (!checkEligibility) {
                return null
            }

            DnircQuoteCommand dnircQuoteCommand = DnircQuoteCommand.generateDnircQuoteCommand(quoteCommand)

            CarQuote quote = CarQuote.load(quoteCommand.quoteId)
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.
                findAllByIsDeletedAndQuoteAndProviderAndExpiryDateGreaterThan(
                    false, quote, Provider.load(PROVIDER_ID), LocalDateTime.now())
            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - carQuoteCovers: ${carQuoteCovers?.size()}")

            List<DnircRateCommand> rateCommands = []

            if (carQuoteCovers.size() > 0) {
                rateCommands = dubaiNationalApiService.toProviderRateCommand(JSON.parse(carQuoteCovers.last().covers), dnircQuoteCommand)
                dnircQuoteCommand.insuredValue = carQuoteCovers.last().insuredValue
            } else {
                rateCommands = dubaiNationalApiService.getBaseRateMinPremium(dnircQuoteCommand)

                if (rateCommands.size() == 0) {
                    return null
                }
            }

            RateCommand rateCommand = null

            rateCommands.each {
                if (it.productId == TPL_PRODUCT_ID) {
                    RateCommand command = toRateCommand(dnircQuoteCommand, it)
                    rateCommand = populateRatings(ratingService, dnircQuoteCommand, command)
                }
            }

            log.info("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - rateCommand ${rateCommand}")
            return rateCommand
        } catch (Exception e) {
            log.error("${UtilService.getQuoteLoggingPrefix('getTplRate', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - exception: ${e.getMessage()}", e)
            return null
        }

    }


    boolean checkEligibility(QuoteCommand quoteCommand,
                             CoverageTypeEnum coverageTypeEnum = CoverageTypeEnum.COMPREHENSIVE) {
        //  boolean isEligible = ratingService.generalEligibilityCheck(quoteCommand, coverageTypeEnum)

        if (!quoteCommand.valuationSource) {
            return false
        }

        if (!quoteCommand.insuredValueMin || quoteCommand.insuredValueMin == 0) {
            return false
        }

        if (!quoteCommand.insuredValueMax || quoteCommand.insuredValueMax == 0) {
            return false
        }

        return true
    }


    RateCommand toRateCommand(DnircQuoteCommand dnircQuoteCommand, DnircRateCommand dnircRateCommand) {
        log.info("${UtilService.getQuoteLoggingPrefix('toRateCommand', dnircQuoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - dnircRateCommand: ${dnircRateCommand}")

        Integer productId = dnircRateCommand.productId
        if (!productId) return null

        Product product = Product.read(productId)
        if (!product.isActive()) return null

        RateCommand rateCommand = new RateCommand()
        rateCommand.insuredValue = dnircRateCommand.insuredValue
        rateCommand.productId = dnircRateCommand.productId
        rateCommand.providerId = PROVIDER_ID

        rateCommand = ratingService.applyCovers(dnircQuoteCommand, rateCommand)

        rateCommand.basePremium = dnircRateCommand.netPremium
        rateCommand.premium = rateCommand.basePremium
        rateCommand.minPremium = rateCommand.premium
        rateCommand.excess = dnircRateCommand.productId == TPL_PRODUCT_ID ? null : dnircRateCommand.excess
        rateCommand.insuredValue = dnircQuoteCommand.insuredValue.intValue()
        rateCommand.currency = dnircQuoteCommand.currency
        rateCommand.agencyRepair = dnircRateCommand.isAgency
        rateCommand = applyCovers(dnircQuoteCommand, rateCommand, dnircRateCommand)

        rateCommand.dynamicAddons = dnircRateCommand.optionalCovers.findAll {
            it.premium != "0"
        }.collect {
            [
                label: it.name, price: new BigDecimal(it.premium), providerCode: it.code,
                code : AddonCodeEnum.DYNAMIC_ADDON.code, description: it.benefits ? "Covered upto " + it.benefits.toString() : it.name]
        }

        return rateCommand

    }

    RateCommand applyCovers(DnircQuoteCommand dnircQuoteCommand, RateCommand rateCommand, DnircRateCommand dnircRateCommand) {

        List<ProviderRateCommand.RateCoverCommand> allCovers = []
        allCovers.addAll(dnircRateCommand.covers)
        allCovers.addAll(dnircRateCommand.optionalCovers)

        boolean isTpl = dnircRateCommand.productId == TPL_PRODUCT_ID

        ProviderRateCommand.RateCoverCommand pax = allCovers.find {
            isTpl ? it.code == CarCoversEnum.PAB_PASSENGER.dnircTplCode : CarCoversEnum.PAB_PASSENGER.dnircCode
        }

        rateCommand.personalAccidentPax = pax ? pax.premium == "0" || !pax.premium ? "yes" : pax.premium : "no"

        ProviderRateCommand.RateCoverCommand rsa = allCovers.find {
            isTpl ? it.code == CarCoversEnum.RSA_SILVER.dnircTplCode : it.code == CarCoversEnum.RSA_GOLD.dnircCode
        }

        if (isTpl) {
            rateCommand.breakdownCover = rsa ? rsa.premium == "0" || !rsa.premium ? CarCoversEnum.findByDnircTplCode(rsa.code).shortName : rsa.premium : "none"
        } else {
            rateCommand.breakdownCover = rsa ? rsa.premium == "0" || !rsa.premium ? CarCoversEnum.findByDnircCode(rsa.code).shortName : rsa.premium : "none"
        }

        ProviderRateCommand.RateCoverCommand ambulanceCover = allCovers.find {
            isTpl ? it.code == CarCoversEnum.AMBULANCE_SERVICE.dnircTplCode : it.code == CarCoversEnum.AMBULANCE_SERVICE.dnircCode
        }

        if (ambulanceCover && (!ambulanceCover.premium || ambulanceCover.premium == "0")) {
            rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.AMBULANCE_SERVICE.nameEn)
        }

        ProviderRateCommand.RateCoverCommand offRoadCover = allCovers.find {
            isTpl ? it.code == CarCoversEnum.OFF_ROAD_COVER.dnircTplCode : it.code == CarCoversEnum.OFF_ROAD_COVER.dnircCode
        }

        rateCommand.offRoadDesertRecovery = offRoadCover ? offRoadCover.premium == "0" || !offRoadCover.premium ? "yes" : offRoadCover.premium : "no"

        if (offRoadCover && (!offRoadCover.premium || offRoadCover.premium == "0")) {
            rateCommand.roadsideAssistances.add(RoadsideAssistanceEnum.OFFROAD_RECOVERY.nameEn)
        }

        ProviderRateCommand.RateCoverCommand naturalClimate = allCovers.find {
            isTpl ? it.code == CarCoversEnum.NATURAL_CALAMITY.dnircTplCode : it.code == CarCoversEnum.NATURAL_CALAMITY.dnircCode
        }
        rateCommand.naturalCalamity = naturalClimate ? naturalClimate.premium == "0" || !naturalClimate.premium ? "yes" : naturalClimate.premium : "no"

        ProviderRateCommand.RateCoverCommand pad = allCovers.find {
            isTpl ? it.code == CarCoversEnum.PAB_DRIVER.dnircTplCode : it.code == CarCoversEnum.PAB_DRIVER.dnircCode
        }
        rateCommand.paCover = pad ? pad.premium == "0" || !pad.premium ? "yes" : pad.premium : "no"

        ProviderRateCommand.RateCoverCommand windScreen = allCovers.find {
            isTpl ? it.code == CarCoversEnum.WIND_SCREEN.dnircTplCode : it.code == CarCoversEnum.WIND_SCREEN.dnircCode
        }
        if (windScreen != null) {
            rateCommand.windScreenCover = (windScreen.premium == "0" || !windScreen.premium) ? windScreen.benefits?.toString()?.replaceAll("[^0-9]", "") : "no"
        } else {
            rateCommand.windScreenCover = "no"
        }

        ProviderRateCommand.RateCoverCommand medicalEmergency = allCovers.find {
            isTpl ? it.code == CarCoversEnum.MEDICAL_EMERGENCY.dnircTplCode : it.code == CarCoversEnum.MEDICAL_EMERGENCY.dnircCode
        }
        if (medicalEmergency != null) {
            rateCommand.emergencyMedical = (medicalEmergency.premium == "0" || !medicalEmergency.premium) ? medicalEmergency.benefits?.toString()?.replaceAll("[^0-9]", "") : "no"
        } else {
            rateCommand.emergencyMedical = "no"
        }

        ProviderRateCommand.RateCoverCommand rentCar = allCovers.find {
            isTpl ? it.code == CarCoversEnum.RENT_A_CAR_7.dnircTplCode : it.code == CarCoversEnum.RENT_A_CAR_7.dnircCode
        }
        rateCommand.replacementCar = rentCar ? rentCar.premium == "0" || !rentCar.premium ? "yes" : rentCar.premium : "no"

        if (dnircQuoteCommand.vechileTypeId == VehicleTypeEnum.FOURx4.value && rateCommand.productId == STANDARD_PRODUCT_ID &&
            !rateCommand.isAgencyRepair()) {
            rateCommand.specialFeatures += "Courtesy Car Hire Benefit is provided for 7 days;"
        }

        return rateCommand
    }

    def saveQuoteDetails(CarQuote quote) {

        try {
            List<CarQuoteAddon> dynamicAddons = CarQuoteAddon.findAllByCarQuoteAndCode(quote, AddonCodeEnum.DYNAMIC_ADDON.code)
            log.info("${UtilService.getQuoteLoggingPrefix('saveQuoteDetails', quote.id, InsuranceProviderEnum.DNIRC)} - dynamicAddonsSize: ${dynamicAddons?.size()}")

            if (dynamicAddons.size() > 0) {
                dubaiNationalApiService.SaveQuoteApi(quote, dynamicAddons)
            }
        } catch (Exception e) {
            log.error("${UtilService.getQuoteLoggingPrefix('saveQuoteDetails', quote.id, InsuranceProviderEnum.DNIRC)} - exception: ${e.getMessage()}", e)
        }

    }

    DnircRateCommand getSelectedRateCommand(List<DnircRateCommand> rateCommands, QuoteCommand quoteCommand) {
        RepairTypeEnum selectedRepairType = quoteCommand.selectedRepairType
        String productId = quoteCommand.productId
        log.info("${UtilService.getQuoteLoggingPrefix('getSelectedRateCommand', quoteCommand.quoteId, InsuranceProviderEnum.DNIRC)} - rateCommandsSize: ${rateCommands.size()}, selectedRepairType: ${selectedRepairType}, productId: ${productId}")

        for (DnircRateCommand rateCommand : rateCommands) {
            if (selectedRepairType == RepairTypeEnum.AGENCY && rateCommand.productId == STANDARD_PRODUCT_ID && rateCommand.isAgency) {
                return rateCommand
            } else if ((selectedRepairType == RepairTypeEnum.PREMIUM_GARAGE || selectedRepairType == RepairTypeEnum.GARAGE) && rateCommand.productId == STANDARD_PRODUCT_ID && !rateCommand.isAgency) {
                return rateCommand
            } else if (productId == TPL_PRODUCT_ID && rateCommand.productId == TPL_PRODUCT_ID) {
                return rateCommand
            }
        }

        return null
    }


    def updateQuote(CarQuote carQuote) {
        try {
            QuoteCommand quoteCommand = quoteService.toQuoteCommand(carQuote)
            log.info("${UtilService.getQuoteLoggingPrefix('updateQuote', carQuote.id, InsuranceProviderEnum.DNIRC)} - quoteCommand: ${quoteCommand}")

            DnircQuoteCommand dnircQuoteCommand = DnircQuoteCommand.generateDnircQuoteCommand(quoteCommand)
            log.info("${UtilService.getQuoteLoggingPrefix('updateQuote', carQuote.id, InsuranceProviderEnum.DNIRC)} - dnircQuoteCommand: ${dnircQuoteCommand}")

            dubaiNationalApiService.callUpdateQuoteApi(dnircQuoteCommand, carQuote)
        } catch (Exception e) {
            log.error("${UtilService.getQuoteLoggingPrefix('updateQuote', carQuote.id, InsuranceProviderEnum.DNIRC)} - exception: ${e.getMessage()}", e)
        }
    }
}

