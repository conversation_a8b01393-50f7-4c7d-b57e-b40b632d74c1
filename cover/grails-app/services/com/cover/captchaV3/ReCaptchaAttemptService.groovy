package com.cover.captchaV3

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

/**
 * It is important to understand that by integrating reCAPTCHA, every request made will cause the server to create a socket to validate the request.
 * While we'd need a more layered approach for a true DoS mitigation, we can implement an elementary cache that restricts a client to 4 failed captcha responses
 */
class ReCaptchaAttemptService {
    private final int MAX_ATTEMPT = 4
    private LoadingCache<String, Integer> attemptsCache

    ReCaptchaAttemptService() {
        super()
        attemptsCache = CacheBuilder.newBuilder().expireAfterWrite(4, TimeUnit.HOURS).build(new CacheLoader<String, Integer>() {
            @Override
            Integer load(final String key) {
                return 0
            }
        })
    }

    void reCaptchaSucceeded(final String key) {
        attemptsCache.invalidate(key)
    }

    void reCaptchaFailed(final String key) {
        int attempts = attemptsCache.getUnchecked(key)
        attempts++
        attemptsCache.put(key, attempts)
    }

    boolean isBlocked(final String key) {
        return attemptsCache.getUnchecked(key) >= MAX_ATTEMPT
    }
}
