package com.cover.captchaV3

import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.web.client.RestClientException

/**
 * Google ReCaptcha service to validate either the request from human or bot.
 * Extends BaseCaptchaService.
 */
class CaptchaV3Service extends BaseCaptchaService {

    @Override
    void processResponse(String response, final String action) throws ReCaptchaInvalidException {
        securityCheck(response)
        final URI verifyUri = URI.create(String.format(RECAPTCHA_URL_TEMPLATE, getReCaptchaSecretV3(), response, getClientIP()))

        try {
            final GoogleResponse googleResponse = restTemplate.exchange(verifyUri, HttpMethod.GET, setApiHeaders(action), GoogleResponse.class).getBody()
            log.debug("Google's response: {} ", googleResponse.toString())

            if (!googleResponse.isSuccess() || !googleResponse.getAction().equals(action) || googleResponse.getScore() < captchaSettingsService.getThreshold()) {
                if (googleResponse.hasClientError()) {
                    reCaptchaAttemptService.reCaptchaFailed(getClientIP())
                }
                if (googleResponse.getAction() && !googleResponse.getAction().equals(action)){
                    throw new ReCaptchaInvalidException("reCaptcha was not successfully validated, reason: request action: [$action] does not match response action: [${googleResponse.getAction()}]")
                } else if (googleResponse.getScore() && googleResponse.getScore() < captchaSettingsService.getThreshold()){
                    throw new ReCaptchaInvalidException("reCaptcha was not successfully validated, reason: Score: [${googleResponse.getScore()}] is less than the Threshold: [${captchaSettingsService.getThreshold()}]")
                }
                throw new ReCaptchaInvalidException("reCaptcha was not successfully validated, reason: ${googleResponse.errorCodes.toString()}")
            }
        } catch (RestClientException rce) {
            throw new ReCaptchaUnavailableException("unavailable at this time.  Please try again later.", rce)
        }
        reCaptchaAttemptService.reCaptchaSucceeded(getClientIP())
    }
}
