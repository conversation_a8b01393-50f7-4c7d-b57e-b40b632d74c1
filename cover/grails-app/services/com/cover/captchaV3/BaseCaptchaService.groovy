package com.cover.captchaV3

import com.safeguard.util.CaptchaRequestResponseLoggingInterceptor
import grails.transaction.Transactional
import org.grails.web.servlet.mvc.GrailsWebRequest
import org.grails.web.util.WebUtils
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.ClientHttpRequestFactory
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.util.StringUtils
import org.springframework.web.client.RestTemplate

import javax.annotation.PostConstruct
import javax.servlet.http.HttpServletRequest
import java.util.regex.Pattern

/**
 * Implementing ICaptchaService interface.
 * Security checks for valid/blocked IPs, also for valid response token passed from Client Side.
 * Extract Client IP from request.
 * Set Headers for logging.
 */
@Transactional
class BaseCaptchaService implements ICaptchaService{

    def captchaSettingsService
    def reCaptchaAttemptService
    def commonUtilService

    RestTemplate restTemplate


    @PostConstruct
    private init() {
        ClientHttpRequestFactory factory = new BufferingClientHttpRequestFactory(getClientHttpRequestFactory())
        restTemplate = new RestTemplate(factory)
        restTemplate.setInterceptors(Collections.singletonList(new CaptchaRequestResponseLoggingInterceptor()))
    }

    //Override timeouts in request factory
    private SimpleClientHttpRequestFactory getClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory clientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        //Connect timeout
        clientHttpRequestFactory.setConnectTimeout(-1);

        //Read timeout
        clientHttpRequestFactory.setReadTimeout(-1);
        return clientHttpRequestFactory;
    }

    protected static final Pattern RESPONSE_PATTERN = Pattern.compile("[A-Za-z0-9_-]+")

    protected static final String RECAPTCHA_URL_TEMPLATE = "https://www.google.com/recaptcha/api/siteverify?secret=%s&response=%s&remoteip=%s"

    @Override
    String getReCaptchaSiteV3() {
        return captchaSettingsService.getSiteV3()
    }

    @Override
    String getReCaptchaSecretV3() {
        return captchaSettingsService.getSecretV3()
    }


    protected void securityCheck(final String response) {
        log.debug("Attempting to validate response {}", response)

        if (reCaptchaAttemptService.isBlocked(getClientIP())) {
            throw new ReCaptchaInvalidException("Client exceeded maximum number of failed attempts")
        }

        if (!responseSanityCheck(response)) {
            throw new ReCaptchaInvalidException("Response contains invalid characters")
        }
    }

    protected boolean responseSanityCheck(final String response) {
        return StringUtils.hasLength(response) && RESPONSE_PATTERN.matcher(response).matches()
    }

    protected String getClientIP() {
        GrailsWebRequest grailsWebRequest = WebUtils.retrieveGrailsWebRequest()
        HttpServletRequest request = grailsWebRequest.request

        def clientIp = commonUtilService.getClientIp(request.remoteAddr, request.getHeader("X-Forwarded-For"))
        return clientIp
    }

    protected HttpEntity setApiHeaders(String action) {
        HttpHeaders headers = new HttpHeaders()
        headers.add("X-User-Agent", commonUtilService.getUserAgent())
        headers.add("X-ClientIP", getClientIP())
        headers.add("X-Action", action)
        HttpEntity httpEntity = new HttpEntity(headers)
        return httpEntity
    }

}
