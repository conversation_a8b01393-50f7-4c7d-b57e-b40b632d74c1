package com.cover.captchaV3

import grails.transaction.Transactional
import grails.util.Holders

/**
 * Service to fetch reCaptcha settings from plugin.yml
 */
@Transactional
class CaptchaSettingsService {

    //reCAPTCHA V3
    private String prefix
    private String siteV3
    private String secretV3
    private float threshold

    CaptchaSettingsService() {
       prefix = "googleCaptcha.keys."
       siteV3 = Holders.grailsApplication.config.getProperty("${prefix}siteV3")
       secretV3 = Holders.grailsApplication.config.getProperty("${prefix}secretV3")
       threshold = Holders.grailsApplication.config.getProperty("${prefix}threshold").toFloat()
    }

    String getSiteV3() {
        return siteV3
    }

    String getSecretV3() {
        return secretV3
    }

    float getThreshold() {
        return threshold
    }
}
