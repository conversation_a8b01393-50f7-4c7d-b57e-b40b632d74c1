package com.cover.pa

import com.safeguard.pa.PaLeadCommand
import com.safeguard.pa.PaRating
import grails.transaction.Transactional

@Transactional(readOnly = true)
class PaRatingService {

    PaRating ratings(PaLeadCommand command) {
        int age = command.calculateAge()
        List<PaRating> ratings = PaRating.findAllByAgeFromLessThanEqualsAndAgeToGreaterThanEquals(age, age)
        return ratings ? ratings[0] : null
    }
}
