package com.cover.watch

import com.safeguard.AsyncEventConstants
import com.safeguard.User
import com.safeguard.watch.WatchQuote

class WatchQuoteService {
    def saveQuote(def params) {
        WatchQuote watchQuote = new WatchQuote()
        watchQuote.name = params.name
        watchQuote.email = params.email
        watchQuote.phone = params.phone
        watchQuote.brand_model = params.brand_model

        User user = User.findByEmail(params.email);

        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.phone
            watchQuote.user = newUser
        } else {
            user.name = params.name
            user.mobile = params.phone
            watchQuote.user = user

        }
        watchQuote.save()

        notify AsyncEventConstants.WATCH_QUOTE_CREATED, [quoteId: watchQuote.getId(), model: watchQuote.getBrand_model()]
    }
}
