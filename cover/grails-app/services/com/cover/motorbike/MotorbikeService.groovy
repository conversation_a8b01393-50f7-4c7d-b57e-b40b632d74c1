package com.cover.motorbike

import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.User
import com.safeguard.motorbike.MotorbikeQuote
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter

class MotorbikeService {
    def saveQuote(def params) {
        MotorbikeQuote motorbikeQuote = new MotorbikeQuote()

        motorbikeQuote.name = params.name
        motorbikeQuote.email = params.email
        motorbikeQuote.phone = params.phone
        motorbikeQuote.make = params.make
        motorbikeQuote.model = params.model

        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd")
        LocalDate dt = dateTimeFormatter.parseLocalDate(params.dob);

        motorbikeQuote.dob = dt
        motorbikeQuote.engine_size = params.engine_size

        User user = User.findByEmail(params.email);
        Country userCountry = Country.findById(params.nationality.toInteger())
        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.phone
            newUser.dob = dt
            newUser.nationality = userCountry
            motorbikeQuote.user = newUser
        } else {
            user.dob = dt
            user.name = params.name
            user.mobile = params.phone
            user.nationality = userCountry
            motorbikeQuote.user = user
        }

        motorbikeQuote.country = userCountry

        motorbikeQuote.save()

        notify AsyncEventConstants.MOTORBIKE_QUOTE_CREATED, [quoteId: motorbikeQuote.getId(), make: motorbikeQuote.getMake(), model: motorbikeQuote.getModel(), engine: motorbikeQuote.getEngine_size()]
    }

}
