package com.cover.business

import com.safeguard.AsyncEventConstants
import com.safeguard.User
import com.safeguard.business.BusinessQuote

class BusinessQuoteService {
    def saveQuote(def params) {
        BusinessQuote businessQuote = new BusinessQuote()
        businessQuote.name = params.name
        businessQuote.email = params.email
        businessQuote.phone = params.phone
        businessQuote.company_name = params.company_name

        User user = User.findByEmail(params.email);

        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.phone
            businessQuote.user = newUser
        } else {
            user.name = params.name
            user.mobile = params.phone
            businessQuote.user = user

        }

        businessQuote.save()

        notify AsyncEventConstants.BUSINESS_QUOTE_CREATED, [quoteId: businessQuote.getId(), companyName: businessQuote.getCompany_name()]
    }
}
