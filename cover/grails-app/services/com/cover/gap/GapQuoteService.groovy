package com.cover.gap

import com.safeguard.AsyncEventConstants
import com.safeguard.User
import com.safeguard.gap.GapQuote

class GapQuoteService {
    def saveQuote(def params) {
        GapQuote gapQuote = new GapQuote()
        gapQuote.name = params.name
        gapQuote.email = params.email
        gapQuote.phone = params.phone
        gapQuote.make = params.make
        gapQuote.model = params.model

        User user = User.findByEmail(params.email);

        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.phone
            gapQuote.user = newUser
        } else {
            user.name = params.name
            user.mobile = params.phone
            gapQuote.user = user

        }

        gapQuote.save()

        notify AsyncEventConstants.GAP_QUOTE_CREATED, [quoteId: gapQuote.getId(), make: gapQuote.getMake() , model: gapQuote.getModel()]
    }

}
