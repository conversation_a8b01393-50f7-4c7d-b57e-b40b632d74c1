package com.cover.life

import com.safeguard.Product
import com.safeguard.life.LifeRating
import grails.transaction.Transactional

@Transactional(readOnly = true)
class LifeMetLifeRateService {

    public static final Integer PROVIDER_ID = 60
    public static final Integer PRODUCT_LIFE_SALARY_PROTECT_5Y = 1101
    public static final Integer PRODUCT_LIFE_SALARY_PROTECT_10Y = 1102

    def getRatings() {

        List<LifeRating> ratings = LifeRating.createCriteria().list {
            product {
                provider : {
                    active: 1
                }
                active: 1
                inList("id", [PRODUCT_LIFE_SALARY_PROTECT_5Y, PRODUCT_LIFE_SALARY_PROTECT_10Y])
            }
        }

        return ratings
    }
}
