package com.cover.life

import com.safeguard.AsyncEventConstants
import com.safeguard.CountryEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.exception.LifeApiException
import com.safeguard.life.AxaApiWrapperService
import com.safeguard.life.Constants
import com.safeguard.life.ErrorMessages
import com.safeguard.life.LifeAdditionalInfo
import com.safeguard.life.LifeProductService
import com.safeguard.life.LifeSponsorDetails
import grails.converters.JSON
import com.safeguard.life.LifeQuote
import com.safeguard.life.LifePolicyDocument
import com.safeguard.DocumentTypeEnum
import com.safeguard.DocumentUploadService.S3UploadResult
import com.safeguard.DocumentType
import org.hibernate.exception.JDBCConnectionException
import org.joda.time.LocalDate
import org.json.JSONArray
import org.json.JSONObject
import org.springframework.http.HttpStatus
import org.springframework.web.client.ResourceAccessException
import org.springframework.web.multipart.MultipartFile
import com.safeguard.DocumentUploadService
import org.jets3t.service.S3ServiceException
import com.safeguard.util.AESCryption

class ProductService {

    LifeProductService lifeProductService
    AxaApiWrapperService axaApiWrapperService
    def commonUtilService
    DocumentUploadService documentUploadService
    LifeCheckoutService lifeCheckoutService

    def listProducts() {
        try {
            def result = axaApiWrapperService.callListProducts()
            result
        }catch(ResourceAccessException e){
            throw new LifeApiException(ErrorMessages.CONNECTION_PROBLEM, HttpStatus.SERVICE_UNAVAILABLE, e)
        }
    }

    def retrieveProductForm(String productSlug) {
        log.info("product.retrieveProductForm - productSlug:${productSlug}")

        try {
            def form = axaApiWrapperService.callGetFirstForm(productSlug)
            // sort nationalities alphabitically
            sortNationalities(form)
            return form
        }catch(ResourceAccessException e){
            throw new LifeApiException(ErrorMessages.CONNECTION_PROBLEM, HttpStatus.SERVICE_UNAVAILABLE, e)
        }
    }

    void sortNationalities(def form){
        List<org.grails.web.json.JSONArray> list = new ArrayList<org.grails.web.json.JSONArray>()
        def fields = form["form"]["fields"]
        org.grails.web.json.JSONObject nationalityField = null
        if(fields) {
            for (def field : fields) {
                if(field["name"]=="nationality"){
                    nationalityField = field
                    def options = field["options"]
                    for (def option : options) {
                        list.add(option)
                    }
                }
            }
        }
        // sort
        Collections.sort(list, new NationalitySorter());
        org.grails.web.json.JSONArray arr = new org.grails.web.json.JSONArray(list);
        nationalityField.put("options", arr)
    }

    class NationalitySorter implements Comparator<org.grails.web.json.JSONArray>
    {
        // Used for sorting in ascending order of nationality name
        public int compare(org.grails.web.json.JSONArray first, org.grails.web.json.JSONArray second)
        {
            return ((String)first[1]).compareTo((String)second[1]);
        }
    }

    def submitProductForm(String productSlug, def formDataJson, Boolean createLifeQuote = true) {
        // call axa api
        def submitFirstFormResponseBody, result

        try{
            submitFirstFormResponseBody = axaApiWrapperService.callSubmitFirstForm(productSlug, formDataJson)
            checkForValidationErrors(submitFirstFormResponseBody)
            result = axaApiWrapperService.callGetQuotesForSubProduct(submitFirstFormResponseBody)
        }catch(ResourceAccessException e){
            LifeApiException a = new LifeApiException();
            throw new LifeApiException(ErrorMessages.CONNECTION_PROBLEM, HttpStatus.SERVICE_UNAVAILABLE, e)
        }

        // create or update products in db (map axa subproduct to yc product)
        lifeProductService.createOrUpdateProducts(productSlug, submitFirstFormResponseBody.form.fields[0].options)

        // sort the quotes in reversed order
        JSONArray reversedQuotes = getReversedQuotes(result["quotes"]);
        result.put("quotes", reversedQuotes)
        // persist quote in our db
        def policyNumber = submitFirstFormResponseBody["policy_number"]
        if (createLifeQuote) {
            LifeQuote lifeQuote = lifeProductService.initQuote(formDataJson, policyNumber, productSlug)
            String encryptedQuoteId = AESCryption.encrypt(lifeQuote.id.toString())

            result.put("encryptedQuoteId", encryptedQuoteId)

            CountryEnum countryEnum = CountryEnum.findCountryByIsoAlpha2Code(lifeQuote.quoteCountry.code)
            notify AsyncEventConstants.WHATSAPP_NOTIFICATION_TRIGGER, [templateName:"life_quote_update_v3",
                                                                       quoteId     :encryptedQuoteId,
                                                                       lang        :lifeQuote.lang,
                                                                       country     :countryEnum.code,
                                                                       recipient   :lifeQuote.mobile,
                                                                       type        : "life"]
        }

        return result;
    }

    private JSONArray getReversedQuotes(JSONArray original){
        JSONArray ret = new JSONArray();
        for (int i= original.length()-1; i>=0; i--)
            ret.put(original.get(i))
        return ret;
    }

    def submitSelectedQuote(def selectedQuoteJson, def productSlug) {
        // get sub product
        def policyNumber = selectedQuoteJson["target"]["policy_number"]
        def subProductSlug = selectedQuoteJson["target"]["plan_option"]
        //
        def result, subProductJson
        try{
            result = axaApiWrapperService.callSubmitSelectedQuote(productSlug, selectedQuoteJson)
            checkForValidationErrors(result)
            subProductJson = axaApiWrapperService.getDetailsForSingleSubProduct(subProductSlug, policyNumber)
        }catch(ResourceAccessException e){
            throw new LifeApiException(ErrorMessages.CONNECTION_PROBLEM, HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        // update quote
        def newPolicyNumber = result["policy_number"]
        LifeQuote quote = lifeProductService.updateQuote(subProductJson, selectedQuoteJson, newPolicyNumber)
        //
        lifeProductService.saveAdditionalInfoForm(result, newPolicyNumber, quote)

        return result
    }

    /**
     /* save document to DB and upload to S3 server
     */
    def saveDocument(def url, def policyNumber, def fileType, def quote, def documentTypeEnumValue){
        try {
            def fileName = policyNumber + '_' + fileType + '.pdf';
            DocumentType documentType
            documentType = DocumentType.read(documentTypeEnumValue.id)
            def document = new LifePolicyDocument(filename: fileName, documentType: documentType, lifeQuote: quote)
            // save document to DB
            document.save(failOnError: true)
            // upload document to S3
            S3UploadResult uploadResult = lifeProductService.uploadFileToS3(url, fileName, quote.id, document.id)
            // update document full path
            document.fullPath = uploadResult.fileUrl
            document.save(failOnError: true)
        }
        catch(JDBCConnectionException e){
            throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
    }

    /**
     /* Send request to Axa to upload document
     */
    def oldcallAxaToUploadDocument(def url, def policyNumber, def fileType){
        UploadDocumentCommand attachDocumentBody = new UploadDocumentCommand()
        // submit document
        attachDocumentBody.name = fileType
        attachDocumentBody.data = url
        def result
        try{
            result = axaApiWrapperService.callUploadDocument(attachDocumentBody as JSON, policyNumber)
        }catch(ResourceAccessException e){
            throw new LifeApiException(ErrorMessages.CONNECTION_PROBLEM, HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        def docReferenceNumber = result["file_reference"]
        return docReferenceNumber
    }

    /**
     /* Send request to Axa to upload document
     */
    def callAxaToUploadDocument(MultipartFile file, def policyNumber, def fileType){
        // encode file into Base64 format
        byte[] bytes = file.getBytes();
        String url = "data:" + file.getContentType() + ";base64," + Base64.getEncoder().encodeToString(bytes);

        UploadDocumentCommand attachDocumentBody = new UploadDocumentCommand()
        // submit document
        attachDocumentBody.name = fileType
        attachDocumentBody.data = url
        def result
        try{
            result = axaApiWrapperService.callUploadDocument(attachDocumentBody as JSON, policyNumber)
        }catch(ResourceAccessException e){
            throw new LifeApiException(ErrorMessages.CONNECTION_PROBLEM, HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        //def docReferenceNumber = result["file_reference"]
        return result
    }


    def savePolicyDocument(PolicyDocumentCommand policyDocumentCommand) {
        log.info("saving policy document")
        String policyNumber = policyDocumentCommand.policy_number
        MultipartFile file = policyDocumentCommand.file
        String docTypeStr = policyDocumentCommand.document_type
        DocumentTypeEnum docType;

        if(docTypeStr.endsWith("national_id"))
            docType = DocumentTypeEnum.ID_CARD_FRONT
        else if(docTypeStr.endsWith("visa"))
            docType = DocumentTypeEnum.VISA
        else if(docTypeStr.endsWith("passport"))
            docType = DocumentTypeEnum.PASSPORT

        log.info("document type ${docType}")
        if(docType==null)
            throw new LifeApiException(ErrorMessages.MISSING_FORM_FIELDS, HttpStatus.BAD_REQUEST)

        if(!commonUtilService.isDocValid(file)){
            throw new LifeApiException(ErrorMessages.POLICY_DOCUMENT_NOT_VALID, HttpStatus.BAD_REQUEST)
        }
        String fileName = file.originalFilename
        InputStream fileInputStream = file.inputStream
        // get quote by policy number
        LifeQuote quote
        DocumentType documentType
        LifePolicyDocument pd
        try{
            quote = LifeQuote.findByPolicyNo(policyNumber)
            documentType = DocumentType.read(docType.id)
            pd = new LifePolicyDocument(filename: fileName, documentType: documentType, lifeQuote: quote )
            pd.save(failOnError: true)
            quote.addToDocuments(pd)
        }
        catch(JDBCConnectionException e){
            throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
        S3UploadResult uploadResult
        try{
            uploadResult = documentUploadService.uploadToS3(fileInputStream, fileName, quote.id, pd.id, ProductTypeEnum.LIFE)
        }
        catch(S3ServiceException e){
            throw new LifeApiException(ErrorMessages.UPLOAD_TO_S3_PROBLEM, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
        log.info("document added to S3")
        pd.fullPath = uploadResult.fileUrl
        // submit docs to Axa
        def axaResponse = callAxaToUploadDocument(file, policyNumber, docType)
        log.info("document uploaded to Axa")
        try{
            pd.save(flush: true, failOnError: true)
        }
        catch(JDBCConnectionException e){
            throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
        //
        log.debug("ProductService.savePolicyDocument() operation done, response: " + axaResponse.toString())
        axaResponse
    }

    /**
     /* add additional information
     */
    def submitAdditionalData(def submitAdditionalInformationCommand, def productSlug){
        def policyNumber = submitAdditionalInformationCommand.target["policy_number"]
        LifeQuote quote
        // get quote by policy number
        try{
            quote = LifeQuote.findByPolicyNo(policyNumber)
        }
        catch(JDBCConnectionException e){
            throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
        //
        def result
        try{
            submitAdditionalInformationCommand.target.start_date = new LocalDate()
            result = axaApiWrapperService.callSubmitAdditionalInformation(productSlug, submitAdditionalInformationCommand)
            checkForValidationErrors(result)
        }catch(ResourceAccessException e){
            throw new LifeApiException(e.getMessage(), HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        // update quote
        lifeProductService.updateQuote2(submitAdditionalInformationCommand, policyNumber)
        // finalize Axa payment
        processAxaPayment(policyNumber)
        // get policy schedule
        LifeAdditionalInfo additionalInfo = LifeAdditionalInfo.findByQuote(quote)
        def policySchedule = getPolicySchedule(additionalInfo.finalPolicyNo)
        // capture payment & issue policy
        lifeCheckoutService.capturePaymentAndIssuePolicy(quote)
        // send Email
        //notify AsyncEventConstants.AXA_POLICY_ISSUED, [quoteId: quote.id, attach: policySchedule]
        // return result
        log.debug("payment captured successfully and policy issued, ...about to go to Hooray page, result to be returned = " + result.toString())
        result
    }

    /**
     /* Sponsor Protect: upload insurance documents & add additional information
     */
    def submitSponsorProtectAdditionalData(def submitSponsorProtectAdditionalInformationCommand, productSlug){
        //
        def policyNumber = submitSponsorProtectAdditionalInformationCommand.target["policy_number"]
        // get quote by policy number
        LifeQuote quote
        try{
            quote = LifeQuote.findByPolicyNo(policyNumber)
        }
        catch(JDBCConnectionException e){
            throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
        //
        def result
        try{
            submitSponsorProtectAdditionalInformationCommand.target.start_date = new LocalDate()
            result = axaApiWrapperService.callSubmitAdditionalInformation(productSlug, submitSponsorProtectAdditionalInformationCommand)
        }catch(ResourceAccessException e){
            throw new LifeApiException(e.getMessage(), HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        // update quote
        lifeProductService.updateQuote2ForSponsorProtect(submitSponsorProtectAdditionalInformationCommand, policyNumber)
        // finalize Axa payment
        processAxaPayment(policyNumber)
        // get policy schedule
        LifeAdditionalInfo additionalInfo = LifeAdditionalInfo.findByQuote(quote)
        def policySchedule = getPolicySchedule(additionalInfo.finalPolicyNo)
        // capture payment & issue policy
        lifeCheckoutService.capturePaymentAndIssuePolicy(quote)
        // send Email
        //notify AsyncEventConstants.AXA_POLICY_ISSUED, [quoteId: quote.id, attach: policySchedule]
        // return result
        log.debug("payment captured successfully and policy issued, ...about to go to Hooray page, result to be returned = " + result.toString())
        result
    }

    /**
     /* upload insurance documents and add additional information
     */
    def oldsubmitDocumentsAndAdditionalData(def submitAdditionalInformationCommand, productSlug){
        def policyNumber = submitAdditionalInformationCommand.target["policy_number"]
        List<String> nationalIdList = (List) submitAdditionalInformationCommand.target["national_id"]
        List<String> passportList = (List) submitAdditionalInformationCommand.target["passport"]
        List<String> visaList = (List) submitAdditionalInformationCommand.target["visa"]
        def nationalId, passport, visa
        try{
            nationalId = nationalIdList.get(0)
            passport  = passportList.get(0)
            visa = visaList.get(0)
        }
        catch(NullPointerException e){
            throw new LifeApiException(ErrorMessages.MISSING_FORM_FIELDS, HttpStatus.BAD_REQUEST, e)
        }
        LifeQuote quote
        // get quote by policy number
        try{
            quote = LifeQuote.findByPolicyNo(policyNumber)
        }
        catch(JDBCConnectionException e){
            throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
        // submit national Id
        def nationalIdReference = callAxaToUploadDocument(nationalId, policyNumber, "national_id")
        saveDocument(nationalId, policyNumber, "national_id", quote, DocumentTypeEnum.ID_CARD_FRONT)
        // submit Passport
        def passportReference = callAxaToUploadDocument(passport, policyNumber, "passport")
        saveDocument(passport, policyNumber, "passport", quote, DocumentTypeEnum.PASSPORT)
        // submit Visa
        def visaReference = callAxaToUploadDocument(visa, policyNumber, "visa")
        saveDocument(visa, policyNumber, "visa", quote, DocumentTypeEnum.CREDIT_NOTE)
        // submit additional information
        nationalIdList.removeAt(0)
        nationalIdList.add(nationalIdReference)
        passportList.removeAt(0)
        passportList.add(passportReference)
        visaList.removeAt(0)
        visaList.add(visaReference)
        //
        def result
        try{
            submitAdditionalInformationCommand.start_date = new LocalDate()
            result = axaApiWrapperService.callSubmitAdditionalInformation(productSlug, submitAdditionalInformationCommand)
            checkForValidationErrors(result)
        }catch(ResourceAccessException e){
            throw new LifeApiException(e.getMessage(), HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        // update quote
        lifeProductService.updateQuote2(submitAdditionalInformationCommand, policyNumber)
        // send Email & SMS
        notify AsyncEventConstants.LIFE_QUOTE_PURCHASED, [lifeQuoteId: quote.id]
        // return result
        result
    }

    /**
     /* Sponsor Protect: upload insurance documents & add additional information
     */
    def oldsubmitSponsorProtectDocumentsAndAdditionalData(def submitSponsorProtectAdditionalInformationCommand, productSlug){
        //
        def policyNumber = submitSponsorProtectAdditionalInformationCommand.target["policy_number"]
        List<String> policyHolderNationalIdList = (List) submitSponsorProtectAdditionalInformationCommand.target["policy_holder_national_id"]
        List<String> policyHolderPassportList = (List) submitSponsorProtectAdditionalInformationCommand.target["policy_holder_passport"]
        List<String> insuredNationalIdList = (List) submitSponsorProtectAdditionalInformationCommand.target["insured_national_id"]
        List<String> insuredPassportList = (List) submitSponsorProtectAdditionalInformationCommand.target["insured_passport"]
        List<String> insuredVisaList = (List) submitSponsorProtectAdditionalInformationCommand.target["insured_visa"]

        def policyHolderNationalId = policyHolderNationalIdList.get(0)
        def policyHolderPassport  = policyHolderPassportList.get(0)
        def insuredNationalId = insuredNationalIdList.get(0)
        def insuredPassport  = insuredPassportList.get(0)
        def insuredVisa = insuredVisaList.get(0)
        // get quote by policy number
        LifeQuote quote = LifeQuote.findByPolicyNo(policyNumber)
        // submit policy holder national Id
        def policyHolderNationalIdReference = callAxaToUploadDocument(policyHolderNationalId, policyNumber, "policy_holder_national_id")
        saveDocument(policyHolderNationalIdReference, policyNumber, "policy_holder_national_id", quote, DocumentTypeEnum.ID_CARD_FRONT)
        // submit policy holder passport
        def policyHolderPassportReference = callAxaToUploadDocument(policyHolderPassport, policyNumber, "policy_holder_passport")
        saveDocument(policyHolderPassportReference, policyNumber, "policy_holder_passport", quote, DocumentTypeEnum.PASSPORT)
        // submit insured national Id
        def insuredNationalIdReference = callAxaToUploadDocument(insuredNationalId, policyNumber, "insured_national_id")
        saveDocument(insuredNationalIdReference, policyNumber, "insured_national_id", quote, DocumentTypeEnum.ID_CARD_FRONT)
        // submit insured passport
        def insuredPassportReference = callAxaToUploadDocument(insuredPassport, policyNumber, "insured_passport")
        saveDocument(insuredPassportReference, policyNumber, "insured_passport", quote, DocumentTypeEnum.PASSPORT)
        // submit insured visa
        def insuredVisaReference = callAxaToUploadDocument(insuredVisa, policyNumber, "insured_visa")
        saveDocument(insuredVisaReference, policyNumber, "insured_visa", quote, DocumentTypeEnum.CREDIT_NOTE)
        // submit additional information
        policyHolderNationalIdList.removeAt(0)
        policyHolderNationalIdList.add(policyHolderNationalIdReference)
        policyHolderPassportList.removeAt(0)
        policyHolderPassportList.add(policyHolderPassportReference)
        insuredNationalIdList.removeAt(0)
        insuredNationalIdList.add(insuredNationalIdReference)
        insuredPassportList.removeAt(0)
        insuredPassportList.add(insuredPassportReference)
        insuredVisaList.removeAt(0)
        insuredVisaList.add(insuredVisaReference)
        //
        def result
        try{
            result = axaApiWrapperService.callSubmitAdditionalInformation(productSlug, submitSponsorProtectAdditionalInformationCommand)
        }catch(ResourceAccessException e){
            throw new LifeApiException(e.getMessage(), HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        // update quote
        lifeProductService.updateQuote2ForSponsorProtect(submitSponsorProtectAdditionalInformationCommand, policyNumber)
        result
    }

    def confirm(def ConfirmCommand, def policyNumber){
        def result
        try{
            result = axaApiWrapperService.callConfirmPolicy(ConfirmCommand, policyNumber)
        }catch(ResourceAccessException e){
            throw new LifeApiException(e.getMessage(), HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        if(!result["success"])
            throw new LifeApiException(ErrorMessages.ORDER_NOT_CONFIRMED, HttpStatus.INTERNAL_SERVER_ERROR)
        result
    }

    def retrieveAdditionalInfoForm(def policyNo){
        try{
            LifeQuote quote = LifeQuote.findByPolicyNo(policyNo)
            LifeAdditionalInfo additionalInfo = LifeAdditionalInfo.findByQuote(quote)
            return additionalInfo?.form
        }
        catch(JDBCConnectionException e){
            throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
        }
    }

    /*
    check for validation errors received from axa
     */
    static checkForValidationErrors(def obj) {
        String errorMsg
        if (obj["has_errors"] && obj["form"]) {
            def fields = obj["form"]["fields"]
            if (fields) {
                for (def field : fields) {
                    //String msg = field["error_msg"]
                    if (field["title"].toString().equalsIgnoreCase("Beneficiaries")) {
                        def beneficiaries = field["values"]
                        if (beneficiaries) {
                            for (def beneficiary : beneficiaries) {
                                if (beneficiary){
                                    def ben_fields = beneficiary["fields"]
                                    if (ben_fields){
                                        for (def ben_field : ben_fields) {
                                            if (ben_field["error_msg"]) {
                                                String msg = ben_field["error_msg"][0]
                                                if (msg.equalsIgnoreCase("Required"))
                                                    errorMsg = ben_field["title"] + " " + "Required"
                                                else
                                                    errorMsg = ben_field["error_msg"][0]
                                                break
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (field["error_msg"]) {
                        String msg = field["error_msg"][0]
                        if (msg.equalsIgnoreCase("Required"))
                            errorMsg = field["title"] + " " + "Required"
                        else
                            errorMsg = field["error_msg"][0]
                        break
                    }
                }
            }
        }
        if (errorMsg)
            throw new LifeApiException(errorMsg, HttpStatus.BAD_REQUEST)
    }

    def isPolicyExisting(def policyNo){
        LifeQuote quote = LifeQuote.findByPolicyNo(policyNo)
        boolean policyExisting = quote != null;
        String objStr = "{'policyExisting':" + policyExisting + "}"

        return (JSONObject) JSON.parse(objStr);
    }

    /**
     /* is quote valid
     */
    def isQuoteValid(def policyNumber){
        def result
        try{
            result = axaApiWrapperService.isQuoteValid(policyNumber)
        }catch(ResourceAccessException e){
            throw new LifeApiException(ErrorMessages.CONNECTION_PROBLEM, HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        return result
    }

    /**
     * Notify Axa of payment finalization
     */
    public void processAxaPayment(def policyNumber){
        // get available payment gateways
        def gatewaysJson = axaApiWrapperService.callGetPaymentGateways(policyNumber)
        log.debug("'get pay gateways' response json: ${gatewaysJson.toString()}")
        // check Payfort is available, otherwise throw exception
        def payFortGateway = null;
        for(def gateway : gatewaysJson){
            if(gateway["gateway"].equalsIgnoreCase("yalla_compare"))
                payFortGateway = gateway;
        }
        if(payFortGateway == null)
            throw new LifeApiException(Constants.PAY_GATEWAY_NOT_ACCEPTABLE_BY_AXA, HttpStatus.INTERNAL_SERVER_ERROR)
        String gatewayName = payFortGateway["gateway"]
        // call init payment API
        String initPaymentObj = '{\n' +
            '"policy_number":"' + policyNumber + '",\n' +
            '"gateway":"' + gatewayName + '"\n' +
            '}'
        def initPaymentResponse = axaApiWrapperService.callInitiatePayment(initPaymentObj)
        log.debug("init payment response: ${initPaymentResponse.toString()}")
        String action = initPaymentResponse["action"]
        if(action == null || !action.equalsIgnoreCase("Payment to be invoiced"))
            throw new LifeApiException(Constants.GATEWAY_CANNOT_BE_INVOICED, HttpStatus.INTERNAL_SERVER_ERROR)
        // call complete payment API
        String token = initPaymentResponse["token"]
        String payRefNumber = getPaymentReferenceNumber(policyNumber)
        String completePaymentObj = '{\n' +
            '"payment_reference":"' + payRefNumber + '",\n' +
            '}'
        def completePaymentResponse = axaApiWrapperService.callCompletePayment(completePaymentObj, gatewayName, token)
        log.debug("complete payment response: ${completePaymentResponse.toString()}")
        String transactionStatus = completePaymentResponse['success']
        if(transactionStatus == null || !transactionStatus)
            throw new LifeApiException(Constants.PAYMENT_COMPLETION_FAILURE_AXA, HttpStatus.INTERNAL_SERVER_ERROR)
        String finalPolicyNumber = completePaymentResponse['policy_number']
        log.info("Final policy number: ${finalPolicyNumber}")
        // save final policy number in DB
        LifeAdditionalInfo additionalInfo = LifeAdditionalInfo.findByPolicyNo(policyNumber)
        if(additionalInfo){
            additionalInfo.finalPolicyNo = finalPolicyNumber
            additionalInfo.save(flush: true, failOnError: true)
        }
    }

    private String getPaymentReferenceNumber(def policyNumber){
        LifeQuote quote = LifeQuote.findByPolicyNo(policyNumber)
        return quote?.pgPaymentId
    }



    /**
     /* get policy schedule
     */
    def getPolicySchedule(String policyNumber){
        File file
        try{
            def body = axaApiWrapperService.callGetPolicySchedule(policyNumber)
            policyNumber = policyNumber.replace('/','-')
            String fileName = "PolicySchedule" + policyNumber + ".pdf"

            file = new File(fileName)
            file.createNewFile()
            FileOutputStream fos = new FileOutputStream(file)
            fos.write(body);
            fos.flush()
            fos.close()
            log.info("Successfully written data to the file")

        }catch(Exception e){
            throw new LifeApiException("Problem occurred while getting policy schedule document!", HttpStatus.SERVICE_UNAVAILABLE, e)
        }
        return file
    }

    def getPreVisitedQuoteInfo(def encQuoteId){
        int lifeQuoteId
        try {
            String quoteIdStr = AESCryption.decrypt(encQuoteId)
            lifeQuoteId = Integer.parseInt(quoteIdStr)
        }
        catch (Exception ex) {
            throw new LifeApiException("Unable to decrypt life quote id ${lifeQuoteId}!", HttpStatus.INTERNAL_SERVER_ERROR, ex)
        }

        LifeQuote quote = LifeQuote.findById(lifeQuoteId)

        if (!quote) {
            throw new LifeApiException("No life quote found", HttpStatus.BAD_REQUEST, ex)
        }

        FirstFormCommand firstFormCommand = new FirstFormCommand()
        firstFormCommand.country_of_residence = quote.quoteCountry.code
        firstFormCommand.mobile = quote.mobile
        firstFormCommand.email = quote.email
        firstFormCommand.nationality = quote.user.nationality?.code
        firstFormCommand.dob = quote.dob.toString("yyyy-MM-dd")
        firstFormCommand.form_name = "Your Details"
        firstFormCommand.client_code = "10124"

        String[] names = quote.name.split(" ")
        firstFormCommand.first_name = names[0]
        firstFormCommand.last_name = names[1]

        if (quote.title) {
            firstFormCommand.title = quote.title
        } else {
            firstFormCommand.title = "Mr"
        }

        if (quote.maritalStatus) {
            firstFormCommand.marital_status = quote.maritalStatus
        } else {
            firstFormCommand.marital_status = "N"
        }

        if (quote.productCategory == "sponsor-protect") {
            LifeSponsorDetails lifeSponsorDetails = LifeSponsorDetails.findByLifeQuote(quote)
            firstFormCommand.insured_title = (lifeSponsorDetails?.insuredTitle) ?: "Mr"
            firstFormCommand.insured_first_name = (lifeSponsorDetails?.insuredFirstName) ?: "First Name"
            firstFormCommand.insured_last_name = (lifeSponsorDetails?.insuredLastName) ?: "Last Name"
            firstFormCommand.insured_dob = (lifeSponsorDetails?.insuredDob?.toString("yyyy-MM-dd")) ?: "2001-11-02"
            firstFormCommand.insured_country_of_residence = (lifeSponsorDetails?.insuredCountryOfResidence?.code) ?: "AE"
            firstFormCommand.insured_nationality = (lifeSponsorDetails?.insuredNationality?.code) ?: "AL"
            firstFormCommand.insured_national_id_number = (lifeSponsorDetails?.insuredNationalIdNumber) ?: "123123123123"
            firstFormCommand.insured_national_id_expiry_date = (lifeSponsorDetails?.insuredNationalIdExpiryDate?.toString("yyyy-MM-dd")) ?: "2020-11-12"
        }

        def obj = submitProductForm(quote.productCategory, firstFormCommand as JSON, false)

        obj.put("policy_number", quote.policyNo)
        obj.put("name", quote.name)
        obj.put("email", quote.email)
        obj.put("mobile", quote.mobile)
        obj.put("dob", quote.dob?.toString("dd-MM-yyyy"))
        obj.put("title", quote.title)
        obj.put("nationality", [
            id  :   quote.user.nationality?.id,
            name:   quote.user.nationality?.nameEn
        ])

        return obj
    }
}
