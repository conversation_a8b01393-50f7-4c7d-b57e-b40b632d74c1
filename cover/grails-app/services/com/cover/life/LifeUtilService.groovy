package com.cover.life

import com.cover.lifeInsuranceCommands.DetailsCommand
import com.cover.lifeInsuranceCommands.LifeQuoteCommand
import com.cover.lifeInsuranceCommands.LifeRateCommand
import com.cover.lifeInsuranceCommands.PostQuestionnaireCommand
import com.cover.util.IConstant
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.LifeSalaryCategoryEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.RequestSourceEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.translation.AddonTranslation
import com.safeguard.life.LifeBeneficiary
import com.safeguard.life.LifeQuote
import com.safeguard.life.LifeRating
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import grails.web.mapping.LinkGenerator
import org.grails.web.util.WebUtils
import org.joda.time.DateTime
import org.joda.time.Period
import org.joda.time.LocalDate
import org.springframework.context.i18n.LocaleContextHolder

@Transactional
class LifeUtilService {

    def grailsApplication
    def lifeQuoteService
    def commonUtilService
    def lifeRateService

    def paymentService

    LinkGenerator grailsLinkGenerator

    @Transactional(readOnly = true)

    /**
     * @param lifeInsurance Detail command
     * @return Age
     */
    Integer getAge(DetailsCommand command) {
            Period period = new Period(new LocalDate(command.dob), (new LocalDate()))
            // add +1 in year if required
            return  (period.getYears())
    }

    @Transactional(readOnly = true)

    /**
     * @param String
     * @return Age
     */
    Integer getAgeByDobLocalDate(LocalDate dob) {
        Period period = new Period(new LocalDate(dob), (new LocalDate()))
        // add +1 in year if required
        return  (period.getYears())
    }

    @Transactional(readOnly = true)

    def getCountry(){
        def webUtils = WebUtils.retrieveGrailsWebRequest()
        CountryEnum countryEnum = webUtils.session[IConstant.SITE_COUNTRY]
        if (!countryEnum) {
            countryEnum = CountryEnum.UAE
        }
        Integer id = countryEnum.country.getAt("id")
        return Country.get(id)
    }


    @Transactional(readOnly = true)
    def getCurrency() {
        //TODO: get it from session when we support multiple currency
        String currency = getCountry().currency

        if (!currency) {
            currency = grailsApplication.config.getProperty("cover.fallback.currency")
        }

        currency
    }

    @Transactional(readOnly = true)
    def addToPostQuestionnaireUpdate(Long quoteId, PostQuestionnaireCommand cmd) {
        // do whatever you have to do with other updates

        LifeQuote quote = LifeQuote.get(quoteId)

        quote.correspondenceAddress = cmd.correspondenceAddress

        if(cmd.name != quote.name) {
            quote.name = cmd.name
        }

        cmd.beneficiaries.each { LifeBeneficiary beneficiary ->
            beneficiary.dateCreated = DateTime.now()
            quote.addBeneficiary(beneficiary)
        }
        if(quote.paymentMethod != PaymentMethodEnum.COD){
            quote.changePaymentStatus(PaymentStatusEnum.RECEIVED, null)
        }

//        updateCrmStatus(quote, crmStatusEnum, "Uploaded by customer after purchase")

        quote.save(failOnError:true)
    }

    @Transactional(readOnly = true)
    String getPostQuestionnaireLink(LifeQuote lifeQuote, lang='en'){

        def hash = AESCryption.encrypt(lifeQuote.id.toString())
        def postQuestionnaireLink = null
        postQuestionnaireLink = grailsLinkGenerator.link(mapping: "lifePostQuestions", params: [id: hash, country: CountryEnum.findCountryByCurrency(lifeQuote.currency).code, lang: lang, utm_source: "mandrill", utm_medium: "email", utm_campaign: "life-insurance-sale", utm_content: "email"], absolute: true)
        postQuestionnaireLink
    }

    /**
     *
     * @param quote
     * @param dropdownList
     * @param insuranceType
     * @return complate drop down list
     */
    @Transactional(readOnly = true)
    def addLifeAddonToDropDownList(quote, dropdownList, insuranceType){

        LocalDate dob = null
        LifeSalaryCategoryEnum salary = null

        if(quote instanceof CarQuote){
            dob = quote.dob
            salary = LifeSalaryCategoryEnum.NO_INCOME
        }


        // get life insurance add on && attach life insurance addon in car quote checkout

        def webUtils = WebUtils.retrieveGrailsWebRequest()
        if(quote && webUtils.session[IConstant.SITE_COUNTRY] == CountryEnum.UAE && dob && salary){
            LifeQuoteCommand lifeQuoteCommand = new LifeQuoteCommand()
            lifeQuoteCommand.salary = salary
            lifeQuoteCommand.age = getAgeByDobLocalDate( dob )
            lifeQuoteCommand.countryEnum = CountryEnum.UAE

            List<LifeRateCommand> lifeRatings = lifeQuoteService.getRatings(lifeQuoteCommand)

            String termsAndConditionsLink = "https://assets.yallacompare.com/insurance/company-terms/union-smart-term-life-insurance-terms-and-conditions.pdf"

            String lifeLabel = "Life Insurance by Union Insurance <small><a target='_blank' href='" + termsAndConditionsLink + "'>(Terms and Conditions)</a></small>"
            String lifeDescription = "Protect your family's future. Up to 1 Million AED of life insurance."
            if(LocaleContextHolder.locale.language == 'ar'){
                lifeLabel = " التأمين على الحياة ذكية الأجل عن طريق الاتحاد للتأمين <small><a target='_blank' href='" + termsAndConditionsLink + "'>(الشروط و الأحكام)</a></small>"
                lifeDescription = "حماية مستقبل عائلتك. ما يصل إلى مليون درهم من التأمين على الحياة"
            }
            def lifeSelected = null

            def lifeRatingsOptions = []
            lifeRatings.each { option ->

                def optionTitle = "Up to ${commonUtilService.formatNumber(option.cover)} AED payout for (${commonUtilService.formatNumber(option.originalPremium)} $option.currency/Year) ${option.discountAmount > 0 ? option.hasPercentDiscount ? option.discountPercent+'% off' : option.discountAmount+' '+option.currency+' off' : '' }"
//                def optionTitle = "Up to ${commonUtilService.formatNumber(option.cover)} AED payout for (${commonUtilService.formatNumber(option.originalPremium)} $option.currency/Year)"
                if(LocaleContextHolder.locale.language == 'ar'){
                    optionTitle = " ما يصل إلى ${commonUtilService.formatNumber(option.cover)}  درهم  لمبلغ (${commonUtilService.formatNumber(option.originalPremium)} $option.currency / السنة) ${option.discountAmount > 0 ? option.hasPercentDiscount ? option.discountPercent+'% خصم' : option.discountAmount+' '+option.currency+' خصم' : '' }"
                }
                lifeRatingsOptions.add([option:optionTitle, value:option.rateId, id:option.rateId, cost: option.totalPrice])
            }
            dropdownList.add([label:lifeLabel, description:lifeDescription,
                              options:lifeRatingsOptions, name:'lifeInsuranceAddon', value:lifeSelected])

        }else{
            log.info("quote is null thats why life cannot get ratings.")
        }

        dropdownList

    }


    /** send addon model with selected life insurance addon. diff scenarios for different type of insurance
     *
     * @param rateId
     * @param quote
     * @return the newly made add on model or null
     */
    @Transactional(readOnly = true)
    def getAddOnDetails(Long rateId, quote){

        if(quote instanceof CarQuote){
            LocalDate dob = quote.dob
            LifeSalaryCategoryEnum salary = LifeSalaryCategoryEnum.NO_INCOME

            AddonTranslation lifeInsuranceAddonTranslations = AddonTranslation.findByCode('lifeInsuranceAddon')
            LifeQuoteCommand lifeQuoteCommand = new LifeQuoteCommand()
            lifeQuoteCommand.age = getAgeByDobLocalDate( dob )
            lifeQuoteCommand.salary = salary
            lifeQuoteCommand.countryEnum = CountryEnum.findCountryById(quote.quoteCountry.id)
            LifeRateCommand rate = lifeQuoteService.getRating(rateId, lifeQuoteCommand)

            new CarQuoteAddon(carQuote: quote, addonTranslation: lifeInsuranceAddonTranslations,
                price: rate.totalPrice, code: "lifeInsuranceRateId-${rateId}")
        }else{
            return
        }

    }

    /**
     *
     * @param rateId
     * @param country
     * @return String
     */
    @Transactional(readOnly = true)
    String getRateLabelByRateId(Long rateId, CountryEnum country=null){
        if(rateId){
            LifeRating lifeRate = LifeRating.get(rateId)
            String str = ""
            if(!country){
                LifeRateCommand lifeRateCommand = lifeRateService.toLifeRateCommand(lifeRate)
                if(LocaleContextHolder.locale.language == 'ar'){
                    str = "$lifeRate.product.nameAr عن طريق $lifeRate.product.provider.nameAr (تغطية حتى ${commonUtilService.formatNumber( lifeRateCommand.cover as BigDecimal )} $lifeRateCommand.currency)"
                }else{
                    str = "$lifeRate.product.name by $lifeRate.product.provider.name (Coverage ${commonUtilService.formatNumber( lifeRateCommand.cover as BigDecimal )} $lifeRateCommand.currency)"
                }
            }else{
                String currency = country.currency
                if(LocaleContextHolder.locale.language == 'ar'){
                    str = "$lifeRate.product.nameAr عن طريق $lifeRate.product.provider.nameAr (تغطية حتى ${commonUtilService.formatNumber( lifeRate.cover as BigDecimal )} $currency)"
                }else{
                    str = "$lifeRate.product.name by $lifeRate.product.provider.name (Coverage ${commonUtilService.formatNumber( lifeRate.cover as BigDecimal )} $currency)"
                }
            }
            str
        }else{
            ""
        }

    }

}
