package com.cover.life

import com.safeguard.life.AxaApiWrapperService
import com.safeguard.life.LifeProductService

class InitializationService {

    LifeProductService lifeProductService
    AxaApiWrapperService axaApiWrapperService

    def init() {
        try {
            axaApiWrapperService.callInit()
            axaApiWrapperService.callLogin()
        } catch (Exception e) {
            log.error("Unable to call AXA APIs", e)
        }
        lifeProductService.createProviderIfNotExits()
    }
    def login() {}
    def retrieveSessionId() {}
    def checkLoginStatus() {}

}
