package com.cover.life

import com.cover.lifeInsuranceCommands.LifeQuoteCommand
import com.cover.lifeInsuranceCommands.LifeRateCommand
import com.safeguard.life.LifeRating
import grails.transaction.Transactional


@Transactional
class LifeUnionRateService {

    def lifeQuoteService
    def utilService
    def lifeRateService

    public static final Integer PROVIDER_ID = 1

    @Transactional(readOnly = true)
    List<LifeRateCommand> getRates(LifeQuoteCommand quoteCommand) {

        List<LifeRateCommand> rateCommandList = []

        List<LifeRating> rates = lifeQuoteService.findApplicableRates(quoteCommand.age, quoteCommand.salary, PROVIDER_ID )

        for (rate in rates) {

            LifeRateCommand rateCommand = lifeRateService.toLifeRateCommand(rate, quoteCommand)

            rateCommandList.add(rateCommand)

        }
        rateCommandList
    }

    @Transactional(readOnly = true)
    LifeRateCommand getRate(Long rateId, LifeQuoteCommand quoteCommand) {
        LifeRating rate = LifeRating.read(rateId)

        LifeRateCommand rateCommand = lifeRateService.toLifeRateCommand(rate, quoteCommand)

        rateCommand
    }
}
