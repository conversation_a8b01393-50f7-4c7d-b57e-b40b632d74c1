package com.cover.life

import com.cover.lifeInsuranceCommands.DetailsCommand
import com.cover.lifeInsuranceCommands.LifeQuoteCommand
import com.cover.lifeInsuranceCommands.LifeRateCommand
import com.safeguard.*
import com.safeguard.LifeSalaryCategoryEnum
import com.safeguard.base.Comparison
import com.safeguard.base.Lead
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon
import com.safeguard.life.LifeQuote
import com.safeguard.life.LifeRating
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.transaction.Transactional
import org.joda.time.LocalDate


/**
 * Life quote service.
 * <AUTHOR>
 */
@Transactional
class LifeQuoteService {

    def utilService
    def paymentService
    def lifeUtilService
    def checkoutService
    def lifeUnionRateService
    def commonUtilService
    def lifeRateService
    def grailsApplication
    def crmService
    def lifeMetLifeRateService
    def leadSgService

    LifeQuote createLifeQuote(DetailsCommand command) {
        LifeQuote lifeQuote = new LifeQuote()
        lifeQuote.name = command.name

        lifeQuote.beneficiaryName = command.beneficiaryName == command.name ? null : command.beneficiaryName
        lifeQuote.mobile = command.mobile
        lifeQuote.email = command.email
        lifeQuote.dob = command.dob
        //lifeQuote.dob = new LocalDate(command.dob)
        lifeQuote.salary = command.salaryEnum
        lifeQuote.age = command.age ?: lifeUtilService.getAge(command)

        lifeQuote.currency = "AED"

        lifeQuote.policyStartDate = new LocalDate(command.policyStartDate)


        try {
            lifeQuote.lang = utilService.getLanguage()
        }
        catch (Exception e) {
            lifeQuote.lang = 'en'
        }


        User user = User.findByEmail(command.email)
        if (!user) {
            //Create new user if user is not found in db
            user = new User(email: command.email, password: 'lifequote', lifeLeadType: LeadType.NORMAL, dob: command.dob)
        }
        Country country = Country.get(command.countryEnum.country.id)
        lifeQuote.quoteCountry = country

        if (!user.enabled) {
            user.name = command.name
            user.country = country
        }

        if (!user.userDetails || !user.userDetails.mobileVerified) {
            user.mobile = command.mobile
        }


        if (!user.lifeLeadType) {
            user.lifeLeadType = LeadType.NORMAL
        }

        user.save(flush: true)

        crmService.handleCrmEvents(user.id, 'life')

        RequestSourceEnum requestSourceEnum = RequestSourceEnum.findRequestSource(command.source) ?: RequestSourceEnum.WEB

        lifeQuote.user = user
        //lifeQuote.paymentStatus = PaymentStatusEnum.DRAFT
        lifeQuote.source = requestSourceEnum
        paymentService.changePaymentStatus(lifeQuote, PaymentStatusEnum.DRAFT)

        lifeQuote.save(flush: true, failOnError: true)
        leadSgService.createLeadAndComparison(lifeQuote)

        lifeQuote
    }


    List<LifeRateCommand> getRatings(LifeQuoteCommand quoteCommand) {
        log.info "life.quote.service.getRatings - ${quoteCommand}"

        // lifeQuoteCommand have age and quote id.
        List<LifeQuoteCommand> rateList = []

        rateList.addAll(lifeUnionRateService.getRates(quoteCommand))

        rateList
    }


    LifeQuoteCommand toLifeQuoteCommand(LifeQuote lifeQuote) {
        if (!lifeQuote.age) {
            def command = new DetailsCommand()
            command.dob = lifeQuote.dob
            lifeQuote.age = lifeUtilService.getAge(command)
        }
        LifeQuoteCommand lifeQuoteCommand = new LifeQuoteCommand(
            age: lifeQuote.age,
            salary: lifeQuote.salary,
            countryEnum: CountryEnum.findCountryById(lifeQuote.quoteCountryId)
        )

        lifeQuoteCommand
    }

    List<LifeRating> findApplicableRates(Integer age, LifeSalaryCategoryEnum salary, Integer providerId = null, productId = null) {

        def c = LifeRating.createCriteria()
        def list = c.list {
            product {
                provider {
                    if (providerId) {
                        eq 'id', providerId
                    }
                }
                if (productId) {
                    eq 'id', productId
                }

                eq 'active', true
            }

            if (salary == LifeSalaryCategoryEnum.NO_INCOME || salary == LifeSalaryCategoryEnum.LESS_THEN_5500) {
                eq 'cover', 250000 as long
            }
            if (salary == LifeSalaryCategoryEnum.FROM_5500_TO_7999) {
                or {
                    eq 'cover', 250000 as long
                    eq 'cover', 500000 as long
                }
            }
            if (salary == LifeSalaryCategoryEnum.FROM_8000_TO_104900) {
                or {
                    eq 'cover', 250000 as long
                    eq 'cover', 500000 as long
                    eq 'cover', 750000 as long
                }
            }

            eq 'age', age


            order('product', 'asc')
            order('cover', 'asc')
//            order('premium', 'asc')
        }

        list
    }


    /**
     * Verify that all data is available for payment
     *
     * @param lifeQuote
     * @return
     */
    boolean isEligibleForPayment(LifeQuote quote) {
        log.debug("lifeQuote.isEligibleForPayment - quote:${quote.id}")

//        boolean allDataAvailable = quote.policyPrice && quote.totalPrice &&  quote.totalPriceWithoutVat &&
//            (!quote.addonPrice || quote.addonPrice && quote.addonVat)
        //       (!quote.discount || quote.discount && quote.discountCode)

        boolean allDataAvailable = quote.policyPrice && quote.totalPrice && quote.totalPriceWithoutVat && (!quote.discount || quote.discount && quote.discountCode)

        log.info("pp:${quote.policyPrice}, ppvat:${quote.policyPriceVat}, " +
            "tp:${quote.totalPrice}, tpwovat:${quote.totalPriceWithoutVat}, " +
//            "add:${quote.addonPrice}, addVAT:${quote.addonVat}," +
            "discount:${quote.discount}, discountCode:${quote.discountCode?.id}")

//        BigDecimal totalPrice = quote.policyPrice.add(quote.addonPrice ?: 0)
//            .subtract(quote.discount ?: 0)
//            .add(quote.getTotalVAT())


        BigDecimal totalPrice = quote.policyPrice.add(quote.addonPrice ?: 0)
            .subtract(quote.discount ?: 0)

        if (!allDataAvailable || totalPrice != quote.totalPrice) {
            log.error("LifeQuote.isEligibleForPayment - quote:${quote.id}, prices not matched. " +
                "Total:${totalPrice}, quote.TotalPrice:${quote.totalPrice}, allDataAvailable:${allDataAvailable}")
            return false
        }

        return true
    }

    void setProductAndRate(LifeQuote lifeQuote, LifeRateCommand lifeRateCommand) {

        Product product = Product.read(lifeRateCommand.productId)
        lifeQuote.product = product
        lifeQuote.policyPrice = lifeRateCommand.totalPrice
        lifeQuote.policyActualPrice = lifeRateCommand.premium
        lifeQuote.policyPriceVat = lifeRateCommand.premiumVAT
        lifeQuote.addonVat = null
        lifeQuote.addonPrice = null
//        lifeQuote.policyReference = commonUtilService.generatePolicyRef(lifeQuote, product.provider)
        lifeQuote.discount = null
        lifeQuote.discountCode = null

        lifeQuote.rate = LifeRating.findById(lifeRateCommand.rateId)

        lifeQuote.totalPrice = lifeRateCommand.totalPrice
        lifeQuote.totalPriceWithoutVat = lifeQuote.totalPrice.subtract(lifeQuote.getTotalVAT())

        lifeQuote.save(flush: true)


    }


    @Transactional(readOnly = true)
    LifeRateCommand getRating(Long rateId, LifeQuoteCommand quoteCommand) {
        log.info "health.quote.service.getRating - ${rateId}"

        LifeRateCommand rateCommand = lifeUnionRateService.getRate(rateId, quoteCommand)
        rateCommand
    }

    def updateLifeQuoteRating(Long quoteId, Long productId, String discountCode,
                              PaymentStatusEnum status = PaymentStatusEnum.DRAFT, Boolean applyVAT = false) {
        applyVAT = false // life insurance is not eligible for vat
        log.info("heathQuote.updateHealthQuoteRating - entering with [, status:$status] ")

        LifeQuote lifeQuote = LifeQuote.get(quoteId)

        lifeQuote.product = Product.get(productId)
        LifeRateCommand rating = getRating(lifeQuote.rate.id, toLifeQuoteCommand(lifeQuote))


        lifeQuote.policyPrice = rating.originalPremium
//        lifeQuote.c4meFee = rating.originalC4meFee

        paymentService.changePaymentStatus(lifeQuote, status)

        BigDecimal discountAmount = 0
        def discount, discountCodeObj

        lifeQuote.discountCode = null

        if (discountCode) {
            try {
                CountryEnum countryEnum = CountryEnum.findCountryByIsoAlpha2Code(lifeQuote.quoteCountry.code)

                (discount, discountCodeObj) = checkoutService.getDiscount(discountCode,
                    rating.getTotalPremiumWithoutVAT(), ProductType.LIFE, lifeQuote.productId, countryEnum.code, lifeQuote.id)

                discountAmount = BigDecimal.valueOf(discount)
                lifeQuote.discountCode = discountCodeObj

            } catch (Exception codeExp) {
                log.error("lifeQuote.updateLifeQuoteRating- Error while getting discount", codeExp)
            }

        } else {

            CountryEnum countryEnum = CountryEnum.findCountryById(lifeQuote.quoteCountry.id)

            (discount, discountCodeObj) = lifeRateService.getDiscount(rating.getTotalPremiumWithoutVAT(), countryEnum, rating.providerId, lifeQuote.user.leadType, rating.productId, rating.originalPremium)

            lifeQuote.discountCode = discountCodeObj

            if (discount) {
                discountAmount = BigDecimal.valueOf(discount)
                lifeQuote.discountCode = discountCodeObj
            } else {
                lifeQuote.discountCode = null
                discountAmount = 0d
            }
        }

        rating = lifeRateService.applyDiscount(rating, discount, discountCodeObj)

        if (applyVAT && !Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))) {
            rating = lifeRateService.applyVAT(rating)
        }
        lifeQuote.policyPriceVat = rating.premiumVAT


        if (!lifeQuote.paymentMethod) {
            lifeQuote.paymentMethod = PaymentMethodEnum.CREDITCARD
        }
        lifeQuote.discount = discountAmount

//        lifeQuote.c4meFeeVat = rating.c4meFeeVAT

        BigDecimal totalVAT = lifeQuote.getTotalVAT()

        lifeQuote.totalPriceWithoutVat = lifeQuote.policyPrice.subtract(lifeQuote.discount)
        lifeQuote.totalPrice = lifeQuote.totalPriceWithoutVat.add(totalVAT)

        lifeQuote.policyReference = commonUtilService.generatePolicyRef(lifeQuote, lifeQuote.product.provider)
        lifeQuote.save()

        [lifeQuote, rating]
    }


    @Transactional(readOnly = true)
    def createLifeQuoteAddonPurchaseIfAny(quote) {

        if (quote instanceof CarQuote) {
            DetailsCommand detailsCmd = new DetailsCommand()
            detailsCmd.name = quote.name
            detailsCmd.dob = quote.dob
            detailsCmd.email = quote.email
            detailsCmd.mobile = quote.mobile
            detailsCmd.salaryEnum = LifeSalaryCategoryEnum.NO_INCOME
            detailsCmd.source = RequestSourceEnum.WEB_CAR.name()
            detailsCmd.countryEnum = CountryEnum.findCountryByCurrency(quote.currency)

            def lifeAddOnInCar = CarQuoteAddon.createCriteria().get() {
                eq 'carQuote', quote
                addonTranslation {
                    eq 'code', 'lifeInsuranceAddon'
                }
            }

            // it should process when once payment done for credit card or when cod is pending. add other condition if needed
            if (lifeAddOnInCar && (
                (quote.paymentMethod == PaymentMethodEnum.CREDITCARD && quote.paymentStatus == PaymentStatusEnum.PAID)
                    || (quote.paymentMethod == PaymentMethodEnum.COD && quote.paymentStatus == PaymentStatusEnum.PENDING))) {
                // process life quote as purchased follow normal process
                LifeQuote lifeQuote = createLifeQuote(detailsCmd)
                try {

                    Long rateId = lifeAddOnInCar.code.split('-')[1] as Long
                    LifeRating lifeRate = LifeRating.get(rateId)

                    //no vat as there is no vat on life insurance
                    //BigDecimal vatCharged = commonUtilService.getVATAmount(lifeAddOnInCar.price as BigDecimal)

                    Product product = lifeRate.product
                    lifeQuote.product = product
                    lifeQuote.policyPrice = lifeAddOnInCar.price
                    lifeQuote.policyActualPrice = lifeAddOnInCar.price
                    lifeQuote.policyPriceVat = 0.00
                    lifeQuote.rate = lifeRate
                    lifeQuote.totalPrice = lifeAddOnInCar.price
                    lifeQuote.totalPriceWithoutVat = lifeAddOnInCar.price

                    lifeQuote.save()

                    checkoutService.clearQuoteWithFromPaymentDetail(lifeQuote, ProductTypeEnum.LIFE)
                    // make it pending using service and follow normal process
                    (lifeQuote) = updateLifeQuoteRating(lifeQuote.id, lifeQuote.product.id, lifeQuote.discountCode ? lifeQuote.discountCode.toString() : null,
                        PaymentStatusEnum.PENDING, false)

                    lifeQuote.paymentMethod = quote.paymentMethod
                    lifeQuote.paidDate = quote.paidDate

                    paymentService.changePaymentStatus(lifeQuote, quote.paymentStatus, null)
                    lifeQuote.merchantRef = quote.merchantRef


                    lifeQuote.save()

                    log.debug("lifeQuote addon is processed. ${lifeQuote.id}")

                    lifeQuote
                } catch (Exception e) {
                    log.error "Error processing life Quote purchase as a addon for car insurance. $e"
                }
            } else {
                return
            }

        }
    }

    List<LifeRating> getMetLifeProducts() {
        return lifeMetLifeRateService.getRatings()
    }

}
