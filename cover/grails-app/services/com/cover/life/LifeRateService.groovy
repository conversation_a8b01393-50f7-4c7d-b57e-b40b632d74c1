package com.cover.life

import com.cover.car.commands.QuoteCommand
import com.cover.lifeInsuranceCommands.LifeQuoteCommand
import com.cover.lifeInsuranceCommands.LifeRateCommand
import com.safeguard.CountryEnum
import com.safeguard.LeadType
import com.safeguard.ProductTypeEnum
import com.safeguard.User
import com.safeguard.car.DiscountCode
import com.safeguard.life.LifeRating
import grails.transaction.Transactional

/**
 * life Rating generic service.
 * <AUTHOR>
 */
@Transactional
class LifeRateService {

    def commonUtilService
    def grailsApplication
    def lifeUtilService
    def discountService


    // move this to lifeRateService
    LifeRateCommand toLifeRateCommand(LifeRating rateList, LifeQuoteCommand lifeQuoteCommand = null){
        def command = new LifeRateCommand()
        command.with {
            rateId = rateList.id
            policyAndTerms = ""
            providerId = rateList.product.provider.id
            providerName = rateList.product.provider.name
            providerNameEn = rateList.product.provider.nameEn
            providerNameAr = rateList.product.provider.nameAr
            providerLogo = rateList.product.provider.logo
            productName = rateList.product.name
            productNameEn = rateList.product.nameEn
            productId = rateList.product.id

            originalPremium = rateList.premium
            premium = rateList.premium
            premiumVAT = 0.0 //need to set this premium vat
            cover = rateList.cover

            currency = lifeUtilService.getCurrency()

            discountCode = '0'  //need to set discounts
            discountAmount = 0.0  //need to set discounts
            discountPercent = 0.0 //need to set discounts

            totalPrice = rateList.premium // total price


        }

        // work on discount
        command = applyExtraDiscount(lifeQuoteCommand, command, lifeQuoteCommand.countryEnum)

        if (Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive')))  {
            command = applyVAT(command)
        }


        command
    }

    LifeRateCommand applyVAT(LifeRateCommand rateCommand) {
//        life insurance is not eligible fo vat
//        rateCommand.premiumVAT = commonUtilService.getVATAmount(rateCommand.originalPremium)
        rateCommand.premiumVAT = 0
//        rateCommand.premium = rateCommand.premium.add(rateCommand.premiumVAT)


        rateCommand.totalPrice = rateCommand.totalPrice.add(rateCommand.premiumVAT)

        rateCommand
    }

    /**
     * Apply Discount on the Life Rate Command
     * @param rateCommand
     * @return
     */
    LifeRateCommand applyExtraDiscount(LifeQuoteCommand quoteCommand, LifeRateCommand rateCommand, CountryEnum countryEnum) {

        // Get the Global discount
        //::TODO need to get user email to apply discount on life insurance
        def (discount, discountCodeObj) = getDiscount(rateCommand.premium, countryEnum, rateCommand.providerId, null, rateCommand.productId, rateCommand.originalPremium)

        //Apply discount onto the rate command
        rateCommand = applyDiscount(rateCommand, discount, discountCodeObj)

        return rateCommand
    }


    LifeRateCommand applyDiscount(LifeRateCommand rateCommand, BigDecimal discount, DiscountCode discountCodeObj) {

        if (discountCodeObj) {
            rateCommand.discountPercent = discountCodeObj.discount
            rateCommand.discountAmount = discount
            rateCommand.hasPercentDiscount = discountCodeObj.hasPercentDiscount

            rateCommand.premium = rateCommand.originalPremium.subtract(discount)
            rateCommand.totalPrice = rateCommand.originalPremium.subtract(discount)
        }

        return rateCommand
    }

    def getDiscount (double amount, CountryEnum countryEnum, Long providerId = null, LeadType leadType=null, Long productId = null, BigDecimal premium = null) {

        BigDecimal discount
        DiscountCode discountCodeObj

        //Is global discount enabled
//        if (Boolean.parseBoolean(grailsApplication.config.getProperty("cover.premium.home.discount.${countryEnum.code}.enabled"))) {
//
//            Long discountCodeId =
//                Long.parseLong(grailsApplication.config.getProperty("cover.premium.home.discount.${countryEnum.code}.id"))
//
//            //Use the discount code if available
//            discountCodeObj = discountCodeId ? DiscountCode.get(discountCodeId) : null
//
//            if(discountCodeObj) {
//                discount = discountCodeObj.discount
//
//                if (discountCodeObj.hasPercentDiscount) {
//                    discount = (discountCodeObj.discount * amount) / 100
//                }
//
//                discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP)
//            }
//        }

        discountCodeObj = discountService.getApplicableDiscountCode(
            countryEnum,
            ProductTypeEnum.LIFE.value(), // as its life insurance
            leadType,
            providerId as Integer,
            productId as Integer,
            premium as Integer
        )

        if(discountCodeObj) {
            discount = discountCodeObj.discount

            if (discountCodeObj.hasPercentDiscount) {
                discount = (discountCodeObj.discount * amount) / 100
            }

            discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        [discount, discountCodeObj]
    }

}
