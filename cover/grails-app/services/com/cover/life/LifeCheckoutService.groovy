package com.cover.life

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CreateQuotePaymentInfoCommand
import com.safeguard.InsuranceProvider
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.QuotePaymentInfo
import com.safeguard.exception.LifeApiException
import com.safeguard.life.ErrorMessages
import com.safeguard.life.LifeQuote
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.hibernate.exception.JDBCConnectionException
import org.springframework.context.i18n.LocaleContextHolder
import grails.converters.JSON
import com.safeguard.whitelabel.WhiteLabelDomain
import org.springframework.http.HttpStatus
import org.springframework.web.client.ResourceAccessException
import com.safeguard.CountryEnum

class LifeCheckoutService {

    def lifePaymentService
    def lifeQuoteService
    def grailsApplication
    def payfortService
    def checkoutService
    def paymentService
    def messageSource
    Locale locale = new Locale('en')

    def preparePaymentAuthorization(String policyNumber, String language, String returnUrl) {
        LifeQuote lifeQuote = lifePaymentService.findLifeQuote(policyNumber)

        if (!lifeQuoteService.isEligibleForPayment(lifeQuote)) {
            throw new LifeApiException(ErrorMessages.LIFE_QUOTE_NOT_ELIGIBLE, HttpStatus.BAD_REQUEST)
        }
        return createReturnedParams(lifeQuote.encodedrMerchantRef(), language, returnUrl, lifeQuote.version)
    }

    @Transactional(readOnly = true)
    private def createReturnedParams(String encodedMerchantRef, String language, String returnUrl, Long quoteVersion) {
        returnUrl = returnUrl + "?p_quote_v=${quoteVersion.toString()}"

        String countryCode = CountryEnum.UAE.code
        String merchantIdentifier = paymentService.getActiveMerchantId(countryCode)
        String accessCode = paymentService.getPayfortAccessCode(merchantIdentifier, countryCode)

        def params = [
            service_command    : grailsApplication.config.getProperty("payfort.serviceCommand"),
            merchant_identifier: merchantIdentifier,
            access_code        : accessCode,
            merchant_reference : encodedMerchantRef,
            language           : language,
            return_url         : returnUrl
        ]

        String sha = payfortService.createHash(params, paymentService.getPayfortRequestPhrase(merchantIdentifier, countryCode))
        params.signature = sha
        params.payfortPaymentUrl = grailsApplication.config.getProperty("payfort.paymentPage.url")
        return params
    }

    def handleReturnedAnnualPaymentAuthorization(def params, String customerIp, String source, String returnUrl3d) {
        log.info("entering with params:$params")
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)
        LifeQuote lifeQuote
        if (isSecured) {
            try{
                lifeQuote = LifeQuote.findById(LifeQuote.decodeMerchantRef(params.merchant_reference))
            }
            catch(JDBCConnectionException e){
                throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
            }
            if (PayfortStatusEnum.INVALID_REQUEST.toString().equals(params.status)) {
                throw new LifeApiException(ErrorMessages.INVALID_PAYMENT_REQUEST, HttpStatus.BAD_REQUEST)
                params.response_message
            }

            try {
                if (lifeQuote.isModified(params.p_quote_v)) {
                    throw new LifeApiException(ErrorMessages.QUOTE_MODIFIED_DURING_PAYMENT, HttpStatus.INTERNAL_SERVER_ERROR)
                }

                params.source = source
                params.return_url = returnUrl3d
                PayfortResponse payfortResponse
                try{
                    payfortResponse = paymentService.process(params, PayfortCommandEnum.AUTHORIZATION, customerIp)
                }
                catch(JDBCConnectionException | ResourceAccessException e){
                    throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
                }

                if (payfortResponse.isThreeDeeSecure) {
                    return [
                        result: 'success',
                        status: 'onhold',
                        three_dee_url: payfortResponse.threeDeeSecureUrl
                    ]
                } else if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(payfortResponse.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(payfortResponse.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    try{
                        paymentService.paid(params, customParams)
                    }
                    catch(JDBCConnectionException e){
                        throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
                    }
                    return [
                        result: 'success',
                        status: 'paid',
                    ]
                }
            } catch (Exception exp) {
                log.error("Exception:", exp)
                throw new LifeApiException(ErrorMessages.PAYMENT_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, e)
            }

            //pushover service sending sms in-case error to dev team
            String failureMessage = params.response_message +
                (params.acquirer_response_message ? " - " + params.acquirer_response_message : "")
            notify AsyncEventConstants.LIFE_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage, quoteId: lifeQuote.id]
            //payfortResponse.responseMessage
            throw new LifeApiException(ErrorMessages.LIFE_PUSHOVER_FAILED_TRANSACTION, HttpStatus.INTERNAL_SERVER_ERROR)
        } else {
            throw new LifeApiException(ErrorMessages.SECURITY_SIGNATURE_NOT_PASSED, HttpStatus.FORBIDDEN)
        }
        //TODO think about returning false here or request to start payment process once again
    }

    def handleThreeDeeAnnualPaymentAuthorization(def params) {
        log.info("entering with params ${params}")
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)
        LifeQuote quote
        if (/*isSecured*/true) {
            def lifeQuoteId = LifeQuote.decodeMerchantRef(params.merchant_reference)
            try{
                quote = LifeQuote.read(lifeQuoteId)
            }
            catch(JDBCConnectionException e){
                throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
            }

            if (quote && quote.isNotProcessed()) {
                paymentService.savePaymentResponse(quote, params)

                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    try{
                        paymentService.paid(params, customParams)
                        log.debug("in handleThreeDeeAnnualPaymentAuthorization: called paymentService.paid successully")
                    }
                    catch(JDBCConnectionException e){
                        throw new LifeApiException(ErrorMessages.INTERNAL_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, e)
                    }

                    return [
                        result: 'success',
                        status: 'authorized',
                    ]
                } else {
                    //pushover service sending sms in-case error to dev team
                    String failureMessage = params.response_message +
                        (params.acquirer_response_message ? " - " + params.acquirer_response_message : "")
                    notify AsyncEventConstants.LIFE_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage, quoteId: quote.id]
                    throw new LifeApiException(ErrorMessages.PAYMENT_FAILED, HttpStatus.INTERNAL_SERVER_ERROR)

                }
            } else {
                log.warn(".life.checkout.success quote is isProcessed -> ${quote?.id}")
                log.debug(".life.checkout.success.is.processed still redirecting to thankyou")
                return [
                    result: 'success',
                    status: 'authorized',
                ]
            }

        } else {
            throw new LifeApiException(ErrorMessages.SECURITY_SIGNATURE_NOT_PASSED, HttpStatus.FORBIDDEN)
        }
        //TODO think about returning false here or request to start payment process once again
    }

    /*
     * capture payment & issue policy
     */
    def capturePaymentAndIssuePolicy(LifeQuote quote) {
        BigDecimal amountToCapture = quote.totalPrice

        if (quote.paymentMethod == PaymentMethodEnum.CREDITCARD) {

            Map paramsMap = [merchant_reference: quote.merchantRef, lang: 'en', country: CountryEnum.UAE.code]

            CreateQuotePaymentInfoCommand createQuotePaymentInfoCommand = new CreateQuotePaymentInfoCommand(
                newPaymentStatus: PaymentStatusEnum.ISSUED, capturedAmount: amountToCapture,
                user: quote.user, remarks: null, chassisNumber: null,
                policyNo: quote.policyNo, policyActualPrice: quote.policyActualPrice, creditNote: null,
                actualPolicyStartDate: quote.policyStartDate, lastYearProvider: null,
                actualInsuredValue: null
            )
            //
            QuotePaymentInfo paymentInfo = paymentService.storeQuotePaymentInfo(createQuotePaymentInfoCommand, quote)
            paramsMap.paymentInfoUuid = paymentInfo.uuid
            // send capture payment request to Payfort
            log.info("Sending request to payfort. PaymentMethod:${quote.paymentMethod} CurrentStatus:${quote.paymentStatus}  Amount:${amountToCapture}")
            //
            PayfortCommandEnum payfortCommand = PayfortCommandEnum.CAPTURE
            PayfortResponse payfortResponse = paymentService.process(paramsMap, payfortCommand, null, amountToCapture)
            if (payfortResponse.status.equalsIgnoreCase(PayfortStatusEnum.CAPTURE_SUCCESS.toString())) {
                log.info("Payfort transaction successfully completed")

            }
            // upate quote
            quote.capturedAmount = amountToCapture
            quote.save(flush: true, failOnError: true)

            log.debug("capturedAmount is saved in LifeQuote successfully.")
        }

        //
        paymentService.changePaymentStatus(quote, PaymentStatusEnum.ISSUED, null, null, amountToCapture, null)

        log.debug("Quote payment status is changed to issued.")
    }
}
