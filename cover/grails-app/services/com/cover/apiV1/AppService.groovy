package com.cover.apiV1

import com.cover.api.AppDeviceCommand
import com.safeguard.User
import grails.transaction.Transactional
import org.joda.time.LocalDateTime
import safeguard.AppDevice

@Transactional
class AppService {

    /**
     * Store Device token
     * Create otherwise update existing device token with provided information
     *
     * @param command
     * @param user
     * @return
     */
    def storeDeviceToken(AppDeviceCommand command, User user) {
        log.info("app.storeDeviceToken - entering with [command:${command}, user:${user}]")

        AppDevice appDevice = AppDevice.findByDeviceToken(command.deviceToken)

        if (!appDevice) {
            appDevice = new AppDevice(deviceToken: command.deviceToken)
        } else {
            appDevice.lastUpdated = LocalDateTime.now()
        }

        if (!appDevice.user) {
            appDevice.user = user
        }

        appDevice.os = command.os
        appDevice.country = command.country
        appDevice.lang = command.lang
        appDevice.appVersion = command.appVersion

        appDevice.save(failOnError: true)

    }
}
