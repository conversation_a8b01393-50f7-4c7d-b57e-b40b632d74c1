package com.cover.apiV1

import com.cover.car.commands.RateCommand
import com.cover.health.commands.HealthCheckoutCommand
import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.cover.travel.TravelQuoteCommand
import com.cover.travel.TravelRateCommand
import com.safeguard.PaLead
import com.safeguard.PaymentStatusEnum
import com.safeguard.CarPcrStatusEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.User
import com.safeguard.car.CarPcr
import com.safeguard.car.CarQuote
import com.safeguard.general.GeneralQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.pet.PetQuote
import com.safeguard.travel.TravelQuote
import com.safeguard.util.AESCryption
import grails.transaction.Transactional
import org.joda.time.DateTime

@Transactional
class ApiQuoteService {

    def healthQuoteService
    def homeQuoteService
    def quoteService
    def commonQuoteService
    def petQuoteService
    def springSecurityService
    def travelQuoteService

    /**
     * This method accepts insuranceType as String and in case an invalid insurance type is provided a null will be
     * returned and the caller of this method will not have any chance to know whether the quote with the provided id
     * does not exist or the provided insuranceType does not exist.
     * Also this is a common method which can be used not only for the API flows hence it should be in a more general
     * class.
     *
     * TODO: delete this method when there is no any usage
     *
     * @deprecated use {@link com.cover.common.CommonQuoteService#getQuote(Long id, ProductTypeEnum productType)} instead.
     */
    @Deprecated
    def getQuoteById(Long quoteId, String insuranceType) {

        insuranceType = insuranceType.toLowerCase()
        def quote

        switch(insuranceType) {
            case ProductTypeEnum.HOME.toString().toLowerCase():
                quote = HomeQuote.get(quoteId)
                break
            case ProductTypeEnum.HEALTH.toString().toLowerCase():
                quote = HealthQuote.get(quoteId)
                break
            case ProductTypeEnum.TRAVEL.toString().toLowerCase():
                quote = TravelQuote.get(quoteId)
                break
            default:
                quote = CarQuote.get(quoteId)
                break
        }

        return quote

    }

    /**
     * Check eligibility of quote for Payment
     *
     * @param quote
     * @return
     */
    @Transactional(readOnly = true)
    def isEligibleForPayment(def quote) {
        if (!quote.productId) return false

        if (quote instanceof CarQuote) {
            return quoteService.isEligibleForPayment(quote)
        } else if (quote instanceof HealthQuote) {
            return healthQuoteService.isEligibleForPayment(quote)
        } else if (quote instanceof HomeQuote) {
            return homeQuoteService.isEligibleForPayment(quote)
        } else if (quote instanceof TravelQuote) {
            return travelQuoteService.isEligibleForPayment(quote)
        } else if (quote instanceof PetQuote) {
            return petQuoteService.isEligibleForPayment(quote)
        } else if (quote instanceof PaLead) {
            return true
        } else if (quote instanceof GeneralQuote) {
            return true
        }

        return false
    }

    /**
     * Update car quote.
     * Clear any AddOns, Premium and VAT. Update Product Id
     * Recalculate Premium, VAT and assign Addons
     *
     * @param quote
     * @param rateCommand
     * @param requestParams
     * @return
     */
    def updateCarQuote(CarQuote quote, RateCommand rateCommand, def requestParams) {
        log.info("apiQuote.updateCarQuote - entering with [quote:${quote.id}, rateCommand:$rateCommand, requestParams:$requestParams]")

        BigDecimal premium = rateCommand.premium
        //Update car quote with product, reset any vat, addons etc
        quoteService.updateCarQuote(quote, rateCommand, premium)

        //Update car quote with discount, addon, vat, status etc
        quoteService.updateCarQuote(requestParams)
    }

    /**
     * Update home Quote.
     *
     * Clear any AddOns, Premium and VAT, also update product Id
     * Recalculate premium, VAT, and assign Addons
     *
     * @param requestParams
     */
    def updateHomeQuote(def requestParams, Integer productId) {
        log.info("apiQuote.updateHomeQuote - entering with [requestParams:$requestParams, productId:$productId]")

        HomeQuote homeQuote = HomeQuote.get(requestParams.homeQuoteId)
        HomeQuoteCommand homeQuoteCommand = homeQuoteService.toHomeQuoteCommand(homeQuote)
        homeQuoteCommand.productId = productId

        HomeRateCommand rateCommand = homeQuoteService.getRating(homeQuoteCommand)

        homeQuoteService.setProduct(homeQuote, rateCommand)

        homeQuoteService.updateHomeQuote(requestParams)
    }

    /**
     * Update Travel Quote.
     *
     * Clear any AddOns, Premium and VAT, also update product Id
     * Recalculate premium, VAT, and assign Addons
     *
     * @param requestParams
     */
    def updateTravelQuote(Long quoteId, def requestParams) {
        log.info("apiQuote.updateTravelQuote - entering with [requestParams:$requestParams, productId:${requestParams.productId}]")

        TravelQuote travelQuote = TravelQuote.get(quoteId)
        TravelQuoteCommand travelQuoteCommand = travelQuoteService.toTravelQuoteCommand(travelQuote)
        travelQuoteCommand.productId = AESCryption.decrypt(requestParams.productId).toInteger()

        TravelRateCommand rateCommand = travelQuoteService.getRating(travelQuoteCommand)

        //travelQuoteService.setProduct(travelQuote, rateCommand)
        travelQuoteService.updateTravelQuote(travelQuote, requestParams, rateCommand)
    }

    /**
     * Update Health Quote
     *
     * Recalculate premium, VAT
     *
     * @return
     */
    def updateHealthQuote(HealthCheckoutCommand checkoutCommand, def params) {
        log.info("apiQuote.updateHealthQuote - entering with [checkoutCommand:$checkoutCommand]")

        healthQuoteService.updateHealthQuoteRating(checkoutCommand, params, PaymentStatusEnum.PENDING, true)
    }

    /**
     * Handles policy cancellation request
     *
     * @param productType - product type(CAR, HEALTH, ...)
     * @param quote - the quote
     * @param country - country of the application deployment
     * @param lang - language of user preference
     * @throws RuntimeException if the quote doesn't belong to the user who has requested for a cancellation
     */
    void handlePolicyCancellationRequest(ProductTypeEnum productType, def quote, String country, String lang) {
        User currentUser = springSecurityService.currentUser
        if (quote.user.id != currentUser.id) {
            String errMsg = "Unable to handle policy cancellation request as quote with id = $quote.id doesn't belong " +
                "to user with id = $currentUser.id who has requested the cancellation"
            log.error(errMsg)
            throw new RuntimeException(errMsg)
        }

        def cancellationRequest

        if (productType == ProductTypeEnum.CAR) {
            cancellationRequest = CarPcr.findByQuoteAndStatusId(quote, CarPcrStatusEnum.DRAFT.id)
            if (!cancellationRequest) {
                cancellationRequest = new CarPcr(
                    uuid: UUID.randomUUID().toString(),
                    quote: quote,
                    statusUpdatedDate: DateTime.now(),
                    statusId: CarPcrStatusEnum.DRAFT.id
                )
                cancellationRequest.save(failOnError: true)
            }
        } else {
            throw new RuntimeException("${productType.toString()} Policy Cancellation is not supported at this moment")
        }

        commonQuoteService.confirmPolicyCancellation(cancellationRequest, productType, country, lang)
    }
}
