package com.cover.apiV1

import com.safeguard.ClosedLeadsReasonEnum
import com.safeguard.LeadStatusEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.User
import com.safeguard.UserDetails
import com.safeguard.autodialer.CallDispositionLog
import com.safeguard.crm.StatusReason
import grails.transaction.Transactional
import grails.web.servlet.mvc.GrailsParameterMap
import org.joda.time.DateTimeConstants
import org.joda.time.LocalDateTime
import org.joda.time.Minutes

@Transactional
class CallService {

    /**
     * Saves call disposition details
     * @param parameterMap
     */
    void saveCallDisposition(GrailsParameterMap parameterMap) {

        CallDispositionLog callDispositionLog = new CallDispositionLog()

        User user = User.get(parameterMap.long("leadid"))

        callDispositionLog.responseParams = parameterMap.toString()
        callDispositionLog.lead = user
        callDispositionLog.agentEmail = parameterMap.userId == "null" ? null : parameterMap.userId
        callDispositionLog.agent = User.findByEmail(callDispositionLog.agentEmail)
        callDispositionLog.phone = parameterMap.phone
        callDispositionLog.callStatus = parameterMap.systemDisposition
        callDispositionLog.save()
        log.info("callService.saveCallDisposition - handling call disposition from autodialer - callDispositionLog saved for ${callDispositionLog.phone} for lead ${parameterMap.leadid}, callDispositionLog id: ${callDispositionLog.id}")

        String status = parameterMap.systemDisposition
        if (user) {
            switch (parameterMap.systemDisposition) {
                case "CONNECTED":
                    if (callDispositionLog.agent) {
                        user.salesPerson = callDispositionLog.agent
                        user.leadAssignmentDate = LocalDateTime.now()
                        log.info("callService.saveCallDisposition - handling call disposition from autodialer - salesperson of lead ${user.email} changed to ${callDispositionLog.agent.email}")
                        user.save()
                    }
                    break
                case "PROVIDER_TEMP_FAILURE":
                    log.info("callService.saveCallDisposition - handling call disposition from autodialer - received PROVIDER_TEMP_FAILURE for user ${user.email} as number is invalid, phone: ${callDispositionLog.phone}")
                    UserDetails userDetails = UserDetails.findOrCreateByUser(user)
                    user.changeLeadStatus(LeadStatusEnum.CLOSED_LOST, user.leadType, 'car')
                    user.save()

                    userDetails.carStatusReason = StatusReason.get(ClosedLeadsReasonEnum.WRONG_NO_EMAIL.id)
                    userDetails.numberOfCallAttempts = 0
                    userDetails.save()
                    log.info("callService.saveCallDisposition - handling call disposition from autodialer - Closing lead due to invalid no., id: ${user.id} ; email: ${user.email}; mobile: ${user.mobile}, callDispositionLog : ${callDispositionLog.id}")
                    break
                case "ATTEMPT_FAILED":
                case "FAILED":
                case "CALL_DROP":
                default:
                    UserDetails userDetails = UserDetails.findOrCreateByUser(user)
                    if (userDetails.numberOfCallAttempts >= 9) { // too many failed status for this number, close the lead
                        user.changeLeadStatus(LeadStatusEnum.CLOSED_LOST, user.leadType, 'car')
                        user.save()

                        Integer numberOfCallAttempts = userDetails.numberOfCallAttempts
                        userDetails.carStatusReason = StatusReason.get(ClosedLeadsReasonEnum.NO_ANSWER.id)
                        userDetails.numberOfCallAttempts = 0
                        userDetails.save()
                        log.info("callService.saveCallDisposition - handling call disposition from autodialer - Closing lead as there ${numberOfCallAttempts + 1} $status calls for this lead, id: ${user.id} ; email: ${user.email}; mobile: ${user.mobile}, callDispositionLog : ${callDispositionLog.id}")
                    } else {
                        userDetails.numberOfCallAttempts = userDetails.numberOfCallAttempts ? userDetails.numberOfCallAttempts + 1 : 1
                        userDetails.save()
                        log.info("callService.saveCallDisposition - handling call disposition from autodialer - received call disposition with status $status, retry attempt: ${userDetails.numberOfCallAttempts} , user ${user.email}, phone: ${callDispositionLog.phone}, callDispositionLog id: ${callDispositionLog.id}")
                    }
                    break
            }
        }
    }

    /**
     * Check if new callback is before 6 PM. If new callback is after, then change it to the next day.
     * @param callbackTime
     * @return
     */
    private LocalDateTime adjustTime(LocalDateTime callbackTime) {
        if (callbackTime.hourOfDay >= 18) {

            Integer difference = Minutes.minutesBetween(callbackTime.withTime(18, 0, 0, 0), callbackTime).minutes
            callbackTime = callbackTime.plusDays(1)
            if (callbackTime.dayOfWeek == DateTimeConstants.SATURDAY || callbackTime.dayOfWeek == DateTimeConstants.FRIDAY) {
                callbackTime = callbackTime.withDayOfWeek(DateTimeConstants.SUNDAY)
            }
            callbackTime = callbackTime.withTime(9, 0, 0, 0)
            callbackTime = callbackTime.plusMinutes(difference)

        }
        callbackTime
    }
}
