package com.cover.home

import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.safeguard.home.HomeBuildingRate
import com.safeguard.home.HomeContentRate
import com.safeguard.home.HomePersonalBelonging
import com.safeguard.home.HomePersonalBelongingRate
import com.safeguard.home.HomeProductCover
import grails.transaction.Transactional

import java.math.RoundingMode

/**
 * Home Rating service for Oman Insurance.
 * <AUTHOR>
 */
@Transactional
class HomeQatarRateService extends HomeRateService {

    public static final Integer PROVIDER_ID = 3

    @Transactional(readOnly = true)
    List<HomeRateCommand> getRates(HomeQuoteCommand quoteCommand) {

        List<HomeProductCover> covers = HomeProductCover.findAllByProviderId(PROVIDER_ID).list()
        List<HomeRateCommand> rateList = []

        BigDecimal buildingPremium = 0
        BigDecimal belongingsPremium = 0
        BigDecimal belongignsCover = quoteCommand.belongingsValue
        BigDecimal contentPremium = 0

        if (quoteCommand.buildingValue) {

            def buildingRates = HomeBuildingRate.findByBuildingValue(PROVIDER_ID, quoteCommand.buildingValue).list()

            if (buildingRates && buildingRates.size()) {
                HomeBuildingRate buildingRate = buildingRates.first()
                BigDecimal bPrem = quoteCommand.buildingValue * buildingRate.baseRate
                buildingPremium = bPrem < buildingRate.minPremium ? buildingRate.minPremium : bPrem
            }
        }

        for (cover in covers) {
            cover.buildingValue = quoteCommand.buildingValue
            cover.contentsValue = quoteCommand.contentsValue
            cover.belongingsValue = quoteCommand.belongingsValue
            // 20% of contents value
            cover.contentsTempRemoved = quoteCommand.contentsValue ? quoteCommand.contentsValue * (0.20) : null
            //20% of building value
            cover.lossOfRent = quoteCommand.buildingValue ? quoteCommand.buildingValue * (0.20) : null
            cover.belongingsValue = belongignsCover

            if (quoteCommand.belongingsValue) {

                HomePersonalBelongingRate belongingRate = HomePersonalBelongingRate
                    .findByProductAndValueFromLessThanEqualsAndValueToGreaterThanEquals(cover.product,
                    quoteCommand.belongingsValue, quoteCommand.belongingsValue)

                belongingsPremium = belongingRate ? belongingRate.premium : null
            }

            //content base rate doesn't apply on building only type
            if (quoteCommand.contentsValue) {
                HomeContentRate contentRate = HomeContentRate
                    .findByProductAndValueFromLessThanEqualsAndValueToGreaterThanEquals(cover.product,
                                                    quoteCommand.contentsValue, quoteCommand.contentsValue)

                contentPremium = contentRate ? contentRate.minPremium : null

                if (contentPremium && belongingsPremium &&
                    (!quoteCommand.buildingValue || (quoteCommand.buildingValue && buildingPremium))) {
                    cover.premium = contentPremium + buildingPremium + belongingsPremium
                } else {
                    //We cannot quote if content and belonging premium not found
                    cover.premium = null
                }

            } else {
                if (buildingPremium) {
                    cover.premium = buildingPremium
                } else {
                    cover.premium = null
                }
            }

            if (cover.premium) {
                //Add rate only when premium is available
                cover.premium = cover.premium.setScale(0, RoundingMode.CEILING)
                HomeRateCommand rateCommand = populateRatings(cover, quoteCommand)

                rateList.add(rateCommand)
            }

        }
        rateList
    }
}
