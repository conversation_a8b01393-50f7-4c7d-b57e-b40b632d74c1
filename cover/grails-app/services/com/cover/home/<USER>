package com.cover.home

import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.safeguard.Product
import com.safeguard.home.HomeContentRate
import com.safeguard.home.HomePersonalBelongingRate
import com.safeguard.home.HomeProductCover
import grails.transaction.Transactional

import java.math.RoundingMode

/**
 * Home Rating service for Watania Insurance.
 * <AUTHOR>
 */
@Transactional
class HomeWataniaRateService extends HomeRateService {

    public static final Integer PROVIDER_ID = 6
    public static final Integer PRODUCT_ID = 87

    @Transactional(readOnly = true)
    List<HomeRateCommand> getRates(HomeQuoteCommand quoteCommand) {

        List<HomeRateCommand> rateList = []

        boolean isEligible = checkEligiblity(quoteCommand)

        if (!isEligible) return rateList

        List<HomeProductCover> covers = HomeProductCover.findAllByProviderId(PROVIDER_ID).list()

        for (cover in covers) {
            BigDecimal buildingPremium = null
            BigDecimal belongingsPremium = null
            BigDecimal jewelMetalsPremium = null
            BigDecimal contentPremium = null
            BigDecimal optionalPremiums = 0
            cover.premium = 0

            cover.buildingValue = quoteCommand.buildingValue
            cover.contentsValue = quoteCommand.contentsValue
            cover.belongingsValue = quoteCommand.belongingsValue

            if (quoteCommand.contentsValue) {
                // 0.02% Premium for Home Contents
                contentPremium = quoteCommand.contentsValue * 0.0002

                // 1% premium applied on Personal Belonging Value Items - Optional
                belongingsPremium = quoteCommand.belongingsValue * 0.01
                jewelMetalsPremium = cover.jewellery * 0.01 //Fixing Jewel & Metal Insured value to 15,000

                optionalPremiums = optionalPremiums.add(belongingsPremium).add(jewelMetalsPremium)

                // Calculate loss of Rent & Premium
                if (!quoteCommand.buildingValue) {
                    // Alternative Accomodation / Loss of Rent
                    BigDecimal lossOfRentPremium = cover.lossOfRent * 0.0002 // 0.02% Premium for Loss of Rent
                    contentPremium = contentPremium.add(lossOfRentPremium)
                }
                log.info("homeWataniaRate.getRates - contentPremium:${contentPremium}")
                cover.premium = contentPremium
            }

            if (quoteCommand.buildingValue) {
                // 0.02% Premium for Building
                buildingPremium = quoteCommand.buildingValue * 0.0002
                log.info("homeWataniaRate.getRates - buildingPremium:${buildingPremium}")

                // 0.02% Premium for Loss of Rent
                BigDecimal lossOfRentPremium = cover.lossOfRent * 0.0002
                log.info("homeWataniaRate.getRates - lossOfRentPremium:${lossOfRentPremium}")
                buildingPremium = buildingPremium.add(lossOfRentPremium)

                /*if (!quoteCommand.contentsValue) {
                    cover.lossOfRent = null
                }*/
                cover.premium = cover.premium.add(buildingPremium)
            }

            if (cover.premium) {
                log.info("homeWataniaRate.getRates - cover.premium:${cover.premium}")
                // Add PA for Insurer and Spouse - Mandatory
                cover.premium = cover.premium.add(50)

                // Add T.P Cover (Tenant Liablity to Third Party)
                cover.premium = cover.premium.add(150)
                log.info("homeWataniaRate.getRates - before min cover.premium:${cover.premium}")
                cover = checkMinimumPremium(cover)
                log.info("homeWataniaRate.getRates - after min cover.premium:${cover.premium} with optionalPremiums:${optionalPremiums}")
                cover.premium = cover.premium.add(optionalPremiums)
                cover.premium = cover.premium.setScale(0, RoundingMode.CEILING)
                log.info("homeWataniaRate.getRates - final cover.premium:${cover.premium}")
                cover = applyExcess(cover)

                HomeRateCommand rateCommand = populateRatings(cover, quoteCommand)

                rateList.add(rateCommand)
            }
        }

        rateList
    }

    HomeProductCover checkMinimumPremium(HomeProductCover cover) {

        //Check minimum permium on mandatory Premiums
        if (cover.premium < 400) {
            cover.premium = 400
        }

        return cover
    }

    HomeProductCover applyExcess(HomeProductCover cover) {
        if (cover.buildingValue) {
            cover.excess = "2500"
        }
        return cover
    }

    boolean checkEligiblity(HomeQuoteCommand quoteCommand) {
        boolean isEligible = true

        //Building value more than 5 Million is not covered
        if (quoteCommand.buildingValue && quoteCommand.buildingValue > 5000000) {
            isEligible =  false
        }

        return isEligible
    }

}
