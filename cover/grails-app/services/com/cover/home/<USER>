package com.cover.home

import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.safeguard.home.HomeProductCover
import grails.transaction.Transactional

/**
 * Home Rating service for Sukoon Insurance.
 * old name was OMAN
 * <AUTHOR>
 */
@Transactional
class HomeSukoonRateService extends HomeRateService {

    public static final Integer PROVIDER_ID = 2

    @Transactional(readOnly = true)
    List<HomeRateCommand> getRates(HomeQuoteCommand quoteCommand) {

        List covers = HomeProductCover.
            findApplicableCovers(PROVIDER_ID, quoteCommand.contentsValue,
                quoteCommand.belongingsValue, quoteCommand.buildingValue).list()

        List<HomeRateCommand> rateList = []
        for (cover in covers) {
            HomeRateCommand rateCommand = populateRatings(cover, quoteCommand)

            rateList.add(rateCommand)
        }
        rateList
    }
}
