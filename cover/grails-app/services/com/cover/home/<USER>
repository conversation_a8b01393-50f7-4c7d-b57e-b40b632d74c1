package com.cover.home


import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.cover.homeInsuranceCommands.CustomerDetailsCommand
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.DonationTypeEnum
import com.safeguard.LeadType
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.ProductType
import com.safeguard.ProductTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.User
import com.safeguard.car.Addon
import com.safeguard.car.CarQuoteAddon
import com.safeguard.car.translation.AddonTranslation
import com.safeguard.home.HomeQuote
import grails.gorm.DetachedCriteria
import grails.transaction.Transactional
import org.joda.time.LocalDate

import java.math.RoundingMode

/**
 * Home quote service.
 * <AUTHOR>
 */
@Transactional
class HomeQuoteService {

    def grailsApplication
    def homeRateService
    def homeUnionRateService
    def homeSukoonRateService
    def homeQatarRateService
    def homeWataniaRateService
    def homeNoorRateService
    def utilService
    def commonUtilService
    def checkoutService
    def paymentService
    def crmService
    def leadSgService
    def commonPolicySgService

    @Transactional(readOnly = true)
    List<HomeRateCommand> getRatings(HomeQuoteCommand quoteCommand) {
        log.info "home.quote.service.getRatings - ${quoteCommand}"
        List<HomeRateCommand> rateList = []

        rateList.addAll(homeUnionRateService.getRates(quoteCommand))
        rateList.addAll(homeSukoonRateService.getRates(quoteCommand))
        rateList.addAll(homeQatarRateService.getRates(quoteCommand))
        rateList.addAll(homeWataniaRateService.getRates(quoteCommand))
        rateList.addAll(homeNoorRateService.getRates(quoteCommand))

        rateList
    }

    HomeRateCommand getRating(HomeQuoteCommand quoteCommand) {

        List<HomeRateCommand> rateList = getRatings(quoteCommand)

        rateList.find { it.productId == quoteCommand.productId}
    }

    HomeQuote createHomeQuote (CustomerDetailsCommand command) {
        HomeQuote homeQuote = new HomeQuote()
        /*homeQuote.homeInsuranceCategory = HomeInsuranceCategory.read(command.homeInsuranceCategoryId)
        homeQuote.homeContent = HomeContent.read(command.homeContentId)
        homeQuote.homePersonalBelonging = HomePersonalBelonging.read(command.homePersonalBelongingId)*/
        homeQuote.name = command.name
        homeQuote.mobile = command.mobile
        homeQuote.email = command.email
        //homeQuote.buildingValue = command.buildingValue
        homeQuote.currency = "AED"
        homeQuote.policyStartDate = new LocalDate(command.policyStartDate)
        homeQuote.lang = utilService.getLanguage()
        homeQuote.communicationOptIn = command.communicationOptIn
        homeQuote.queryString = command.marketingTracking.queryString
        homeQuote.utmSource = command.marketingTracking.utmSource
        homeQuote.utmMedium = command.marketingTracking.utmMedium
        homeQuote.utmCampaign = command.marketingTracking.utmCampaign
        homeQuote.gclid = command.marketingTracking.gclid
        homeQuote.fbclid = command.marketingTracking.fbclid

        User user = User.findByEmail(command.email)
        if (!user) {
            //Create new user if user is not found in db
            user = new User(email: command.email, password: 'homequote', homeLeadType: LeadType.NORMAL)
        }

        Country country = Country.get(command.countryEnum.country.id)
        homeQuote.quoteCountry = country

        if (!user.enabled) {
            user.name = command.name
            user.country = country
        }

        if (!user.userDetails || !user.userDetails.mobileVerified) {
            user.mobile = command.mobile
        }


        if (!user.homeLeadType) {
            user.homeLeadType = LeadType.NORMAL
        }
        user.save(flush: true)

        RequestSourceEnum requestSourceEnum = RequestSourceEnum.findRequestSource(command.source) ?: RequestSourceEnum.WEB

        crmService.handleCrmEvents(user.id, 'home')

        homeQuote.user = user
        homeQuote.requestSource = requestSourceEnum

        paymentService.changePaymentStatus(homeQuote, PaymentStatusEnum.DRAFT)

        homeQuote.save(flush:true, failOnError:true)
        leadSgService.createLeadAndComparison(homeQuote)

        homeQuote
    }

    /**
     * Updates HomeQuote with addons and price
     */
    HomeQuote updateHomeQuote(params) {
        HomeQuote homeQuote = HomeQuote.findById(params.homeQuoteId)
        def domesticHelperCover = params.domesticHelper
        def homeAssistCover = params.homeAssist
        def airportDropOffCover = params.airportDropOff
        def discountCode = params.discountCode
        def donationAmount = params.donationBox
        def smileSaverDentalCard = params.smileSaverDentalCard

        BigDecimal addonPrice = 0.0
        BigDecimal discountAmount = 0

        List quoteAddons = []

        if (params.domesticHelper) {
            AddonTranslation addonTranslation = AddonTranslation.findById(domesticHelperCover)
            quoteAddons.add(new CarQuoteAddon(homeQuote: homeQuote, addonTranslation:addonTranslation,
                price:addonTranslation.price, code:addonTranslation.code))
        }
        if (homeAssistCover) {
            AddonTranslation addonTranslation = AddonTranslation.findById(homeAssistCover)
            quoteAddons.add(new CarQuoteAddon(homeQuote: homeQuote, addonTranslation:addonTranslation,
                price:addonTranslation.price, code:addonTranslation.code))
        }
        if (airportDropOffCover) {
            AddonTranslation addonTranslation = AddonTranslation.findById(airportDropOffCover)
            quoteAddons.add(new CarQuoteAddon(homeQuote: homeQuote, addonTranslation:addonTranslation,
                price:addonTranslation.price, code:addonTranslation.code))
        }

        if (smileSaverDentalCard) {
            def smileSaverDentalCardAddon = AddonTranslation.findById(smileSaverDentalCard)

            quoteAddons.add(new CarQuoteAddon(homeQuote: homeQuote, addonTranslation: smileSaverDentalCardAddon,
                price: smileSaverDentalCardAddon.price, code: smileSaverDentalCardAddon.code))
        }

        // Update donation information
        commonPolicySgService.updateDonation(homeQuote, ProductTypeEnum.HOME, params.donationBox, DonationTypeEnum.CHARITY)

        HomeRateCommand rateCommand = getRating(toHomeQuoteCommand(homeQuote))

        homeQuote.discountCode = null
        homeQuote.discount = 0

        def discount
        def discountCodeObj

        if (discountCode) {
            try {

                CountryEnum countryEnum = CountryEnum.findCountryByIsoAlpha2Code(homeQuote.quoteCountry.code)

                (discount, discountCodeObj) = checkoutService.getDiscount(discountCode,
                    rateCommand.getTotalPremiumWithoutVAT(), ProductType.HOME, homeQuote.productId, countryEnum.code, homeQuote.id)

                discountAmount = BigDecimal.valueOf(discount)

                homeQuote.discountCode = discountCodeObj
                homeQuote.discount = discountAmount

            } catch (Exception codeExp) {
                log.error(".QuoteService.saveAddon Error while getting discount", codeExp)
            }

        } else {

            CountryEnum countryEnum = CountryEnum.findCountryById(homeQuote.quoteCountry.id)

            (discount, discountCodeObj) = homeRateService.getDiscount(rateCommand.getTotalPremiumWithoutVAT(), countryEnum, rateCommand.providerId, rateCommand.productId, homeQuote.user.homeLeadType)

            if (discount) {
                discountAmount = BigDecimal.valueOf(discount)
                homeQuote.discountCode = discountCodeObj
                homeQuote.discount = discountAmount
            } else {
                homeQuote.discountCode = null
                homeQuote.discount = 0d
            }
        }

        rateCommand = homeRateService.applyDiscount(rateCommand, discount, discountCodeObj)

        rateCommand = homeRateService.applyVAT(rateCommand)

        clearAddons(homeQuote)

        //Save all the addons
        quoteAddons*.save()

        //Get price of addons
        quoteAddons.collect { addonPrice += it.price}

        homeQuote.policyPriceVat = rateCommand.premiumVAT

        //Set addon price
        homeQuote.addonPrice = addonPrice
        homeQuote.addonVat = commonUtilService.getVATAmount(homeQuote.addonPrice)

        //Set payment status to pending
        paymentService.changePaymentStatus(homeQuote, PaymentStatusEnum.PENDING)

        //adding premium of policy issued
        homeQuote.policyPrice = rateCommand.getTotalPremiumWithoutVAT()

        //Calculate price again add addon price and minus discount
        homeQuote.totalPriceWithoutVat = homeQuote.policyPrice.subtract(discountAmount).add(homeQuote.addonPrice)

        homeQuote.totalPrice = homeQuote.totalPriceWithoutVat.add(homeQuote.policyPriceVat).add(homeQuote.addonVat)

        homeQuote.save(flush:true)

        homeQuote
    }

    def clearAddons(HomeQuote homeQuote) {
        CarQuoteAddon.executeUpdate("delete CarQuoteAddon a where a.homeQuote.id = :id", [id:homeQuote.id])
    }

    HomeQuoteCommand toHomeQuoteCommand(HomeQuote homeQuote) {

        HomeQuoteCommand homeQuoteCommand = new HomeQuoteCommand(
            quoteTypeId: homeQuote.homeInsuranceCategoryId,
            contentsValue:homeQuote.homeContent?.value,
            belongingsValue: homeQuote.homePersonalBelonging?.value,
            buildingValue: homeQuote.buildingValue,
            productId: homeQuote.productId,
            countryEnum: utilService.getCountryByIdOrCurrency(homeQuote.quoteCountryId, homeQuote.currency)
        )

        homeQuoteCommand
    }

    void setProduct(HomeQuote homeQuote, HomeRateCommand homeRateCommand) {

        Product product = Product.read(homeRateCommand.productId)
        homeQuote.product = product
        homeQuote.policyPrice = homeRateCommand.premium.subtract(homeRateCommand.premiumVAT ?: 0)
        homeQuote.policyPriceVat = homeRateCommand.premiumVAT
        homeQuote.addonVat = null
        homeQuote.addonPrice = null
        homeQuote.excess = homeRateCommand.excess
        homeQuote.policyReference = commonUtilService.generatePolicyRef(homeQuote, product.provider)
        homeQuote.discount = null
        homeQuote.discountCode = null

        homeQuote.totalPrice = homeQuote.policyPrice.add(homeQuote.getTotalVAT())
        homeQuote.totalPriceWithoutVat = homeQuote.totalPrice.subtract(homeQuote.getTotalVAT())

        homeQuote.save(flush:true)
    }

    /**
     * Verify that all data is available for payment
     *
     * @param HomeQuote
     * @return
     */
    boolean isEligibleForPayment(HomeQuote quote) {
        log.debug("homeQuote.isEligibleForPayment - quote:${quote.id}")

        boolean allDataAvailable = quote.policyPrice && quote.policyPriceVat &&
            quote.totalPrice &&  quote.totalPriceWithoutVat &&
            (!quote.addonPrice || quote.addonPrice && quote.addonVat)
            (!quote.discount || quote.discount && quote.discountCode)

        log.info("pp:${quote.policyPrice}, ppvat:${quote.policyPriceVat}, " +
            "tp:${quote.totalPrice}, tpwovat:${quote.totalPriceWithoutVat}, " +
            "add:${quote.addonPrice}, addVAT:${quote.addonVat}," +
            "discount:${quote.discount}, discountCode:${quote.discountCode?.id}")

        BigDecimal totalPrice = quote.policyPrice.add(quote.addonPrice ?: 0)
            .subtract(quote.discount ?: 0)
            .add(quote.getTotalVAT())

        if (!allDataAvailable || totalPrice != quote.totalPrice) {
            log.error("homeQuote.isEligibleForPayment - quote:${quote.id}, prices not matched. " +
                "Total:${totalPrice}, quote.TotalPrice:${quote.totalPrice}, allDataAvailable:${allDataAvailable}")
            return false
        }

        return true
    }

    def getAddons(HomeRateCommand rateCommand, boolean domesticHelperSelected) {
        Product product = Product.read(rateCommand.productId)

        Set checkBoxlist = []

        DetachedCriteria criteria = Addon.where {
            active == true && showForHealth == true && (product == null || product == product)
        }
        List addons = criteria.list(sort:"sortOrder")

        addons.each { addon ->
            AddonTranslation translation = AddonTranslation.findByAddonAndLocale(addon, 'en')
            boolean selected = false
            if (translation.code == "domesticHelper") {
                selected = domesticHelperSelected
            }

            checkBoxlist << [label: translation.label, description: translation.description, name: translation.code,
                             value: translation.price, valueText: translation.priceText, id: translation.id, selected: selected]
        }

        if (Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))) {
            checkBoxlist = applyVAT(checkBoxlist)

        }

        return checkBoxlist
    }


    def applyVAT(def items) {
        items.each { item ->
            item.originalValue = item.value
            item.value = commonUtilService.addVAT(item.value)
            item.value = new BigDecimal(item.value).setScale(2, RoundingMode.CEILING)
        }

        items
    }

}
