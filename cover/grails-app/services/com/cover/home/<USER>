package com.cover.home

import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.safeguard.CountryEnum
import com.safeguard.LeadType
import com.safeguard.ProductTypeEnum
import com.safeguard.car.DiscountCode
import com.safeguard.home.HomeCategoryTypeEnum
import com.safeguard.home.HomeProductCover
import grails.transaction.Transactional
import org.joda.time.LocalDate

/**
 * Home Rating generic service.
 * <AUTHOR>
 */
@Transactional
class HomeRateService {

    def commonUtilService
    def grailsApplication
    def homeQuoteService
    def discountService

    HomeRateCommand populateRatings(HomeProductCover cover, HomeQuoteCommand quoteCommand) {
        HomeRateCommand rateCommand = new HomeRateCommand()
        rateCommand.with {
            if (quoteCommand.quoteTypeId != HomeCategoryTypeEnum.BUILDING_ONLY.value()) {
                excess = cover.excess
                contentsValue = cover.contentsValue
                belongingsValue = cover.belongingsValue
                personalLiability = cover.personalLiability
                secondMedicalOpinion = cover.secondMedicalOption
                contentsInTheOpen = cover.contentsInTheOpen
                domesticHelper = cover.domesticHelper
                contentsTempRemoved = cover.contentsTempRemoved
                lossOfRent = cover.lossOfRent
                visitorsPersonalEffects = cover.visitorsPersonalEffects
                lossTheftMoney = cover.lossTheftMoney
                replacementOfLocks = cover.replacementOfLocks
                spoilageOfFood = cover.spoilageOfFood
                lossOfDocuments = cover.lossOfDocuments
                fatalInjury = cover.fatalInjury
                jewellery = cover.jewellery
                tenantLiability = cover.tenantLiability
            }
            policyAndTerms = cover.policyAndTerms
            providerId = cover.product.provider.id
            providerName = cover.product.provider.name
            providerNameEn = cover.product.provider.nameEn
            providerLogo = cover.product.provider.logo
            productName = cover.product.name
            productNameEn = cover.product.nameEn
            productId = cover.productId
            buildingValue = cover.buildingValue
            premium = cover.premium
        }

        rateCommand = storeOriginalPremiumBeforeC4meFee(rateCommand)

        rateCommand = applyExtraDiscount(rateCommand, quoteCommand.countryEnum)

        if (Boolean.parseBoolean(grailsApplication.config.getProperty('prices.vat.inclusive'))) {
            rateCommand = applyVAT(rateCommand)
        }

        rateCommand
    }

    HomeRateCommand applyVAT(HomeRateCommand rateCommand) {
        rateCommand.premiumVAT = commonUtilService.getVATAmount(rateCommand.originalPremium)
        rateCommand.premium = rateCommand.premium.add(rateCommand.premiumVAT)
        rateCommand.domesticHelper = commonUtilService.addVAT(rateCommand.domesticHelper)

        rateCommand
    }

    /**
     *
     * @param rateCommand
     * @return
     */
    HomeRateCommand storeOriginalPremiumBeforeC4meFee(HomeRateCommand rateCommand) {
        rateCommand.originalPremium = rateCommand.premium
        rateCommand
    }

    /**
     * Apply Discount on the Health Rate Command
     * @param rateCommand
     * @return
     */
    HomeRateCommand applyExtraDiscount(HomeRateCommand rateCommand, CountryEnum countryEnum) {

        // Get the Global discount
        def (discount, discountCodeObj) = getDiscount(rateCommand.premium, countryEnum, rateCommand.providerId, rateCommand.productId)

        //Apply discount onto the rate command
        rateCommand = applyDiscount(rateCommand, discount, discountCodeObj)

        return rateCommand
    }


    HomeRateCommand applyDiscount(HomeRateCommand rateCommand, BigDecimal discount, DiscountCode discountCodeObj) {

        if (discountCodeObj) {
            if (discountCodeObj.hasPercentDiscount) {
                rateCommand.discountPercent = discountCodeObj.discount
            }
            rateCommand.discountAmount = discount

            rateCommand.premium = rateCommand.originalPremium.subtract(discount)
        }

        return rateCommand
    }

    def getDiscount (double amount, CountryEnum countryEnum, Long providerId = null, Long productId = null, LeadType leadType=null) {

        BigDecimal discount
        DiscountCode discountCodeObj = discountService.getApplicableDiscountCode(countryEnum,
            ProductTypeEnum.HOME.value(),
            leadType,
            providerId as Integer,
            productId as Integer,
            amount
        )

        //Is global discount enabled
//        if (Boolean.parseBoolean(grailsApplication.config.getProperty("cover.premium.home.discount.${countryEnum.code}.enabled"))) {
//
//            Long discountCodeId =
//                Long.parseLong(grailsApplication.config.getProperty("cover.premium.home.discount.${countryEnum.code}.id"))
//
//            //Use the discount code if available
//            discountCodeObj = discountCodeId ? DiscountCode.get(discountCodeId) : null
//
//
//        }

        if(discountCodeObj) {
            discount = discountCodeObj.discount

            if (discountCodeObj.hasPercentDiscount) {
                discount = (discountCodeObj.discount * amount) / 100
            }

            discount = discount.setScale(0, BigDecimal.ROUND_HALF_UP)
        }

        [discount, discountCodeObj]
    }

}
