package com.cover.whatsapp

import com.safeguard.AsyncEventConstants
import grails.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import reactor.bus.Event
import reactor.spring.context.annotation.Consumer
import reactor.spring.context.annotation.Selector

@Consumer
@Transactional(readOnly = true)
class WhatsAppEventConsumerService {
    def grailsApplication

    @Selector(AsyncEventConstants.WHATSAPP_NOTIFICATION_TRIGGER)
    def sendTravelQuoteEmail(Event event) {
        String url = getQuoteUrl(event)
        log.info("WhatsApp URL: " + url)
        log.info("WhatsApp Recipient: " + event.data.recipient)

        List<String> variables = new ArrayList<>()
        variables.add(url)

        String[] recipients = new String[1];
        recipients[0] = event.data.recipient

        String response = WhatsAppService
            .sendWhatsAppMessage(event.data.templateName, "", recipients, variables)

        log.info("Response: " + response)
    }

    private String getQuoteUrl(Event event) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(grailsApplication.config.getProperty('yallacompare.baseURL'))
        if(!(event.data.type).equals("pet-insurance")) {
            stringBuilder.append("/insurance")
        }
        stringBuilder.append("/").append(event.data.country)
            .append("/").append(event.data.lang)
            .append("/").append(event.data.type)
        if((event.data.type).equals("pet-insurance")) {
            stringBuilder.append("/quote")
        } else {
            stringBuilder.append("/quotes")
        }
        stringBuilder.append("/").append(event.data.quoteId)

        return stringBuilder.toString()
    }
}
