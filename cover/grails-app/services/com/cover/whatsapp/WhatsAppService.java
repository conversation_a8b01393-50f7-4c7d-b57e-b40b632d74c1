package com.cover.whatsapp;

import com.cmtelecom.text.sdk.MessageBuilder;
import com.cmtelecom.text.sdk.MessagingClient;
import com.cmtelecom.text.sdk.models.Channel;
import com.cmtelecom.text.sdk.models.Message;
import com.cmtelecom.text.sdk.models.Response;
import com.cmtelecom.text.sdk.models.multichannel.MediaMessage;
import com.cmtelecom.text.sdk.models.templates.LocalizableParam;
import com.cmtelecom.text.sdk.models.templates.TemplateComponents;
import com.cmtelecom.text.sdk.models.templates.TemplateLanguage;
import com.cmtelecom.text.sdk.models.templates.TemplateMessage;
import com.cmtelecom.text.sdk.models.templates.TemplateMessageContent;
import com.cmtelecom.text.sdk.models.templates.TemplateParameters;
import com.cmtelecom.text.sdk.models.templates.WhatsAppTemplate;

import java.util.List;

public class WhatsAppService {

  private static final String YC_NUMBER = "0097142485899";
  private static final String DEFAULT_MESSAGE = "Hi from Yallacompare";
  private static final String PRODUCT_TOKEN = "80ADDB58-772E-4210-8753-434FEF89DE8D";
  private static final String WHATSAPP_NAMESPACE = "bda91942_0323_491a_b197_d80c48b4ed0f";

  /**
   * For Sending WhatsApp Message
   * @param templateName
   * @param message
   * @param recipients
   * @param variables
   * @return
   */
  public static String sendWhatsAppMessage(String templateName, String message, String[] recipients, List<String> variables) {
    MessagingClient client =
        new MessagingClient(PRODUCT_TOKEN);
    MessageBuilder messageBuilder = getMessageBuilder(message, recipients);
    messageBuilder.WithAllowedChannels(new Channel[] {Channel.WhatsApp});

    TemplateMessage templateMessage = getWhatsAppTemplate(templateName, variables);
    messageBuilder.WithTemplate(templateMessage);

    Message actualMessage = messageBuilder.Build();

    Response.HttpResponseBody responseBody = client.sendMessage(actualMessage);
    return responseBody.toString();
  }

  /**
   * For Sending Plain Text message
   * @param message
   * @param recipients
   * @return
   */
  private static String textMessage(String message, String[] recipients) {
    MessagingClient client =
        new MessagingClient(PRODUCT_TOKEN);
    MessageBuilder builder = getMessageBuilder(message, recipients);

    builder.WithAllowedChannels(new Channel[] {Channel.WhatsApp});

    Message actualMessage = builder.Build();

    Response.HttpResponseBody responseBody = client.sendMessage(actualMessage);
    return responseBody.toString();
  }

  /**
   * For Sending Rich Text (Images, Links, Videos) messages
   * @param message
   * @param recipients
   * @param mediaMessageModel
   * @return
   */
  private static String richMessage(String message, String[] recipients, MediaMessageModel mediaMessageModel) {
    MessagingClient client =
        new MessagingClient(PRODUCT_TOKEN);
    MessageBuilder builder = getMessageBuilder(message, recipients);

    builder.WithAllowedChannels(new Channel[] {Channel.WhatsApp});

    builder.WithRichMessage(new MediaMessage(mediaMessageModel.getMediaName(), mediaMessageModel.getMediaUri(), mediaMessageModel.getMimeType()));

    Message actualMessage = builder.Build();

    Response.HttpResponseBody responseBody = client.sendMessage(actualMessage);
    return responseBody.toString();
  }


  private static MessageBuilder getMessageBuilder(String message, String[] recipients) {
    if(message == null || message.length() == 0) {
      message = DEFAULT_MESSAGE;
    }

    return new
        MessageBuilder(message, YC_NUMBER, recipients);
  }


  private static TemplateMessage getWhatsAppTemplate(String templateName, List<String> variables) {
    TemplateMessage templateMessage = new TemplateMessage();
    templateMessage.Content = new TemplateMessageContent();
    templateMessage.Content.WhatsAppTemplate = new WhatsAppTemplate();
    templateMessage.Content.WhatsAppTemplate.Name = templateName;
    templateMessage.Content.WhatsAppTemplate.Namespace = WHATSAPP_NAMESPACE;
    templateMessage.Content.WhatsAppTemplate.Language =  new TemplateLanguage("en_US", "deterministic");
    templateMessage.Content.WhatsAppTemplate.LocalizableParams = new LocalizableParam[] {};
    templateMessage.Content.WhatsAppTemplate.Components = new TemplateComponents[] {};

    if (variables.size() > 0) {
      TemplateParameters[] templateParameters = new TemplateParameters [variables.size()];
      for (int i = 0; i < templateParameters.length; i++) {
        templateParameters[i] = new TemplateParameters("text", variables.get(i));
      }

      templateMessage.Content.WhatsAppTemplate.Components =
          new TemplateComponents[] { new TemplateComponents("body", templateParameters)};
    }

    return templateMessage;
  }
}
