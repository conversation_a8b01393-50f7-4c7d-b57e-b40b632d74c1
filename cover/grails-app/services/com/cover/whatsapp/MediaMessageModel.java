package com.cover.whatsapp;

public class MediaMessageModel {
  private String mediaName;
  private String mediaUri;
  private String mimeType;

    public MediaMessageModel(String mediaName, String mediaUri, String mimeType) {
        this.mediaName = mediaName;
        this.mediaUri = mediaUri;
        this.mimeType = mimeType;
    }

    public String getMediaName() {
        return mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public String getMediaUri() {
        return mediaUri;
    }

    public void setMediaUri(String mediaUri) {
        this.mediaUri = mediaUri;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }
}
