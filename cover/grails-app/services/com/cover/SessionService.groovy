package com.cover

import com.safeguard.ClaimPeriodEnum
import com.safeguard.ExtraFieldCodeEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.QuoteExtraField
import com.safeguard.RequestSourceEnum
import com.safeguard.car.CarQuote
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import com.cover.util.IConstant
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.web.servlet.mvc.GrailsHttpSession
import org.grails.web.util.WebUtils
import grails.transaction.Transactional

@Transactional(readOnly = true)
class SessionService {

    def grailsApplication

    String getBaseUrl() {
        GrailsHttpSession session = getSession()
        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]

        String baseUrl = whiteLabelDomain ? whiteLabelDomain.baseUrl : grailsApplication.config.getProperty('yallacompare.baseURL')

        baseUrl
    }

    String getBrandCode() {
        GrailsHttpSession session = getSession()
        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain

        String code = whiteLabelDomain ? whiteLabelDomain.brand.code : 'YC'

        code
    }

    WhiteLabelBrandEnum getBrand(){
        String code = getBrandCode()
        if (code) {
            return WhiteLabelBrandEnum.findBrandByCode(code)
        }
        null

    }

    RequestSourceEnum getRequestSource() {
        GrailsHttpSession session = getSession()
        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]
        RequestSourceEnum requestSource

        switch(whiteLabelDomain?.brand?.id) {
            case WhiteLabelBrandEnum.NATIONALBONDS.id:
                requestSource = RequestSourceEnum.NATIONALBONDS
                break
            case WhiteLabelBrandEnum.ETISALAT_SMILES.id:
                requestSource = RequestSourceEnum.ETISALAT_SMILES
                break
            case WhiteLabelBrandEnum.PETRA.id:
                requestSource = RequestSourceEnum.PETRA
                break
            case WhiteLabelBrandEnum.ADIB.id:
                requestSource = RequestSourceEnum.ADIB
                break
            default:
                requestSource = RequestSourceEnum.WEB
                break
        }
        requestSource
    }

    GrailsHttpSession getSession() {
        return WebUtils.retrieveGrailsWebRequest().session
    }

    void updateSession(CarQuote carQuote) {
        session[IConstant.STEP1_PURCHASE_DATE] = carQuote.purchaseDate?.toDate()
        session[IConstant.STEP1_IS_NOT_BOUGHT_YET] = carQuote.isNotBoughtYet
        session[IConstant.STEP1_IS_BRAND_NEW] = carQuote.isBrandNew
        session[IConstant.STEP1_IS_FIRST_CAR] = carQuote.isFirstCar
        session[IConstant.STEP1_YEAR] = carQuote.year
        session[IConstant.STEP1_MAKE] = carQuote.model.makeId
        session[IConstant.STEP1_MODEL_MASTER] = carQuote.model.modelMasterId
        session[IConstant.STEP1_MODEL] = carQuote.modelId

        QuoteExtraField quoteExtraFieldCarMakeModel = QuoteExtraField.
            findByQuoteIdAndInsuranceTypeAndExtraFieldCode(carQuote.id, InsuranceTypeEnum.CAR, ExtraFieldCodeEnum.MAKE_MODEL_TRIM)
        session[IConstant.STEP1_MAKE_MODEL_TRIM] = quoteExtraFieldCarMakeModel?.extraFieldValue

        session[IConstant.STEP1_CAR_CITY] = carQuote.registrationCityId
        session[IConstant.STEP1_REGISTRATION_DATE] = carQuote.firstRegistrationDate.toDate()
        session[IConstant.STEP1_INSURED_VALUE] = carQuote.insuredValue
        session[IConstant.STEP1_IS_NON_GCC] = carQuote.isNonGcc
        session[IConstant.STEP1_IS_POLICY_EXPIRED] = carQuote.isExpiredPolicy
        session[IConstant.STEP1_IS_THIRD_PARTY] = carQuote.isThirdParty
        session[IConstant.STEP1_IS_OLD_AGENCY] = carQuote.isOldAgency
        session[IConstant.STEP2_NATIONALITY] = carQuote.nationalityId
        session[IConstant.STEP2_COUNTRY] = carQuote.licenseCountryId
        session[IConstant.STEP2_INTERNATIONAL_EXPERIENCE] = carQuote.internationalExperienceId
        session[IConstant.STEP2_LOCAL_EXPERIENCE] = carQuote.localExperienceId
        session[IConstant.STEP2_POLICY_START_DATE] = carQuote.policyStartDate.toDate()
        session[IConstant.STEP2_NCD] = carQuote.noClaimDiscountId
        session[IConstant.STEP2_HAS_CLAIM] = carQuote.hasClaim
        session[IConstant.STEP2_LAST_CLAIM_PERIOD] = carQuote.lastClaimPeriod ?: (carQuote.hasClaim ? ClaimPeriodEnum.TWELVE_MONTHS : null)
        session[IConstant.STEP2_NAME] = carQuote.name
        session[IConstant.STEP2_MOBILE] = carQuote.mobile
        session[IConstant.STEP2_EMAIL] = carQuote.email
        session[IConstant.STEP2_DOB] = carQuote.dob.toDate()
        session[IConstant.STEP2_CARID] = carQuote.id
        session[IConstant.STEP2_COVER_PREFERENCE] = carQuote.coverPreference
    }

}
