package com.cover.boat

import com.safeguard.AsyncEventConstants
import com.safeguard.User
import com.safeguard.boat.BoatQuote

class BoatQuoteService {
    def saveQuote(def params) {
        BoatQuote boatQuote = new BoatQuote()
        boatQuote.name = params.name
        boatQuote.email = params.email
        boatQuote.phone = params.phone
        boatQuote.boat_details = params.boat_details
        boatQuote.engine_details = params.engine_details


        User user = User.findByEmail(params.email);

        if (!user) {
            User newUser = new User()
            newUser.name = params.name
            newUser.email = params.email
            newUser.mobile = params.phone
            boatQuote.user = newUser
        } else {
            user.name = params.name
            user.mobile = params.phone
            boatQuote.user = user

        }

        boatQuote.save()

        notify AsyncEventConstants.BOAT_QUOTE_CREATED, [quoteId: boatQuote.getId(), boatDetails: boatQuote.getBoat_details(), boatEngine: boatQuote.getEngine_details()]
    }
}
