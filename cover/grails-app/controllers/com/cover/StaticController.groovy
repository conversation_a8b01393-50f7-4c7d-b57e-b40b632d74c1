package com.cover

import com.cover.util.IConstant
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.car.StaticContent
import com.safeguard.whitelabel.WhiteLabelBrand
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.http.HttpStatus

class StaticController {

    static namespace = "static"

    def sessionService

    def index() {
        String baseUrl = sessionService.getBaseUrl()
        redirect mapping: 'carIndex', params: [lang: params.lang, country: params.country], base:baseUrl, permanent:true
    }

    def hub() {
        render view: '/index', model: [country: params.country]
    }

    def contactUs() {

        if (request.method == 'POST') {
            notify 'contactus.email', params
            flash.message = g.message(code: "contactus.sentSuccessfully")
            String baseUrl = sessionService.getBaseUrl()

            redirect mapping: 'commonContactUs', params: [lang: params.lang, country: params.country], base:baseUrl

            return
        }

        Country country = Country.get(CountryEnum.findCountry(params.country).id)
        StaticContent staticContent = StaticContent.findByCountryAndSlug(country, 'contact-us')

        if (staticContent) {
            render view: '/common/contactUs', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def aboutUs() {
        Country country = Country.get(CountryEnum.findCountry(params.country).id)
        Map whiteLabelBrandMap = session[IConstant.WHITE_LABEL_DOMAIN]
        StaticContent staticContent = StaticContent.findByCountryAndSlugAndWhiteLabelBrand(country, 'about-us', WhiteLabelBrand.load(whiteLabelBrandMap.brand.id))

        if (staticContent) {
            render view: '/common/aboutUs', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def disclaimer() {
        Country country = Country.get(CountryEnum.findCountry(params.country).id)
        StaticContent staticContent = StaticContent.findByCountryAndSlug(country, 'disclaimer')

        if (staticContent) {
            render view: '/common/disclaimer', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def partners() {
        render view: '/common/partners', model: [country: params.country]
    }

    def privacyPolicy() {
        Country country = Country.get(CountryEnum.findCountry(params.country).id)
        StaticContent staticContent = StaticContent.findByCountryAndSlug(country, 'privacy-policy')

        if (staticContent) {
            render view: '/common/privacyPolicy', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def termsAndConditions() {
        Country country = Country.get(CountryEnum.findCountry(params.country).id)
        StaticContent staticContent = StaticContent.findByCountryAndSlug(country, 'terms-and-conditions')

        if (staticContent) {
            render view: '/common/termsAndConditions', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def insurancePartners() {
        Country country = Country.get(CountryEnum.findCountry(params.country).id)
        StaticContent staticContent = StaticContent.findByCountryAndSlug(country, 'insurance-partners')

        if (staticContent) {
            render view: '/common/insurancePartners', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }
}
