package com.cover

import groovy.json.JsonSlurper

class PolicyEmailController {

    def instantIssueService

    def parse(String key) {
        String policyEmailKey = grailsApplication.config.getProperty("mandrill.policyEmailKey")
        if (key != policyEmailKey) {
            log.warn("policyEmail.parse - Invalid key provided, params: ${params}")
            render text: "failed"
            return
        }

        if (params.mandrill_events) {
            JsonSlurper jsonSlurper = new JsonSlurper()
            List<Map<String, Object>> mandrillEvents = (List<Map<String, Object>>) jsonSlurper.parseText(params.mandrill_events)
            instantIssueService.processDocumentsFromMandrillWebhook(mandrillEvents)
        } else {
            log.warn("policyEmail.parse - no mandrill_events found, params: ${params}")
        }

        render "success"
    }

}
