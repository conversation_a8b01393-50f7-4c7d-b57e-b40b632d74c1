package com.cover.apiV2

import com.cover.api.CarQuoteDetailV2Command
import com.cover.api.CarQuoteV2Command
import com.cover.api.DrivingLicenseDocumentCommand
import com.cover.api.EmiratesIdDocumentCommand
import com.cover.api.QuotePaymentV2Command
import com.cover.api.VehicleRegistrationDocumentCommand
import com.cover.api.response.ErrorResponse
import com.cover.car.commands.DriverCommand
import com.cover.car.commands.VehicleCommand
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.AutoDataTrimDto
import com.safeguard.ClaimPeriodEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverPreferenceEnum
import com.safeguard.ExternalDataSource
import com.safeguard.ExternalDataSourceEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.QuoteCreatorEnum
import com.safeguard.SubRequestSourceEnum
import com.safeguard.ValuationSourceEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.vehicle.Model
import com.safeguard.car.vehicle.ModelExternal
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.springframework.http.HttpStatus

class CarController {

    static namespace = "apiV2"

    def commonQuoteService
    def commonUtilService
    def messageSource
    def quoteService
    def utilService
    def valuationService

    static allowedMethods = [

    ]

    /**
     * Get list of static data required for car insurance
     * @return list of static data as JSON
     */
    def ddl() {
        log.info("apiV2.car.ddl - entering with params:[${params}]")
        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)
        log.info("country:${country}, countryEnum:$countryEnum")
        List cities = utilService.getCities(country, false).collect {
            [id:it.id, name:it.name]
        }

        List drivingExperience = utilService.getDrivingExperienceList().collect {
            [id:it.id, name:it.name]
        }

        List ncd = utilService.getNoClaimDiscountList().collect {
            [id:it.id, name:it.name]
        }

        List<Country> countries = utilService.getCountries('nameEn')
            .collect {[id: it.id,
                               name: it.name,
                               nationality: it.nationality,
                               isoCode: it.isoCode]}

        def model = [:]
        model.carYears = commonUtilService.getCarYears()
        model.registrationCities = cities
        model.drivingExperience = drivingExperience
        model.ncd = ncd
        model.countries = countries
        model.claimPeriod = ClaimPeriodEnum.findAll().sort {it.sortOrder}.collect {[name:it.name, value:it.toString()]}
        model.coverPreference = [CoverPreferenceEnum.COMPREHENSIVE, CoverPreferenceEnum.THIRD_PARTY].collect {[name:it.getLabel(params.lang), value:it.toString()]}

        render model as JSON
    }

    def autoDataSpecsAndValuation(int year, Integer trim, Boolean isBrandNew) {

        Model model = Model.read(trim)
        List<AutoDataTrimDto> autoDataSpecs = quoteService.getAutoDataTrims(year, model, null)

        Country country = Country.get(utilService.convertToCountry(params.country).id)
        def valuation = valuationService
            .getValuation(trim, year, isBrandNew.booleanValue(), country, null)

        def result = autoDataSpecs.collect {
            [
                admeId: it.admeId,
                description: "${it.description}",
                maxPrice: it.maxPrice,
                minPrice: it.minPrice,
                valuationSource: it.valuationSource.toString()
                //avgPrice: it.avgPrice
            ]
        }

        result.push([admeId:null, description:null,
                     maxPrice: valuation.maximum,
                     minPrice: valuation.minimum,
                     valuationSource: ValuationSourceEnum.YC.toString()])

        def models = [adValuations: result]
        render(contentType: 'text/json', text: models as JSON)
    }

    /**
     * Create Quote along with the selected Product and repair type
     * @param command
     * @return
     */
    def createQuote() {
        log.info("apiV2.car.createQuote - entering createQuote with request body: ${request.JSON.toString()}")
        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        try {
            CarQuoteV2Command carQuoteCommand = new CarQuoteV2Command(request.JSON)
            carQuoteCommand.country = params.country
            //Set default values
            if (carQuoteCommand.year && !carQuoteCommand.firstRegistrationDate) {
                String defaultDate = "01-01-${carQuoteCommand.year}"
                carQuoteCommand.firstRegistrationDate = carQuoteCommand.firstRegistrationDate ?: defaultDate
            }

            if (!carQuoteCommand.validate()) {
                log.error("apiV2.car.createQuote - validation error - command:$carQuoteCommand")

                def errorList = utilService.generateErrorResponse(carQuoteCommand.errors, locale, "car.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            VehicleCommand vehicleCommand = carQuoteCommand.toVehicleCommand()
            DriverCommand driverCommand = carQuoteCommand.toDriverCommand()

            log.info("apiV2.car.createQuote - carQuoteCommand is Valid:${carQuoteCommand.validate()}")

            DateTimeFormatter formatter = DateTimeFormat.forPattern("dd-MM-yyyy")

            Date policyStartDate = LocalDate.parse(carQuoteCommand.policyStartDate, formatter).toDate()
            Date firstRegistrationDate = LocalDate.parse(carQuoteCommand.firstRegistrationDate, formatter).toDate()

            Date dob = LocalDate.parse(carQuoteCommand.dob, formatter).toDate()

            vehicleCommand.oldPolicyExpiryDate = null
            vehicleCommand.purchaseDate = null
            driverCommand.policyStartDate = policyStartDate
            driverCommand.dob = dob
            vehicleCommand.firstRegistrationDate = firstRegistrationDate

            log.info("apiV2.car.createQuote - policyStart:${policyStartDate}, firstReg:$firstRegistrationDate, dob:$dob")

            if (!vehicleCommand.validate()) {
                log.error("apiV2.car.createQuote - validation error - vehicleCommand:$vehicleCommand")

                def errorList = utilService.generateErrorResponse(vehicleCommand.errors, locale, "car.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            if (!driverCommand.validate()) {
                log.error("apiV2.car.createQuote - validation error - driverCommand:$driverCommand")

                def errorList = utilService.generateErrorResponse(driverCommand.errors, locale, "car.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
            marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
            marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
            marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
            marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
            marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
            marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
            driverCommand.marketingTracking = marketingTracking

            CarQuote quote = quoteService.createCarQuoteAndUpdateProduct(carQuoteCommand, vehicleCommand, driverCommand,
                country, lang)

            notify AsyncEventConstants.CAR_QUOTE_CREATED, [quoteId: quote.id, lang: lang,
                                                           creator: QuoteCreatorEnum.API]

            def model = [:]
            model.carQuote = quote
            model.encQuoteId = AESCryption.encrypt(quote.id.toString())

            render view:"/apiV2/car/quoteDetail", model:model
            return
        } catch (Exception e) {
            log.error("apiV2.saveQuoteDetailedData  - error updating user documents data. request body:${request.JSON}", e)

            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())

            ErrorResponse error = new ErrorResponse()
            error.developerMessage = e.message
            error.userMessage = messageSource.getMessage("default.general.error", [].toArray(), locale)

            def errors = [errors:[error]]
            render errors as JSON
            return
        }
    }

    def saveQuoteDetailedData() {
        log.info("apiV2.saveQuoteDetailedData - ${request.JSON.toString()}")

        CarQuoteDetailV2Command detailV2Command = new CarQuoteDetailV2Command(request.JSON)

        CountryEnum countryEnum = utilService.convertToCountry(params.country)

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        try {
            Long quoteId = AESCryption.decrypt(params.id).toLong()

            quoteService.saveQuoteRawData(quoteId, InsuranceTypeEnum.CAR, request.JSON.toString())

            if (!detailV2Command.validate()) {
                log.error("apiV2.car.updateQuote - validation error - command:$detailV2Command")

                def errorList = utilService.generateErrorResponse(detailV2Command.errors, locale, "car.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            def resp = [:]
            CarQuote quote = CarQuote.findById(quoteId)
            log.info("apiV2.car.updateQuote - quote:$quote")
            if (!quote) {
                log.warn "apiV2.car.updateQuote - Quote is not found "
                response.status = HttpStatus.NOT_FOUND.value()
                resp.message = "Not Found"
                render resp as JSON
                return

            } else if (quote.paidDate) {
                log.warn "apiV2.car.updateQuote - Quote is already processed for quote id: ${quote?.id}"
                response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
                resp.message = g.message(code:'error.alreadyProcessed')
                render resp as JSON
                return

            } else if (quote && quote.isAncient()) {
                log.info "apiV2.car.updateQuote - Policy [quoteId:${quoteId}] created on ${quote.dateCreated} so redirecting to 404"
                response.status = HttpStatus.NOT_FOUND.value()
                resp.message = "Not Found"
                render resp as JSON
                return
            }

            Country nationality = Country.findByIsoCode(detailV2Command.nationality)
            if (nationality == null) {
                nationality = Country.load(CountryEnum.UNKNOWN.id)
            }
            Country firstDrivingLicenseCountry = Country.findByIsoCode(detailV2Command.firstDrivingLicenseCountry)
            if (firstDrivingLicenseCountry == null) {
                firstDrivingLicenseCountry = Country.load(CountryEnum.UNKNOWN.id)
            }

            VehicleCommand vehicleCommand = detailV2Command.toVehicleCommand()
            DriverCommand driverCommand = detailV2Command.toDriverCommand()
            driverCommand.nationality = nationality.id
            EmiratesIdDocumentCommand emiratesIdDocCommand = detailV2Command.toEmiratesIdDocumentCommand()
            emiratesIdDocCommand.nationality = nationality.nameEn
            VehicleRegistrationDocumentCommand vehRegDocCommand = detailV2Command.toVehicleRegistrationCommand()
            vehRegDocCommand.nationality = nationality.nameEn
            DrivingLicenseDocumentCommand drivingLicenseDocCommand = detailV2Command.toDrivingLicenseDocumentCommand()
            drivingLicenseDocCommand.nationality = nationality.nameEn

            DateTimeFormatter formatter = DateTimeFormat.forPattern("dd-MM-yyyy")

            LocalDate dateOfBirthObj = LocalDate.parse(detailV2Command.dateOfBirth, formatter)
            Date dob = dateOfBirthObj.toDate()
            Date policyStartDate = LocalDate.parse(detailV2Command.policyStartDate, formatter).toDate()

            //Set default values
            if (detailV2Command.firstRegistrationYear) {
                String defaultDate = "01-01-${detailV2Command.firstRegistrationYear}"
                LocalDate defaultFirstRegLocalDate = LocalDate.parse(defaultDate, formatter)
                vehicleCommand.firstRegistrationDate = defaultFirstRegLocalDate.toDate()
                vehRegDocCommand.registrationDate = defaultFirstRegLocalDate.toString("yyyy-MM-dd'T'00:00:00.000'Z'")
            }
            driverCommand.policyStartDate = policyStartDate
            driverCommand.dob = dob
            emiratesIdDocCommand.dob = dateOfBirthObj.toString("dd/MM/yyyy")
            emiratesIdDocCommand.idCardExpiryDate = LocalDate.parse(detailV2Command.emiratesIdExpiryDate, formatter).toString("dd/MM/yyyy")
            drivingLicenseDocCommand.dob = dateOfBirthObj.toString("dd/MM/yyyy")
            drivingLicenseDocCommand.expiryDate = LocalDate.parse(detailV2Command.drivingLicenseExpiryDate, formatter).toString("dd/MM/yyyy")

            ExternalDataSource gigDataSource = ExternalDataSource.load(ExternalDataSourceEnum.GIG.id)

            ModelExternal modelExternal = ModelExternal.findByExternalDataSourceAndExternalId(gigDataSource,
                detailV2Command.modelVersion, [readonly: true])
            vehicleCommand.model = modelExternal.modelId.toInteger()
            log.info("vehicleCommand.model:${vehicleCommand.model}")

            driverCommand.firstLicenseCountry = firstDrivingLicenseCountry.id
            driverCommand.mobile = quote.mobile

            if (!vehicleCommand.validate()) {
                log.error("apiV2.car.updateQuote - validation error - vehicleCommand:$vehicleCommand")

                def errorList = utilService.generateErrorResponse(vehicleCommand.errors, locale, "car.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            if (!driverCommand.validate()) {
                log.error("apiV2.car.updateQuote - validation error - driverCommand:$driverCommand")

                def errorList = utilService.generateErrorResponse(driverCommand.errors, locale, "car.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            quote = quoteService.updateCarQuoteDetail(quote, detailV2Command, vehicleCommand, driverCommand,
                emiratesIdDocCommand, drivingLicenseDocCommand, vehRegDocCommand)


            if (vehicleCommand.model) {
                log.info("updating quote:${quote.id} with vehicleCommand.model:${vehicleCommand.model}")

                //Updating via Query, due to concurrent update issue when we do quote.model = model & save
                CarQuote.executeUpdate("Update CarQuote q set q.model.id = :modelId where q.id = :id",
                    [modelId: vehicleCommand.model, id: quote.id])
            }

            def model = [success: "true"]
            render model as JSON
            return

        } catch (Exception e) {
            log.error("apiV2.saveQuoteDetailedData  - error updating user documents data. detailV2Command:${detailV2Command.toString()}", e)

            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())

            ErrorResponse error = new ErrorResponse()
            error.developerMessage = e.message
            error.userMessage = messageSource.getMessage("default.general.error", [].toArray(), locale)

            def errors = [errors:[error]]
            render errors as JSON
            return
        }
    }

    def updateQuotePayment(QuotePaymentV2Command quotePaymentV2Command) {
        log.info("car.updateQuotePayment - params:$params, quotePaymentV2Command:$quotePaymentV2Command")

        String insuranceType = params.insuranceType
        String id = params.id

        Long quoteId = Long.parseLong(AESCryption.decrypt(id))
        def quote = commonQuoteService.getQuote(quoteId, ProductTypeEnum.findByName(insuranceType))

        def resp = [:]
        if (!quote) {
            log.warn "car.updateQuotePayment - Quote is not found "
            response.status = HttpStatus.NOT_FOUND.value()
            resp.message = "Not Found"
            render resp as JSON
            return

        } else if (quote.paidDate) {
            log.warn "car.updateQuotePayment - Quote is already processed for quote id: ${quote?.id}"
            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            resp.message = g.message(code:'error.alreadyProcessed')
            render resp as JSON
            return
        } else if (quote.requestSubSource != SubRequestSourceEnum.COMIN.toString()) {
            log.warn "car.updateQuotePayment - Not a comin quote, quote id: ${quote?.id}"

            resp.message = g.message(code:'error.notAllowed')
            render resp as JSON
            return
        }


        commonQuoteService.updatePaymentInformation(quote, quotePaymentV2Command)

        def model = [success: "true"]
        render model as JSON
        return

    }
}
