package com.cover.apiV2

import com.cover.api.response.ErrorResponse
import com.cover.api.v2.TravelQuoteV2Command
import com.cover.api.v2.TravelUpdateQuoteV2Command
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.travel.TravelQuoteCommand
import com.cover.util.IConstant
import com.safeguard.InsuranceTypeEnum
import com.safeguard.travel.TravelQuote
import grails.converters.JSON
import org.springframework.http.HttpStatus

class TravelController {

    static namespace = "apiV2"

    def travelQuoteService
    def quoteService
    def utilService
    def messageSource

    static allowedMethods = [
        createQuote: ['POST']
    ]

    def createQuote(String country, String lang) {
        log.info("apiV2.travel.createQuote - entering createQuote, request: ${request.JSON.toString()}")
        String localeLang = utilService.convertToLocale(lang)
        Locale locale = new Locale(localeLang)

        try {
            TravelQuoteV2Command travelQuoteV2Command = new TravelQuoteV2Command(request.JSON)
            if (!travelQuoteV2Command.validate()) {
                def errorList = utilService.generateErrorResponse(travelQuoteV2Command.errors, locale, "travel.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
            marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
            marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
            marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
            marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
            marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
            marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]

            TravelQuoteCommand travelQuoteCommand = travelQuoteService.toTravelQuoteCommand(travelQuoteV2Command, marketingTracking)

            TravelQuote travelQuote = travelQuoteService.createTravelQuote(travelQuoteCommand, travelQuoteV2Command.passportNumber)
            quoteService.saveQuoteRawData(travelQuote.id, InsuranceTypeEnum.TRAVEL, request.JSON.toString())

            travelQuoteService.saveCominReference(travelQuote, travelQuoteV2Command.cominReference)
            if (travelQuoteV2Command.subSource) {
                travelQuoteService.saveSubSource(travelQuote, travelQuoteV2Command.subSource)
            }

            Map result = [
                quoteId: travelQuote.id
            ]

            render result as JSON
        } catch (e) {
            log.error("apiV2.travel.createQuote  - error while creating travel quote, travelQuoteV2Command:${travelQuoteV2Command.toString()}", e)
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())

            ErrorResponse errorResponse = new ErrorResponse()
            errorResponse.developerMessage = e.message
            errorResponse.userMessage = g.message(code: "default.general.error")

            def errors = [errors: [errorResponse]]
            render errors as JSON
        }
    }

    def updateQuote(TravelUpdateQuoteV2Command travelUpdateQuoteV2Command) {
        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)
        try {
            quoteService.saveQuoteRawData(travelUpdateQuoteV2Command.quoteId, InsuranceTypeEnum.TRAVEL, request.JSON.toString())
            if (!travelUpdateQuoteV2Command.validate()) {
                log.error("apiv2.travel.updateQuote - validation error - command: ${travelUpdateQuoteV2Command}")
                def errorList = utilService.generateErrorResponse(travelUpdateQuoteV2Command.errors, locale, "travel.quote")
                Map errors = [
                    errors: errorList
                ]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            TravelQuote travelQuote = TravelQuote.read(travelUpdateQuoteV2Command.quoteId)

            Map resp = [:]
            if (!travelQuote) {
                response.setStatus(HttpStatus.NOT_FOUND.value())
                resp.message = "Not Found"
                render resp as JSON
                return
            }

            if (!travelQuote.isNotProcessed()) {
                log.warn("apiv2.travel.updateQuote - quote is already processed, travel quote id ${travelQuote.id}")
                resp.message = g.message(code: "error.alreadyProcessed")
                render resp as JSON
                return
            }

            travelQuoteService.updateTravelQuote(travelUpdateQuoteV2Command)
        } catch (Exception e) {
            log.error("apiv2.travel.updateQuote - error while updating travel quote, command: ${travelUpdateQuoteV2Command} ", e)
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())

            ErrorResponse errorResponse = new ErrorResponse()
            errorResponse.developerMessage = e.message
            errorResponse.userMessage = messageSource.getMessage("default.general.error", [].toArray(), locale)

            Map errors = [
                errors: [
                    errorResponse
                ]
            ]
            render errors as JSON
            return
        }

    }
}
