package com.cover.apiV2

import com.cover.api.response.ErrorResponse
import com.cover.api.v2.HealthMemberV2Command
import com.cover.api.v2.HealthQuoteV2Command
import com.cover.api.v2.HealthUpdateMemberV2Command
import com.cover.api.v2.HealthUpdateQuoteV2Command
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.util.IConstant
import com.safeguard.InsuranceTypeEnum
import com.safeguard.Product
import com.safeguard.health.HealthMember
import com.safeguard.health.HealthQuote
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.joda.time.LocalDateTime
import org.springframework.http.HttpStatus

class HealthController {

    static namespace = "apiV2"

    def utilService
    def healthQuoteService
    def quoteService

    static allowedMethods = [
        createQuote: ['POST'],
        updateQuote: ['PUT']
    ]

    def createQuote(String country, String lang) {
        log.info("apiV2.health.createQuote - entering createQuote, request JSON: ${request.JSON.toString()}")
        String localeLang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(localeLang)

        try {
            HealthQuoteV2Command healthQuoteV2Command = new HealthQuoteV2Command(request.JSON)

            if (!healthQuoteV2Command.validate()) {
                def errorList = utilService.generateErrorResponse(healthQuoteV2Command.errors, locale, "health.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
            marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
            marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
            marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
            marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
            marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
            marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]

            HealthQuoteCommand healthQuoteCommand = healthQuoteService.toHealthQuoteCommand(healthQuoteV2Command, marketingTracking)

            HealthQuote healthQuote = healthQuoteService.createHealthQuote(healthQuoteCommand)
            quoteService.saveQuoteRawData(healthQuote.id, InsuranceTypeEnum.HEALTH, request.JSON.toString())

            healthQuoteService.saveCominReference(healthQuote, healthQuoteV2Command.cominReference)
            if (healthQuoteV2Command.subSource) {
                healthQuoteService.saveSubSource(healthQuote, healthQuoteV2Command.subSource)
            }

            Map resp = [
                quoteId     : healthQuote.id,
                encQuoteId  : AESCryption.encrypt(healthQuote.id.toString()),
            ]

            render resp as JSON
        } catch (e) {
            log.error("apiV2.health.createQuote  - error while creating health quote, reuqest:${request.JSON.toString()}", e)

            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())

            ErrorResponse error = new ErrorResponse()
            error.developerMessage = e.message
            error.userMessage = g.message(code: "default.general.error")

            def errors = [errors:[error]]
            render errors as JSON
        }
    }

    def updateQuote(String country, String lang) {
        log.info("apiV2.health.updateQuote - entering updateQuote, params: $params, healthUpdateQuoteV2Command: ${request.JSON.toString()}")
        String localeLang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(localeLang)

        try {
            Long quoteId = AESCryption.decrypt(params.id).toLong()
            HealthUpdateQuoteV2Command healthUpdateQuoteV2Command = new HealthUpdateQuoteV2Command(request.JSON)

            if (!healthUpdateQuoteV2Command.validate()) {
                def errorList = utilService.generateErrorResponse(healthUpdateQuoteV2Command.errors, locale, "health.quote")
                def errors = [errors:errorList]

                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON
                return
            }

            HealthQuote healthQuote = HealthQuote.get(quoteId)
            quoteService.saveQuoteRawData(quoteId, InsuranceTypeEnum.HEALTH, request.JSON.toString())
            Map resp = [:]
            if (!healthQuote || healthQuote.dateCreated.isBefore(LocalDateTime.now().minusMonths(6))) {
                response.setStatus(HttpStatus.NOT_FOUND.value())
                resp.message = "Quote not found"
                render resp as JSON
                return
            } else if (!healthQuote.isNotProcessed()) {
                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                resp.message = g.message(code:'error.alreadyProcessed')
                render resp as JSON
                return
            } else if (!Product.findBySlug(healthUpdateQuoteV2Command.product)) {
                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                resp.message = "Product not found"
                render resp as JSON
                return
            }

            healthQuoteService.updateHealthQuote(healthQuote, healthUpdateQuoteV2Command)

            resp.success = true
            render resp as JSON
        } catch (e) {
            log.error("apiV2.health.updateQuote - error while updating health quote, request: ${request.JSON.toString()}", e)
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())
            ErrorResponse error = new ErrorResponse()
            error.developerMessage = e.message
            error.userMessage = g.message(code: "default.general.error")

            def errors = [errors:[error]]
            render errors as JSON
        }
    }
}
