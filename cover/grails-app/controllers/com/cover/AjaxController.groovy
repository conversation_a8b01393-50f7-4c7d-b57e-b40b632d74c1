package com.cover

import com.cover.car.NoorRateV2Service
import com.cover.checkout.DiscountCodeException
import com.cover.health.commands.HealthQuoteCommand
import com.safeguard.AutoDataTrimDto
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.Product
import com.safeguard.Provider
import com.safeguard.Renewal
import com.safeguard.User
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCover
import com.safeguard.car.vehicle.Model
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.plugins.rest.client.RestBuilder
import org.apache.http.HttpStatus
import org.springframework.web.client.ResourceAccessException

class AjaxController {

    private static final int MIN_CAR_MODEL_YEAR = 2000
    def vehicleService
    def petService
    def valuationService
    def checkoutService
    def configurationService
    def utilService
    def noorApiService
    def quoteService

    def modelMasters(int year, int make, String countryCode) {

        CountryEnum countryEnum = CountryEnum.findCountryByCode(countryCode)
        Country country = Country.read(countryEnum ? countryEnum.id : CountryEnum.UAE.id)

        //TODO: Fix language later.
        def modelMasterList = vehicleService.getModelMasterList("en", make, year, country)
        def result = modelMasterList.collect {
            [
                id: it.id,
                name: "${it.name}"
            ]
        }.sort { it.name }

        def modelMasters = [modelMasters: result]
        render(contentType: 'text/json', text: modelMasters as JSON)
    }

    def models(int year, int make, int modelMaster, String countryCode ) {

        CountryEnum countryEnum = CountryEnum.findCountryByCode(countryCode)
        Country country = Country.read(countryEnum ? countryEnum.id : CountryEnum.UAE.id)

        //TODO: Fix language later.
        def modelDetail = vehicleService.getModelList("en", year, make, modelMaster, null, country)
        def modelList = modelDetail.modelList

        def result = modelList.collect {
            [
                id: it.modelId,
                name: "${it.trim}"
            ]
        }.sort { it.name }
        def models = [models: result]
        render(contentType: 'text/json', text: models as JSON)
    }

    def autoDataSpecs(int year, Integer modelMaster, Integer model, Long quoteId, String countryCode ) {

        List<AutoDataTrimDto> autoDataSpecs = quoteService.getAutoDataTrims(year, Model.read(model), quoteId ? AESCryption.encrypt(quoteId + "") : null)

        def result = autoDataSpecs.collect {
            [
                admeId: it.admeId,
                description: "${it.description}",
                maxPrice: it.maxPrice,
                minPrice: it.minPrice,
                avgPrice: it.avgPrice
            ]
        }

        def models = [autoDataSpecs: result]
        render(contentType: 'text/json', text: models as JSON)
    }

    /**
     * Returns update valuation range
     *
     * @param model
     * @param year
     * @return
     */
    def updateInsuranceRange(int model, int year, boolean isBrandNew) {
        if (validateInsuranceFlow(model, year)) {
            log.info("Scrapping is performed with incorrect modelId: ${model} or year: ${year}")
            return
        }
        def country = utilService.getCountry()

        def valuation = valuationService.getValuation(model, year, isBrandNew, country, null, false)

        def result = [label:g.message(code:"funnel.step1.valuationlbl", args:
            [valuation.currency, valuation.minimum, valuation.maximum ]), minimum: g.formatNumber(number:valuation.minimum, type:'number')]

        //Render result map as JSON [label and minimum]
        render result as JSON
    }

    /**
     * Returns Auto Data valuation range
     *
     * @param model
     * @param year
     * @return
     */
    def autoDataInsuranceRange(String admeId, Long quoteId) {

        CarQuote quote = CarQuote.load(quoteId)
        Country country = utilService.getCountry()

        if (admeId != "0" && admeId != "-1") {
            admeId = admeId.split(":")[0]
        }

        def valuation = quoteService.getValuation(admeId, quote)
        log.info("valuation:${valuation}")
        BigDecimal valuationMax = valuation.maximum
        BigDecimal valuationMin = valuation.minimum

        // valuation null safety check
        def result = [label: valuationMin == null ? "" : g.message(code: "funnel.step1.valuationlbl", args: [country.currency, valuationMin, valuationMax]),
                        minimum: valuationMin, maximum: valuationMax, valuationSource: valuation.valuationSource.toString()]

        //Render result map as JSON [label and minimum]
        render result as JSON
    }

    /**
     *
     * @param code
     * @return
     */
    def discount (String code, double amount, Integer type, long productId, long quoteId) {
        Map map = [error:true]

        try {
            def country = utilService.getCountry()
            CountryEnum countryEnum = CountryEnum.findCountryByIsoAlpha2Code(country.code)

            def (discount, discountCodeObj) = checkoutService.getDiscount(code, amount, type, productId, countryEnum.code, quoteId)
            map = [discount:discount, error:false]
        } catch (DiscountCodeException discountException) {
            log.warn(".discount DiscountCode exception(${discountException.messageKey}) while getting discount for code:${code} and amount:${amount}");
            map = [error:true, message:g.message(code:discountException.messageKey, args: discountException.args)]
        } catch (Exception exp) {
            log.error(".discount Exception thrown while getting discount for " +
                "code:${code} and amount:${amount}", exp)
            map.message = g.message(code:'default.general.error')
        }

        render map as JSON
    }

    def refresh () {
        log.info("#################### Refresh is called ####################")
        configurationService.referesh()

        render text: 'Ahh.....it\'s refreshing!!'
    }

    def rssFeed() {

        String feedUrl = g.message(code: 'rssFeed.url')

        RestBuilder rest = new RestBuilder()

        def resp

        try {
            resp = rest.get(feedUrl)
        }
        catch (ResourceAccessException e) {
            log.warn("Cannot fetch rssFeed. Prehaps this blog feed does not exist yet?")
        }

        render(text: resp?.responseEntity?.body, contentType: "text/xml", encoding: "UTF-8")
    }

    def createCrossSaleLead() {
        try {
            def user = User.read(AESCryption.decrypt(params.userId as String).toInteger())
            def cmd
            String url

            switch (params.to) {
                case InsuranceTypeEnum.HEALTH.toString():
                    cmd = new HealthQuoteCommand(
                        name: user.name,
                        email: user.email,
                        phone: user.mobile,
                        cityId: 1,
                        salaryOver4k: true,
                        source: params.from,
                        members: []
                    )
                    url = "${grailsApplication.config.getProperty('yallacompare.apiBaseURL')}/insurance/v1/health/members/${params.country}/${params.lang}"
                    break
                default:
                    cmd = []
                    url = ""
            }

            RestBuilder rest = new RestBuilder()
            def resp = rest.post(url) {
                header("Content-Type", "application/json")
                header("x-api-key", "icwijKUUKj6cystS7GCRN6JvrxllpgSt9EoAvLmZ")
                body cmd as JSON
            }

            if (resp.statusCode.value() != 200) {
                log.error "Error creating cross-sale lead: " +
                    "from=${params.from} " +
                    "to=${params.to} " +
                    "userId=${params.userId} " +
                    "quoteId=${params.quoteId} "
            }

            response.status = HttpStatus.SC_OK
            render(resp.statusCode as JSON)
        }
        catch (ex) {
            log.error "Error creating cross-sale lead: " +
                "from=${params.from} " +
                "to=${params.to} " +
                "userId=${params.userId} " +
                "quoteId=${params.quoteId} ", ex

            response.status = HttpStatus.SC_INTERNAL_SERVER_ERROR
            render([message: "Internal Server error: ${ex.message}"] as JSON)
        }
    }

    private boolean validateInsuranceFlow(int model, int year) {
        // Car model year must be between the min model year and upcoming year
        return (model < 1 ||
            year < MIN_CAR_MODEL_YEAR ||
            year > Calendar.getInstance().get(Calendar.YEAR) + 1)
    }


    def getPetType(String countryCode ) {

        def petType = petService.getPetTypeList()

        def result = petType.collect {
            [
                id: it.id,
                name: "${it.name}"
            ]
        }.sort { it.name }
        def petTypes = [petTypes: result]
        render(contentType: 'text/json', text: petTypes as JSON)
    }

    def getPetBreed(int petTypeId ) {


        def petBreed = petService.getPetBreedList(petTypeId)

        def result = petBreed.collect {
            [
                id: it.id,
                name: "${it.name}"
            ]
        }.sort { it.name }
        def petBreeds = [petBreeds: result]
        render(contentType: 'text/json', text: petBreeds as JSON)
    }

    def confirmNoorRatingsLoaded(CarQuote carQuote) {
        Provider noorProvider = Provider.load(InsuranceProviderEnum.NOOR_TAKAFUL.id)

        Map result = [
            noorQuotesLoaded    :   false
        ]
        if (carQuote.isRenewal) {
            Renewal renewal = Renewal.findByCarQuote(carQuote)
            if (renewal.product.providerId == noorProvider.id) {
                render result as JSON
                return
            }
        }

        List<String> noorSchemeCodes = noorApiService.getComprehensivePlanSchemeCodeList(carQuote.model)
        Product noorTPLProduct = Product.read(NoorRateV2Service.THIRD_PARTY_ID)
        if (noorTPLProduct.active) {
            String tplPlanCode = noorApiService.getSchemeCode(noorTPLProduct, null, VehicleTypeEnum.findById(carQuote.model.vehicleTypeId))
            if (tplPlanCode) {
                noorSchemeCodes += tplPlanCode
            }
        }

        if (noorSchemeCodes) {
            List<CarQuoteCover> carQuoteCovers = CarQuoteCover.findAllByQuoteAndProviderAndSchemeCodeInList(carQuote, noorProvider, noorSchemeCodes)

            if (carQuoteCovers.size() == noorSchemeCodes.size() && carQuoteCovers.any { !it.isDeleted }) {
                result.noorQuotesLoaded = true
            }
        }

        render result as JSON
    }


}
