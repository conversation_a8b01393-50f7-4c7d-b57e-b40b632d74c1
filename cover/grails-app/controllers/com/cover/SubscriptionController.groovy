package com.cover

import grails.converters.JSON

class SubscriptionController {

    def subscriptionService

    private static final String KEY = 'hussainminhajderek123345i345c4me'

    def webhook() {
        log.info "subs.webhook ${params}"
        if (params.key == KEY && request.method == 'POST') {
            def json = JSON.parse(params.mandrill_events)

            subscriptionService.parseWebhookRequest(json)

            render "Webhook Processed"
        } else {
            log.warn "Webhook called with invalid key ${params.key} - ${request.method}"
            render "Invalid key ${params.key}"
        }
    }

}
