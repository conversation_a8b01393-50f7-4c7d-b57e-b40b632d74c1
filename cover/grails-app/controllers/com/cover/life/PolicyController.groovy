package com.cover.life

import com.cover.api.FileUploadCommand
import com.cover.lifeInsuranceCommands.PostQuestionnaireCommand
import com.safeguard.*
import com.safeguard.life.LifePolicyDocument
import com.safeguard.life.LifeQuote
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.springframework.http.HttpStatus


class PolicyController {

    static namespace = "life"

    def lifeUtilService

    static allowedMethods = [
        renderUploadPolicyCancellationDocsPage: 'GET',
        uploadPolicyCancellationDocs: 'POST'
    ]

    def documentUploadService

    /**
     * Post questionnaire with Policy Start Date and to Received
     * @param cmd Post questionnaire object
     * @return
     */
    def postQuestionnaire(PostQuestionnaireCommand command) {
        log.info("policy.postQuestionnaire - entering with [cmd:${params.id}]")

        if (!params.id) {
            redirect(mapping: 'lifeIndex', params: [country: params.country, lang: params.lang])
            return
        }
        def quoteId
        try{
            // added try catch here in case of it could not decrpt the id
            quoteId = Long.parseLong(AESCryption.decrypt((String) params.id))
        }catch(Exception e){
            log.error("exception while getting id of quote, quoteId:${params.id}", e)
            redirect(mapping: "lifeIndex", params: [country: params.country, lang: params.lang])
            return
        }

        if(!quoteId){
            log.error("No Quote found., quoteId:${params.id}")
            redirect(mapping: "lifeIndex", params: [country: params.country, lang: params.lang])
            return
        }

        LifeQuote quote = LifeQuote.read(quoteId)

        if ( quote.paymentStatus == PaymentStatusEnum.PAID && quote.paymentMethod == PaymentMethodEnum.CREDITCARD
            || (quote.paymentStatus == PaymentStatusEnum.PENDING && quote.paymentMethod == PaymentMethodEnum.COD) ) {

            try {

                if (request.method == "GET") {
                    def documents = LifePolicyDocument.findAllByLifeQuoteAndIsDeleted(quote, false)
                    command.name = quote.name
                    command.correspondenceAddress = quote.correspondenceAddress
                    if(quote.beneficiaries.size() > 0){
                        command.beneficiaries = quote.getBeneficiaries().toList()
                    }

                    render(view: "/life/postQuestionnaire/uploadDocs", model: [quote: quote, quoteDocuments: documents, command: command, country: params.country, lang: params.lang])
                    return
                }else if(request.method == 'POST'){

                    try{
                        // add beneficiaries here and update life quote
                        lifeUtilService.addToPostQuestionnaireUpdate(quote.id, command)

                        render(view: "/life/postQuestionnaire/docsReceived", model: [country: params.country, lang: params.lang])
                    }
                    catch(Exception e){
                        log.error("exception while submitting post questionnaire to received, quoteId:${quote.id}", e)
                        redirect(mapping: "lifePostQuestions", params: [id: params.id, country: params.country, lang: params.lang])
                    }


                }

            } catch (Exception e) {
                log.error("exception while submitting quote beneficiaries, quoteId:${quote.id}", e)
                redirect(mapping: "lifeIndex", params: [country: params.country, lang: params.lang])
            }

            return

        } else if(quote.paymentStatus == PaymentStatusEnum.RECEIVED || quote.paymentStatus == PaymentStatusEnum.ISSUED){
            render(view: "/life/postQuestionnaire/uploadDocs", model: [quote: quote, command: command, country: params.country, lang: params.lang])
            return
        }else {
            redirect(mapping: "lifeIndex", params: [ country: params.country, lang: params.lang])
        }
    }

    /**
     * Upload document to the provided quote
     *
     * @param cmd
     * @return
     */
    def uploadPolicyDocs(FileUploadCommand cmd) {
        log.info("policy.uploadPolicyDocs - entering with [id:${params.id}, country:${params.country}]")

        def id = Long.parseLong(AESCryption.decrypt((String) params.id))
        LifeQuote quote = LifeQuote.get(id)
        if (!quote) {
                log.info "policy.uploadPolicyDocs - Quote not found, quoteId:${id}"
            render status: 404
            return
        }
        
        try {
            if (request.method == "GET") {
                renderUploadDocumentsView(quote, params)

            } else if (request.method == "POST") {
                if (cmd.validate()) {

                    def file = params.file

                    // make sure files were actually uploaded
                    if (!file) {
                        log.info("policy.uploadPolicyDocs - no file received to server, quoteId:${quote.id}")

                        render(status: HttpStatus.BAD_REQUEST.value())
                        return
                    }

                    def result = documentUploadService.savePolicyDocument(cmd.file, DocumentType.findByCode(cmd.docType),
                        quote, ProductTypeEnum.findByName(cmd.productType), true, false, null)

                    if(!result){
                        log.error("policy.uploadPolicyDocs - file is corrupted., quoteId:${quote.id}")
                        render(text: "Invalid File", contentType: "text/xml", encoding: "UTF-8")
                    }

                    def policyDocument = result.policyDocument

                    log.info("policy.uploadPolicyDocs - returning policyDocument:${policyDocument}")

                    render([message: "File Uploaded", path: policyDocument.fullPath, documentId: AESCryption.encrypt(policyDocument.id.toString())] as JSON)
                } else {
                    log.error("policy.uploadPolicyDocs - error validating file, quoteId:${quote.id}")

                    render(text: "Invalid File", contentType: "text/xml", encoding: "UTF-8")
                }
            }
        } catch (Exception e) {
            log.error("policy.uploadPolicyDocs - error caught uploading file, quoteId:${quote.id}", e)

            response.status = HttpStatus.INTERNAL_SERVER_ERROR.value()
            render(text: "<xml>some xml</xml>", contentType: "text/xml", encoding: "UTF-8")
//            renderUploadDocumentsView(quote, params)
        }
    }

    /**
     * Render view to upload documents
     *
     * @param quote
     * @param params
     * @return
     */
    def renderUploadDocumentsView(LifeQuote quote, def params) {
        def documents = LifePolicyDocument.get(quote, false)

        render(view: "/life/postQuestionnaire/uploadDocs",
            model: [quote: quote, quoteDocuments: documents,
                    id   : quote.id, country: params.country, lang: params.lang])
    }



    /**
     * Delete Life document
     *
     * @param id
     * @return
     */
    def deletePolicyDocs (String id) {
        def docId = Long.parseLong(AESCryption.decrypt(id))
        def quoteId = Long.parseLong((String)params.quoteId)
        log.info("checkout.deletePolicyDocs - quoteId: $quoteId")

        LifeQuote quote = LifeQuote.get(quoteId)

        try {
            LifePolicyDocument document = LifePolicyDocument.get(docId)

            if(!document){
                response.status = HttpStatus.NOT_FOUND.value()
                render "unauthorized"
            }

            //Allow only when document belongs to provided quote
            if (document.lifeQuote.id == quote.id) {

                documentUploadService.deleteDocumentOnly(docId, quote.id, ProductTypeEnum.LIFE)

                render "success"

            } else {
                response.status = HttpStatus.UNAUTHORIZED.value()
                render "unauthorized"
            }

        } catch (Exception e) {

            render(status: HttpStatus.INTERNAL_SERVER_ERROR.value())
        }
    }




}
