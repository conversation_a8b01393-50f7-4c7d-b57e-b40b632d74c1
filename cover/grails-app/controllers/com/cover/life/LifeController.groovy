package com.cover.life

import com.cover.common.commands.MarketingTrackingCommand
import com.cover.util.IConstant
import com.safeguard.DocumentTypeEnum
import com.safeguard.exception.LifeApiException
import grails.converters.JSON

class LifeController extends AbstractLifeApiExceptionHandler {

    static namespace = 'life-api'

    def productService

    def listProducts() {
        log.info("lifeProduct.listProducts")
        def products = productService.listProducts()
        render products
    }

    def getFirstForm() {
        log.info("lifeProduct.getFirstForm - entering with [params:${params}]")
        def form = productService.retrieveProductForm(params.productSlug)
        render form
    }

    def getAdditionalInfoForm() {
        log.info("lifeProduct.getAdditionalInfoForm - entering with [params:${params}]")
        def form = productService.retrieveAdditionalInfoForm(params.policyNumber)
        if (!form) return []
        render form
    }

    def submitFirstForm(FirstFormCommand firstFormCommand) {
        log.debug('in LifeProductController.submitFirstForm')
        def quotesForProductsList = productService.submitProductForm(params.productSlug, firstFormCommand as JSON)
        render quotesForProductsList
    }

    def submitSelectedQuote(SubmitSelectedQuoteCommand submitSelectedQuoteCommand) {
        log.debug('in LifeProductController.submitSelectedQuote')
        def result = productService.submitSelectedQuote(submitSelectedQuoteCommand as JSON, params.productSlug)
        render result
    }

    def submitDocumentsAndAdditionalData(SubmitAdditionalInformationCommand submitAdditionalInformationCommand) {
        log.info("lifeProduct.submitDocumentsAndAdditionalData - submitAdditionalInformationCommand:${submitAdditionalInformationCommand.toString()}")

        def result = productService.submitAdditionalData(submitAdditionalInformationCommand as JSON, params.productSlug)

        log.debug('in LifeProductController.submitDocumentsAndAdditionalData, result = ' + result.toString())
        render result
    }

    def savePolicyDocument(PolicyDocumentCommand policyDocumentCommand) {
        log.debug('in LifeProductController.savePolicyDocument')
        def result = productService.savePolicyDocument(policyDocumentCommand)
        log.debug("LifeProductController.savePolicyDocument() operation done, response: " + result.toString())
        render result as JSON
    }

    def submitSponsorProtectDocumentsAndAdditionalData(SubmitSponsorProtectAdditionalInfoCommand submitSponsorProtectAdditionalInfoCommand) {
        def result = productService.submitSponsorProtectAdditionalData(submitSponsorProtectAdditionalInfoCommand as JSON, params.productSlug)
        log.debug('in LifeProductController.submitSponsorProtectDocumentsAndAdditionalData, result = ' + result.toString())
        render result
    }

    def confirmPolicy(ConfirmCommand confirmCommand) {
        def result = productService.confirm(confirmCommand as JSON, params.policyNumber)
        render result
    }

    def isPolicyExisting() {
        def policyExisting = productService.isPolicyExisting(params.policyNumber)
        render policyExisting
    }

    def isQuoteValid() {
        def isQuoteValid = productService.isQuoteValid(params.policyNumber)
        render isQuoteValid
    }

    def initPayment() {
        def result = productService.initPayment(params.policyNumber)
        render result
    }

    def completePayment() {
        def result = productService.completePayment(params.policyNumber, params.token)
        render result
    }

    def getPolicySchedule() {
        def result = productService.getPolicySchedule(params.policyNumber)
        render result
    }

    def getPreVisitedQuoteInfo() {
        log.info("life.getPreVisitedQuoteInfo - params:${params}")
        def result = productService.getPreVisitedQuoteInfo(params.encQuoteId)
        render result
    }
}
