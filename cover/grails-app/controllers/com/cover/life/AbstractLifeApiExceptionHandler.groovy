package com.cover.life

import com.safeguard.exception.LifeApiException
import grails.converters.JSON
import org.springframework.http.HttpStatus

class AbstractLifeApiExceptionHandler {

    def handleLifeApiException(LifeApiException e) {
        // server side logging
        log.error("Server side exception: ", e)
        // send response to client
        response.status = e.statusCode.value
        def response = [status: e.statusCode.value(), error: e.message]
        render response as JSON
    }


}
