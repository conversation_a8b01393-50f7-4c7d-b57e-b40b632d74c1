package com.cover.life

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.lifeInsuranceCommands.LifeCheckoutCommand
import com.safeguard.AsyncEventConstants
import com.safeguard.life.LifeQuote
import com.c4m.payfort.util.PayfortResponse
import com.cover.payment.CheckoutService
import com.cover.util.IConstant
import org.springframework.context.i18n.LocaleContextHolder
import grails.converters.JSON
import com.safeguard.exception.LifeApiException

import java.nio.charset.StandardCharsets

class LifeCheckoutController extends AbstractLifeApiExceptionHandler {
    AbstractLifeApiExceptionHandler exceptionsHandler = new AbstractLifeApiExceptionHandler()
    static namespace = 'life-api'

    def lifeCheckoutService

    def prepareAnnualPaymentAuthorization(PaymentPrepareCommand paymentPrepareCommand) {
        // in: policy number + language + returnUrl
        // out: creditcard params + quoteVersion + payfortPaymentUrl
        // exceptions:
        log.debug("in prepareAnnualPaymentAuthorization: return_url = " + paymentPrepareCommand.return_url)
        render lifeCheckoutService.preparePaymentAuthorization(paymentPrepareCommand.policy_number, paymentPrepareCommand.language, paymentPrepareCommand.return_url) as JSON
    }



    def recieveTokenizationResponse(PaymentAuthCommand paymentAuthCommand){
        String domain = grailsApplication.config.getProperty("cover.domain")
        String path = domain + "/uae/en/life/authorize"
        log.debug("return_url : " + paymentAuthCommand.return_url)
        log.debug("merchant_reference : " + paymentAuthCommand.merchant_reference)
        log.debug("p_quote_v : " + paymentAuthCommand.p_quote_v)
        log.debug("status : " + paymentAuthCommand.status)
        log.debug("response_message : " + paymentAuthCommand.response_message)
        log.debug("token_name : " + paymentAuthCommand.token_name)
        log.debug("signature : " + paymentAuthCommand.signature)
        log.debug("language : " + paymentAuthCommand.language)
        log.debug("card_bin : " + paymentAuthCommand.card_bin)
        log.debug("service_command : " + paymentAuthCommand.service_command)
        log.debug("access_code : " + paymentAuthCommand.access_code)
        log.debug("merchant_identifier : " + paymentAuthCommand.merchant_identifier)
        log.debug("expiry_date : " + paymentAuthCommand.expiry_date)
        log.debug("card_holder_name : " + paymentAuthCommand.card_holder_name)
        log.debug("card_number : " + paymentAuthCommand.card_number)
        //log.debug("country : " + paymentAuthCommand.country)
        log.debug("response_code : " + paymentAuthCommand.response_code)
        //log.debug("lang : " + paymentAuthCommand.lang)
        //log.debug("customer_ip : " + paymentAuthCommand.customer_ip)
        //log.debug("return_url_3d : " + paymentAuthCommand.return_url_3d)
        StringBuilder sb = new StringBuilder()

        sb.append(path)
        String encodedReturnUrl = URLEncoder.encode(paymentAuthCommand.return_url, StandardCharsets.UTF_8.toString());




        sb.append("?return_url=" + encodedReturnUrl)
        sb.append("&merchant_reference=" + paymentAuthCommand.merchant_reference)
        sb.append("&p_quote_v=" + paymentAuthCommand.p_quote_v)
        sb.append("&status=" + paymentAuthCommand.status)
        sb.append("&response_message=" + paymentAuthCommand.response_message)
        sb.append("&token_name=" + paymentAuthCommand.token_name)
        sb.append("&signature=" + paymentAuthCommand.signature)
        sb.append("&language=" + paymentAuthCommand.language)
        sb.append("&card_bin=" + paymentAuthCommand.card_bin)
        sb.append("&service_command=" + paymentAuthCommand.service_command)
        sb.append("&access_code=" + paymentAuthCommand.access_code)
        sb.append("&merchant_identifier=" + paymentAuthCommand.merchant_identifier)
        sb.append("&expiry_date=" + paymentAuthCommand.expiry_date)
        sb.append("&card_holder_name=" + paymentAuthCommand.card_holder_name)
        sb.append("&card_number=" + paymentAuthCommand.card_number)
        //sb.append("&country=" + paymentAuthCommand.country)
        sb.append("&response_code=" + paymentAuthCommand.response_code)
        //sb.append("&lang=" + paymentAuthCommand.lang)
        //sb.append("&customer_ip=" + paymentAuthCommand.customer_ip)
        //sb.append("&return_url_3d=" + paymentAuthCommand.return_url_3d)

        path = sb.toString()

        log.debug("path to redirect to in recieveTokenizationResponse = " + path)

        redirect(url: path)
    }

    def recievePaymentSuccess(PaymentSuccessResponseCommand paymentSuccessResponseCommand){
        log.debug("In LifeCheckoutController.recievePaymentSuccess()")

        log.debug("p_c4me_fee : " + paymentSuccessResponseCommand.p_c4me_fee)
        log.debug("p_c4me_vat : " + paymentSuccessResponseCommand.p_c4me_vat)
        log.debug("policy_number : " + paymentSuccessResponseCommand.policy_number)
        log.debug("p_discount_id : " + paymentSuccessResponseCommand.p_discount_id)
        log.debug("p_discount : " + paymentSuccessResponseCommand.p_discount)
        log.debug("p_product_id : " + paymentSuccessResponseCommand.p_product_id)
        log.debug("p_total_price : " + paymentSuccessResponseCommand.p_total_price)
        log.debug("p_policy_price : " + paymentSuccessResponseCommand.p_policy_price)
        log.debug("p_quote_v : " + paymentSuccessResponseCommand.p_quote_v)
        log.debug("p_pprice_vat : " + paymentSuccessResponseCommand.p_pprice_vat)

        log.debug("amount : " + paymentSuccessResponseCommand.amount)
        log.debug("response_code : " + paymentSuccessResponseCommand.response_code)
        log.debug("card_number : " + paymentSuccessResponseCommand.card_number)
        log.debug("card_holder_name : " + paymentSuccessResponseCommand.card_holder_name)
        log.debug("signature : " + paymentSuccessResponseCommand.signature)
        log.debug("merchant_identifier : " + paymentSuccessResponseCommand.merchant_identifier)
        log.debug("access_code : " + paymentSuccessResponseCommand.access_code)
        log.debug("order_description : " + paymentSuccessResponseCommand.order_description)
        log.debug("payment_option : " + paymentSuccessResponseCommand.payment_option)
        log.debug("expiry_date : " + paymentSuccessResponseCommand.expiry_date)
        log.debug("customer_ip : " + paymentSuccessResponseCommand.customer_ip)
        log.debug("language : " + paymentSuccessResponseCommand.language)
        log.debug("eci : " + paymentSuccessResponseCommand.eci)
        log.debug("fort_id : " + paymentSuccessResponseCommand.fort_id)
        log.debug("command : " + paymentSuccessResponseCommand.command)
        log.debug("response_message : " + paymentSuccessResponseCommand.response_message)
        log.debug("merchant_reference : " + paymentSuccessResponseCommand.merchant_reference)
        log.debug("customer_email : " + paymentSuccessResponseCommand.customer_email)
        log.debug("currency : " + paymentSuccessResponseCommand.currency)
        log.debug("customer_name : " + paymentSuccessResponseCommand.customer_name)
        log.debug("status : " + paymentSuccessResponseCommand.status)

        String domain = grailsApplication.config.getProperty("cover.domain")
        String path = domain + "/uae/en/life/success"

        StringBuilder sb = new StringBuilder()
        sb.append(path)

        sb.append("?amount=" + paymentSuccessResponseCommand.amount)
        sb.append("&response_code=" + paymentSuccessResponseCommand.response_code)
        sb.append("&card_number=" + paymentSuccessResponseCommand.card_number)
        sb.append("&card_holder_name=" + paymentSuccessResponseCommand.card_holder_name)
        sb.append("&signature=" + paymentSuccessResponseCommand.signature)
        sb.append("&merchant_identifier=" + paymentSuccessResponseCommand.merchant_identifier)
        sb.append("&access_code=" + paymentSuccessResponseCommand.access_code)
        sb.append("&order_description=" + paymentSuccessResponseCommand.order_description)
        sb.append("&payment_option=" +  getNotNullValue(paymentSuccessResponseCommand.payment_option))
        sb.append("&expiry_date=" + paymentSuccessResponseCommand.expiry_date)
        sb.append("&customer_ip=" +  getNotNullValue(paymentSuccessResponseCommand.customer_ip))
        sb.append("&language=" +  getNotNullValue(paymentSuccessResponseCommand.language))
        sb.append("&eci=" +  getNotNullValue(paymentSuccessResponseCommand.eci))
        sb.append("&fort_id=" + paymentSuccessResponseCommand.fort_id)
        sb.append("&command=" +  getNotNullValue(paymentSuccessResponseCommand.command))
        sb.append("&response_message=" + paymentSuccessResponseCommand.response_message)
        sb.append("&merchant_reference=" + paymentSuccessResponseCommand.merchant_reference)
        sb.append("&customer_email=" + paymentSuccessResponseCommand.customer_email)
        sb.append("&currency=" + paymentSuccessResponseCommand.currency)
        sb.append("&customer_name=" + paymentSuccessResponseCommand.customer_name)
        sb.append("&status=" + paymentSuccessResponseCommand.status)

        sb.append("&p_c4me_fee=" +  getNotNullValue(paymentSuccessResponseCommand.p_c4me_fee))
        sb.append("&p_c4me_vat=" +  getNotNullValue(paymentSuccessResponseCommand.p_c4me_vat))
        sb.append("&policy_number=" +  paymentSuccessResponseCommand.policy_number)
        sb.append("&p_discount_id=" +  getNotNullValue(paymentSuccessResponseCommand.p_discount_id))
        sb.append("&p_discount=" +  getNotNullValue(paymentSuccessResponseCommand.p_discount))
        sb.append("&p_product_id=" +  getNotNullValue(paymentSuccessResponseCommand.p_product_id))
        sb.append("&p_total_price=" +  paymentSuccessResponseCommand.p_total_price)
        sb.append("&p_policy_price=" +  paymentSuccessResponseCommand.p_policy_price)
        sb.append("&p_quote_v=" +  paymentSuccessResponseCommand.p_quote_v)
        sb.append("&p_pprice_vat=" + getNotNullValue(paymentSuccessResponseCommand.p_pprice_vat))

        path = sb.toString()
        log.debug("path to redirect to in recievePaymentSuccess = " + path)
        redirect(url: path)
    }

    /**
     * Return a not null value
     * @param param
     * @return
     */
    def private getNotNullValue(def param) {
        if (param && param != "null") return param
        return ""
    }

    def handleReturnedAnnualPaymentAuthorization(PaymentAuthCommand paymentAuthCommand) {
        log.info("life.handleReturnedAnnualPaymentAuthorization - entering with [command:${paymentAuthCommand.toString()}, params:$params]")
        // in: payfort passed params + customer ip + returned url
        // out: true or false (optional: payment details such as transaction id .. etc) or threeDeeSecureUrl
        // exceptions:
        params["p_quote_v"]= paymentAuthCommand.p_quote_v
        params["response_code"]= paymentAuthCommand.response_code
        params["card_number"]= paymentAuthCommand.card_number
        params["card_holder_name"]= paymentAuthCommand.card_holder_name
        params["signature"]= paymentAuthCommand.signature
        params["merchant_identifier"]= paymentAuthCommand.merchant_identifier
        params["expiry_date"]= paymentAuthCommand.expiry_date
        params["access_code"]= paymentAuthCommand.access_code
        params["language"]= paymentAuthCommand.language
        params["service_command"]= paymentAuthCommand.service_command
        params["response_message"]= paymentAuthCommand.response_message
        params["merchant_reference"]= paymentAuthCommand.merchant_reference
        params["token_name"]= paymentAuthCommand.token_name
        params["return_url"]= paymentAuthCommand.return_url
        params["card_bin"]= paymentAuthCommand.card_bin
        params["status"]= paymentAuthCommand.status
        params["country"] = 'uae'
        params["lang"] = 'en'
        //
        log.debug("in handleReturnedAnnualPaymentAuthorization: return_url = " + paymentAuthCommand.return_url_3d)
        def result = lifeCheckoutService.handleReturnedAnnualPaymentAuthorization(params, paymentAuthCommand.customer_ip, 'life-frontend', paymentAuthCommand.return_url_3d)
        render result as JSON
    }

    def handleThreeDeeAnnualPaymentAuthorization(HandleThreeDeeCommand handleThreeDeeCommand) {
        // in: payfort 3d passed params
        // out: true or false (optional: payment details such as transaction id .. etc) or threeDeeSecureUrl
        // exception
        prepareThreeDeeParams(params, handleThreeDeeCommand)
        def result = lifeCheckoutService.handleThreeDeeAnnualPaymentAuthorization(params)
        render result as JSON
    }

    def prepareThreeDeeParams(def params, def handleThreeDeeCommand){
        params.remove("controller")
        params.remove("action")
        params.remove("policyNumber")
        params["p_c4me_fee"]= handleThreeDeeCommand.p_c4me_fee?: ""
        params["p_c4me_vat"]= handleThreeDeeCommand.p_c4me_vat?: ""
        params["p_discount_id"]= handleThreeDeeCommand.p_discount_id?: ""
        params["p_discount"]= handleThreeDeeCommand.p_discount?: ""
        params["p_product_id"]= handleThreeDeeCommand.p_product_id
        params["p_total_price"]= handleThreeDeeCommand.p_total_price
        params["p_policy_price"]= handleThreeDeeCommand.p_policy_price
        params["p_quote_v"]= handleThreeDeeCommand.p_quote_v
        params["p_pprice_vat"]= handleThreeDeeCommand.p_pprice_vat?: ""
        params["amount"]= handleThreeDeeCommand.amount
        params["response_code"]= handleThreeDeeCommand.response_code
        params["card_number"]= handleThreeDeeCommand.card_number
        params["card_holder_name"]= handleThreeDeeCommand.card_holder_name
        params["signature"]= handleThreeDeeCommand.signature
        params["merchant_identifier"]= handleThreeDeeCommand.merchant_identifier
        params["access_code"]= handleThreeDeeCommand.access_code
        params["order_description"]= handleThreeDeeCommand.order_description
        params["payment_option"]= handleThreeDeeCommand.payment_option
        params["expiry_date"]= handleThreeDeeCommand.expiry_date
        params["customer_ip"]= handleThreeDeeCommand.customer_ip
        params["language"]= handleThreeDeeCommand.language
        params["eci"]= handleThreeDeeCommand.eci
        params["fort_id"]= handleThreeDeeCommand.fort_id
        params["command"]= handleThreeDeeCommand.command
        params["response_message"]= handleThreeDeeCommand.response_message
        params["merchant_reference"]= handleThreeDeeCommand.merchant_reference
        params["authorization_code"]= handleThreeDeeCommand.authorization_code
        params["customer_email"]= handleThreeDeeCommand.customer_email
        params["token_name"]= handleThreeDeeCommand.token_name
        params["currency"]= handleThreeDeeCommand.currency
        params["customer_name"]= handleThreeDeeCommand.customer_name
        params["status"]= handleThreeDeeCommand.status
    }

}
