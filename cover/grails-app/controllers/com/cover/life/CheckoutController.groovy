package com.cover.life

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.lifeInsuranceCommands.LifeCheckoutCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CountryEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.life.LifeQuote
import com.safeguard.util.AESCryption
import org.springframework.context.i18n.LocaleContextHolder


class CheckoutController {
    static namespace = 'life'

    def lifeQuoteService
    def checkoutService
    def paymentService
    def paymentMethodService
    def utilService

    def lifeUtilService

    def paynow() {
        def lifeQuoteId = session[IConstant.LIFE_INSURANCE_QUOTEID]

        if (lifeQuoteId) {
            LifeQuote lifeQuote = LifeQuote.read(lifeQuoteId as Long)

            if (!lifeQuoteService.isEligibleForPayment(lifeQuote)) {
                flash.error = g.message(code:'checkout.quote.modified')
                redirect mapping: 'lifeInsuranceCheckoutIndex',
                    params: [country:params.country, lang:params.lang, lifeQuoteId: lifeQuote.id]
                return
            }

            checkoutService.clearQuoteWithFromPaymentDetail(lifeQuote, ProductTypeEnum.LIFE)

            def domain = grailsApplication.config.getProperty("cover.domain")
            def country = params.country
            def lang = params.lang

            String returnUrl = "$domain/$country/$lang/${ProductTypeEnum.LIFE.toString().toLowerCase()}/checkout/payment"

            String quoteVersion = lifeQuote.version.toString()

            def  (creditCardParams, installmentParams) =  paymentMethodService.getMerchantPageParams(
                lifeQuote.encodedrMerchantRef(), lifeQuote.currency, lifeQuote.totalPrice,
                "${returnUrl}?p_quote_v=${quoteVersion}", country, null)

            render view: "/life/checkout/paymentMethods",
                model: [lifeQuote:lifeQuote, creditCardParams:creditCardParams, installmentParams:installmentParams,
                        quoteVersion:quoteVersion, country: session[IConstant.SITE_COUNTRY]]
        }
    }

    /**
     * Step 2 - payment page
     * @param command
     * @return
     */
    def updateOrder(LifeCheckoutCommand command) {
        log.info("lifeCheckOut.updateOrder - command:${command}")


        LifeQuote quote = LifeQuote.get(command.lifeQuote.id)

        if (!quote.isNotProcessed()) {
            flash.error = g.message(code: 'error.alreadyProcessed')
            redirect mapping:"lifeIndex", params: [lang: params.lang, country: params.country]
            return
        }

        session[IConstant.LIFE_INSURANCE_QUOTEID] = command.lifeQuote.id

        lifeQuoteService.updateLifeQuoteRating(command.lifeQuote.id, command.product.id, command.discountCode,
            PaymentStatusEnum.PENDING, false)

        session[IConstant.LIFE_INSURANCE_QUOTEID] = command.lifeQuote.id

        redirect mapping: 'lifeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]

    }

    /**
     * Payfort redirect to this action upon credit card form submission.
     */
    def payment() {
        log.info("checkout.payment - entering with params:$params")
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        if (isSecured) {

            LifeQuote lifeQuote = LifeQuote.findById(LifeQuote.decodeMerchantRef(params.merchant_reference))

            if (PayfortStatusEnum.INVALID_REQUEST.toString().equals(params.status)) {
                flash.error = params.response_message
                redirect mapping: 'lifeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]
                return
            }

            try {

                if (lifeQuote.isModified(params.p_quote_v)) {
                    flash.error = g.message(code:'checkout.quote.modified')
                    log.error("life.checkout.payment - quote was modified:${lifeQuote.id} ")
                    redirect mapping: 'lifeInsuranceCheckoutIndex',
                        params: [country:params.country, lang:params.lang,
                                 productId:lifeQuote.productId, lifeQuoteId:lifeQuote.id]
                    return
                }

                PayfortResponse payfortResponse = paymentService.process(params, PayfortCommandEnum.AUTHORIZATION,
                    utilService.getClientIp(request))


                session[IConstant.LIFE_CHECKOUT_QUOTE_ID] = lifeQuote.id

                //This is important so that we know incase of failed transaction it was installments
                if (payfortResponse.isInstallments) {
                    paymentService.setInstallments(lifeQuote, payfortResponse.numberOfInstallments)
                }

                if (payfortResponse.isThreeDeeSecure) {
                    render view: '/car/checkout/_redirect', model: [url:payfortResponse.threeDeeSecureUrl,
                                                                    quote:lifeQuote,
                                                                    lang: LocaleContextHolder.locale.language,
                                                                    country: session[IConstant.SITE_COUNTRY]]
                    return
                } else if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(payfortResponse.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(payfortResponse.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)
                    log.debug(".checkout.payment Redirect user to thankyou for merchantRef: ${params.merchant_reference}")
                    redirect mapping: 'lifeInsuranceCheckoutThankyou', params: [country: params.country, lang: params.lang]
                    return
                }
                //pushover service sending sms in-case error to dev team
                String failureMessage = payfortResponse.responseMessage  +
                    (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                notify AsyncEventConstants.LIFE_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage,
                                                                              quoteId: lifeQuote.id]

                flash.error = payfortResponse.responseMessage

            } catch (Exception exp) {
                log.error("Exception:", exp)
                flash.error = g.message(code:'checkout.general.error')
            }
        } else {
            log.error(".checkout.payment **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code:'checkout.general.error')
        }

        redirect mapping: 'lifeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]
    }

    /**
     * All offline payment method submit to this action.
     * Note: Currently we support COD only. 21.Jun.2016
     */
    def order() {
        String method = params.paymentMethod
        def lifeQuoteId = params.getLong('quoteId')

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.findPaymentMethod(method)

        if (!paymentMethodEnum) {
            log.warn("Invalid paymentmethod passed: ${params.paymentMethod}")
            flash.message = g.message(code:"default.general.error")
            redirect mapping: 'lifeInsuranceCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        LifeQuote lifeQuote = LifeQuote.read(lifeQuoteId as Long)

        if (!lifeQuote.isNotProcessed()) {
            log.warn(".checkout.order quote is processed -> ${lifeQuote?.id}")
            flash.message = g.message(code:"default.general.error")
            redirect mapping:"lifeInsuranceCheckoutIndex", params: [country: params.country, lang: params.lang]
            return
        }

        if (lifeQuote.isModified(params.p_quote_v)) {
            flash.error = g.message(code:'checkout.quote.modified')
            log.error("life.checkout.order - quote was modified:${lifeQuote.id} ")
            redirect mapping: 'lifeInsuranceCheckoutIndex',
                params: [country:params.country, lang:params.lang,
                         productId:lifeQuote.productId, lifeQuoteId:lifeQuote.id]
            return
        }

        checkoutService.offlinePayment(lifeQuote, paymentMethodEnum)

        flash.lifeQuoteId = lifeQuoteId

        redirect mapping: 'lifeInsuranceCheckoutThankyou', params: [country: params.country, lang: params.lang]
    }

    /**
     * Payment success action
     */
    def thankyou () {

        def lifeQuoteId = flash.lifeQuoteId

        log.debug(".checkout.thankyou entering with lifeQuoteId: ${lifeQuoteId}")

        //if params are null redirect to lifeInsurance page
        if (!lifeQuoteId) {
            redirect mapping: 'lifeInsuranceCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        LifeQuote lifeQuote = LifeQuote.get(lifeQuoteId as Long)

        if(!lifeQuote){
            redirect mapping: 'lifeInsuranceCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        String postQuestionnaireLink = lifeUtilService.getPostQuestionnaireLink(lifeQuote, params.lang)

        log.debug(".checkout.thankyou CarQuote found with id ${lifeQuote.id}")

        notify AsyncEventConstants.LIFE_QUOTE_PURCHASED, [lifeQuoteId: lifeQuote.id, lang: LocaleContextHolder.locale.language, postQuestionnaireLink: postQuestionnaireLink]
        session.invalidate()

        def newSession = request.getSession()
        LocaleContextHolder.setLocale(new Locale(params.lang))
        newSession.'org.springframework.web.servlet.i18n.SessionLocaleResolver.LOCALE' = LocaleContextHolder.locale
        newSession[IConstant.SITE_COUNTRY] = CountryEnum.findCountry(params.country)
        newSession[IConstant.LIFE_INSURANCE_DETAILS_NAME] = lifeQuote.name
        newSession[IConstant.LIFE_INSURANCE_DETAILS_MOBILE] = lifeQuote.mobile
        newSession[IConstant.LIFE_INSURANCE_QUOTEID] = lifeQuote.id

        render view: '/life/checkout/thankyou', model:[lifeQuote:lifeQuote, session: newSession, postQuestionnaireLink: postQuestionnaireLink,
                                                       country: newSession[IConstant.SITE_COUNTRY]]
    }

    /**
     * 3d Secure returns to this action
     */
    def success() {
        log.info ".life.checkout.controller.success ${params}"
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        def lifeQuoteId = LifeQuote.decodeMerchantRef(params.merchant_reference)

        LifeQuote quote = LifeQuote.read(lifeQuoteId)

        flash.lifeQuoteId = quote.id

        if (isSecured) {
            if (quote && quote.isNotProcessed()) {
                paymentService.savePaymentResponse(quote, params)

                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)

                    log.debug(".life.checkout.success Redirect user to thankyou for quoteId: ${quote.id}")
                    //All good? redirect user to thankyou page
                    redirect mapping: 'lifeInsuranceCheckoutThankyou', params: [country: params.country, lang: params.lang]
                    return
                } else {
                    //pushover service sending sms in-case error to dev team
                    String failureMessage = params.response_message +
                        (params.acquirer_response_message ? " - " + params.acquirer_response_message : "")
                    notify AsyncEventConstants.LIFE_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage, quoteId: quote.id]
                    session[IConstant.LIFE_CHECKOUT_QUOTE_ID] = quote.id
                    flash.error = params.response_message
                    log.error "#### LIFE ERROR #### -> ${params.response_message} for ${quote.id} with status-> ${params.status} #### ERROR ####"
                }
            } else {
                log.warn(".life.checkout.success quote is isProcessed -> ${quote?.id}")
                log.debug(".life.checkout.success.is.processed still redirecting to thankyou")
                redirect mapping: 'lifeInsuranceCheckoutThankyou', params: [country: params.country, lang: params.lang]
                return
            }

        } else {
            log.error(".life.checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code:'checkout.general.error')
            redirect mapping: 'lifeInsuranceCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        redirect mapping: 'lifeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]
    }
}
