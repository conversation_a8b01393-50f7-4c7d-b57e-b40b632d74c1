package com.cover.gadget

import com.safeguard.*
import com.safeguard.gadget.*
import com.cover.gadget.GadgetQuoteService

class GadgetController {
    static namespace = 'gadget'
    def gadgetQuoteService

    def index() {
        if(params.country == 'uae') {
            if(request.method == 'POST') {
                gadgetQuoteService.saveQuote(params)
                redirect mapping: "gadgetThankyou", params: [lang: params.lang, country: params.country]
                return
            }else {
                render view: '/gadget/index', model: [lang: params.lang, country: params.country]
                return
            }
        } else {
            render view: '/common/notFound'
        }
    }

    def thankyou() {
        if(params.country == 'uae') {
            render view: '/gadget/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render view: '/common/notFound'
            return
        }
    }

}
