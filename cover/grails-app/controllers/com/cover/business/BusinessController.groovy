package com.cover.business;

import com.safeguard.*
import com.safeguard.business.*
import com.cover.business.BusinessQuoteService

class BusinessController {
    static namespace = 'business'
    def businessQuoteService

    def index() {
        if(params.country == 'uae') {
            if(request.method == 'POST') {
                businessQuoteService.saveQuote(params)
                redirect mapping: "businessThankyou", params: [lang: params.lang, country: params.country]
                return
            }else {
                render view: '/business/index', model: [lang: params.lang, country: params.country]
                return
            }
        } else {
            render view: '/common/notFound'
        }
    }

    def thankyou() {
        if(params.country == 'uae') {
            render view: '/business/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render view: '/common/notFound'
            return
        }
    }

}
