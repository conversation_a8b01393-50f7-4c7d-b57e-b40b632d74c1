package com.cover

import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteAddon

class EmailTestController {

    def index(Long id) {
        def data = CarQuote.get(id)
        String lang = params.lang?: "en"

        String htmlView = "/car/emails/quotePurchased"

        render view: htmlView, model: [
            quote: data,
            locale: lang,
            dir: lang == 'en' ? 'ltr' : 'rtl',
            imageCode: "compareit4meLogo.url"
        ]
    }

    def installment(Long id) {
        def data = getData(id)
        String lang = params.lang?: "en"

        String htmlView = "/car/emails/quotePurchased"

        render view: htmlView, model: [
            quote: data.carQuote,
            addons:data.addonList,
            locale: lang,
            dir: data.lang == 'en' ? 'ltr' : 'rtl',
            imageCode: "compareit4meLogo.url"
        ]
    }

    def cod(Long id) {
        def data = getData(id)
        String lang = params.lang?: "en"

        String htmlView = "/car/emails/codQuotePurchased"

        render view: htmlView, model: [
            quote: data.carQuote,
            addons:data.addonList,
            locale: lang,
            dir: data.lang == 'en' ? 'ltr' : 'rtl',
            imageCode: "compareit4meLogo.url"
        ]
    }

    private getData (Long id) {
        CarQuote carQuote = CarQuote.read(id)
        String lang = "en"

        CarQuoteAddon[] addons = CarQuoteAddon.findAllByCarQuote(carQuote)
        List addonList = []

        addons.collect {
            def addon = [:]
            if ('ar'.equalsIgnoreCase(lang)) {
                addon.label = it.addonTranslation.displayAr
            } else {
                addon.label = it.addonTranslation.labelEn
            }

            addon.price = it.price
            addonList.add(addon)
        }

        [carQuote: carQuote, addins: addons, lang: lang, country: params.country]
    }
}
