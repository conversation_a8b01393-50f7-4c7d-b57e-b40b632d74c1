package com.cover

import grails.converters.JSON

/**
 * Send fake response to any request on specified action whose User-Agent header is in blacklist
 */
class ScraperInterceptor {

    def messageSource

    ScraperInterceptor() {
        match(controller: ~/(ajax)/, action:'updateInsuranceRange')
    }

    boolean before() {

        grailsApplication.config.ajax.blacklist.userAgents.each { String userAgent ->

            if (!request.getHeader("User-Agent") || request.getHeader("User-Agent").indexOf(userAgent) != -1) {
                Random random = new Random()
                Integer fakeMinValuation = random.nextInt(10 ** 5)
                Integer fakeMaxValuation = fakeMinValuation + (fakeMinValuation * 10)/100

                def resp = [label:messageSource.getMessage("funnel.step1.valuationlbl",
                    ['AED', fakeMinValuation, fakeMaxValuation ].toArray(), new Locale(params.lang ?: 'en')), minimum:fakeMinValuation]

                render resp as <PERSON><PERSON><PERSON>
                return false
            }
        }


        true
    }

    boolean after() { true }

    void afterView() {
        // no-op
    }
}
