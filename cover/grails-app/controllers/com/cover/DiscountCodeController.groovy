package com.cover

import com.safeguard.DiscountCodeUsage
import com.safeguard.car.DiscountCode
import grails.web.RequestParameter

class DiscountCodeController {

    def messageSource
    def utilService

    def usage(@RequestParameter('code') String codeStr) {
        List<DiscountCodeUsage> usageList = []

        DiscountCode discountCode = DiscountCode.findByCode(codeStr)
        if (discountCode) {
            usageList = DiscountCodeUsage.findAllByDiscountCode(discountCode)
        } else {
            Locale locale = utilService.convertToLocale(params.lang)
            flash.error = messageSource.getMessage('discount.code.invalid', [].toArray(), locale)
        }

        render view: "/common/discountCodeUsage", model: [
            usageList:usageList,
            discountCode: discountCode,
            country: params.country, lang: params.lang
        ]
    }
}
