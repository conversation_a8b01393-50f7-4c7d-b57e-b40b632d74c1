package com.cover.motorbike
import com.safeguard.*
import com.safeguard.motorbike.*
import com.cover.motorbike.MotorbikeService


class MotorbikeController {
    static namespace = 'motorbike'
    def motorbikeService

    def index() {
        if(params.country == 'uae') {
            if(request.method == 'POST') {
                motorbikeService.saveQuote(params)
                redirect mapping: "motorbikeThankyou", params: [lang: params.lang, country: params.country]
                return
            }else {
                render view: '/motorbike/index', model: [lang: params.lang, country: params.country]
                return
            }
        } else {
            render view: '/common/notFound'
        }
    }

    def thankyou() {
        if(params.country == 'uae') {
            render view: '/motorbike/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render view: '/common/notFound'
            return
        }
    }

}
