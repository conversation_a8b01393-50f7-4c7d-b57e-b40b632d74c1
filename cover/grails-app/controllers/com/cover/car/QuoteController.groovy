package com.cover.car

import com.cover.common.CommonQuoteService
import com.safeguard.*
import com.safeguard.car.CarPcr
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteDetail
import com.safeguard.security.AuthenticationToken
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.apache.commons.codec.DecoderException
import org.apache.commons.validator.routines.EmailValidator
import org.springframework.http.HttpStatus

/**
 * API for getting Quotes.
 * <AUTHOR>
 */
class QuoteController {

    static responseFormats = ['json']

    static namespace = "car"

    def kwtQuoteService
    def lbnQuoteService
    def egyQuoteService
    def quoteService
    def commonQuoteService
    def cookieService

    /**
     * Home page form submits to this action
     */
    def index(Long id) {

        def ratings
        def quote = CarQuote.read(id.toInteger())
        String countryCode = quote.quoteCountry.code.toLowerCase()

        if (countryCode == CountryEnum.UAE.dfp) {
            (ratings, quote) = quoteService.getRatings(id.toInteger(), true)
        } else if (countryCode == CountryEnum.LBN.dfp) {
            (ratings, quote) = lbnQuoteService.getRatings(id.toInteger(), true)
        } else if (countryCode == CountryEnum.KWT.dfp) {
            (ratings, quote) = kwtQuoteService.getRatings(id.toInteger(), true)
        } else if (countryCode == CountryEnum.EGYPT.dfp) {
            (ratings, quote) = egyQuoteService.getRatings(id.toInteger(), true)
        }

        ratings.sort { a, b ->
            // Compare by type and then by premium
            a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
        }

        render ratings as JSON
    }

    def requestPolicyCancellation(String email) {
        log.info("com.cover.car.PolicyController#requestPolicyCancellation - called with params:$params")
        email = email.trim()

        String status

        if (!EmailValidator.getInstance().isValid(email)) {
            status = 'invalidEmail'
        } else {
            User user = User.findByEmail(email)

            if (!user) {
                status = 'unknownEmail'
            } else {
                List<CarQuoteDetail> carQuoteDetailList = quoteService.getActiveCarQuoteDetailsList(user)

                if (carQuoteDetailList.size()) {
                    quoteService.handlePolicyCancellationRequest(carQuoteDetailList, user, params.country, params.lang)
                    status = 'ok'
                } else {
                    status = 'noPolicies'
                }
            }
        }

        def response = [status: status]
        render response as JSON
    }

    def confirmPolicyCancellation(String uuid) {
        log.info("com.cover.car.PolicyController#confirmPolicyCancellation - called with params: $params")

        log.debug("Encrypted uuid of the cancellation request is: $uuid")
        if (!uuid) {
            throw new RuntimeException("Request parameter 'uuid' is required")
        }

        log.debug("Trying to decrypt encrypted uuid")
        try {
            uuid = AESCryption.decrypt(uuid)
        } catch (DecoderException e) {
            throw new RuntimeException("Request parameter 'uuid' is invalid", e)
        }

        log.debug("Successfully decrypted encrypted uuid. Decrypted uuid: $uuid")

        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)

        if (!cancellationRequest) {
            throw new RuntimeException("There is no policy cancellation request corresponding to provided 'uuid'(value = $uuid)")
        }

        CarQuote carQuote = cancellationRequest.quote
        CarQuoteDetail carQuoteDetail = CarQuoteDetail.findByQuote(carQuote)
        CrmStatusEnum crmStatus = carQuoteDetail.crmStatus

        if (cancellationRequest.statusId == CarPcrStatusEnum.DRAFT.id) {
            if (crmStatus == CrmStatusEnum.ISSUED) {
                commonQuoteService.confirmPolicyCancellation(cancellationRequest, ProductTypeEnum.CAR, params.country, params.lang)

                redirect mapping: 'uploadPolicyCancellationDocs', params: [
                    uuid: AESCryption.encrypt(cancellationRequest.uuid),
                    country: params.country,
                    lang: params.lang
                ]

                return

            } else if (carQuote.paidDate && !(crmStatus in [CrmStatusEnum.PENDING_CANCEL, CrmStatusEnum.CANCELLATION_DOCS_PENDING,
                                                            CrmStatusEnum.CANCELLATION_DOCS_RECEIVED, CrmStatusEnum.CANCELLATION_PROCESSED])) {

                commonQuoteService.confirmPolicyCancellation(cancellationRequest, ProductTypeEnum.CAR, params.country, params.lang)

            } else {
                // normally should not happen
                throw new RuntimeException("Unable to confirm Car Policy Cancellation Request. Cancellation Request ID: " +
                    "$cancellationRequest.id, Cancellation Request Status: ${CarPcrStatusEnum.getById(cancellationRequest.statusId).toString()}. " +
                    "Quote ID: $carQuote.id, Crm Status: ${crmStatus.toString()}, Payment Status: ${carQuote.paymentStatus.toString()}")
            }

        }

        render view: "/common/policyCancellationConfirmed", model: [
            cancelReqId: cancellationRequest.id,
            cancelReqStatusId: cancellationRequest.statusId,
            crmStatus: crmStatus,
            quote: carQuote,
            country: params.country,
            lang: params.lang,
            insuranceProvider: carQuote.product.provider.nameEn
        ]
    }

    /**
     * Create a request and also confirms the policy cancellation.
     * Amalgamation of requestPolicyCancellation & confirmPolicyCancellation service
     * @param encryptedQuoteId
     * @return
     */
    def requestConfirmPolicyCancellation(String quoteId) {
        log.info("quote.requestConfirmPolicyCancellation - entering with quoteId:${quoteId}")

        Locale locale = new Locale(params.lang)

        String accessToken = cookieService.getCookie('access_token')

        User user = null
        String errorMessage = null

        if (accessToken) {
            AuthenticationToken authenticationToken = AuthenticationToken.findByToken(accessToken)
            if (authenticationToken) {
                user = User.findByEmail(authenticationToken.username)
            }
        }

        if (!accessToken || !user) {
            response.status = HttpStatus.UNAUTHORIZED.value()
            errorMessage = g.message(code:"cancellation.unauthorized", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        }

        CarQuote quote = null
        if (quoteId) {
            Long id = AESCryption.decrypt(quoteId)?.toLong()
            if (id) {
                quote = CarQuote.read(id)
            }
        }

        if (!quote) {
            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            errorMessage = g.message(code:"cancellation.quote.notFound", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        } else if (quote.user != user) {
            response.status = HttpStatus.UNAUTHORIZED.value()
            errorMessage = g.message(code:"cancellation.unauthorized", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        }

        log.info("quote.requestConfirmPolicyCancellation - user:${user?.id}, quote:${quote?.id}")

        //Make sure the quote is not already requested or processed for cancellation
        List<CarQuoteDetail> carQuoteDetailList = quoteService.getActiveCarQuoteDetailsList(user, quote)
        log.info("quote.requestConfirmPolicyCancellation - list:$carQuoteDetailList")
        String cancellationRequestUUID = null

        if (carQuoteDetailList.size()) {
            log.info("quote.requestConfirmPolicyCancellation - list:$carQuoteDetailList - cancellation request creation")

            quoteService.handlePolicyCancellationRequest(carQuoteDetailList, user, params.country, params.lang, false)
            log.info("quote.requestConfirmPolicyCancellation - cancellation requestuuid: ${carQuoteDetailList.cancelReqUuid}")

            cancellationRequestUUID = carQuoteDetailList[0].cancelReqUuid
        }

        //Possibly, Quote is already requested for cancellation or processed.
        if (!cancellationRequestUUID) {
            log.info("quote.requestConfirmPolicyCancellation - request not created. Finding existing one, quote:${quote.id}")

            //Find a non processed request
            CarPcr cancellationRequest = CarPcr.findByQuoteIdAndStatusNotIn(
                quote.id,
                [CarPcrStatusEnum.WITHDRAWN_BY_CUSTOMER.id, CarPcrStatusEnum.WITHDRAWN_INTERNALLY.id, CarPcrStatusEnum.EXPIRED.id,
                 CarPcrStatusEnum.REJECTED.id, CarPcrStatusEnum.CANCELLATION_PROCESSED.id]
            )
            cancellationRequestUUID = cancellationRequest?.uuid
        }

        //Found one non processed cancellation request
        if (cancellationRequestUUID) {
            log.info("quote.requestConfirmPolicyCancellation - cancellationRequestUUID:$cancellationRequestUUID, quote:${quote?.id}")

            String encryptedUuid = AESCryption.encrypt(cancellationRequestUUID)

            log.info("quote.requestConfirmPolicyCancellation - uuid:${cancellationRequestUUID}, redirecting to confirm cancellation")

            confirmPolicyCancellation(encryptedUuid)
            return

        } else {
            //Possibly Quote is already processsed and hence is not valid for cancellation
            errorMessage = g.message(code:"cancellation.notValid", locale:locale)
        }

        response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
        render view: "/common/errorMessage", model: [errorMessage:errorMessage]
        return

    }
}
