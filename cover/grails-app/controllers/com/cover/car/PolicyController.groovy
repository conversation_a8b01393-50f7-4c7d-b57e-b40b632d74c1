package com.cover.car

import com.cover.api.FileUploadCommand
import com.cover.car.commands.CarPcrDocsCommand
import com.cover.car.commands.CarPcrInsurerReviewCommand
import com.cover.car.commands.ConditionalQuestionsCommand
import com.cover.car.commands.UpdatePolicyCommand
import com.cover.util.IConstant
import com.safeguard.*
import com.safeguard.car.CarPcr
import com.safeguard.car.CarPcrDocumentGroup
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteCallRequest
import com.safeguard.car.CarQuoteConditionalQuestionsValues
import com.safeguard.car.CarQuoteDetail
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.converters.JSON
import org.apache.commons.codec.DecoderException
import org.apache.tika.config.TikaConfig
import org.apache.tika.io.TikaInputStream
import org.apache.tika.metadata.Metadata
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.http.HttpStatus
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.util.HtmlUtils

class PolicyController {

    static namespace = "car"

    static allowedMethods = [
        renderUploadPolicyCancellationDocsPage: 'GET',
        uploadPolicyCancellationDocs: 'POST'
    ]

    def carPolicyCancellationService
    def documentUploadService
    def policyService
    def policySgService
    def commonUtilService
    def sessionService

    def renderUploadPolicyCancellationDocsPage(String uuid) {
        log.info("com.cover.car.PolicyController#renderUploadPolicyCancellationDocsPage - called with params: $params")

        log.debug("Encrypted uuid of the cancellation request is: $uuid")
        if (!uuid) {
            throw new RuntimeException("Request parameter 'uuid' is required")
        }

        log.debug("Trying to decrypt encrypted uuid")
        try {
            uuid = AESCryption.decrypt(uuid)
        } catch (DecoderException e) {
            throw new RuntimeException("Request parameter 'uuid' is invalid, most probably it has been altered by the user", e)
        }

        log.debug("Successfully decrypted encrypted uuid. Decrypted uuid: $uuid")

        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)
        CarQuote carQuote = cancellationRequest.quote
        CarQuoteDetail carQuoteDetail = CarQuoteDetail.findByQuote(carQuote)
        CrmStatusEnum crmStatus = carQuoteDetail.crmStatus

        if (!cancellationRequest) {
            throw new RuntimeException("There is no policy cancellation request corresponding to provided 'uuid'(value = $uuid)")
        }

        if (cancellationRequest.statusId in [CarPcrStatusEnum.DOCS_PENDING.id, CarPcrStatusEnum.DOCS_UPDATE_PENDING.id]) {
            render view: "/car/policy/policyCancellationDocumentUpload", model: [
                quote           : cancellationRequest.quote,
                cancelReqId     : cancellationRequest.id,
                uuid            : AESCryption.encrypt(cancellationRequest.uuid),
                country         : params.country,
                lang            : params.lang
            ]
        } else if (cancellationRequest.statusId == CarPcrStatusEnum.DRAFT.id) {
            // normally should not happen
            throw new RuntimeException("An attempt has been made to navigate to 'Upload Car Policy Cancellation Document(s)' " +
                "page but Cancellation Request Status is ${CarPcrStatusEnum.DRAFT.toString()}. " +
                "Quote ID: $carQuote.id, Crm Status: ${crmStatus.toString()}")
        } else {
            redirect mapping: 'renderPolicyCancellationDocsUploadedPage', params: [
                uuid   : AESCryption.encrypt(uuid),
                lang   : params.lang,
                country: params.country
            ]
        }
    }

    def renderPolicyCancellationDocsUploadedPage(String uuid) {
        log.info("com.cover.car.PolicyController#renderPolicyCancellationDocsUploadedPage - called with params: $params")

        log.debug("Encrypted uuid of the cancellation request is: $uuid")
        if (!uuid) {
            throw new RuntimeException("Request parameter 'uuid' is required")
        }

        log.debug("Trying to decrypt encrypted uuid")
        try {
            uuid = AESCryption.decrypt(uuid)
        } catch (DecoderException e) {
            throw new RuntimeException("Request parameter 'uuid' is invalid, most probably it has been altered by the user", e)
        }

        log.debug("Successfully decrypted encrypted uuid. Decrypted uuid: $uuid")

        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)
        CarQuote carQuote = cancellationRequest.quote
        CarQuoteDetail carQuoteDetail = CarQuoteDetail.findByQuote(carQuote)
        CrmStatusEnum crmStatus = carQuoteDetail.crmStatus

        if (!cancellationRequest) {
            throw new RuntimeException("There is no policy cancellation request corresponding to provided 'uuid'(value = $uuid)")
        }

        if (cancellationRequest.statusId in [CarPcrStatusEnum.DRAFT.id, CarPcrStatusEnum.DOCS_PENDING.id]) {
            // normally should not happen
            throw new RuntimeException("An attempt has been made to navigate to 'Car Policy Cancellation Document(s) Uploaded' " +
                "page but Cancellation Request Status is ${CarPcrStatusEnum.getById(cancellationRequest.statusId).toString()}. " +
                "Quote ID: $carQuote.id, Crm Status: ${crmStatus.toString()}")
        }

        render view: "/common/policyCancellationDocumentsUploaded", model: [
            cancelReqId      : cancellationRequest.id,
            cancelReqStatusId: cancellationRequest.statusId,
            crmStatus        : crmStatus,
            quote            : carQuote,
            insuranceProvider: carQuote.product.provider.nameEn
        ]
    }

    def uploadPolicyCancellationDocs(CarPcrDocsCommand cmd) {
        log.info("com.cover.car.PolicyController#uploadPolicyCancellationDocs - called with params: $params")

        if (cmd.hasErrors()) {
            // TODO: use AbstractCommand#concatenateAllErrors(...) method here after that code is merged to this branch
            throw new RuntimeException("com.cover.car.PolicyController#uploadPolicyCancellationDocs has been called with " +
                "invalid request parameters. Errors: ${cmd.errors.toString()}")
        }

        String uuid = AESCryption.decrypt(cmd.uuid)

        log.debug("Successfully decrypted encrypted uuid. Decrypted uuid: $uuid")

        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)
        CarQuote carQuote = cancellationRequest.quote
        CarQuoteDetail carQuoteDetail = CarQuoteDetail.findByQuote(carQuote)
        CrmStatusEnum crmStatus = carQuoteDetail.crmStatus

        if (!cancellationRequest) {
            throw new RuntimeException("There is no policy cancellation request corresponding to provided 'uuid'(value = $uuid)")
        }

        int cancelReqStatusId = cancellationRequest.statusId

        if (cancelReqStatusId in [CarPcrStatusEnum.DOCS_PENDING.id, CarPcrStatusEnum.DOCS_UPDATE_PENDING.id]) {
            CarPcrReasonEnum reason = CarPcrReasonEnum.getById(cmd.cancellationReasonId)

            List<FileAndDocTypeId> fileAndDocTypeIdList = []
            List<String> namesOfFilesWithInvalidMimeTypes = []

            cmd.fileAndDocTypeIdList.each { FileAndDocTypeId fileAndDocTypeId ->
                if (fileAndDocTypeId.file && !fileAndDocTypeId.file.isEmpty()) { // some files can be null as we can have empty file inputs in UI
                    String invalidMimeType = validateCarPcrDocMimeType(fileAndDocTypeId.file) // will be non-null only when validation fails
                    if (invalidMimeType != null) {
                        namesOfFilesWithInvalidMimeTypes.add(fileAndDocTypeId.file.originalFilename)
                        log.info("User uploaded cancellation document having disallowed mime-type of '$invalidMimeType'.")
                    }

                    fileAndDocTypeIdList.add(fileAndDocTypeId)
                }
            }

            if (namesOfFilesWithInvalidMimeTypes.isEmpty()) {
                policyService.processPolicyCancellationDocuments(cancellationRequest, reason, fileAndDocTypeIdList)
            } else {
                render([status: "invalid-mime-type", namesOfFilesWithInvalidMimeTypes: namesOfFilesWithInvalidMimeTypes] as JSON)
                return
            }

            flash.justUploaded = true
        } else if (cancellationRequest.statusId == CarPcrStatusEnum.DRAFT.id) {
            // normally should not happen
            throw new RuntimeException("An attempt has been made to upload Car Policy Cancellation document(s) but " +
                "Cancellation Request Status is ${CarPcrStatusEnum.DRAFT.toString()}. " +
                "Quote ID: $carQuote.id, Crm Status: ${crmStatus.toString()}")
        }

        render([status: "ok", redirectUrl: g.createLink(mapping: "renderPolicyCancellationDocsUploadedPage",
            params: [uuid: params.uuid, lang: params.lang, country: params.country])] as JSON)
    }

    def withdrawCancellation() {
        log.info("com.cover.car.PolicyController#withdrawCancellation - called with params: $params")

        String uuid = params.uuid

        log.debug("Encrypted uuid of the cancellation request is: $uuid")
        if (!uuid) {
            throw new RuntimeException("Request parameter 'uuid' is required")
        }

        log.debug("Trying to decrypt encrypted uuid")
        try {
            uuid = AESCryption.decrypt(uuid)
        } catch (DecoderException e) {
            throw new RuntimeException("Request parameter 'uuid' is invalid, most probably it has been altered by the user", e)
        }

        log.debug("Successfully decrypted encrypted uuid. Decrypted uuid: $uuid")

        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)

        if (!cancellationRequest) {
            throw new RuntimeException("There is no policy cancellation request corresponding to decrypted 'uuid'(value = $uuid)")
        }

        CarPcrStatusEnum cancellationRequestStatus = CarPcrStatusEnum.getById(cancellationRequest.statusId)

        if (!(cancellationRequestStatus in [CarPcrStatusEnum.DOCS_PENDING, CarPcrStatusEnum.WITHDRAWN_BY_CUSTOMER,
                                            CarPcrStatusEnum.WITHDRAWN_INTERNALLY, CarPcrStatusEnum.EXPIRED,
                                            CarPcrStatusEnum.CANCELLATION_PROCESSED, CarPcrStatusEnum.APPROVED,
                                            CarPcrStatusEnum.INTERNAL_REVIEW, CarPcrStatusEnum.INSURER_REVIEW,
                                            CarPcrStatusEnum.REJECTED])) {
            throw new RuntimeException("Customer has requested for Policy Cancellation Request withdrawal but the Cancellation Request " +
                "has an unexpected status of ${cancellationRequestStatus.toString()}. Cancellation Request ID: $cancellationRequest.id")
        }

        CarQuote quote = cancellationRequest.quote

        if (cancellationRequestStatus == CarPcrStatusEnum.DOCS_PENDING) {
            policySgService.withdrawPolicyCancellationRequest(quote.id, 'Withdrawn by customer', true)

            notify AsyncEventConstants.CAR_PCR_EMAIL_WITHDRAWN_TO_CUSTOMER, [
                cancelReqId    : cancellationRequest.id, productType: ProductTypeEnum.CAR, user: quote.user,
                referenceNumber: quote.policyReference
            ]
        }

        render view: "/common/cancellationRequestWithdrawn", model: [
            userName                  : quote.user.name,
            refNumber                 : quote.policyReference,
            cancellationRequestId     : cancellationRequest.id,
            cancellationRequestStatus : cancellationRequestStatus,
            insuranceProvider         : quote.product.provider.nameEn

        ]
    }

    def renderPolicyCancellationInsurerReviewPage(String encryptedUuid, String encryptedProviderContactId) {
        log.info("Initiating car policy cancellation review........")
        if (!carPolicyCancellationService.validateEncryptedUuid(encryptedUuid)) {
            throw new IllegalArgumentException("Encrypted uuid of policy cancellation request is invalid - $encryptedUuid. Most probably " +
                "the url has been altered at the client side.")
        }

        if (!ProviderContact.validateEncryptedId(encryptedProviderContactId)) {
            String userAgent = request.getHeader('user-agent')
            log.warn "Request coming from user-agent: ${userAgent}"
            if (userAgent) {
                if (userAgent.toLowerCase().contains('googlebot')) {
                    log.warn "This is google bot"
                    response.sendError(404)
                    return
                }
            }
            throw new IllegalArgumentException("Encrypted provider contact id is invalid - $encryptedProviderContactId. Most probably " +
                "the url has been altered at the client side.")
        }

        String uuid = AESCryption.decrypt(encryptedUuid)
        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)
        CarQuote carQuote = cancellationRequest.quote

        List<CarPcrDocumentGroup> carPcrDocumentGroupList =
            (List<CarPcrDocumentGroup>) CarPcrDocumentGroup.findAllByCarPcr(cancellationRequest, [max: 1, sort: "dateCreated", order: "desc"])

        render view: "/car/policy/policyCancellationInsurerReview", model: [
            quote                     : carQuote,
            policyPrice               : carQuote.policyPrice.setScale(0, BigDecimal.ROUND_DOWN),
            cancelReqId               : cancellationRequest.id,
            cancellationRequest       : cancellationRequest,
            cancellationRequestStatus : CarPcrStatusEnum.getById(cancellationRequest.statusId),
            encryptedProviderContactId: params.encryptedProviderContactId.trim(),
            currency                  : message(code: "currency.$params.country"),
            cancellationDocList       : PolicyDocument.findAllByCarPcrDocumentGroup(carPcrDocumentGroupList[0])
        ]
    }

    def policyCancellationInsurerReviewResult(CarPcrInsurerReviewCommand cmd) {
        if (cmd.hasErrors()) {
            throw new RuntimeException("The request parameters are invalid. Command object errors: $cmd.errors")
        }

        ProviderContact providerContact = ProviderContact.get(AESCryption.decrypt(cmd.encryptedProviderContactId))

        String uuid = AESCryption.decrypt(cmd.encryptedUuid)
        CarPcr cancellationRequest = CarPcr.findByUuid(uuid)
        CarQuote carQuote = cancellationRequest.quote

        if (cancellationRequest.statusId != CarPcrStatusEnum.INSURER_REVIEW.id) {
            throw new RuntimeException("Insurer employee $providerContact.emailAddress attempted to submit policy cancellation" +
                " review result but cancellation request status is ${CarPcrStatusEnum.getById(cancellationRequest.statusId)}. " +
                "Cancellation Request ID = $cancellationRequest.id")
        }

        String currency = message(code: "currency.$params.country")

        if (cmd.rejectionReasonId) {
            String remarks = "Provider contact: $providerContact.emailAddress, Cancellation Rejection Reason: " +
                "${CarPcrRejectionReasonEnum.getById(cmd.rejectionReasonId).toString()}"

            policySgService.rejectPolicyCancellationRequest(
                cancellationRequest,
                CarPcrRejectionReasonEnum.getById(cmd.rejectionReasonId),
                HtmlUtils.htmlEscape(cmd.invalidDocExplanation), // html escaping just in case of XSS attacks
                remarks,
                providerContact
            )

            if (cmd.rejectionReasonId != CarPcrRejectionReasonEnum.INVALID_DOCUMENT.id) {
                // in case of invalid document, email sending will be triggered from broker manually

                notify AsyncEventConstants.CAR_PCR_EMAIL_CANCELLATION_REJECTED_TO_CUSTOMER, [
                    user               : carQuote.user,
                    policyNo           : carQuote.policyNo,
                    insuranceProvider  : carQuote.product.provider.nameEn,
                    cancellationRequest: cancellationRequest,
                    rejectionReasonId  : cmd.rejectionReasonId
                ]
            }

        } else {
            policyService.approvePolicyCancellationRequest(
                cmd.encryptedUuid,
                cmd.refundAmount,
                cmd.debitNote,
                cmd.creditNote,
                providerContact,
                currency
            )
        }

        List<ProviderContactToRole> providerContactToRoleList = ProviderContactToRole.findByRoleAndProviderIdForCurrentEnv(
            ProviderContactRoleEnum.POLICY_CANCELLATION,
            carQuote.product.provider.id
        )

        // send an email to insurer as a confirmation for the review result
        notify AsyncEventConstants.CAR_PCR_EMAIL_REVIEW_RESULT_TO_INSURER, [
            policyNo                 : carQuote.policyNo,
            cancellationRequest      : cancellationRequest,
            insuranceProvider        : carQuote.product.provider,
            providerContactToRoleList: providerContactToRoleList,
            rejectionReasonId        : cmd.rejectionReasonId,
            currency                 : currency
        ]

        render(['status': 'ok'] as JSON)


    }

    /**
     * Validates that provided file has a mimetype that is in the following list:
     * 'image/tiff', 'application/pdf', 'image/bmp', 'image/png', 'image/jpeg'
     * @param file file the mime type of which should be validated
     * @return null if valid, otherwise the mime type of file
     */
    private static String validateCarPcrDocMimeType(MultipartFile file) {
        TikaConfig tika = new TikaConfig()
        String mimetype = tika.getDetector().detect(TikaInputStream.get(file.inputStream), new Metadata())

        boolean isValid = mimetype in ['image/tiff', 'application/pdf', 'image/bmp', 'image/png', 'image/jpeg']

        isValid ? null : mimetype
    }

    /**
     * Update Policy with Policy Start Date and to Received
     * @param cmd UpdatePolicyCommand object
     * @return
     */
    def updatePolicy(UpdatePolicyCommand cmd) {
        log.info("policy.updatePolicy - entering with [cmd:${cmd.toString()}]")

        if (!cmd.id) {
            redirect(mapping: 'carIndex', params: [country: params.country, lang: params.lang])
            return
        }

        Long quoteId = Long.parseLong(AESCryption.decrypt((String) cmd.id))
        CarQuote quote = CarQuote.read(quoteId)
        if (quote.paymentStatus != PaymentStatusEnum.ISSUED && quote.paymentStatus != PaymentStatusEnum.REFUND &&
            quote.paymentStatus != PaymentStatusEnum.CANCEL && quote.paymentStatus != PaymentStatusEnum.RECEIVED &&
            (quote.paymentMethod == PaymentMethodEnum.CREDITCARD || quote.paymentMethod == PaymentMethodEnum.INSTALLMENT
                || quote.paymentMethod == PaymentMethodEnum.ETISALAT_SMILES_POINTS || quote.paymentMethod == PaymentMethodEnum.ETISALAT_SMILES_MIX )) {

            try {
                if (!cmd.validate()) {

                    def errors = g.renderErrors(bean: cmd)

                    flash.message = errors
                    flash.success = false

                    redirect(mapping: "carUploadPolicyDocs", params: params, base: getBaseUrl())
                    return
                }

                List<DocumentTypeEnum> missingDocuments = []
                List<PolicyDocument> documents = PolicyDocument.findAllByCarQuoteAndIsDeleted(quote, false)

                int idCardFrontCount = documents.findAll {
                    it.documentTypeId == DocumentTypeEnum.ID_CARD_FRONT.id
                }.size()
//                int idCardBackCount = documents.findAll { it.documentTypeId == DocumentTypeEnum.ID_CARD_BACK.id }.size()

                int licenseCount = documents.findAll { it.documentTypeId == DocumentTypeEnum.DRIVING_LICENSE.id }.size()
                int registrationCount = documents.findAll {
                    it.documentTypeId == DocumentTypeEnum.VEHICLE_REGISTRATION.id
                }.size()

                if (!idCardFrontCount) missingDocuments.add(DocumentTypeEnum.ID_CARD_FRONT)
//                if (!idCardBackCount) missingDocuments.add(DocumentTypeEnum.ID_CARD_BACK)
                if (!licenseCount) missingDocuments.add(DocumentTypeEnum.DRIVING_LICENSE)
                if (!quote.isBrandNew && !registrationCount) missingDocuments.add(DocumentTypeEnum.VEHICLE_REGISTRATION)

                // check additional documents and make it required
                List<DocumentTypeEnum> additionalDocs = commonUtilService.getRequiredDocumentsList(quote)
                additionalDocs.each { addDoc ->
                    int matchedAdditionalDocs = documents.findAll { doc ->
                        doc.documentTypeId == addDoc.id
                    }.size()
                    if (!matchedAdditionalDocs) {
                        missingDocuments.add(addDoc)
                    }
                }
                // need to add conditional docs mandatory here on the base of questions

                List<CarConditionalDocument> conditionalDocsQuestions = commonUtilService.getConditionalDocumentQuestionAndDocumentType(quote)

                List<String> missingTextConditionalQuestions = []

                conditionalDocsQuestions.each { condDocQuestions ->
                    if (condDocQuestions.question.answerType == 'boolean' && condDocQuestions.docTypes) {

                        condDocQuestions.docTypes.each { docType ->

                            int thisDocs = documents.findAll {
                                it.documentTypeId == docType.id
                            }.size()

                            ConditionalQuestionsCommand question = cmd.conditionalQuestions.find{
                                it.type == 'boolean' && it.key == condDocQuestions.question.slug
                            }

                            String defaultVal = condDocQuestions.question.getDefaultSelectedOptionYesOrNo() == 'yes' ? 'true' : "false"

                            if (!thisDocs && question.value != defaultVal) {
                                missingDocuments.add(docType)
                            }
                        }

                    } else if (condDocQuestions.question.answerType == 'text') {
                        ConditionalQuestionsCommand textQuestion = cmd.conditionalQuestions.find{
                            it.type == 'text' && it.key == condDocQuestions.question.slug
                        }

                        if ( !(textQuestion && textQuestion.value != '' && textQuestion.value != null ) ) {
                            // making tcf number optional.
                            if (textQuestion.key != 'TCF_NUMBER') {
                                missingTextConditionalQuestions.add( "${params.lang == 'ar' ? condDocQuestions.question.label_ar+' مطلوب ' : condDocQuestions.question.label_en + ' is required'}" )
                            }
                        }
                    } else if (condDocQuestions.question.answerType == 'emailVerificationButton') {
                        // ignoring this
                    } else{
                        ConditionalQuestionsCommand textQuestion = cmd.conditionalQuestions.find{
                            it.key == condDocQuestions.question.slug
                        }

                        if ( !(textQuestion && textQuestion.value != '' && textQuestion.value != null ) ) {
                            // making other mandatory.
                            missingTextConditionalQuestions.add( "${params.lang == 'ar' ? condDocQuestions.question.label_ar+' مطلوب ' : condDocQuestions.question.label_en + ' is required'}" )
                        }
                    }
                    missingDocuments.unique { a, b ->
                        a.id <=> b.id
                    }
                }


                if (missingDocuments.size() || missingTextConditionalQuestions) {
                    List<String> documentsTitles = params.lang == 'ar' ? missingDocuments*.titleAr : missingDocuments*.title
                    if (missingTextConditionalQuestions) {
                        documentsTitles.addAll(missingTextConditionalQuestions)
                    }
                    String errors = g.message(code: 'doc.upload.missing.documents', args: ["${documentsTitles.join('</li><li>')}"])
                    log.info("policy.updatePolicy - missing documents, errors:${errors}, missing documents:${documentsTitles}, quoteId:${quote.id}")

                    flash.message = errors
                    flash.missingDocuments = documentsTitles
                    flash.success = false

                    redirect(mapping: "carUploadPolicyDocs", params: [country: params.country, lang: params.lang, id: params.id], base: getBaseUrl())
                    return
                }

                if (cmd.conditionalQuestions) {
                    cmd.conditionalQuestions.each {
                        commonUtilService.processConditionalQuestions(it.carQuoteId, it.type, it.key, it.value)
                    }
                }

                policySgService.updatePolicyDocumentsReceived(quote, cmd.policyStartDate, ProductTypeEnum.CAR)

                render(view: "/car/checkout/docsReceived", model: [country: params.country, lang: params.lang, quote:quote], base: getBaseUrl())

            } catch (Exception e) {
                log.error("exception while submitting documents to received, quoteId:${quote.id}", e)
                redirect(mapping: "carUploadPolicyDocs", params: [id: cmd.id, country: params.country, lang: params.lang])
            }

            return

        } else {
            redirect(mapping: "carUploadPolicyDocs", params: [id: cmd.id, country: params.country, lang: params.lang], base: getBaseUrl())
        }
    }

    /**
     * Upload document to the provided quote
     *
     * @param cmd
     * @return
     */
    def uploadPolicyDocs(FileUploadCommand cmd) {
        log.info("policy.uploadPolicyDocs - entering with [id:${params.id}, country:${params.country}]")

        Long id
        try {
            id = Long.parseLong(AESCryption.decrypt((String) params.id))
        } catch (DecoderException e) {
            log.warn("policy.uploadPolicyDocs - Invalid Hex for uploading file: ${params.id}, ${e.stackTrace.join("\n")}")
            render status: HttpStatus.BAD_REQUEST, text: "Bad Request"
            return
        }
        CarQuote quote = CarQuote.get(id)
        if (!quote) {
                log.info "policy.uploadPolicyDocs - Quote not found, quoteId:${id}"
            render status: 404
            return
        }
        try {
            if (request.method == "GET") {
                renderUploadDocumentsView(quote, params)

            } else if (request.method == "POST") {
                if (cmd.validate()) {

                    def file = params.file

                    // make sure files were actually uploaded
                    if (!file) {
                        log.info("policy.uploadPolicyDocs - no file received to server, quoteId:${quote.id}")

                        render(status: HttpStatus.BAD_REQUEST.value())
                        return
                    }

                    ProductTypeEnum productType = ProductTypeEnum.findByName(cmd.productType)
                    def result = documentUploadService.savePolicyDocument(cmd.file, DocumentType.findByCode(cmd.docType),
                        quote, productType, true, false, null)

                    PolicyDocument policyDocument = result.policyDocument

                    /*if (policyDocument.documentTypeId == DocumentTypeEnum.ID_CARD_FRONT.id && productType == ProductTypeEnum.CAR) {
                        notify AsyncEventConstants.EMIRATES_ID_UPLOADED, [
                            policyDocumentId: policyDocument.id
                        ]
                    }*/

                    log.info("policy.uploadPolicyDocs - returning policyDocument:${policyDocument}")

                    render([message: "File Uploaded", path: policyDocument.fullPath, documentId: AESCryption.encrypt(policyDocument.id.toString())] as JSON)
                } else {
                    log.error("policy.uploadPolicyDocs - error validating file, quoteId:${quote.id}, errors:${cmd.errors}")

                    render(text: "Invalid File", contentType: "text/xml", encoding: "UTF-8")
                }
            }
        } catch (Exception e) {
            log.error("policy.uploadPolicyDocs - error caught uploading file, quoteId:${quote.id}", e)

            response.status = HttpStatus.INTERNAL_SERVER_ERROR.value()
            render(text: "<xml>some xml</xml>", contentType: "text/xml", encoding: "UTF-8")
//            renderUploadDocumentsView(quote, params)
        }
    }

    /**
     * Render view to upload documents
     *
     * @param quote
     * @param params
     * @return
     */
    def renderUploadDocumentsView(CarQuote quote, def params) {
        log.info(".renderUploadDocumentsView - quote:${quote.id}, params:${params}")
        def documents = PolicyDocument.findAllByCarQuoteAndIsDeleted(quote, false)
        List<DocumentTypeEnum> additionalDocs = commonUtilService.getRequiredDocumentsList(quote)
        List<CarConditionalDocument> conditionalDocumentList = commonUtilService.getConditionalDocumentQuestionAndDocumentType(quote)

        // to eliminate repeating docs
        // get the items that needs to remove and remove them after iterating on that list. it will not throw error
        additionalDocs.each { addDoc ->
            conditionalDocumentList.each { condDocQuest ->
                List<DocumentTypeEnum> removeThese = []
                condDocQuest.docTypes.each { condDoc ->
                    if ( condDoc.code == addDoc.code ) {
                        removeThese.add(condDoc)
//                        condDocQuest.docTypes.remove(condDoc)
                    }
                }
                removeThese.each { removeAbleDoc ->
                    condDocQuest.docTypes.remove(removeAbleDoc)
                }

            }
        }

        List<CarQuoteConditionalQuestionsValues> carQuoteConditionalQuestionsValues = CarQuoteConditionalQuestionsValues.findAllByCarQuote(quote)
        render(view: "/car/checkout/uploadDocs",
            model: [
                quote                             : quote,
                carQuoteConditionalQuestionsValues: carQuoteConditionalQuestionsValues,
                quoteDetails                      : CarQuoteDetail.findByQuote(quote),
                quoteDocuments                    : documents,
                additionalDocs                    : additionalDocs,
                conditionalDocs                   : conditionalDocumentList,
                id                                : quote.id,
                country                           : params.country,
                lang                              : params.lang
            ])
    }

    /**
     * Delete car document
     *
     * @param id
     * @return
     */
    def deletePolicyDocs (String id) {
        def docId = Long.parseLong(AESCryption.decrypt(id))
        def quoteId = Long.parseLong((String)params.quoteId)
        log.info("checkout.deletePolicyDocs - quoteId: $quoteId")

        CarQuote quote = CarQuote.get(quoteId)

        try {
            PolicyDocument document = PolicyDocument.read(docId)

            //Allow only when document belongs to provided quote
            if (document.carQuote.id == quote.id) {

                documentUploadService.deleteDocumentOnly(docId, quote.id, ProductTypeEnum.CAR)

                render "success"

            } else {
                response.status = HttpStatus.UNAUTHORIZED.value()
                render "unauthorized"
            }

        } catch (Exception e) {
            render(status: HttpStatus.INTERNAL_SERVER_ERROR.value())
        }
    }


    JSON processNCDRecordedOnEmail() {

        Map resp = [
            result: false
        ]
        if(!params.id){
            return resp as JSON
        }

        Long qouteId
        try{
            qouteId = AESCryption.decrypt(params.id).toLong()
        }
        catch (Exception e){
            log.error "Entereing in processNCDRecordedOnEmail, and quote id cannot decrypt: $params.quoteId"
            return resp as JSON
        }


        CarQuote carQuote = CarQuote.read(qouteId)
        CarQuoteDetail quoteDetails = CarQuoteDetail.findByQuote(carQuote)


        if (quoteDetails.ncdRecordedOnEmail == null || !quoteDetails.ncdRecordedOnEmail) {
            quoteDetails = policySgService.processingNcdRecordedOnEmail(qouteId, false)
            // send email here
            // verified email send here
            String selfDecVerifyLink = g.createLink(mapping: "carNCDVerification", params:[lang: params.lang, country: params.country, id: params.id], absolute: true, base: sessionService.getBaseUrl())
            String callbackRequestLink = g.createLink(mapping: "carRequestCallBack", params:[lang: params.lang, country: params.country, id: params.id, page: 'selfDecEmail', reason: "Clicked from self dec email"], absolute: true, base: sessionService.getBaseUrl())
            String providerName = carQuote.product?.provider?.name
            notify AsyncEventConstants.CAR_POLICY_SELF_DEC, [
                carQuoteId         : carQuote.id,
                lang               : params.lang,
                selfDecVerifyLink  : selfDecVerifyLink,
                callbackRequestLink: callbackRequestLink,
                providerName       : providerName
            ]

            resp.result = true
            resp.ncdOnEmail = quoteDetails.ncdRecordedOnEmail

            return resp as JSON
        } else {
            return resp as JSON
        }



    }

    def processNCDRecordedOnEmailVerification() {
        Map resp = [
            result: false
        ]
        if(!params.id){
            render status: HttpStatus.NOT_FOUND
            return
        }

        Long qouteId
        try{
            qouteId = AESCryption.decrypt(params.id).toLong()
        }
        catch (Exception e){
            log.error "Entereing in processNCDRecordedOnEmailVerification, and quote id cannot decrypt: $params.quoteId"
            render status: HttpStatus.NOT_FOUND
            return
        }

        CarQuote carQuote = CarQuote.read(qouteId)
        CarQuoteDetail quoteDetails = CarQuoteDetail.findByQuote(carQuote)

        if (quoteDetails.ncdRecordedOnEmail == false) {
            policySgService.processingNcdRecordedOnEmail(qouteId, true)
        }

        redirect mapping: 'carUploadPolicyDocs', params: [
            id   : params.id,
            lang   : params.lang,
            country: params.country
        ]
    }

    def requestCallBack() {
        Map resp = [
            result: false
        ]
        if (!(params.id && params.page)) {
            render status: HttpStatus.NOT_FOUND
            return
        }

        Long qouteId
        try {
            qouteId = AESCryption.decrypt(params.id).toLong()
        }
        catch (Exception e) {
            log.error "error in entereing in requestCallBack, and quote id cannot decrypt: $params.quoteId"
            render status: HttpStatus.NOT_FOUND
            return
        }

        String page = params.page
        String reason = params.reason ?: null

        CarQuoteCallRequest callRequest = policySgService.callbackRequested(qouteId, page, reason)
        // verified email send here

        if (callRequest) {
            resp.result = true
        }

        render view: "/common/customMessage", model: [heading: g.message(code: 'component.breadcrumbs.thankyou'),subheading: "One of our Agent will call you shortly."]
    }

    private String getBaseUrl() {
        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]
        String baseUrl = whiteLabelDomain ? whiteLabelDomain.baseUrl : grailsApplication.config.getProperty('yallacompare.baseURL')
        log.info("baseUrl:${baseUrl}")

        baseUrl
    }
}
