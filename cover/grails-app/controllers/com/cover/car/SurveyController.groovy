package com.cover.car

import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CountryEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelDomain

class SurveyController {

    static namespace = "car"

    def quoteService
    def utilService
    def checkoutService
    def sessionService

    def initSurveyPage() {
        def quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        log.info("initSurveyPage - flash.error :${flash.error}")
        /**
         * Bots are trying to access this action for that reason if no quoteId found in session send on 404
         */
        if (!quoteId) {
            render status: 404
            return
        }
        CarQuote quote = CarQuote.findById(quoteId)

        render view: "/car/survey/surveySchedulingEgy", model: [carQuote:quote, country:params.country, lang:params.lang]
    }

    def scheduleSurvey() {
        def quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        log.info("initSurveyPage - flash.error :${flash.error}")
        /**
         * Bots are trying to access this action for that reason if no quoteId found in session send on 404
         */
        if (!quoteId) {
            render status: 404
            return
        }
        CarQuote quote = CarQuote.findById(quoteId)
        quoteService.storeSurveyBooking(quote, params.surveyDate)
        render view: "/car/survey/thankyou", model: [carQuote:quote, country:params.country, lang:params.lang]
    }
}
