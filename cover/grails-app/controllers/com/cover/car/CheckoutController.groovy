package com.cover.car

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.ProviderPaymentGatewayCommand
import com.cover.apiV1.UserPaymentCardController
import com.cover.car.commands.RateCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CyberSourcePaymentStatusEnum
import com.safeguard.DocumentTypeEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.KnetPaymentStatusEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.ProductType
import com.safeguard.ProductTypeEnum
import com.safeguard.Renewal
import com.safeguard.RepairTypeEnum
import com.safeguard.User
import com.safeguard.VehicleTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.EtisalatSmilesPayment
import com.safeguard.life.LifeQuote
import com.safeguard.pa.PersonalAccidentService
import com.safeguard.partner.PartnerLeadPayment
import com.safeguard.payment.PaymentCardDetails
import com.safeguard.payment.PaymentInvoice
import com.safeguard.payment.PaymentMethod
import com.safeguard.payment.ProviderPaymentQuoteMapping
import com.safeguard.payment.ProviderPaymentMethod
import com.safeguard.payment.QuoteAdditionalPayment
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.converters.JSON
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.http.HttpStatus

import java.math.RoundingMode

/**
 * Checkout related actions
 */
class CheckoutController {

    static namespace = "car"

    def quoteService
    def utilService
    def checkoutService
    def paymentService
    def springSecurityService
    def lifeQuoteService
    def con
    def lifeUtilService
    def commonUtilService
    def sessionService
    def etisalatApiService
    def partnerSgService
    def configurationService
    def paymentMethodService
    def paymentMethodSgService
    def tapPaymentService
    def gigApiService

    /**
     * Checkout process index page/select payment method.
     */
    def index(String encryptedQuoteId, String encryptedProductId, Integer repairType) {

        CarQuote quote
        if (encryptedQuoteId && encryptedProductId && repairType != null) {
            log.info "checkout.index - entering with id [ ${session[IConstant.STEP2_CARID]} ]"
            if (request.method == "HEAD") {
                log.info("request received with HEAD method")
                //SendInBlue send a HEAD request which causes issues, since this method try to update CarQuote
                //and sendInBlue for each product (TPL, Non Agency and Agency) send 3 parallel requests, it locks the
                //quote and throw exception
                return ""
            }

            //Save country into session, since its a directly link
            session[IConstant.SITE_COUNTRY] = CountryEnum.findCountry(params.country)

            Long quoteId = Long.parseLong(AESCryption.decrypt(encryptedQuoteId))
            quote = CarQuote.get(quoteId)

            Long productId = Long.parseLong(AESCryption.decrypt(encryptedProductId))
            Product product = Product.read(productId)


            if (!quote || !product) {
                if (!quote) {
                    log.info "checkout.index - Quote not found, quoteId:${quoteId}"
                } else {
                    log.info "checkout.index - Product not found, productId:${productId}"
                }
                render status: 404
                return
            }

            if (quote.isAncient()) {
                log.info "checkout.index - policy [quoteId:${quoteId}] created on ${quote.dateCreated} so redirecting to 404"
                render view: '/car/expiredQuote', model: [country: params.country, lang: params.lang]
                return
            } else if (!quote.isNotProcessed()) {
                log.warn "checkout.index - Quote is already processed for quote id: ${quote.id}"
                render view: '/car/funnel/quoteProcessed', model: [country: params.country]
                return
            }

            sessionService.updateSession(quote)

            RateCommand rateCommand = quoteService.getRating(quote, product.id, RepairTypeEnum.findById(repairType))
            quoteService.updateCarQuote(quote, rateCommand, rateCommand.premium)
        } else {
            log.info "checkout.index - entering with quote id [ ${session[IConstant.STEP2_CARID]} ]"
            quote = CarQuote.get(session[IConstant.STEP2_CARID])

            if (quote && !quote.isNotProcessed()) {
                log.warn "checkout.index - Quote is already processed for quote id: ${quote.id}"
                render view: '/car/funnel/quoteProcessed', model: [country: params.country]
                return
            }
        }

        if (quote) {
            String standAloneDiscountCode = null
            String standAloneDiscountGroup = null
            if (session[IConstant.STAND_ALONE_DISCOUNT_CODE] && quote.email == (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[0]) {
                standAloneDiscountCode = (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[1]
            }
            if (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] && quote.email == (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[0]) {
                standAloneDiscountGroup = AESCryption.encrypt((session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[1])
            }

            RepairTypeEnum selectedRepairType = quote.repairType ?: (quote.isAgencyRepair ? RepairTypeEnum.AGENCY : RepairTypeEnum.GARAGE)
            RateCommand rateCommand = quoteService.getRating(quote, quote.productId as Integer, selectedRepairType,
                standAloneDiscountCode,
                standAloneDiscountGroup
            )

            WhiteLabelBrandEnum brandEnum = sessionService.getBrand()
            if (brandEnum && brandEnum == WhiteLabelBrandEnum.ETISALAT_SMILES) {
                EtisalatSmilesPayment etisalatSmilesPayment = EtisalatSmilesPayment.findByQuote(quote)
                if (etisalatSmilesPayment != null) {
                    session['etisalatUserId'] = quote.user.id
                    def totalPoints, tokenType, token, authToken
                    (totalPoints, tokenType, token, authToken) = etisalatApiService.getTotalPoints(etisalatSmilesPayment.transactionId, etisalatSmilesPayment.etisalatUser.loyalityId)
                    session['etisalatTotalPoints'] = totalPoints
                } else {
                    render view: "/car/checkout/index", model: [command: rateCommand, quote: quote, country: session[IConstant.SITE_COUNTRY], whitelabel: session[IConstant.WHITE_LABEL_DOMAIN]]
                    return
                }
            }

            if (!rateCommand) {
                log.warn "checkout.index quoteId ${quote.id} rateCommand is null redirecting to quotes page"
                redirect mapping: 'carQuotes', params: [lang: utilService.getLanguage(), country: session[IConstant.SITE_COUNTRY]], base: getBaseUrl()
                return
            }

            render view: "/car/checkout/index", model: [command: rateCommand, quote: quote, country: session[IConstant.SITE_COUNTRY], whitelabel: session[IConstant.WHITE_LABEL_DOMAIN], donation: 0]
            return
        } else {
            log.warn "checkout.index quote is null"
            redirect mapping: 'carVehicleDetails', base: getBaseUrl(),
                params: [lang: utilService.getLanguage(), country: CountryEnum.UAE.code]
        }

    }

    /**
     * Update addons and redirect to credit card page
     */
    def updateOrder() {
        try {
            PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.findPaymentMethod(params.paymentMethod)

            CarQuote quote = CarQuote.get(params.quoteId)

            if (!quote.isNotProcessed()) {
                flash.error = g.message(code: 'error.alreadyProcessed')
                redirect mapping: "carHome", params: [lang: params.lang, country: params.country], base: getBaseUrl()
                return
            }

            def carQuote
            if (session[IConstant.STAND_ALONE_DISCOUNT_CODE] && quote.email == (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[0]) {
                String standAloneDiscountCode = (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[1]
                carQuote = quoteService.updateCarQuote(params, paymentMethodEnum, standAloneDiscountCode)
            } else if (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] && quote.email == (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[0]) {
                String standAloneDiscountGroup = AESCryption.encrypt((session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[1])
                carQuote = quoteService.updateCarQuote(params, paymentMethodEnum, null, standAloneDiscountGroup)
            } else {
                carQuote = quoteService.updateCarQuote(params, paymentMethodEnum)
            }

            session[IConstant.CHECKOUT_QUOTE_ID] = carQuote.id
            flash.quoteId = carQuote.id

            if (carQuote.isCod()) {
                redirect mapping: 'carCheckoutThankyou', params: [country: params.country, lang: params.lang], base: getBaseUrl()
                return
            } else {
                redirect mapping: 'carCheckoutPaynow', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            }

        } catch (Exception e) {
            log.error("Error on updateOrder", e)
            redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang], base: getBaseUrl()
        }
    }

    /**
     * Render credit card page in an iFrame
     */
    def paynow() {
        session['testingETS'] = "True"
        log.info("Paynow Session ID ${session.getId().toString()}")
        Long quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        log.info("paynow - enting with params:${params}, quoteId:$quoteId")
        log.info("checkout.paynow - flash.error :${flash.error}")
        /**
         * Bots are trying to access this action for that reason if no quoteId found in session send on 404
         */
        if (!quoteId && params.id) {
            quoteId = Long.parseLong(AESCryption.decrypt(params.id))
            session[IConstant.CHECKOUT_QUOTE_ID] = quoteId
        }

        if (!quoteId) {
            render status: 404
            return
        }

        CarQuote quote = CarQuote.findById(quoteId)

        if (quote && !quote.isNotProcessed()) {
            log.warn "checkout.index - Quote is already processed for quote id: ${quote.id}"
            render view: '/car/funnel/quoteProcessed', model: [country: params.country]
            return
        }

        String encQuoteId = AESCryption.encrypt(quoteId + "")

        EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(quote)
        WhiteLabelBrandEnum brandEnum = sessionService.getBrand()
        def totalPoints, tokenType, token, authToken
        if (brandEnum && brandEnum == WhiteLabelBrandEnum.ETISALAT_SMILES) {
            if (payment){
                (totalPoints, tokenType, token, authToken) = etisalatApiService.getTotalPoints(payment.transactionId, payment.etisalatUser.loyalityId)
            }else {
                (totalPoints, tokenType, token, authToken) = etisalatApiService.getTotalPoints(session['etisalatTransactionId'], session['etisalatloyaltyId'])
            }

            if (payment && payment.pointsAmount && new BigDecimal(totalPoints).compareTo(new BigDecimal(payment.pointsAmount)) < 0) {
                def errorDetails = []

                errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
                errorDetails.add([title: "Loyality ID", message: "${session['etisalatloyaltyId']}"])
                errorDetails.add([title: "Partner Transaction Id", message: "${quoteId}"])

                def errorMessage = g.message(message: "Sorry you do not have enough smiles points for this purchase")
                render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                return
            }
            log.info("PayNow Mix Money ${session['MixMoneyAmount']}")
//            log.info("Update Price Mix Point ${session['MixPointsAmount']}")
            if (payment && payment.moneyAmount) {
                if (!validateEtisalatPayment(quote)) {
                    def errorDetails = []

                    errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
                    errorDetails.add([title: "Loyality ID ", message: "${session['etisalatloyaltyId']}"])
                    errorDetails.add([title: "Partner Transaction Id", message: "${quoteId}"])

                    def errorMessage = g.message(message: "Sorry, incorrect input amount for this purchase")
                    render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                    return
                }

            }
            log.info("Before Setting session ${session['testingETS']}")
            setEtisalatSession(totalPoints, tokenType, token, authToken)
            log.info("After Setting session ${session['testingETS']}")

        }

        if (!quoteService.isEligibleForPayment(quote)) {
            flash.error = g.message(code: 'checkout.quote.modified')
            redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        }

        //notify AsyncEventConstants.SALESFORCE_CAR_QUOTE_SYNC, quote.id

        checkoutService.clearQuoteWithFromPaymentDetail(quote, ProductTypeEnum.CAR)

        String domain = getBaseUrl() //grailsApplication.config.getProperty("cover.domain")
        String country = params.country
        String lang = params.lang

        String quoteVersion = quote.version.toString()

        def creditCardParams, installmentParams
        boolean showMerchantPage = true
        String payfortReturnUrl = null

        if (country in [CountryEnum.UAE.code, CountryEnum.EGYPT.code]) {
            //Merchant page
            payfortReturnUrl = "$domain/$country/$lang/${ProductTypeEnum.CAR.toString().toLowerCase()}/checkout/payment"

           /* (creditCardParams, installmentParams) = paymentMethodService.getMerchantPageParams(quote.encodedrMerchantRef(),
                quote.currency, quote.totalPrice, "${returnUrl}?p_quote_v=${quoteVersion}", country)
            */
        } else if (country in [CountryEnum.KWT.code]) {

            /*creditCardParams = checkoutService.getPayTabsPageParams(quote, quoteVersion, country)*/
            /*creditCardParams = checkoutService.getCyberSourcePaymentParams(quote, quoteVersion, country,
                                                utilService.getClientIp(request), request.session.id)*/
        } else {
            //Redirection
            String path = grailsApplication.config.getProperty("payfort.payment.carSuccessPath")
            payfortReturnUrl = "$domain/$country/$lang/$path"

            /*(creditCardParams, installmentParams) = paymentMethodService.getRedirectionParams(quote,
                "${returnUrl}", utilService.getClientIp(request))*/
        }

        log.info("quote.providerPolicyReference:${quote.providerPolicyReference}")

        //If Renewal Quote, then show payment intent page
        Renewal renewal = Renewal.findByCarQuote(quote)
        if ((quote.product.providerId.intValue() == InsuranceProviderEnum.NOOR_TAKAFUL.id) ||
            (quote.isRenewal && quote.productId && renewal &&
                quote.productId == renewal.productId && renewal.hasPreviousProvider &&
                quote.product.providerId.intValue() in [InsuranceProviderEnum.SALAMA.id] &&
                quote.model.vehicleTypeId.intValue() in [VehicleTypeEnum.COUPE.value, VehicleTypeEnum.CONVERTIBLE.value]) ||
            (quote.isRenewal && quote.productId && renewal &&
                quote.productId == renewal.productId && renewal.hasPreviousProvider &&
                quote.product.providerId.intValue() in [InsuranceProviderEnum.GIG_INSURANCE.id,
                                                    InsuranceProviderEnum.NOOR_TAKAFUL.id])) {

            notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: quote.id,
                                                                      productId: quote.product.id]

            paymentService.addPaymentIntent(ProductTypeEnum.CAR, quote.id, quote.product.id)

            redirect mapping: 'carPreparePaymentPage', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        }

        if (quote.product.providerId.intValue() == GigRateV2Service.PROVIDER_ID.intValue() &&
                quote.providerPolicyReference) {
            ProviderPaymentGatewayCommand providerPaymentGatewayCommand =
                gigApiService.getAuthorizedPaymentLink(quote.id, quote.providerPolicyReference)
            log.info("providerPaymentGatewayCommand:$providerPaymentGatewayCommand")
            if (providerPaymentGatewayCommand.paymentPageHtml) {
                log.info("rendering Payment gateway page")
                render providerPaymentGatewayCommand.paymentPageHtml
                return
            }
        }

        ProviderPaymentMethod providerPaymentMethod =
            paymentMethodSgService.getProviderPaymentMethod(quote.product, quote.productType)

        if (providerPaymentMethod) {
            log.info("providerPaymentMethod found :${providerPaymentMethod}")

            if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.NOT_AVAILABLE) {
                notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: quote.id,
                                                                        productId: quote.product.id]

                paymentService.addPaymentIntent(ProductTypeEnum.CAR, quote.id, quote.product.id)

                redirect mapping: 'carPreparePaymentPage', params: [country: params.country, lang: params.lang], base: getBaseUrl()
                return
            } else if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.TAP_PAYMENT) {
                log.info("TAP payment gateway found for provider - including both TAP credit card and Tabby payment options")

                // Get all TAP_PAYMENT gateway payment methods (both CREDITCARD and TABBY)
                List<PaymentMethod> tapPaymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountryAndPaymentGateway(
                    true, ProductTypeEnum.CAR, Country.load(CountryEnum.findCountryByCode(country).id), PaymentGatewayEnum.TAP_PAYMENT)

                if (tapPaymentMethods && tapPaymentMethods.size() > 0) {
                    log.info("Found ${tapPaymentMethods.size()} TAP payment methods: ${tapPaymentMethods.collect { it.name }}")

                    render view: "/car/checkout/paymentMethods",
                        model: [paymentMethods   : tapPaymentMethods, carQuote: quote,
                                 tapMerchantId:      "${providerPaymentMethod.merchantId}",
                                 quoteVersion     : quoteVersion, creditCardParams: [:],
                                 installmentParams: [:], country: country,
                                 lang             : lang, showMerchantPage: true,
                                 totalPoints       : totalPoints ? totalPoints : 0,
                                 conversionRate   : configurationService.getValue('etisalatSmiles.api.conversionRate')
                        ]
                    return

                    /* //For redirection
                    def createChargeResp = tapPaymentService.initializeCharge(quote, params.tapPaymentCardToken)

                    if (createChargeResp.threeDSecure) {
                        render view: '/car/checkout/_redirect', model: [url           : createChargeResp.transaction.url,
                                                                        quote         : quote, country: params.country, lang: params.lang,
                                                                        conversionRate: configurationService.getValue('etisalatSmiles.api.conversionRate')
                        ]
                        return
                    }*/
                }
            }
        }

        log.info("no provider specific method found, falling back to Checkout.com")
        List<PaymentMethod> paymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountry(true,
            ProductTypeEnum.CAR, Country.load(CountryEnum.findCountryByCode(country).id))

        //Fall back to Prepare Payment Page when no payment method found
        if (paymentMethods.size() == 0) {
            notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: quote.id,
                                                                      productId: quote.product.id]

            paymentService.addPaymentIntent(ProductTypeEnum.CAR, quote.id, quote.product.id)

            redirect mapping: 'carPreparePaymentPage', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        }

        //Is Credit Card Payment Method Available?
        PaymentMethod creditCardPaymentMethod = paymentMethods.find { it.name == PaymentMethodEnum.CREDITCARD.toString() }

        if (creditCardPaymentMethod) {
            String checkoutSuccessUrl = "${domain}/payments/response/checkoutPsp/car/${encQuoteId}/success"
            String checkoutFailureUrl = "${domain}/payments/response/checkoutPsp/car/${encQuoteId}/failure"
            String checkoutCancelUrl = "${domain}/payments/response/checkoutPsp/car/${encQuoteId}/cancel"

            (creditCardParams, installmentParams, showMerchantPage) =
                paymentMethodService.getCreditCardPaymentParameters(creditCardPaymentMethod.paymentGateway, quote,
                    quote.user, country, utilService.getClientIp(request),
                    payfortReturnUrl, checkoutSuccessUrl, checkoutFailureUrl, checkoutCancelUrl, null)
        }

        render view: "/car/checkout/paymentMethods", model: [paymentMethods   : paymentMethods, carQuote: quote,
                                                             quoteVersion     : quoteVersion, creditCardParams: creditCardParams,
                                                             installmentParams: installmentParams, country: country,
                                                             lang             : lang, showMerchantPage: showMerchantPage,
                                                             totalPoints       : totalPoints ? totalPoints : 0,
                                                             conversionRate   : configurationService.getValue('etisalatSmiles.api.conversionRate')

        ]

    }

    /**
     * Get updated Checkout Hosted Page Link with updated price
     * @return
     */
    def getUpdatedCheckoutPspHostedPageDetail() {
        Long quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        log.info("checkout.getUpdatedCheckoutPspHostedPageDetail - entering with [params:$params, quoteId:${quoteId}")
        def model = [:]

        /**
         * Bots are trying to access this action for that reason if no quoteId found in session send on 404
         */
        if (!quoteId && params.id) {
            quoteId = Long.parseLong(AESCryption.decrypt(params.id))
            session[IConstant.CHECKOUT_QUOTE_ID] = quoteId
        }

        if (!quoteId) {
            model.error = "Quote not found"
            render model
            return
        }

        CarQuote quote = CarQuote.findById(quoteId)

        if (quote && !quote.isNotProcessed()) {
            log.warn "checkout.getUpdatedCheckoutPspHostedPageDetail - Quote is already processed, id: ${quote.id}"
            model.error = "Quote is already processed"
            render model
            return
        }

        if (!quoteService.isEligibleForPayment(quote)) {
            log.warn ("checkout.getUpdatedCheckoutPspHostedPageDetail - Quote is not eligible for payment, id:${quote.id}")
            model.error = g.message(code: 'checkout.quote.modified')
            render model
            return
        }

        EtisalatSmilesPayment etisalatSmilesPayment = EtisalatSmilesPayment.findByQuote(quote)

        String encQuoteId = AESCryption.encrypt(quoteId + "")

        String domain = getBaseUrl()

        String checkoutSuccessUrl = "${domain}/payments/response/checkoutPsp/car/${encQuoteId}/success"
        String checkoutFailureUrl = "${domain}/payments/response/checkoutPsp/car/${encQuoteId}/failure"
        String checkoutCancelUrl = "${domain}/payments/response/checkoutPsp/car/${encQuoteId}/cancel"

        def (pspHostedPageLink, merchantRef) = paymentMethodService.generateCheckoutPspHostedPageLink(quote, quote.user,
            utilService.getClientIp(request), checkoutSuccessUrl, checkoutFailureUrl, checkoutCancelUrl,
            etisalatSmilesPayment.moneyAmount)

        model = [pspHostedPageLink:pspHostedPageLink]

        render model as JSON
        return
    }

    /**
     * Payfort redirect to this action upon credit card form submission.
     */
    def payment() {

        log.info("checkout.payment - ${request.getMethod()}")
        def customParams = checkoutService.getCustomParams(params)

        String deviceFingerprint = params.device_fingerprint
        params.remove('device_fingerprint')


        boolean isSecured = checkoutService.isSecured(params)

        if (isSecured) {

            if (PayfortStatusEnum.INVALID_REQUEST.toString().equals(params.status)) {
                flash.error = params.response_message
                redirect mapping: 'carCheckoutPaynow', params: [country: params.country, lang: params.lang], base: getBaseUrl()
                return
            }

            try {
                CarQuote carQuote = CarQuote.findById(CarQuote.decodeMerchantRef(params.merchant_reference))
                log.info("Payment Mix Money ${session['MixMoneyAmount']}")
                log.info("Payment Session ID ${session.getId().toString()}")
//                log.info("Update Price Mix Point ${session['MixPointsAmount']}")
                EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(carQuote)
                if (payment  && payment.moneyAmount) {
                    if (!validateEtisalatPayment(carQuote)) {
                        def errorDetails = []

                        errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
                        errorDetails.add([title: "Loyality ID", message: "${session['etisalatloyaltyId']}"])
                        errorDetails.add([title: "Partner Transaction Id", message: "${quoteId}"])

                        def errorMessage = g.message(message: "Sorry, incorrect input amount for this purchase")
                        render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                        return
                    }
                }

                if (carQuote.isModified(params.p_quote_v)) {
                    flash.error = g.message(code: 'checkout.quote.modified')
                    log.error("car.checkout.payment - quote was modified:${carQuote.id} ")
                    redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang], base: getBaseUrl()
                    return
                }

                log.info("checkout.payment - params:${params}}")
                if (carQuote.quoteCountry.code.toLowerCase() == CountryEnum.KWT.dfp && !deviceFingerprint) {
                    log.info("checkout.payment - kuwait")

                    render view: '/car/checkout/_fingerprint', model: [quote         : carQuote, country: params.country,
                                                                       lang          : params.lang,
                                                                       conversionRate: configurationService.getValue('etisalatSmiles.api.conversionRate')
                    ]
                    return
                }

                params.device_fingerprint = deviceFingerprint

                PayfortResponse payfortResponse
                log.info("Payment Other Mix Money ${session['MixMoneyAmount']}")
//                log.info("Update Price Mix Point ${session['MixPointsAmount']}")
                WhiteLabelBrandEnum brandEnum = sessionService.getBrand()
                if (brandEnum && brandEnum == WhiteLabelBrandEnum.ETISALAT_SMILES && payment && payment.moneyAmount) {
                    def moneyAmount = payment.moneyAmount
                    carQuote.paymentMethod = PaymentMethodEnum.ETISALAT_SMILES_MIX
                    payfortResponse = paymentService.process(params,
                        PayfortCommandEnum.AUTHORIZATION, utilService.getClientIp(request), new BigDecimal(moneyAmount))
                } else {
                    payfortResponse = paymentService.process(params,
                        PayfortCommandEnum.AUTHORIZATION, utilService.getClientIp(request))
                }


                session[IConstant.CHECKOUT_QUOTE_ID] = carQuote.id

                //This is important so that we know incase of failed transaction it was installments
                if (payfortResponse.isInstallments) {
                    paymentService.setInstallments(carQuote, payfortResponse.numberOfInstallments)
                }

                if (payfortResponse.isThreeDeeSecure) {
                    render view: '/car/checkout/_redirect', model: [url           : payfortResponse.threeDeeSecureUrl,
                                                                    quote         : carQuote, country: params.country, lang: params.lang,
                                                                    conversionRate: configurationService.getValue('etisalatSmiles.api.conversionRate')
                    ]
                    return
                } else if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(payfortResponse.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(payfortResponse.status)) {

                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)
                    log.debug(".checkout.payment Redirect user to thankyou for merchantRef: ${params.merchant_reference}")
                    redirect mapping: "carCheckoutThankyou", params: [country: params.country, lang: params.lang], base: getBaseUrl()
                    return
                }

                String failureMessage = payfortResponse.responseMessage  +
                    (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message: failureMessage,
                                                                         quoteId: carQuote.id]
                flash.error = payfortResponse.responseMessage

            } catch (Exception exp) {
                log.error("Exception:", exp)
                flash.error = g.message(code: 'checkout.general.error')
            }
        } else {
            log.error(".checkout.payment **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code: 'checkout.general.error')
            redirect mapping: 'carCheckoutPaynow', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        }

        redirect mapping: 'carCheckoutPaynow', params: [country: params.country, lang: params.lang], base: getBaseUrl()
    }

    /**
     * 3d Secure returns to this action
     */
    def success() {
        log.info ".checkout.controller.success ${params}"
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)
        ProviderPaymentQuoteMapping providerPaymentQuoteMapping = null

        // check if the response received from payfort is for Car Insurance
        if (!params.merchant_reference.toString().startsWith('PC')) {
            providerPaymentQuoteMapping = ProviderPaymentQuoteMapping.findByProviderPaymentReference(params.merchant_reference.toString())
            if (providerPaymentQuoteMapping && providerPaymentQuoteMapping.productType == ProductTypeEnum.CAR) {
                //Found in Payment Mapping
            } else {
                log.warn "Response sent is not for CarQuote ${params.merchant_reference}"
                redirect mapping: 'carCheckoutPaynow', params: [country: params.country, lang: params.lang], base: getBaseUrl()
                return
            }
        }

        Long carQuoteId = providerPaymentQuoteMapping ? providerPaymentQuoteMapping.quoteId : CarQuote.decodeMerchantRef(params.merchant_reference)

        CarQuote quote = CarQuote.get(carQuoteId)
        flash.quoteId = quote.id

        //Ignoring Signature matching when its coming from provider (GIG)
        if ((providerPaymentQuoteMapping && quote)|| isSecured) {
            if (quote && quote.isNotProcessed()) {
                paymentService.savePaymentResponse(quote, params)

                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status)) {

                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT
                    if (providerPaymentQuoteMapping && quote) {
                        customParams.paymentGatewayEnum = PaymentGatewayEnum.PROVIDER_GATEWAY
                    }
                    log.info("Success Session ID ${session.getId().toString()}")
                    paymentService.paid(params, customParams)

                    log.info("Success Mix Money ${session['MixMoneyAmount']}")
                    WhiteLabelBrandEnum brandEnum = sessionService.getBrand()
                    log.info("Brand Enum ${brandEnum.id}")

                    if (brandEnum && brandEnum == WhiteLabelBrandEnum.ETISALAT_SMILES) {
                        EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(quote)

                        def paymentMethod = payment.moneyAmount ? PaymentMethodEnum.ETISALAT_SMILES_MIX.name() : PaymentMethodEnum.CREDITCARD.name()
                        def moneyAmount = payment.moneyAmount ? payment.moneyAmount : quote.totalPrice
//                        def etisalatSmilesPaymentId = session['EtisalatSmilesPaymentId'] ? session['EtisalatSmilesPaymentId'] : null
                        def etisalatSmilesPaymentId, isSuccessful
                        def token
                        def tokenType
                        def authToken
                        (tokenType, token, authToken) = etisalatApiService.authApisWrapper(payment.transactionId, payment.etisalatUser.loyalityId)
                        if (paymentMethod == PaymentMethodEnum.ETISALAT_SMILES_MIX.name()) {
                            log.info("Payment Methods --- ${paymentMethod}")
                            log.info("Etisalat User ID --- ${session['etisalatUserId']}")
                            log.info("Mix Points AMount --- ${session['MixPointsAmount']}")
                            log.info("Payment Methods --- ${paymentMethod}")

                            etisalatSmilesPaymentId = etisalatApiService.callRedeemPointsForPartner(quote.id.toString(), String.valueOf((int) payment.pointsAmount),
                                PaymentMethodEnum.ETISALAT_SMILES_MIX.name(), session['etisalatUserId'], payment.transactionId, payment.etisalatUser.loyalityId, authToken, tokenType, token)

                            if (etisalatSmilesPaymentId) {
                                session['EtisalatSmilesPaymentId'] = etisalatSmilesPaymentId
                            } else {
                                //TODO send void authorize
                                paymentService.process(params, PayfortCommandEnum.VOID_AUTHORIZATION, null, null)
                                paymentService.changePaymentStatus(quote, PaymentStatusEnum.CANCEL)
                                payment.pointsAmount = null
                                payment.moneyAmount = null
                                payment.save(flash: true)
                                def errorDetails = []

                                errorDetails.add([title: "Transaction Id", message: "${payment.transactionId}"])
                                errorDetails.add([title: "Loyality ID", message: "${payment.etisalatUser.loyalityId}"])
                                errorDetails.add([title: "Partner Transaction Id", message: "${quote.id}"])

                                def errorMessage = g.message(message: "Sorry something went wrong while redeeming points. Payment has been rolled back")
                                render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                                return
                            }

                        }
                        log.info("Etisalat Quote Id --- ${quote.id.toString()}")
                        log.info("Etisalat User Id --- ${payment.etisalatUser.userId}")
                        log.info("Etisalat Loyalty ID --- ${payment.etisalatUser.loyalityId}")
                        log.info("Etisalat Token --- ${session['etisalatToken']}")
                        log.info("Etisalat Token Type--- ${session['etisalatTokenType']}")
                        log.info("Etisalat Auth Token --- ${session['etisalatAuthToken']}")

                        if (!etisalatApiService.callAccrualForPartner(quote.id.toString(), moneyAmount.toString(), paymentMethod, session['etisalatUserId'], payment.transactionId, payment.etisalatUser.loyalityId,
                            authToken, tokenType, token, etisalatSmilesPaymentId.toString())) {
                            //TODO send void authorize
                            paymentService.process(params, PayfortCommandEnum.VOID_AUTHORIZATION, null, null)
                            paymentService.changePaymentStatus(quote, PaymentStatusEnum.CANCEL)
                            payment.pointsAmount = null
                            payment.moneyAmount = null
                            payment.save(flash: true)
                            def errorDetails = []

                            errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
                            errorDetails.add([title: "Loyality ID", message: "${session['etisalatloyaltyId']}"])
                            errorDetails.add([title: "Partner Transaction Id", message: "${quote.id}"])

                            def errorMessage = g.message(message: "Sorry something went wrong while accruing points. Payment has been rolled back")
                            render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                            return
                        }
                    }

                    log.debug(".checkout.success Redirect user to thankyou for quoteId: ${quote.id}")
                    //All good? redirect user to thankyou page
                    redirect mapping: "carCheckoutThankyou", params: [country: params.country, lang: params.lang], base: getBaseUrl()
                    return
                } else {
                    String failureMessage = params.response_message +
                        (params.acquirer_response_message ? " - " + params.acquirer_response_message : "")
                    notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message: failureMessage, quoteId: quote.id]

                    session[IConstant.CHECKOUT_QUOTE_ID] = quote.id
                    flash.error = params.response_message
                    log.error "#### ERROR #### -> ${params.response_message} for ${quote.id} with status-> ${params.status} #### ERROR ####"
                }
            } else {
                log.warn(".checkout.success quote is isProcessed -> ${quote?.id}")
                log.debug(".checkout.success.is.processed still redirecting to thankyou")
                redirect mapping: "carCheckoutThankyou", params: [country: params.country, lang: params.lang], base: getBaseUrl()
                return
            }

        } else {
            log.error(".checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code: 'checkout.general.error')
            redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        }

        if (providerPaymentQuoteMapping) {
            session[IConstant.STEP2_CARID] = providerPaymentQuoteMapping.quoteId
            redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        }
        redirect mapping: 'carCheckoutPaynow', params: [country: params.country, lang: params.lang], base: getBaseUrl()
    }

    /**
     * All offline payment method submit to this action.
     * Note: Currently we support COD only. 21.Jun.2016
     */
    def order() {
        log.info("checkout.order - entering with params:$params")
        String method = params.paymentMethod
        def quoteId = params.quoteId

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.findPaymentMethod(method)

        if (!paymentMethodEnum) {
            log.warn("Invalid paymentmethod passed: ${params.paymentMethod}")
            flash.message = g.message(code: "default.general.error")
            redirect mapping: "carHome", params: [lang: utilService.getLanguage(), country: session[IConstant.SITE_COUNTRY]], base: getBaseUrl()
            return
        }

        CarQuote quote = CarQuote.findById(quoteId)

        if (!quote.isNotProcessed()) {
            log.warn(".checkout.order quote is processed -> ${quote?.id}")
            flash.message = g.message(code: "default.general.error")
            redirect mapping: "carHome", params: [lang: utilService.getLanguage(), country: session[IConstant.SITE_COUNTRY]], base: getBaseUrl()
            return
        }

        if (quote.isModified(params.p_quote_v)) {
            flash.error = g.message(code: 'checkout.quote.modified')
            log.error("car.checkout.order - quote was modified:${quote.id} ")
            redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        }

        checkoutService.offlinePayment(quote, paymentMethodEnum)

        flash.quoteId = quoteId

        redirect mapping: 'carCheckoutThankyou', params: [country: params.country, lang: params.lang], base: getBaseUrl()
    }

    def knetPayment() {
        log.info("checkout.knetPayment - params:${params}")
        String method = params.paymentMethod
        def quoteId = params.quoteId
        String country = params.country

        CarQuote quote = CarQuote.findById(quoteId)

        if (!quote.isNotProcessed()) {
            log.warn(".checkout.order quote is processed -> ${quote?.id}")
            flash.message = g.message(code: "default.general.error")
            redirect mapping: "carHome", params: [lang: utilService.getLanguage(), country: session[IConstant.SITE_COUNTRY]]
            return
        }

        if (quote.isModified(params.p_quote_v)) {
            flash.error = g.message(code: 'checkout.quote.modified')
            log.error("car.checkout.order - quote was modified:${quote.id} ")
            redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        String quoteVersion = quote.version.toString()

        String knetUrl = checkoutService.getKnetPaymentPage(quote, quoteVersion, country)

        log.info("knetUrl:${knetUrl}")
        redirect url: knetUrl
    }

    /**
     * Show Prepare Payment Page
     * @return
     */
    def preparePaymentPage() {
        log.info(".preparePaymentPage - params:$params")

        def quoteId = flash.quoteId

        if (!quoteId) {
            //Try looking into quote parameter
            quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        }

        if (quoteId && quoteId instanceof String) {
            quoteId = Long.parseLong(quoteId)
        }

        log.debug(".checkout.preparePaymentPage entering with quoteId: ${quoteId}")

        //if params are null redirect to homeInsurance page
        if (!quoteId) {
            CountryEnum country = CountryEnum.findCountry(params.country) ?: CountryEnum.UAE
            redirect mapping: 'carIndex', params: [lang: utilService.getLanguage(), country: country.code], base: getBaseUrl()
            return
        }

        CarQuote carQuote = CarQuote.get(quoteId)

        String hash = AESCryption.encrypt(carQuote.id.toString())

        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain

        List<DocumentTypeEnum> additionalDocs = commonUtilService.getRequiredDocumentsList(carQuote)

        render view: '/payment/preparePayment', model: [
            quote               : carQuote,
            lang                   : LocaleContextHolder.locale.language,
            hash                   : hash,
            additionalDocs         : additionalDocs,
            whitelabel             : whiteLabelDomain.brand,
            totalPoints            : 0,
            conversionRate         : configurationService.getValue('etisalatSmiles.api.conversionRate')
        ]
    }

    /**
     * Payment success action
     */
    def thankyou() {

        def quoteId = flash.quoteId

        log.info("Etisalat Session Mix Money Amount ------  ${session['MixMoneyAmount']}")

        if (!quoteId) {
            //Try looking into quote parameter
            quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        }

        if (quoteId && quoteId instanceof String) {
            quoteId = Long.parseLong(quoteId)
        }

        log.debug(".checkout.thankyou entering with quoteId: ${quoteId}")

        //if params are null redirect to homeInsurance page
        if (!quoteId) {
            CountryEnum country = CountryEnum.findCountry(params.country) ?: CountryEnum.UAE
            redirect mapping: 'carIndex', params: [lang: utilService.getLanguage(), country: country.code], base: getBaseUrl()
            return
        }

        CarQuote carQuote = CarQuote.get(quoteId)
        EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(carQuote)
        //Quote is not processed yet, redirect back to quotes srp page
        if (carQuote.isNotProcessed()) {
            CountryEnum country = CountryEnum.findCountry(params.country) ?: CountryEnum.UAE

            redirect mapping: 'carQuotes', id: AESCryption.encrypt(carQuote.id.toString()),
                params: [lang: utilService.getLanguage(), country: country.code], base: getBaseUrl()
            return
        }

        log.debug(".checkout.thankyou CarQuote found with id ${carQuote.id}")

        Country country = Country.get(CountryEnum.findCountry(params.country)?.getId())

        String discountCode
        if (country) {
            //Referral only for UAE
            if (country.code == CountryEnum.UAE.isoAlpha2Code) {
                discountCode = checkoutService.getOrCreateDiscountCode(ProductType.CAR, country, carQuote.user, quoteId)
            }
        } else {
            log.warn("Could not find a Country object corresponding to country code = $params.country. Discount code " +
                "can not be created without the country information, skipping that step.")
        }

        def user = carQuote.user
        def hash = AESCryption.encrypt(carQuote.id.toString())
        String brand = sessionService.getBrandCode()

        notify(
            AsyncEventConstants.CAR_QUOTE_PURCHASED,
            [quoteId: carQuote.id, country: CountryEnum.findCountry(params.country), lang: LocaleContextHolder.locale.language, discountCode: discountCode,
             hash   : hash, brand: brand, serverName: request.serverName]
        )

        // for life quote addon
        LifeQuote lifeQuoteAddonPurchased
        lifeQuoteAddonPurchased = lifeQuoteService.createLifeQuoteAddonPurchaseIfAny(carQuote)

        if (lifeQuoteAddonPurchased) {
            String postQuestionnaireLink = lifeUtilService.getPostQuestionnaireLink(lifeQuoteAddonPurchased, 'en')
            notify(
                AsyncEventConstants.LIFE_QUOTE_PURCHASED,
                [lifeQuoteId: lifeQuoteAddonPurchased.id, lang: LocaleContextHolder.locale.language, postQuestionnaireLink: postQuestionnaireLink]
            )
        }

        String conversionRate = configurationService.getValue('etisalatSmiles.api.conversionRate')
        String etisalatloyaltyId
        String mixMoneyAmount
        String mixPointsAmount
        if (payment){
            etisalatloyaltyId = payment.etisalatUser.loyalityId

        }else {
            etisalatloyaltyId = session['etisalatLoyalityId']
        }
        if (payment && payment.paymentMethod == PaymentMethodEnum.ETISALAT_SMILES_MIX.name()){
            mixMoneyAmount = payment.moneyAmount.toString()
            mixPointsAmount = payment.pointsAmount.toString()
        }else {
            mixMoneyAmount = session['MixMoneyAmount']
            mixPointsAmount = session['MixPointsAmount']
        }
        log.info("Thankyou Session ID ${session.getId().toString()}")
        log.info("Thankyou Mix Money ${session['MixMoneyAmount']}")

        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain
        List attributeNames = session.attributeNames.toList()
        attributeNames.each { String attribute ->
            session.removeAttribute(attribute)
        }
        session[IConstant.WHITE_LABEL_DOMAIN] = whiteLabelDomain

        //Create new session and add locale, name and mobile
        def newSession = request.getSession()
        LocaleContextHolder.setLocale(new Locale(params.lang))
        newSession.'org.springframework.web.servlet.i18n.SessionLocaleResolver.LOCALE' = LocaleContextHolder.locale
        newSession[IConstant.STEP2_NAME] = carQuote.name
        newSession[IConstant.STEP2_MOBILE] = carQuote.mobile
        newSession[IConstant.SITE_COUNTRY] = CountryEnum.findCountry(params.country)
        newSession[IConstant.WHITE_LABEL_DOMAIN] = whiteLabelDomain
        newSession[IConstant.INSURANCE_UTM_SOURCE] = carQuote.utmSource

        newSession['etisalatloyaltyId'] = etisalatloyaltyId


        log.info("Etisalat Mix Money Amount ------  ${session['MixMoneyAmount']}")
        log.info("Etisalat Converstion Rate ------  ${newSession[IConstant.CONVERSION_RATE]}")

        List<DocumentTypeEnum> additionalDocs = commonUtilService.getRequiredDocumentsList(carQuote)

        render view: '/car/checkout/thankyou', model: [
            carQuote               : carQuote,
            country                : newSession[IConstant.SITE_COUNTRY],
            session                : newSession,
            lang                   : LocaleContextHolder.locale.language,
            discountCode           : discountCode,
            hash                   : hash,
            additionalDocs         : additionalDocs,
            whitelabel             : whiteLabelDomain.brand,
            etisalatMixMoneyAmount : mixMoneyAmount,
            etisalatMixPointsAmount: mixPointsAmount,
            conversionRate         : conversionRate

        ]
    }

    /**
     * Store notifications from payfort in responses
     * This action will only be called, when manually Captured, Refund or Void Authorized
     *
     * In case of Captured manually, this action will process the policy.
     * In case of Void Authorization or Refund, this will treat this as warning and store the response.
     *
     * @return
     */
    def notificationFeedback() {
        log.info("checkout.notificationFeedback - params:${params}")
        Thread.sleep(5000L) //Wait for 1 seconds, to allow redirect response handler to handle response

        if (params.merchant_reference == null || params.status == null || params.signature == null) {
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render "Missing data"
            return
        }

        String merchantReference = params.merchant_reference
        String[] merchantReferenceParts = merchantReference.split("-")

        boolean isInvoicePayment = PaymentInvoice.findByMerchantRef(merchantReference)
        // checking if payment is made against an invoice
        // merchant reference for an invoice has 4 parts, seperated by dash but it shouldn't be MetLife Merchant Ref.
        // Can be: PA-time-PaLeadId-UUID
        if (isInvoicePayment) {
            // Invoice Payments: Pet and Life invoices: PP-time-PetId-InvoiceId, PL-time-PetId-InvoiceId,
            if (checkoutService.isSecured(params)) {
                paymentService.savePaymentResponse(null, params)

                if (params.status.toString() == PayfortStatusEnum.PURCHASE_SUCCESS.status) {
                    paymentService.handleRecurringPaymentNotificationOnPayfortLink(params)
                } else {
                    log.warn("checkout.notificationFeedback - Payment not successful, merchant reference: $merchantReference , response code: ${params.response_code} , response message: ${params.response_message}")
                }
            } else {
                log.error("checkout.notificationFeedback **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED **** params:$params")
            }

        } else if ((merchantReferenceParts.length == 4 && !merchantReference.startsWith("MT") &&
            !merchantReference.startsWith("PP") && !merchantReference.startsWith("PL") )) {

            if (checkoutService.isSecured(params)) {
                if (params.status.toString() == PayfortStatusEnum.PURCHASE_SUCCESS.status) {
                    QuoteAdditionalPayment quoteAdditionalPayment = paymentService.handlePaymentNotificationOnPayfortLink(params)
                    if (merchantReference.startsWith("PA")) {
                        paymentService.handlePersonalAccidentPaymentOnPayfortLink(params, quoteAdditionalPayment, PersonalAccidentService.SALAMA_PA_YOUTH_PRODUCT_ID)
                        notify AsyncEventConstants.PA_YOUTH_SALE, [merchantReference: params.merchant_reference]
                    }
                } else {
                    log.warn("checkout.notificationFeedback - Payment not successful, merchant reference: $merchantReference , response code: ${params.response_code} , response message: ${params.response_message}")
                }

            } else {
                log.error("checkout.notificationFeedback **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED **** params:$params")
            }

        } else if (merchantReference.startsWith("MT")) { // payment notification for Metlife
            if (checkoutService.isSecured(params)) {
                if (params.status.toString() == PayfortStatusEnum.PURCHASE_SUCCESS.status) {

                    handleMetLifeNotification(merchantReference, params)

                } else {
                    log.warn("checkout.notificationFeedback - Payment not successful, merchant reference: $merchantReference , response code: ${params.response_code} , response message: ${params.response_message}")
                }
            } else {
                log.error("checkout.notificationFeedback **** Alert!! SECURITY SIGNATURE CHECK FAILED **** params:$params")
            }
        } else if (merchantReference.startsWith("UT")) {
            //UT-time-UserId
            if (checkoutService.isSecured(params)) {
                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status.toString()) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status.toString())) {
                    //This merchant reference is only used for storing Payment Cards
                    handleUserPaymentCardNotification(merchantReference, params)
                }
            } else {
                log.error("checkout.notificationFeedback **** Alert!! SECURITY SIGNATURE CHECK FAILED **** params:$params")
            }

        } else {
            HttpStatus httpStatus = checkoutService.processPayfortFeedback("directFeedback", params)
            response.setStatus(httpStatus.value())
            if (merchantReference.startsWith("PA") && httpStatus.value() == HttpStatus.OK.value()) {
                notify AsyncEventConstants.PA_YOUTH_SALE, [merchantReference: params.merchant_reference]
            }
        }
        log.info("returning response - merchantReference:${merchantReference} with httpStatus:${response.status}")

        render "OK"
    }

    /**
     * Handle Direct server to server response from Payfort.
     * Process the purchase for all Insurances, if not already processed
     *
     * @return
     */
    def directFeedback() {
        log.info("checkout.directFeedback - params:${params}")
        Thread.sleep(5000L) //Wait for 1 seconds, to allow redirect response handler to handle response

        if (params.merchant_reference == null || params.status == null || params.signature == null) {
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render "Missing data"
            return
        }

        String merchantReference = params.merchant_reference
        String[] merchantReferenceParts = merchantReference.split("-")

        boolean isInvoicePayment = PaymentInvoice.findByMerchantRef(merchantReference)
        // checking if payment is made against an invoice
        // merchant reference for an invoice has 4 parts, seperated by dash but it shouldn't be MetLife Merchant Ref.
        // Can be: PA-time-PaLeadId-UUID
        if (isInvoicePayment) {
            // Invoice Payments: Pet and Life invoices: PP-time-PetId-InvoiceId, PL-time-PetId-InvoiceId,
            if (checkoutService.isSecured(params)) {
                paymentService.savePaymentResponse(null, params)

                if (params.status.toString() == PayfortStatusEnum.PURCHASE_SUCCESS.status) {
                    paymentService.handleRecurringPaymentNotificationOnPayfortLink(params)
                } else {
                    log.warn("checkout.directFeedback - Payment not successful, merchant reference: $merchantReference , response code: ${params.response_code} , response message: ${params.response_message}")
                }
            } else {
                log.error("checkout.directFeedback **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED **** params:$params")
            }

        } else if ((merchantReferenceParts.length == 4 && !merchantReference.startsWith("MT") &&
            !merchantReference.startsWith("PP") && !merchantReference.startsWith("PL") )) {

            if (checkoutService.isSecured(params)) {
                if (params.status.toString() == PayfortStatusEnum.PURCHASE_SUCCESS.status) {
                    QuoteAdditionalPayment quoteAdditionalPayment = paymentService.handlePaymentNotificationOnPayfortLink(params)
                    if (merchantReference.startsWith("PA")) {
                        paymentService.handlePersonalAccidentPaymentOnPayfortLink(params, quoteAdditionalPayment, PersonalAccidentService.SALAMA_PA_YOUTH_PRODUCT_ID)
                        notify AsyncEventConstants.PA_YOUTH_SALE, [merchantReference: params.merchant_reference]
                    }
                } else {
                    log.warn("checkout.directFeedback - Payment not successful, merchant reference: $merchantReference , response code: ${params.response_code} , response message: ${params.response_message}")
                }

            } else {
                log.error("checkout.directFeedback **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED **** params:$params")
            }

        } else if (merchantReference.startsWith("MT")) { // payment notification for Metlife
            if (checkoutService.isSecured(params)) {
                if (params.status.toString() == PayfortStatusEnum.PURCHASE_SUCCESS.status) {

                    handleMetLifeNotification(merchantReference, params)

                } else {
                    log.warn("checkout.directFeedback - Payment not successful, merchant reference: $merchantReference , response code: ${params.response_code} , response message: ${params.response_message}")
                }
            } else {
                log.error("checkout.directFeedback **** Alert!! SECURITY SIGNATURE CHECK FAILED **** params:$params")
            }
        } else if (merchantReference.startsWith("UT")) {
            //UT-time-UserId
            if (checkoutService.isSecured(params)) {
                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status.toString()) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status.toString())) {
                    //This merchant reference is only used for storing Payment Cards
                    handleUserPaymentCardNotification(merchantReference, params)
                }
            } else {
                log.error("checkout.directFeedback **** Alert!! SECURITY SIGNATURE CHECK FAILED **** params:$params")
            }

        } else {
            HttpStatus httpStatus = checkoutService.processPayfortFeedback("directFeedback", params)
            response.setStatus(httpStatus.value())
            if (merchantReference.startsWith("PA") && httpStatus.value() == HttpStatus.OK.value()) {
                notify AsyncEventConstants.PA_YOUTH_SALE, [merchantReference: params.merchant_reference]
            }
        }
        log.info("returning response - merchantReference:${merchantReference} with httpStatus:${response.status}")

        render "OK"
    }

    /**
     * Check card was stored, and payment was refunded otherwise do so
     */
    private handleUserPaymentCardNotification(String merchantReference, def params) {
        //TODO: //If payment card is not saved, save it and see Check status of reference, if not voided, void it
        //TODO: Save Payment Card against user
        Long userId = utilService.decodeMerchantRef(params.merchant_reference)
        log.info("userId:${userId}")
        if (!userId) {
            log.error("User not found")
            return
        }
        User user = User.read(userId)
        if (!user) {
            log.error("User not found")
            return
        }

        PaymentCardDetails paymentCard = PaymentCardDetails.findByMerchantRef(merchantReference)
        if (paymentCard == null) {
            log.info("checkout.handleUserPaymentCardNotification - Saving card details")
            paymentService.savePaymentCardDetails(userId, params)

            params.insuranceType = UserPaymentCardController.INSURANCE_TYPE
            params.country = CountryEnum.findCountryById(user.country.id).code
            //Void Auth the amount
            log.info("checkout.handleUserPaymentCardNotification - Void Auth Amount")
            paymentService.process(params, PayfortCommandEnum.VOID_AUTHORIZATION, null, null)
        }

    }

    private handleMetLifeNotification(String merchantReference, def params) {
        log.info("checkout.handleMetLifeNotification - merchantReference:${merchantReference}")

        String oldStatus
        PartnerLeadPayment partnerLeadPayment
        boolean isFirstPayment = merchantReference.split("-").length == 3

        if (isFirstPayment) {
            partnerLeadPayment = PartnerLeadPayment.findByMerchantRef(merchantReference)
            oldStatus = partnerLeadPayment.paymentStatus
        }

        partnerLeadPayment = partnerSgService.updatePaymentStatustoPaid(merchantReference, params)

        if (partnerLeadPayment && isFirstPayment) {
            log.info("Payment status change notification: merchantReference:$merchantReference, " +
                "oldStatus:$oldStatus, newStatus:${partnerLeadPayment.paymentStatus}")

            notify AsyncEventConstants.PAYMENT_STATUS_UPDATED,
                [partnerLeadPaymentId: partnerLeadPayment.id, paidDate: partnerLeadPayment.paidDate,
                 newStatus           : partnerLeadPayment.paymentStatus.toString(), oldStatus: oldStatus]
        }

        if (isFirstPayment) {
            notify AsyncEventConstants.METLIFE_SALE, partnerLeadPayment.id
        }
    }
    /**
     * Response handle for Knet Payments
     * @return
     */
    def knetResponse() {
        log.info("checkout.knetResponse - params:$params")
        CarQuote quote = CarQuote.findByKnetPaymentId(params.paymentid)

        if (!quote) {
            //TODO: Show Error page.
        }

        paymentService.savePaymentResponse(quote, params)
        paymentService.saveKnetPaymentResponse(quote, params)
        session[IConstant.CHECKOUT_QUOTE_ID] = quote.id
        //flash.quoteId = quote.id //used in Thankyou action

        if (quote.isNotProcessed()) {
            if (params.result == KnetPaymentStatusEnum.CAPTURED.getValue()) {

                paymentService.paid(params, [payment_method    : PaymentMethodEnum.KNET.toString(),
                                             paymentGatewayEnum: PaymentGatewayEnum.KNET])

                String receiptPath = g.createLink(mapping: 'carCheckoutThankyou',
                    params: [country: 'kwt', lang: quote.lang], absolute: true)
                receiptPath = "REDIRECT=${receiptPath}${params.toQueryString()}"
                render receiptPath
                return
            }
        } else {
            flash.error = "Quote already processed"
        }

        flash.error = params.response_message
        //TODO: if not captured redirect to error or payment page with error message

        String receiptUrl = g.createLink(mapping: 'carCheckoutPaynow',
            params: [country: 'kwt', lang: quote.lang], absolute: true)

        log.info("receiptUrl:${receiptUrl}")
        render "REDIRECT=${receiptUrl}${params.toQueryString()}"
        return
    }

    /**
     * Error Response handle for Knet Payments
     * Error either from Knet or from Our system when we are unable to handle response
     * @return
     */
    def knetError() {
        log.info("checkout.knetError - params:$params")

        paymentService.updateKnetPaymentResponse(params.PaymentID, params.ErrorCode)

        Map parameters = [country: 'kwt', lang: utilService.getLanguage()]
        parameters.putAll(params)

        String receiptUrl = g.createLink(mapping: 'carCheckoutPaynow', params: parameters, absolute: true)
        log.info("receiptUrl:${receiptUrl}")

        redirect url: receiptUrl
    }

    /**
     * Response from CyberSource payment.
     * Performs security check, and based on decision, make it paid or redirect to error
     *
     * @return
     */
    def cyberSourceRedirect() {
        log.info("checkout.cyberSourceRedirect - entering with params:${params}")

        String lang = params.req_locale ?: utilService.getLanguage()

        String country = null
        if (params.req_bill_to_address_country) {
            country = CountryEnum.findCountryByDfp(params.req_bill_to_address_country)?.code
        }
        country = country ?: CountryEnum.findCountryById(utilService.getCountry().id).code

        boolean isSecured = checkoutService.isCyberSourceRequestSecured(params)

        def carQuoteId = CarQuote.decodeMerchantRef(params.req_reference_number)

        CarQuote quote = CarQuote.get(carQuoteId)
        flash.quoteId = quote.id

        if (isSecured) {

            if (quote && quote.isNotProcessed()) {

                paymentService.savePaymentResponse(quote, params)

                if (CyberSourcePaymentStatusEnum.ACCEPT.toString().equals(params.decision)) {

                    params.merchant_reference = params.req_reference_number

                    paymentService.paid(params, [paymentGatewayEnum: PaymentGatewayEnum.CYBERSOURCE])

                    log.debug(".checkout.success Redirect user to thankyou for quoteId: ${quote.id}")
                    //All good? redirect user to thankyou page
                    redirect mapping: "carCheckoutThankyou", params: [country: country, lang: lang]
                    return
                } else {
                    notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message: params.message, quoteId: quote.id]
                    session[IConstant.CHECKOUT_QUOTE_ID] = quote.id
                    flash.error = params.message
                    log.error "#### ERROR #### -> ${params.message} for ${quote.id} with decision-> ${params.decision} #### ERROR ####"
                }

            } else {
                log.warn(".checkout.success quote is isProcessed -> ${quote?.id}")
                log.debug(".checkout.success.is.processed still redirecting to thankyou")
                redirect mapping: "carCheckoutThankyou", params: [country: country, lang: lang]
                return
            }

        } else {
            log.error(".checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code: 'checkout.general.error')

            redirect mapping: 'carCheckoutIndex', params: [country: country, lang: lang]
            return
        }

        redirect mapping: 'carCheckoutPaynow', params: [country: country, lang: lang]

    }

    /**
     * Notification response from Cyber Source.
     *
     * @return
     */
    def cyberSourceNotification() {
        log.info("checkout.cyberSourceNotification - entering with params:${params}")

        boolean isSecured = checkoutService.isCyberSourceRequestSecured(params)

        def carQuoteId = CarQuote.decodeMerchantRef(params.req_reference_number)

        CarQuote quote = CarQuote.get(carQuoteId)
        flash.quoteId = quote.id

        if (isSecured) {

            paymentService.savePaymentResponse(quote, params)

            //TODO: Its not confirmed when this response will be received. Ideally this should update the quote status
            //TODO: based on the decision if not already updated

        } else {
            log.error(".checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            render g.message(code: 'checkout.general.error')
            return
        }

        render "OK"
    }

    def payByEtisalatPoints() {
        def quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        def totalAmountInPoints = params.totalAmountInPoints
        PaymentMethodEnum paymentMethod = params.paymentMethod
        /**
         * Bots are trying to access this action for that reason if no quoteId found in session send on 404
         */
        if (!quoteId) {
            render status: 404
            return
        }
        def totalPoints, tokenType, token, authToken, etisalatSmilesPaymentId, isSuccessful

        CarQuote quote = CarQuote.findById(quoteId)
        EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(quote)
        (totalPoints, tokenType, token, authToken) = etisalatApiService.getTotalPoints(payment.transactionId, payment.etisalatUser.loyalityId)

        def correctTotalAmountInPoints = new BigDecimal((quote.totalPrice) * new BigDecimal(session[IConstant.CONVERSION_RATE])).setScale(0, RoundingMode.UP)
        if (new BigDecimal(totalPoints).compareTo(new BigDecimal(totalAmountInPoints)) < 0) {
            paymentService.changePaymentStatus(quote, PaymentStatusEnum.CANCEL)

            def errorDetails = []

            errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
            errorDetails.add([title: "Loyality ID Number", message: "${session['etisalatloyaltyId']}"])
            errorDetails.add([title: "Partner Transaction Id", message: "${quoteId}"])

            def errorMessage = g.message(message: "Sorry, you do not have enough smiles points for this purchase")
            render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country, goBack: true]
            return
        }
        if (new BigDecimal(correctTotalAmountInPoints) != new BigDecimal(totalAmountInPoints)) {
            paymentService.changePaymentStatus(quote, PaymentStatusEnum.CANCEL)

            def errorDetails = []

            errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
            errorDetails.add([title: "Loyality ID", message: "${session['etisalatloyaltyId']}"])
            errorDetails.add([title: "Partner Transaction Id", message: "${quoteId}"])

            def errorMessage = g.message(message: "Sorry, incorrect input smiles points for this purchase")
            render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country, goBack: true]
            return
        }

        setEtisalatSession(totalPoints, tokenType, token, authToken)
        etisalatSmilesPaymentId = etisalatApiService.callRedeemPointsForPartner(quote.id.toString(), totalAmountInPoints, PaymentMethodEnum.ETISALAT_SMILES_POINTS.name(),
            session['etisalatUserId'], payment.transactionId, payment.etisalatUser.loyalityId, session['etisalatAuthToken'], session['etisalatTokenType'], session['etisalatToken'])

        if (etisalatSmilesPaymentId) {
            session['etisalatSmilesPaymentId'] = etisalatSmilesPaymentId
            paymentService.paidByEtisalatPoints(quote)
            flash.quoteId = quote.id
            log.debug(".checkout.success Redirect user to thankyou for quoteId: ${quote.id}")
            //All good? redirect user to thankyou page
            redirect mapping: 'carCheckoutThankyou', params: [country: params.country, lang: params.lang], base: getBaseUrl()
            return
        } else {
            paymentService.changePaymentStatus(quote, PaymentStatusEnum.CANCEL)
            payment.pointsAmount = null
            payment.moneyAmount = null
            payment.save(flash: true)
            def errorDetails = []

            errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
            errorDetails.add([title: "Loyality ID", message: "${session['etisalatloyaltyId']}"])
            errorDetails.add([title: "Partner Transaction Id", message: "${quote.id}"])

            def errorMessage = g.message(message: "Sorry something went wrong while redeeming points")

            render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country, goBack: true]
            return
        }
    }


    def updatePrice() {
        session['MixMoneyAmount'] = params.moneyAmount
        log.info("Params For Update Price ${params}")
        EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(CarQuote.findById(CarQuote.decodeMerchantRef(params.merchantRef)))
        payment.moneyAmount = new BigDecimal(params.moneyAmount)
        payment.pointsAmount = new BigDecimal(params.pointsAmount)
        payment.save(flush: true)
        session['MixPointsAmount'] = params.pointsAmount
        log.info("Update Price Mix Money ${session['MixMoneyAmount']}")
        log.info("Update Price Mix Point ${session['MixPointsAmount']}")
        log.info("Update Price Session ID ${session.getId().toString()}")
        render "ok"
    }

    private boolean validateEtisalatPayment(CarQuote quote) {
        log.info("Validate Mix Money ${session['MixMoneyAmount']}")
        log.info("Validate Price Mix Point ${session['MixPointsAmount']}")
        EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(quote)
        def moneyDifference = (new BigDecimal((quote.totalPrice)) - new BigDecimal(payment.moneyAmount)).setScale(2, RoundingMode.HALF_UP)
        def correctAmountInPoints = new BigDecimal(moneyDifference * new BigDecimal(configurationService.getValue('etisalatSmiles.api.conversionRate'))).setScale(0, RoundingMode.HALF_UP)
        if (new BigDecimal(payment.pointsAmount).compareTo(new BigDecimal(correctAmountInPoints)) != 0) {
            return false
        }
        return true
    }

    private void setEtisalatSession(totalPoints, tokenType, token, authToken) {
        session['etisalatTotalPoints'] = totalPoints
        session['etisalatTokenType'] = tokenType ? tokenType : null
        session['etisalatToken'] = token ? token : null
        session['etisalatAuthToken'] = authToken ? authToken : null
    }

    private String getBaseUrl() {
        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]
        String baseUrl = whiteLabelDomain ? whiteLabelDomain.baseUrl : grailsApplication.config.getProperty('yallacompare.baseURL')
        log.info("baseUrl:${baseUrl}")

        baseUrl
    }

}
