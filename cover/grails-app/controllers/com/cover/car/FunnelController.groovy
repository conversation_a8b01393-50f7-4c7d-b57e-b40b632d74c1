package com.cover.car


import com.cover.car.commands.*
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CallRequest
import com.safeguard.ClaimPeriodEnum
import com.safeguard.CountryEnum
import com.safeguard.CoverPreferenceEnum
import com.safeguard.Dealer
import com.safeguard.LeadType
import com.safeguard.ProductTypeEnum
import com.safeguard.QuoteCreatorEnum
import com.safeguard.RepairTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.User
import com.safeguard.ValuationSourceEnum
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteKsa
import com.safeguard.car.CarValuationDto
import com.safeguard.car.EtisalatSmilesPayment
import com.safeguard.car.EtisalatUrlLog
import com.safeguard.car.EtisalatUser
import com.safeguard.car.vehicle.Model
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.util.Environment
import org.joda.time.DateTime
import org.joda.time.LocalDateTime
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.springframework.context.i18n.LocaleContextHolder

import java.time.Year

/**
 * Created by Minhaj on 4/24/16.
 */
class FunnelController {

    static namespace = "car"

    def crmService
    def kwtQuoteService
    def lbnQuoteService
    def egyQuoteService
    def quoteService
    def valuationService
    def utilService
    def policySgService
    def cookieService
    def discountService
    def callRequestService
    def sessionService
    def etisalatApiService
    def configurationService
    def quoteSgService

    /**
     * Home page form submits to this action
     */
    def index(HomeCommand command) {
        log.info("funnel.index - command:${command.toString()}, " +
            "IP:${request.getRemoteAddr()}, userAgent:${request.getHeader("User-Agent")}, session:${session.id}")

        if (command.validate()) {
            session[IConstant.STEP1_YEAR] = command.year
            session[IConstant.STEP1_MAKE] = command.make
            session[IConstant.STEP1_MODEL_MASTER] = command.modelMaster
            session[IConstant.STEP1_MODEL] = command.model
            session[IConstant.STEP1_MAKE_MODEL_TRIM] = command.makeModelTrim

            redirect(action: 'vehicle', params: [lang: LocaleContextHolder.locale.language, country: params.country], base: sessionService.getBaseUrl())
        } else {
            Boolean showValentinePromo = false
            String discountUntil = ''

            WhiteLabelBrandEnum brandEnum = sessionService.getBrand()
            WhiteLabelBrand whiteLabelBrand = WhiteLabelBrand.read(brandEnum.id)
            def discount = discountService.getApplicableDiscountCode(CountryEnum.findCountry(params.country), ProductTypeEnum.CAR.value(), null, null, null, null, whiteLabelBrand)

            def discountAmount = null

            if (discount) {
                discountAmount = discount.discount.toBigInteger()
                discountAmount += (discount.hasPercentDiscount) ? '%' : 'AED'

//                (showValentinePromo, discountUntil)=utilService.getValentineData()
            }
            Locale locale = new Locale(params.lang)
            command = new HomeCommand(
                year: session[IConstant.STEP1_YEAR] ?: -1,
                make: session[IConstant.STEP1_MAKE] ?: -1,
                modelMaster: session[IConstant.STEP1_MODEL_MASTER] ?: -1,
                model: session[IConstant.STEP1_MODEL] ?: -1,
                makeModelTrim: session[IConstant.STEP1_MAKE_MODEL_TRIM] ?: ''
            )

            if (brandEnum && brandEnum == WhiteLabelBrandEnum.ADIB) {
//                log.debug('===============HERE Funnel==============')
//                redirect mapping: "adibLandingPage", params: [country: params.country,
//                                                              lang   : params.lang], base: sessionService.getBaseUrl()
                if (params.country == "uae" || params.country == "kwt" || params.country == "lbn" || params.country == "egy") {
                    render view: '/adib/index', model: [showValentinePromo: showValentinePromo,
                                                        discountUntil     : discountUntil,
                                                        command           : command,
                                                        country           : params.country,
                                                        lang              : params.lang,
                                                        discountAmount    : discountAmount]
                    return

                } else {
                    render view: '/common/notFound'
                    return

                }
            }
            if (brandEnum && brandEnum == WhiteLabelBrandEnum.ETISALAT_SMILES) {
                String errorMessage = null

                log.debug("========================URL==========================")
                log.debug(request.getRequestURL().toString())
                log.debug(request.getQueryString().toString())
                EtisalatUrlLog urlLog = new EtisalatUrlLog()
                urlLog.requestUrl = request.getRequestURL().toString()
                urlLog.queryString = request.getQueryString().toString()
                urlLog.dateCreated = LocalDateTime.now()
                urlLog.save(flush: true)
                log.debug("========================URL==========================")

                removeEtsalatSession()
                session['etisalatEncryptedAccountNumber'] = params.accountNumber ? params.accountNumber : (session['etisalatEncryptedAccountNumber'] ? session['etisalatEncryptedAccountNumber'] : null)
                session['etisalatAuthToken'] = params.authToken ? params.authToken : (session['etisalatAuthToken'] ? session['etisalatAuthToken'] : null)
                session['etisalatFirstAuthToken'] = params.authToken ? params.authToken : (session['etisalatFirstAuthToken'] ? session['etisalatFirstAuthToken'] : null)
                session['etisalatEncryptedloyaltyId'] = params.loyaltyId ? params.loyaltyId : (session['etisalatEncryptedloyaltyId'] ? session['etisalatEncryptedloyaltyId'] : null)

                session[IConstant.CONVERSION_RATE] = configurationService.getValue('etisalatSmiles.api.conversionRate')

                if (session['etisalatEncryptedloyaltyId']) {
                    def loyalityId = session['etisalatEncryptedloyaltyId']
                    session['etisalatloyaltyId'] = etisalatApiService.decryptLoyalityId(loyalityId.toString())
                    session['etisalatTransactionId'] = etisalatApiService.generateXTibTransactionId().toString()

                    def loyaltyIdAndAuthTokenValid

                    if (request.getHeader('referer') && request.getHeader('referer').contains('/car/guides/') ||
                        request.getHeader('Referer') && request.getHeader('Referer').contains('/car/guides/') ||
                        request.getHeader('referer') && request.getHeader('referer').contains('/privacy-policy') ||
                        request.getHeader('Referer') && request.getHeader('Referer').contains('/privacy-policy') ||
                        request.getHeader('referer') && request.getHeader('referer').contains('/terms-and-conditions') ||
                        request.getHeader('Referer') && request.getHeader('Referer').contains('/terms-and-conditions')) {

                        loyaltyIdAndAuthTokenValid = etisalatApiService.checkAccountAndAuthTokenValid(session['etisalatloyaltyId'], session['etisalatFirstAuthToken'], session['etisalatTransactionId'])
                    } else {
                        loyaltyIdAndAuthTokenValid = etisalatApiService.checkAccountAndAuthTokenValid(session['etisalatloyaltyId'], session['etisalatAuthToken'], session['etisalatTransactionId'])
                    }
                    if (!loyaltyIdAndAuthTokenValid) {
                        def errorDetails = []
                        errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
                        errorDetails.add([title: "Loyality ID", message: "${session['etisalatloyaltyId']}"])

                        session.removeAttribute('etisalatEncryptedAccountNumber')
                        session.removeAttribute('etisalatEncryptedloyaltyId')

                        errorMessage = g.message(message: "Loyalty ID or auth token is invalid", locale: locale)
                        render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                        return
                    }

                    redirect mapping: "etisalatLandingPage", params: [country: params.country, lang: params.lang], base: sessionService.getBaseUrl()

                } else {
                    errorMessage = g.message(message: "Loyality ID is not provided", locale: locale)
                    render view: "/common/errorMessage", model: [errorMessage: errorMessage, country: params.country]
                    return
                }
            }

            if (params.country == "uae" || params.country == "kwt" || params.country == "lbn" || params.country == "egy") {

                render view: '/car/index', model: [showValentinePromo: showValentinePromo,
                                                   discountUntil     : discountUntil,
                                                   command           : command,
                                                   country           : params.country,
                                                   discountAmount    : discountAmount]
            } else {
                render view: '/common/notFound'
            }

        }
    }

    def requestCall() {
        try {
            CallRequest callRequest = callRequestService.createCallRequest(
                params.phone as String,
                params.country as String,
                params.lang as String,
                "car",
                params.page as String,
                params.button as String
            )

            flash.requestCall = true

            notify AsyncEventConstants.CALL_REQUESTED, callRequest.id

            redirect mapping: "carIndex", params: [country: params.country, lang: params.lang], base: sessionService.getBaseUrl()
        } catch (Exception e) {
            log.error "Error creating CallRequest:", e
        }
    }

    /**
     * vehicle/step1 action
     */
    def vehicle(HomeCommand homeCommand) {
        log.info "car.funnel.vehicle entering with cmd ${homeCommand}, " +
            "IP:${request.getRemoteAddr()}, userAgent:${request.getHeader("User-Agent")}, session:${session.id}"
        VehicleCommand command = new VehicleCommand()

        Integer year = session[IConstant.STEP1_YEAR] ?: Year.now().getValue()
        Date defaultRegDate = new Date()
        defaultRegDate.set(year: year)

        String carQuoteId = cookieService.getCookie(IConstant.COOKIE_CAR_QUOTE_ID)
        if (carQuoteId) {
            CountryEnum countryEnum = CountryEnum.findCountry(params.country)
            CarQuote carQuote = CarQuote.read(Long.parseLong(carQuoteId))
            if (carQuote && carQuote.quoteCountry.id == countryEnum.id) {
                sessionService.updateSession(carQuote)
            }
        }

        command.purchaseDate = session[IConstant.STEP1_PURCHASE_DATE] ?: defaultRegDate
        command.isNotBoughtYet = session[IConstant.STEP1_IS_NOT_BOUGHT_YET] ?: false
        command.isFirstCar = session[IConstant.STEP1_IS_FIRST_CAR] ?: false
        command.isBrandNew = session[IConstant.STEP1_IS_BRAND_NEW] ?: false
        command.year = year
        command.make = session[IConstant.STEP1_MAKE]
        //After model master introduced, browsing or returning user wont have it, setting it from model
        if (!session[IConstant.STEP1_MODEL_MASTER] && session[IConstant.STEP1_MODEL]) {
            Integer modelId = session[IConstant.STEP1_MODEL]
            session[IConstant.STEP1_MODEL_MASTER] = Model.get(modelId)?.modelMasterId
        }
        command.modelMaster = session[IConstant.STEP1_MODEL_MASTER]
        command.model = session[IConstant.STEP1_MODEL]
        command.makeModelTrim = session[IConstant.STEP1_MAKE_MODEL_TRIM]

        command.vehicleCondition = session[IConstant.STEP1_VEHICLE_CONDITION]
        command.mileage = session[IConstant.STEP1_MILEAGE]
        command.city = session[IConstant.STEP1_CAR_CITY] ?: 1 //1 is id for dubai in city table
        command.firstRegistrationDate = session[IConstant.STEP1_REGISTRATION_DATE] ?: defaultRegDate
        //command.insuredValue = session[IConstant.STEP1_INSURED_VALUE]
        command.isNonGcc = session[IConstant.STEP1_IS_NON_GCC] ?: false
        command.isExpiredPolicy = session[IConstant.STEP1_IS_POLICY_EXPIRED] ?: false
        command.oldPolicyExpiryDate = session[IConstant.STEP1_OLD_POLICY_EXPIRY_DATE] ?: new Date()
        command.isThirdParty = session[IConstant.STEP1_IS_THIRD_PARTY] ?: false
        command.isOldAgency = session[IConstant.STEP1_IS_OLD_AGENCY] ?: true
        command.campaignId = 1
        command.campaignInterest = session[IConstant.STEP1_CAMPAIGN_INTEREST] ?: false

        if (request.method == "POST") {

            if (!homeCommand.model || !homeCommand.year || homeCommand.model < 1 || homeCommand.year < 1) {
                log.warn "Model or year are missing [model: ${homeCommand.model}, year ${homeCommand.year}]"
                redirect action: 'vehicle', base: sessionService.getBaseUrl()
                return
            }
            session[IConstant.STEP1_YEAR] = homeCommand.year
            session[IConstant.STEP1_MAKE] = homeCommand.make
            session[IConstant.STEP1_MODEL_MASTER] = homeCommand.modelMaster
            session[IConstant.STEP1_MODEL] = homeCommand.model
            session[IConstant.STEP1_MAKE_MODEL_TRIM] = homeCommand.makeModelTrim

            //def country = utilService.getCountry()
            //def valuation = valuationService.getValuation(homeCommand.model, homeCommand.year, command.isBrandNew, country)
            //command.insuredValue = valuation.minimum
            command.year = homeCommand.year
            command.make = homeCommand.make
            command.modelMaster = homeCommand.modelMaster
            command.model = homeCommand.model
        }

        render view: '/car/funnel/vehicleDetails', model: [command: command, country: params.country]
    }

    /**
     * driver/step2 action
     */
    def driver(VehicleCommand command) {
        log.info "car.funnel.driver entering with cmd ${command}, " +
            "IP:${request.getRemoteAddr()}, userAgent:${request.getHeader("User-Agent")}, session:${session.id}"
        //In-case user coming from breadcrumb, make sure we get value from session and populate form.


        if (request.method != "POST") {

            if (!session[IConstant.STEP1_YEAR]) {
                // direct get access to driver details is not allowed
                // without submitting vehicle details
                log.debug "Redirect to vehicle details as make year is empty ${session[IConstant.STEP1_YEAR]}"
                redirect mapping: 'carVehicleDetails', params: [lang: LocaleContextHolder.locale.language, country: params.country], base: sessionService.getBaseUrl()
                return
            }

            command.purchaseDate = session[IConstant.STEP1_PURCHASE_DATE]
            command.isNotBoughtYet = session[IConstant.STEP1_IS_NOT_BOUGHT_YET] ?: false
            command.isBrandNew = session[IConstant.STEP1_IS_BRAND_NEW] ?: false
            command.isFirstCar = session[IConstant.STEP1_IS_FIRST_CAR] ?: false
            command.year = session[IConstant.STEP1_YEAR]
            command.make = session[IConstant.STEP1_MAKE]
            command.modelMaster = session[IConstant.STEP1_MODEL_MASTER]
            command.model = session[IConstant.STEP1_MODEL]
            command.makeModelTrim = session[IConstant.STEP1_MAKE_MODEL_TRIM]
            command.mileage = session[IConstant.STEP1_MILEAGE]
            command.vehicleCondition = session[IConstant.STEP1_VEHICLE_CONDITION]

            command.city = session[IConstant.STEP1_CAR_CITY]
            command.firstRegistrationDate = session[IConstant.STEP1_REGISTRATION_DATE]
            //command.insuredValue = session[IConstant.STEP1_INSURED_VALUE]
            command.isNonGcc = session[IConstant.STEP1_IS_NON_GCC]
            command.isExpiredPolicy = session[IConstant.STEP1_IS_POLICY_EXPIRED]
            command.isThirdParty = session[IConstant.STEP1_IS_THIRD_PARTY]
            command.isOldAgency = session[IConstant.STEP1_IS_OLD_AGENCY]
            command.oldPolicyExpiryDate = session[IConstant.STEP1_OLD_POLICY_EXPIRY_DATE]
            command.campaignInterest = session[IConstant.STEP1_CAMPAIGN_INTEREST]
            command.campaignId = session[IConstant.STEP1_CAMPAIGN_ID]
        }

        //Update values in session
        session[IConstant.STEP1_PURCHASE_DATE] = command.purchaseDate
        session[IConstant.STEP1_IS_NOT_BOUGHT_YET] = command.isNotBoughtYet
        session[IConstant.STEP1_IS_BRAND_NEW] = command.isBrandNew
        session[IConstant.STEP1_IS_FIRST_CAR] = command.isFirstCar
        session[IConstant.STEP1_YEAR] = command.year
        session[IConstant.STEP1_MAKE] = command.make
        session[IConstant.STEP1_MODEL_MASTER] = command.modelMaster
        session[IConstant.STEP1_MODEL] = command.model
        session[IConstant.STEP1_MAKE_MODEL_TRIM] = command.makeModelTrim
        session[IConstant.STEP1_MILEAGE] = command.mileage
        session[IConstant.STEP1_VEHICLE_CONDITION] = command.vehicleCondition
        session[IConstant.STEP1_CAR_CITY] = command.city
        session[IConstant.STEP1_REGISTRATION_DATE] = command.firstRegistrationDate
        //session[IConstant.STEP1_INSURED_VALUE] = command.insuredValue
        session[IConstant.STEP1_IS_NON_GCC] = command.isNonGcc
        session[IConstant.STEP1_IS_POLICY_EXPIRED] = command.isExpiredPolicy
        session[IConstant.STEP1_IS_THIRD_PARTY] = command.isThirdParty
        session[IConstant.STEP1_IS_OLD_AGENCY] = command.isOldAgency
        session[IConstant.STEP1_OLD_POLICY_EXPIRY_DATE] = command.oldPolicyExpiryDate
        session[IConstant.STEP1_CAMPAIGN_INTEREST] = command.campaignInterest
        session[IConstant.STEP1_CAMPAIGN_ID] = command.campaignId

        //If there's any server side validation error render previous vehicle details page.
        if (!command.validate()) {
            // set default values if null
            log.debug "car.funnel.driver command error ${command}"
            command.year = command.year ?: Year.now().getValue()
            Date defaultDate = new Date()
            defaultDate.set(year: command.year)
            command.purchaseDate = command.purchaseDate ?: defaultDate
            command.firstRegistrationDate = command.firstRegistrationDate ?: defaultDate
            command.oldPolicyExpiryDate = command.oldPolicyExpiryDate ?: defaultDate
            render view: '/car/funnel/vehicleDetails', model: [command: command, valuation: [:], country: params.country]
            return
        }

        DriverCommand driverCommand = new DriverCommand()
        driverCommand.nationality = session[IConstant.STEP2_NATIONALITY]
        driverCommand.firstLicenseCountry = session[IConstant.STEP2_COUNTRY]
        driverCommand.internationalExperience = session[IConstant.STEP2_INTERNATIONAL_EXPERIENCE]
        driverCommand.localExperience = session[IConstant.STEP2_LOCAL_EXPERIENCE]

        DateTime policyStartDateObj = DateTime.now()

        try {
            if (session[IConstant.STEP2_POLICY_START_DATE]) {
                DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd")
                policyStartDateObj = new DateTime(session[IConstant.STEP2_POLICY_START_DATE])
            }
        } catch (Exception e) {
            log.error("Error while parsing policy start date")
        }

        if (policyStartDateObj.isBeforeNow()) {
            policyStartDateObj = DateTime.now()
        }

        DateTime firstDay2018 = new DateTime(2018, 01, 01, 0, 0)
        if (policyStartDateObj < firstDay2018) {
            policyStartDateObj = firstDay2018
        }

        driverCommand.policyStartDate = policyStartDateObj.toDate()
        driverCommand.ncd = session[IConstant.STEP2_NCD]
        driverCommand.name = session[IConstant.STEP2_NAME]
        driverCommand.mobile = session[IConstant.STEP2_MOBILE]
        driverCommand.email = session[IConstant.STEP2_EMAIL]
        driverCommand.dob = session[IConstant.STEP2_DOB]
        driverCommand.hasClaim = session[IConstant.STEP2_HAS_CLAIM] ?: false
        driverCommand.lastClaimPeriod = session[IConstant.STEP2_LAST_CLAIM_PERIOD]
        driverCommand.claimsInTheLastYear = session[IConstant.STEP2_CLAIMS_IN_LAST_YEAR]
        driverCommand.coverPreference = session[IConstant.STEP2_COVER_PREFERENCE]

        render view: '/car/funnel/driverDetails', model: [command: driverCommand, country: params.country]
    }

    /**
     * Save quote
     * @param command
     * @return
     */
    def saveQuote(DriverCommand command) {
        log.info("funnel.saveQuote - entering with params:$params, command:${command.toString()}, " +
            "IP:${request.getRemoteAddr()}, userAgent:${request.getHeader("User-Agent")}, session:${session.id}")
        session[IConstant.STEP2_NATIONALITY] = command.nationality
        session[IConstant.STEP2_COUNTRY] = command.firstLicenseCountry
        session[IConstant.STEP2_INTERNATIONAL_EXPERIENCE] = command.internationalExperience
        session[IConstant.STEP2_LOCAL_EXPERIENCE] = command.localExperience
        session[IConstant.STEP2_POLICY_START_DATE] = command.policyStartDate
        session[IConstant.STEP2_NCD] = command.ncd
        session[IConstant.STEP2_NAME] = command.name
        session[IConstant.STEP2_MOBILE] = command.mobile
        session[IConstant.STEP2_EMAIL] = command.email
        session[IConstant.STEP2_DOB] = command.dob
        if (command.hasClaim == null) {
            if (command.lastClaimPeriod && ((ClaimPeriodEnum) command.lastClaimPeriod) == ClaimPeriodEnum.TWELVE_MONTHS) {
                command.hasClaim = true
            } else if (command.lastClaimPeriod && ((ClaimPeriodEnum) command.lastClaimPeriod) != ClaimPeriodEnum.TWELVE_MONTHS) {
                command.hasClaim = false
            }
        }
        session[IConstant.STEP2_HAS_CLAIM] = command.hasClaim

        session[IConstant.STEP2_LAST_CLAIM_PERIOD] = command.lastClaimPeriod
        session[IConstant.STEP2_CLAIMS_IN_LAST_YEAR] = command.claimsInTheLastYear
        session[IConstant.STEP2_COVER_PREFERENCE] = command.coverPreference

        if (request.method == "POST" && command.validate()) {

            //Direct to driver page if model or year not available
            if (!session[IConstant.STEP1_MODEL] || !session[IConstant.STEP1_YEAR]) {
                log.info("funnel.saveQuote - Model or year not found, redirecting to driver page> params:$params")
                redirect mapping: "cardriverDetails", params: [lang: utilService.getLanguage(), country: session[IConstant.SITE_COUNTRY]], base: sessionService.getBaseUrl()
                return
            }

            def valuation = [minimum: 0, maximum: 99999999] /*valuationService.getValuation((Integer) session[IConstant.STEP1_MODEL],
                (Integer) session[IConstant.STEP1_YEAR], (boolean) session[IConstant.STEP1_IS_BRAND_NEW], country)*/

            String dealerId = null
            if (params.dealerUuid) {
                Dealer dealer = Dealer.findByUuid((String) params.dealerUuid)
                if (!dealer) {
                    log.warn("Couldn't find dealer by dealer UUID = '$params.dealerUuid'")
                } else if (dealer.countryId != ((CountryEnum) session[IConstant.SITE_COUNTRY]).id) {
                    log.warn("countryId(value = '$dealer.countryId') of dealer(id = '$dealer.id') doesn't match with " +
                        "site country id(value = '${((CountryEnum) session[IConstant.SITE_COUNTRY]).id}') in session")
                } else {
                    dealerId = dealer.id
                }
            }

            MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
            marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
            marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
            marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
            marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
            marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
            marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
            command.marketingTracking = marketingTracking

            def quote
            if (CountryEnum.KSA == session[IConstant.SITE_COUNTRY]) {
                quote = quoteService.createKsaCarQuote(command, session[IConstant.STEP1_YEAR],
                    session[IConstant.STEP1_MODEL], session[IConstant.STEP1_CAR_CITY],
                    session[IConstant.STEP1_REGISTRATION_DATE], 0, //session[IConstant.STEP1_INSURED_VALUE],
                    session[IConstant.STEP1_IS_NON_GCC], session[IConstant.STEP1_IS_POLICY_EXPIRED],
                    session[IConstant.STEP1_IS_THIRD_PARTY], session[IConstant.STEP1_IS_OLD_AGENCY],
                    session[IConstant.STEP1_PURCHASE_DATE], session[IConstant.STEP1_IS_NOT_BOUGHT_YET],
                    session[IConstant.STEP1_IS_BRAND_NEW], session[IConstant.INSURANCE_QUERY_STRING],
                    valuation.minimum, valuation.maximum, dealerId)

                flash.ksaQuoteId = quote.id
                redirect mapping: 'carCheckoutThankyou', params: [lang: params.lang, country: params.country], base: sessionService.getBaseUrl()
                return
            } else {
                Boolean newUser = false
                (quote, newUser) = quoteService.createCarQuote(command, session[IConstant.STEP1_YEAR],
                    session[IConstant.STEP1_MODEL], session[IConstant.STEP1_MAKE_MODEL_TRIM], session[IConstant.STEP1_CAR_CITY],
                    session[IConstant.STEP1_REGISTRATION_DATE], 0, //session[IConstant.STEP1_INSURED_VALUE],
                    session[IConstant.STEP1_IS_NON_GCC], session[IConstant.STEP1_IS_POLICY_EXPIRED],
                    session[IConstant.STEP1_IS_THIRD_PARTY], session[IConstant.STEP1_IS_OLD_AGENCY],
                    session[IConstant.STEP1_PURCHASE_DATE], session[IConstant.STEP1_IS_NOT_BOUGHT_YET],
                    session[IConstant.STEP1_IS_BRAND_NEW], session[IConstant.STEP1_IS_FIRST_CAR],
                    null, session[IConstant.INSURANCE_QUERY_STRING],
                    valuation.minimum, valuation.maximum, session[IConstant.STEP1_OLD_POLICY_EXPIRY_DATE],
                    session[IConstant.SITE_COUNTRY], params.lang, null, null, null, null, ValuationSourceEnum.YC,
                    null, null, sessionService.getRequestSource().name(), session[IConstant.STEP1_CAMPAIGN_INTEREST],
                    session[IConstant.STEP1_CAMPAIGN_ID], dealerId, session[IConstant.STEP1_MILEAGE],
                    session[IConstant.STEP1_VEHICLE_CONDITION], command.communicationOptIn)

                session[IConstant.STEP2_CARID] = quote.id

                session[IConstant.NEW_USER] = newUser

                /*WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]

                notify AsyncEventConstants.CAR_QUOTE_CREATED, [quoteId: quote.id, lang: params.lang,
                                                               baseUrl: whiteLabelDomain.baseUrl,
                                                               creator: QuoteCreatorEnum.WEBSITE]*/

                if (session['etisalatloyaltyId']) {
                    EtisalatUser etisalatUser = new EtisalatUser()
                    etisalatUser.loyalityId = session['etisalatloyaltyId']
                    etisalatUser.userId = quote.userId

                    EtisalatSmilesPayment payment = new EtisalatSmilesPayment()
                    payment.etisalatUser = etisalatUser
                    payment.quote = quote;
                    payment.transactionId = session['etisalatTransactionId']
                    payment.quote = quote
                    payment.save(flush: true)
                }

                /*String base = "${request.serverName}${request.serverPort == 8080 ? ':8080' : ''}${request.contextPath?  request.contextPath : ''}"
                log.info("base:${base}")*/
                /*WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]
                String baseUrl = whiteLabelDomain.baseUrl
                log.info("funnel.saveQuote - baseUrl:${baseUrl}")*/

                redirect mapping: 'preCarQuotes', id: AESCryption.encrypt(quote.id.toString()), base: sessionService.getBaseUrl(),
                    params: [lang: params.lang, country: params.country]

                return
            }
        } else if (request.method == "POST" && command.hasErrors()) {
            log.info("funnel.saveQuote - command has errors:${command.errors.toString()}")
        }

        render view: '/car/funnel/driverDetails', model: [command: command, country: params.country]
    }

    def preCarQuotes(String country, String lang, String id) {
        log.info("funnel.preCarQuotes - entering with [country:$country, lang:$lang, id:$id, params:$params]")

        Integer quoteId
        try {
            if (id) {
                quoteId = Integer.parseInt(AESCryption.decrypt(id))
            } else {
                quoteId = session[IConstant.STEP2_CARID]
            }
        } catch (e) {
            log.error "Unable to decrypt quote id ${id}"
            quoteId = session[IConstant.STEP2_CARID]
        }

        def model = [:]

        if (quoteId) {
            CarQuote carQuote = CarQuote.read(quoteId)

            if (!carQuote) {
                log.info "funnel.preCarQuotes - Quote not found, quoteId:${quoteId}"
                render status: 404
                return
            }

            if (carQuote.isAncient()) {
                log.info "funnel.preCarQuotes - Ancient quote [quoteId:${quoteId}] created on ${carQuote.dateCreated}, redirecting to 404"
                render view: '/car/expiredQuote', model: [country: params.country, lang: params.lang]
                return
            } else if (!carQuote.isNotProcessed()) {
                log.warn "funnel.preCarQuotes - Quote is already processed for quote id: ${carQuote.id}"
                render view: '/car/funnel/quoteProcessed', model: [country: params.country]
                return
            }

            if (request.method == "POST") {
                session[IConstant.AUTODATA_TRIM] = params.autodataVehicleTrim
                if (params.autodataVehicleTrim) {
                    String[] autodataIdDesc = [null, null]
                    if (params.autodataVehicleTrim != "0" && params.autodataVehicleTrim != "-1") {
                        autodataIdDesc = params.autodataVehicleTrim.split(":")
                        session[IConstant.AUTODATA_TRIM] = autodataIdDesc[0]
                    } else if (params.autodataVehicleTrim == "0") {
                        autodataIdDesc[0] = "0"
                    }
                    quoteSgService.handleChangeInAutoDataSpecForQuote(carQuote.model, autodataIdDesc[0], autodataIdDesc[1], carQuote)

                } else {
                    log.info("funnel.updateMissingDataCarQuote - No AutoData trim selected, quoteId:${quoteId}")
                }

                if (params.insuredValue) {
                    session[IConstant.STEP1_INSURED_VALUE] = params.insuredValue
                    carQuote.insuredValue = new BigDecimal(params.insuredValue?.replaceAll(",", ""))
                    // valuation null safety check
                    carQuote.valuationSource = params.valuationSource == "null" ? null : (ValuationSourceEnum) params.valuationSource
                    carQuote.insuredValueMax = new BigDecimal(params.insuredValueMax ?: 0)
                    carQuote.insuredValueMin = new BigDecimal(params.insuredValueMin ?: 0)
                    carQuote.save(flush:true, failOnError: true)

                    WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]

                    notify AsyncEventConstants.CAR_QUOTE_CREATED, [quoteId: carQuote.id, lang: params.lang,
                                                                   baseUrl: whiteLabelDomain.baseUrl,
                                                                   creator: QuoteCreatorEnum.WEBSITE]
                }

                redirect mapping: 'carQuotes', params: [lang   : utilService.getLanguage(),
                                                        country: params.country, id: id], base: sessionService.getBaseUrl()

                return
            }

            String carQuoteIdFromCookie = cookieService.getCookie(IConstant.COOKIE_CAR_QUOTE_ID)
            if (carQuoteIdFromCookie == carQuote.id.toString()) {
                model.selectedAutodataTrim = carQuote.autoDataSpecId ?: session[IConstant.AUTODATA_TRIM]
                model.selectedInsuredValue = carQuote.insuredValue ?: session[IConstant.STEP1_INSURED_VALUE]
            }
            model.quote = carQuote
            model.user = carQuote.user

            List autoDataTrims = []
            if (carQuote.model.id != QuoteCommand.UNKNOWN_MODEL_ID) {
                log.info("ycModel.modelMaster:${carQuote.model.modelMaster.id}, ycModel:$carQuote.model.id")
                autoDataTrims = quoteService.getAutoDataTrims(carQuote.year, carQuote.model, id)
                //if (autoDataTrims.size() == 1) {
                //    quoteService.handleChangeInAutoDataSpecForQuote(carQuote.model, autoDataTrims[0].admeId + "", autoDataTrims[0].description, carQuote)
                //} else
                if (!autoDataTrims ) {
                    quoteSgService.handleChangeInAutoDataSpecForQuote(carQuote.model, "0", null, carQuote)
                }
            }
            model.autoDataTrims = autoDataTrims

            model.estimatedValuation = valuationService.getEdataValuation(carQuote.id, carQuote.modelId,
                carQuote.year, carQuote.isBrandNew, !carQuote.isNonGcc)

            log.info("model.estimatedValuation:${model.estimatedValuation}")
            if (model.estimatedValuation == null) {
                model.estimatedValuation = valuationService.getValuation(carQuote.modelId.toLong(), carQuote.year,
                    carQuote.isBrandNew, utilService.getCountry(), "", !carQuote.isNonGcc, true)
                log.info("model.estimatedValuation:${model.estimatedValuation}")

            } else if (model.estimatedValuation.valuationSource == ValuationSourceEnum.EDATA) {

                carQuote.insuredValueMin = model.estimatedValuation.minimum
                carQuote.insuredValueMax = model.estimatedValuation.maximum
                carQuote.valuationSource = model.estimatedValuation.valuationSource
                carQuote.save(flush: true)
            }
        }

        String quotesLink = g.createLink(mapping: 'carQuotes', base: sessionService.getBaseUrl(), params:
            [country: country, id: id, lang: lang])

        model.quotesLink = quotesLink

        render view: '/car/funnel/preQuotes', model: model
    }

    /*def updateMissingDataCarQuote(String country, String lang, String id) {
        log.info("funnel.updateMissingDataCarQuote - entering with params:${params}")
        Integer quoteId
        CarQuote carQuote

        try {
            if (id) {
                quoteId = Integer.parseInt(AESCryption.decrypt(id))
            } else {
                quoteId = session[IConstant.STEP2_CARID]
            }
        } catch (e) {
            log.error "Unable to decrypt quote id ${id}"
            quoteId = session[IConstant.STEP2_CARID]
        }

        def model = [:]

        if (quoteId) {
            carQuote = CarQuote.read(quoteId)

            if (!carQuote) {
                log.info "funnel.updateMissingDataCarQuote - Quote not found, quoteId:${quoteId}"
                render status: 404
                return
            }
        }

        if (request.method == "POST" && params.autodataVehicleTrim) {
            String[] autodataIdDesc = [null, null]
            if (params.autodataVehicleTrim != "0" && params.autodataVehicleTrim != "-1") {
                autodataIdDesc = params.autodataVehicleTrim.split(":")
            }
            quoteService.handleChangeInAutoDataSpecForQuote(carQuote.model, autodataIdDesc[0], autodataIdDesc[1], carQuote)
        } else {
            log.info("funnel.updateMissingDataCarQuote - No AutoData trim selected, quoteId:${quoteId}")
        }

        redirect mapping: 'carQuotes', params: [lang: utilService.getLanguage(),
                                                country: params.country, id:id], base: sessionService.getBaseUrl()

    }*/

    def thankyou() {
        if (flash.ksaQuoteId) {
            CarQuoteKsa quote = CarQuoteKsa.read(flash.ksaQuoteId)
            flash.ksaQuoteId = null
            notify AsyncEventConstants.KSA_CAR_QUOTE_CREATED, [quoteId: quote.id, lang: params.lang]

            String whiteLabel = session[IConstant.WHITE_LABEL_DOMAIN]
            session.invalidate()
            session[IConstant.WHITE_LABEL_DOMAIN] = whiteLabel
            render view: '/car/funnel/thankyou', model: [policyReference: quote.policyReference, country: params.country]
            return
        }

        redirect(mapping: 'carIndex', params: [lang: params.lang, country: params.country], base: sessionService.getBaseUrl())
    }

    /**
     * srp/step3 actions
     */
    def quotes(OutOfRangeCommand command) {
        Integer quoteId
        session[IConstant.CONVERSION_RATE] = configurationService.getValue('etisalatSmiles.api.conversionRate')
        session[IConstant.PREVIOUS_QUOTES_URL] = request.requestURL
        try {
            if (command.id) {
                quoteId = Integer.parseInt(AESCryption.decrypt(command.id))
            } else {
                quoteId = session[IConstant.STEP2_CARID]
            }
        } catch (e) {
            log.error "Unable to decrypt quote id ${command.id}"
            quoteId = session[IConstant.STEP2_CARID]
        }

        if (Environment.current == Environment.PRODUCTION) {
            if (params.country == CountryEnum.EGYPT.code) {
                render view: "/car/contact"
                return
            }
        }

        if (quoteId) {
            cookieService.setCookie(IConstant.COOKIE_CAR_QUOTE_ID, quoteId.toString())
            CarQuote carQuote = CarQuote.findById(quoteId)

            if (!carQuote) {
                log.info "funnel.quotes - Quote not found, quoteId:${quoteId}"
                render status: 404
                return
            }

            // set session only if user coming from quote url
            if (command.id) {
                sessionService.updateSession(carQuote)
            }
            if (carQuote.isAncient()) {
                log.info ".quotes Policy [quoteId:${quoteId}] created on ${carQuote.dateCreated} so redirecting to 404"
                render view: '/car/expiredQuote', model: [country: params.country, lang: params.lang]
                return
            } else if (carQuote.isNotProcessed()) {

                if (carQuote.modelId == QuoteCommand.UNKNOWN_MODEL_ID) {
                    quoteService.setNoQuote(carQuote)
                    notify AsyncEventConstants.SALESFORCE_CAR_QUOTE_SYNC, carQuote.id
                    redirect mapping: 'carNoQuotes', params: [lang: LocaleContextHolder.locale.language, country: params.country], base: sessionService.getBaseUrl()
                    return
                }

                if (!carQuote.autoDataSpecId || ((carQuote.insuredValue == null || carQuote.insuredValue == 0) && !carQuote.isThirdParty)) {
                    log.info("funnel.quotes - Autodata admeid or insured value not found for car quote ${carQuote.id}, " +
                        "redirecting to prequotes page")

                    redirect mapping: 'preCarQuotes', id: command.id, base: sessionService.getBaseUrl(),
                        params: [lang: params.lang, country: params.country]
                    return
                }

                if (carQuote.valuationSource != ValuationSourceEnum.EDATA && (!carQuote.insuredValueMin || carQuote.insuredValueMin == 0) ) {
                    //Existing valuation is not EDATA, check if valuation is available,
                    // if yes, then redirect to prequotes page for insured value selection
                    // if no, then continue to quotes page
                    def edataValuation = valuationService.getEdataValuation(carQuote.id, carQuote.modelId.toInteger(),
                        carQuote.year, carQuote.isBrandNew, !carQuote.isNonGcc)
                    if (edataValuation) {
                        redirect mapping: 'preCarQuotes', id: command.id, base: sessionService.getBaseUrl(),
                            params: [lang: params.lang, country: params.country]
                        return
                    }
                }

                CountryEnum country = CountryEnum.findCountryByDfp(carQuote.quoteCountry.code)

                def ratingQuotes
                if (country == CountryEnum.UAE) {
                    if (session[IConstant.STAND_ALONE_DISCOUNT_CODE] && !command.dc && !command.dcr) {
                        if (carQuote.email == (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[0]) {
                            command.dc = (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[1]
                        }
                        session[IConstant.STAND_ALONE_DISCOUNT_GROUP] = null
                    } else if (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] && !command.dc && !command.dcr) {
                        if (carQuote.email == (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[0]) {
                            command.dcr = AESCryption.encrypt((session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[1])
                        }
                        session[IConstant.STAND_ALONE_DISCOUNT_CODE] = null
                    }

                    if (command.dc) {
                        session[IConstant.STAND_ALONE_DISCOUNT_CODE] = "$carQuote.email-$command.dc"
                        session[IConstant.STAND_ALONE_DISCOUNT_GROUP] = null
                        ratingQuotes = quoteService.getRatings(quoteId, false, command.dc)
                    } else if (command.dcr) {
                        session[IConstant.STAND_ALONE_DISCOUNT_GROUP] = "$carQuote.email-${AESCryption.decrypt(command.dcr).toString()}"
                        session[IConstant.STAND_ALONE_DISCOUNT_CODE] = null
                        ratingQuotes = quoteService.getRatings(quoteId, false, null, session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String)
                    } else {
                        ratingQuotes = quoteService.getRatings(quoteId)
                    }

                } else if (country == CountryEnum.LBN) {
                    ratingQuotes = lbnQuoteService.getRatings(quoteId)
                } else if (country == CountryEnum.KWT) {
                    ratingQuotes = kwtQuoteService.getRatings(quoteId)
                } else if (country == CountryEnum.EGYPT) {
                    ratingQuotes = egyQuoteService.getRatings(quoteId)
                }

                def (List<RateCommand> ratings, quote) = ratingQuotes

                if (session[IConstant.WHITE_LABEL_DOMAIN] && session[IConstant.WHITE_LABEL_DOMAIN].brand.code in ['NB', 'ADIB']) {
                    ratings = ratings.findAll {
                        it.isTakaful
                    }
                }

                if (!ratings) {
                    quoteService.setNoQuote(carQuote)
                    notify AsyncEventConstants.SALESFORCE_CAR_QUOTE_SYNC, carQuote.id
                    redirect mapping: 'carNoQuotes', params: [lang: LocaleContextHolder.locale.language, country: params.country], base: sessionService.getBaseUrl()
                    return
                }

                // Remove offline quotes for ETISALAT
                log.info("quotes - ratingsBeforeOfflineQuotesRemoval: ${ratings.size()}")
                if (carQuote.requestSource == RequestSourceEnum.ETISALAT_SMILES) {
                    ratings = ratings.findAll { !it.isOfflineQuotes }

                    if (ratings.size() == 0) {
                        //No online ratings, set as NO Quotes
                        redirect mapping: 'carNoQuotes', params: [lang: LocaleContextHolder.locale.language, country: params.country], base: sessionService.getBaseUrl()
                        return
                    }
                }
                log.info("quotes - ratingsAfterOfflineQuotesRemoval: ${ratings.size()}")

                // If email_campaign=lost_lead and no action date or action date 2 days expired.
                // and Change lead type to ACTIVE_LOST and agent to null
                if (carQuote.user.leadType == LeadType.LOST && params.email_campaign == "lost_lead" &&
                    (!carQuote.user.nextActionDate ||
                        carQuote.user.nextActionDate < LocalDateTime.now().minusDays(2))) {

                    //Change status to lost active
                    User user = carQuote.user
                    user.changeLeadType(LeadType.ACTIVE_LOST, 'car')
                    carQuote.type = user.leadType
                    user.save()

                    //handle lead status and agent after status change
                    crmService.handleCrmEvents(user.id, 'car', carQuote)


                }

                if (carQuote.coverPreference == CoverPreferenceEnum.THIRD_PARTY) {
                    ratings.sort { a, b ->
                        if (a.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return -1
                        } else if (b.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return 1
                        } else {
                            // Compare by type and then by premium
                            b.coverageTypeId <=> a.coverageTypeId ?: a.premium <=> b.premium
                        }
                    }
                } else {
                    // here you can add other sorting on comprehensive policies
                    ratings.sort { a, b ->
                        if (a.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return -1
                        } else if (b.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return 1
                        } else {
                            // Compare by type and then by premium
                            // a.isAgencyRepair() <=> b.isAgencyRepair() ?: a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
                            a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
                        }
                    }
                }

                def lowestComprehensiveQuote = ratings?.findAll() { it.coverageTypeId == 1 && it.premium != null }?.min() { it.premium }
                def lowestThirdPartyQuote = ratings?.findAll() { it.coverageTypeId == 2 && it.premium != null }?.min() { it.premium }
                def lowestAgencyRepair = ratings?.findAll() { it.coverageTypeId == 1 && it.agencyRepair && it.premium != null }?.min() { it.premium }

                def comprehensiveQuotesLength = ratings?.findAll() { it.coverageTypeId == 1 }?.size()
                def thirdPartyQuotesLength = ratings?.findAll() { it.coverageTypeId == 2 }?.size()
                def agencyRepairLength = ratings?.findAll() { it.coverageTypeId == 1 && it.agencyRepair }?.size()

                def renderNewSRP = false
                if (params.country == 'uae') {
                    renderNewSRP = true
                }

                def template
                // for arabic layout need to comment this check
                if (renderNewSRP) {
                    template = '/car/funnel/quotesV2'
                } else {
                    template = '/car/funnel/quotes'
                }

                // sort list based on isOfflineQuotes
                ratings.sort { rate1, rate2 ->
                    (rate1.isOfflineQuotes ? 1 : 0) <=> (rate2.isOfflineQuotes ? 1 : 0)
                }

                carQuote = CarQuote.load(quoteId)
                render view: template, model: [
                    quotes                   : ratings,
                    quote                    : carQuote,
                    country                  : params.country,
                    lowestComprehensiveQuote : lowestComprehensiveQuote,
                    lowestThirdPartyQuote    : lowestThirdPartyQuote,
                    lowestAgencyRepair       : lowestAgencyRepair,
                    thirdPartyQuotesLength   : thirdPartyQuotesLength,
                    agencyRepairLength       : agencyRepairLength,
                    comprehensiveQuotesLength: comprehensiveQuotesLength
                ]

                return
            } else {
                log.warn "quotes - Quote is already processed for quote id: ${carQuote.id}"
                render view: '/car/funnel/quoteProcessed', model: [country: params.country]
                return
            }
        }

        redirect mapping: 'insuranceIndex', params: [lang: params.lang, country: params.country], base: sessionService.getBaseUrl()
    }

    /**
     * quotedetails/step4 action
     */
    def quoteDetails(QuoteDetailsCommand command) {
        log.info("quoteDetails - Entering with params [${command}]")

        Integer quoteId
        try {
            if (command.id) {
                quoteId = Integer.parseInt(AESCryption.decrypt(command.id))
            } else {
                quoteId = command.quoteId
            }
        } catch (e) {
            log.error "Unable to decrypt quote id ${command.id}"
            quoteId = command.quoteId
        }

        command.quoteId = quoteId

        if (!command.validate()) {
            redirect mapping: 'carVehicleDetails', params: [lang: LocaleContextHolder.locale.language, country: params.country], base: sessionService.getBaseUrl()
            return
        }

        def carQuote = CarQuote.get(quoteId)

        if (command.id) {
            // update session only when accessed directly.
            sessionService.updateSession(carQuote)
        }

        if (!carQuote.isNotProcessed()) {
            log.warn "quoteDetails - Quote is already processed for quote id: ${carQuote.id}"
            render view: '/car/funnel/quoteProcessed', model: [country: params.country]
            return
        }

        //TODO: Pass langage in quote command when covers are updated in db.
        //quoteCommand.locale = params.lang?: 'en'

        def coversMap = [:]

        if (command.breakdownCover) {
            session[IConstant.SRP_BREAKDOWN_COVER] = true
            coversMap."${IConstant.SRP_BREAKDOWN_COVER}" = Integer.parseInt(params.breakdownCoverValue)
        } else {
            coversMap."${IConstant.SRP_BREAKDOWN_COVER}" = null
            session[IConstant.SRP_BREAKDOWN_COVER] = false
        }

        if (command.paCover) {
            session[IConstant.SRP_PA_COVER] = true
            coversMap."${IConstant.SRP_PA_COVER}" = Integer.parseInt(params.paCoverValue)
        } else {
            session[IConstant.SRP_PA_COVER] = false
            coversMap."${IConstant.SRP_PA_COVER}" = null
        }

        if (command.personalAccidentPax) {
            session[IConstant.SRP_PA_COVER_PASSENGER] = true
            int numberOfSeats = carQuote.model.numberOfSeats - 1
            coversMap."${IConstant.SRP_PA_COVER_PASSENGER}" =
                Integer.parseInt(params.personalAccidentPaxValue).multiply(numberOfSeats)
        } else {
            session[IConstant.SRP_PA_COVER_PASSENGER] = false
            coversMap."${IConstant.SRP_PA_COVER_PASSENGER}" = null
        }

        if (command.replacementCar) {
            session[IConstant.SRP_REPLACEMENT_CAR_COVER] = true
            coversMap."${IConstant.SRP_REPLACEMENT_CAR_COVER}" = Integer.parseInt(params.replacementCarValue)
        } else {
            session[IConstant.SRP_REPLACEMENT_CAR_COVER] = false
            coversMap."${IConstant.SRP_REPLACEMENT_CAR_COVER}" = null
        }

        if (command.carHireCashBenefit) {
            session[IConstant.SRP_CAR_HIRE_CASH_BENEFIT] = true
            coversMap."${IConstant.SRP_CAR_HIRE_CASH_BENEFIT}" = Integer.parseInt(params.carHireCashBenefitValue)
        } else {
            session[IConstant.SRP_CAR_HIRE_CASH_BENEFIT] = false
            coversMap."${IConstant.SRP_CAR_HIRE_CASH_BENEFIT}" = null
        }

        if (command.bulletService) {
            session[IConstant.SRP_BULLET_SERVICE] = true
            coversMap."${IConstant.SRP_BULLET_SERVICE}" = Integer.parseInt(params.bulletServiceValue)
        } else {
            session[IConstant.SRP_BULLET_SERVICE] = false
            coversMap."${IConstant.SRP_BULLET_SERVICE}" = null
        }

        RepairTypeEnum selectedRepairType = RepairTypeEnum.findByName(params.selectedRepairType)

        // wasnt working anyway
//        session[IConstant.SRP_HOME_INSURANCE_ID] = true
//        coversMap."${IConstant.SRP_HOME_INSURANCE_ID}" = Integer.parseInt(params.homeInsuranceId)

        session[IConstant.SRP_COVERS_MAP] = coversMap

        def quoteCommand = quoteService.toQuoteCommand(carQuote)
        quoteCommand.productId = command.productId

        def rateCommand
        if (session[IConstant.STAND_ALONE_DISCOUNT_CODE] && carQuote.email == (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[0]) {
            String standAloneDiscountCode = (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[1]
            rateCommand = quoteService.getRating(carQuote, command.productId, selectedRepairType, standAloneDiscountCode)
        } else if (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] && carQuote.email == (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[0]) {
            String standAloneDiscountGroup = AESCryption.encrypt((session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[1])
            rateCommand = quoteService.getRating(carQuote, command.productId, selectedRepairType, null, standAloneDiscountGroup)
        } else {
            rateCommand = quoteService.getRating(carQuote, command.productId, selectedRepairType)
        }


        if (!rateCommand) {
            log.warn "funnel.quoteDetails quoteId ${carQuote.id} rateCommand is null redirecting to quotes page"
            redirect mapping: 'carQuotes', params: [lang: utilService.getLanguage(), country: params.country], base: sessionService.getBaseUrl()
            return
        }

        BigDecimal premuim = rateCommand.premium

        //Add the selected covers value into total premuim
        if (!rateCommand.isOfflineQuotes){
            coversMap.each { key, val ->
                if (val) {
                    premuim = premuim + val
                }
            }
        }


        if (params.more != null) {
            render view: '/car/funnel/quoteDetails', model: [quote: rateCommand, totalPremuim: premuim, command: command, country: params.country]
        } else {
            quoteService.updateCarQuote(carQuote, rateCommand, premuim)

            // render offline quotes page
            if (rateCommand.isOfflineQuotes) {
                return offlineQuotes()
            }

            redirect mapping: 'carCheckoutIndex', params: [country: params.country, lang: params.lang], base: sessionService.getBaseUrl()
        }
    }

    def noQuotes() {
        render view: '/car/funnel/no-quotes', model: [country: params.country]
    }

    def offlineQuotes() {
        render view: '/car/funnel/offline-quotes', model: [country: params.country]
    }

    def tabby() {
        render view: '/car/checkout/tabby', model: [country: params.country]
    }

    def outOfRange(String id) {
        render view: '/car/funnel/outOfRange', model: [id: id, country: params.country]
    }

    private removeEtsalatSession() {
        session.removeAttribute('MixMoneyAmount')
        session.removeAttribute('MixPointsAmount')
        session.removeAttribute('EtisalatSmilesPaymentId')
        session.removeAttribute('etisalatTotalPoints')
        session.removeAttribute('etisalatTokenType')
        session.removeAttribute('etisalatToken')
        session.removeAttribute('etisalatAuthToken')
        session.removeAttribute('etisalatTransactionId')
        session.removeAttribute('etisalatUserId')
        session.removeAttribute('etisalatMixMoneyAmount')
        session.removeAttribute('etisalatMixPointsAmount')
    }
}
