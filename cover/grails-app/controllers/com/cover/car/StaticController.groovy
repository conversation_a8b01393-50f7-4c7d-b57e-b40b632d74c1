package com.cover.car

import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.car.StaticContent
import grails.transaction.Transactional
import org.springframework.http.HttpStatus

class StaticController {

    static namespace = "car"

    def utilService

    def faq() {
        Country country = Country.read(CountryEnum.findCountry(params.country).id)
        Map staticContent = utilService.getStaticContentByCountryAndSlug(country, 'faq')

        if (staticContent) {
            render view: '/car/faq', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def carRepairGuide() {
        render view: '/car/guides/carRepairGuide', model: [country: params.country]
    }

    def tabby() {
        render view: '/car/checkout/tabby', model: [country: params.country]
    }

    def comprehensiveCarInsurance() {
        Country country = Country.read(CountryEnum.findCountry(params.country).id)
        Map staticContent = utilService.getStaticContentByCountryAndSlug(country, 'comprehensive-cover')

        if (staticContent) {
            render view: '/car/guides/comprehensiveCarInsurance', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def dosAndDonts() {
        Country country = Country.read(CountryEnum.findCountry(params.country).id)
        Map staticContent = utilService.getStaticContentByCountryAndSlug(country, 'dos-and-donts')

        if (staticContent) {
            render view: '/car/guides/dosAndDonts', model: [country: params.country, staticContent :  staticContent]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def makeAClaim() {
        Country country = Country.read(CountryEnum.findCountry(params.country).id)
        Map staticContent = utilService.getStaticContentByCountryAndSlug(country, 'make-a-claim')

        if (staticContent) {
            render view: '/car/guides/makeAClaim', model: [country: params.country, staticContent : staticContent ]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def noClaimsCertificate() {
        render view: '/car/guides/noClaimsCertificate', model: [country: params.country]
    }

    def topTips() {
        Country country = Country.read(CountryEnum.findCountry(params.country).id)
        Map staticContent = utilService.getStaticContentByCountryAndSlug(country, 'top-tips')

        if (staticContent) {
            render view: '/car/guides/topTips', model: [country: params.country, staticContent : staticContent ]
        } else {
            render status: HttpStatus.NOT_FOUND
        }
    }

    def customerFeedback() {
        render view: '/car/customerFeedback', model: [country: params.country]
    }

    def renewal() {
        render view: '/car/renewal', model: [country: params.country]
    }

}
