package com.cover

import com.cover.util.IConstant
import com.safeguard.whitelabel.WhiteLabelDomain
import org.apache.http.HttpStatus


class WhiteLabelInterceptor {

    int order = HIGHEST_PRECEDENCE + 2

    WhiteLabelInterceptor() {
        matchAll()
    }

    boolean before() {

        log.info "Executing within controller $controllerName , $actionName, server: ${request.serverName}"

        WhiteLabelDomain whiteLabelDomain = WhiteLabelDomain.findByDomainAndActive(request.serverName, true)

        if (whiteLabelDomain && whiteLabelDomain.brand.active) {

            //Allow access to controllers only if they are allowed
            if (whiteLabelDomain.allowEverything || controllerName == 'ajax' || controllerName == 'static' ||
                controllerName == 'policy' || controllerName == 'competition' ||
                (controllerName in ['funnel', 'quote', 'etisalat'] && whiteLabelDomain.allowCarInsurance) ||
                (controllerName in ['checkout', 'icheckout', 'paymentNotification', 'tapPaymentNotification'] && whiteLabelDomain.allowPayment)) {
                log.info("${request.serverName} is allowed to ${controllerName}")

                Map whiteLabelDomainMap = new HashMap()
                whiteLabelDomainMap.brand = [id: whiteLabelDomain.brand.id, nameEn: whiteLabelDomain.brand.nameEn,
                                             nameAr: whiteLabelDomain.brand.nameAr, code: whiteLabelDomain.brand.code]
                whiteLabelDomainMap.baseUrl = whiteLabelDomain.baseUrl
                whiteLabelDomainMap.domain = whiteLabelDomain.domain

                session[IConstant.WHITE_LABEL_DOMAIN] = whiteLabelDomainMap
                return true
            }
            log.info("${request.serverName} is not allowed to ${controllerName}")

        }
        log.info("${request.serverName} is not found or inactive")

        //Return 404 http status if none of the actions are allowed
        response.status = HttpStatus.SC_NOT_FOUND.intValue()
        false
    }

    boolean after() { true }

    void afterView() {
        // no-op
    }
}
