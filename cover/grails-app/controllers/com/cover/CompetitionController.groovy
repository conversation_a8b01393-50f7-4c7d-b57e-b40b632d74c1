package com.cover

import com.safeguard.CountryEnum
import org.springframework.http.HttpStatus

class CompetitionController {

    def index() {
        log.info("Inside Competition Controller ... ")

        CountryEnum countryEnum = CountryEnum.findCountry(params.country)

        if (!countryEnum || countryEnum != CountryEnum.UAE) {
            render status: HttpStatus.NOT_FOUND
            return
        }

        render view: "/competition/index"
    }
}
