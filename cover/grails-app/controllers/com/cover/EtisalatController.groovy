package com.cover

import org.json.JSONObject

class EtisalatController {
    def etisalatApiService

    def index() {
        render view: "/etisalat/index"
        return
    }


    def quotes() {
        def quotes = etisalatApiService.getMyQuotes(session['etisalatTransactionId'], session['etisalatloyaltyId'])
        render view: "/etisalat/quotes", model: [quotes: quotes]
        return
    }
}
