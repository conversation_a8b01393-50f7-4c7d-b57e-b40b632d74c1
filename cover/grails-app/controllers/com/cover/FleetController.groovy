package com.cover
import com.safeguard.*
import com.safeguard.fleet.*
import com.cover.fleet.FleetQuoteService

class FleetController {

    static namespace = 'fleet'

    def fleetQuoteService

    def index() {
        if(params.country == 'uae') {

            if(request.method == 'POST'){

                fleetQuoteService.saveQuote(params)
                redirect mapping: "fleetThankyou", params: [lang: params.lang, country: params.country]
                return
            } else {
                render view: '/fleet/index', model: [lang: params.lang, country: params.country]
                return
            }

        } else {
            render view: '/common/notFound'
            return
        }
    }

    def thankyou() {
        if(params.country == 'uae') {
            render view: '/fleet/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render view: '/common/notFound'
            return
        }
    }
}
