package com.cover

import com.cover.util.IConstant
import com.safeguard.CountryEnum
import com.safeguard.InsuranceTypeEnum
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.transaction.Transactional
import org.springframework.context.i18n.LocaleContextHolder

@Transactional
class InsuranceInterceptor {

    int order = HIGHEST_PRECEDENCE + 1

    Collection<String> validLangs = ['en','ar']

    InsuranceInterceptor() {
        matchAll()
            .excludes(controller: 'quote')
            .excludes(controller: 'emailTest')
            .excludes(controller: 'subscription')
            .excludes(controller: 'policyEmail')
            .excludes(controller: 'checkout')
            .excludes(controller: 'homeInsurance', action: 'checkout')
            .excludes(namespace: 'apiV1')
            .excludes(controller: 'paymentNotification')
            .excludes(controller: 'tapPaymentNotification')
            .excludes(namespace: 'homeInsurance', controller: 'checkout')
            .excludes(controller: 'ajax')
            .excludes(uri: '/manifest.json')
            .excludes(uri: '/error')
            .excludes(uri: '/android-chrome-192x192.png')
            .excludes(uri: '/android-chrome-256x256.png')
            .excludes(uri: '/apple-touch-icon.png')
            .excludes(uri: '/browserconfig.xml')
            .excludes(uri: '/favicon.ico')
            .excludes(uri: '/favicon-16x16.png')
            .excludes(uri: '/favicon-32x32.png')
            .excludes(uri: '/mstile-150x150.png')
            .excludes(uri: '/safari-pinned-tab.svg')
            .excludes(uri: '/ui-icons_444444_256x240.png')
            .excludes(uri: '/ui-icons_555555_256x240.png')
            .excludes(uri: '/ui-icons_777620_256x240.png')
            .excludes(uri: '/ui-icons_777777_256x240.png')
            .excludes(uri: '/ui-icons_cc0000_256x240.png')
            .excludes(uri: '/ui-icons_ffffff_256x240.png')
            .excludes(namespace: 'life-api')
    }

    boolean before() {
        if (request.requestURI == "/insurance/error") {
            return true
        }
        Locale defaultLocale = new Locale('en')

        Locale currentLocale = LocaleContextHolder.locale
        boolean hasRightLocale = currentLocale.language in ['ar', 'en']

        if (!hasRightLocale) {
            LocaleContextHolder.setLocale(defaultLocale)
            currentLocale = defaultLocale
        }

        // Get the domain
        String correctDomain = grailsApplication.config.getProperty("cover.domain")
        WhiteLabelDomain whiteLabelDomain = WhiteLabelDomain.findByDomainAndActive(request.serverName, true)
        //Override the base url
        if (whiteLabelDomain && whiteLabelDomain.brand.active) {
            correctDomain = whiteLabelDomain.baseUrl
        }
        String validCountry
        String validLang
        if(params.country) {
            CountryEnum country = CountryEnum.findCountry(params.country.toString())
            session[IConstant.SITE_COUNTRY] = country ?: CountryEnum.UAE
        } else {
            session[IConstant.SITE_COUNTRY] = CountryEnum.UAE
        }

        Boolean countryIsFine = CountryEnum.findCountry(params.country.toString())
        Boolean langIsFine = validLangs.contains(params.lang)

        if (countryIsFine && langIsFine) {
            if (request.forwardURI.contains('/car')) {
                session[IConstant.INSURANCE_TYPE] = InsuranceTypeEnum.CAR.toString().toLowerCase()
            } else if (request.forwardURI.contains('/life')) {
                session[IConstant.INSURANCE_TYPE] = InsuranceTypeEnum.LIFE.toString().toLowerCase()
            } else if (request.forwardURI.contains('/travel')) {
                session[IConstant.INSURANCE_TYPE] = InsuranceTypeEnum.TRAVEL.toString().toLowerCase()
            } else if (request.forwardURI.contains('/home')) {
                session[IConstant.INSURANCE_TYPE] = InsuranceTypeEnum.HOMEINSURANCE.toString().toLowerCase()
            } else if (request.forwardURI.contains('/health')) {
                session[IConstant.INSURANCE_TYPE] = InsuranceTypeEnum.HEALTH.toString().toLowerCase()
            } else if (!session[IConstant.INSURANCE_TYPE]) {
                session[IConstant.INSURANCE_TYPE] = InsuranceTypeEnum.GENERAL.toString().toLowerCase()
            }

            return true
        }

        def requestParams = request.requestURI.split('/').findAll() { it != null && it != "" }

        if (!requestParams.isEmpty() && requestParams[0] == 'insurance') {
            requestParams.remove(0)
        }

        // check for valid country
        if (requestParams.isEmpty() || !CountryEnum.findCountry(requestParams[0])) {

            validCountry = (session[IConstant.SITE_COUNTRY] as CountryEnum).code
        }
        else {
            validCountry = requestParams.remove(0)
        }

        // check for valid lang
        if (requestParams.isEmpty() || !validLangs.contains(requestParams[0].toString())) {
            validLang = currentLocale.language
        }
        else {
            validLang = requestParams.remove(0)
        }

        String remainingRequestParams = (requestParams.isEmpty() ? "" : "${requestParams.join('/')}/")
        String queryString = request.queryString ? ('?' + request.queryString) : ''
        String newUrl = "${correctDomain}/${validCountry}/${validLang}/${remainingRequestParams}${queryString}"

        redirect(uri: newUrl)
        return false
    }

    boolean after() { true }

    void afterView() {
        // no-op
    }
}
