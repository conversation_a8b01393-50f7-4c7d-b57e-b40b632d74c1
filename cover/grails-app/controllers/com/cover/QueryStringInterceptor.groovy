package com.cover

import com.cover.util.IConstant
import com.safeguard.ProductTypeEnum
import com.safeguard.marketing.RequestTracking
import com.safeguard.util.AESCryption
import grails.util.Environment
import org.grails.web.json.JSONObject

/**
 * Intercept home page request and save query string in session so that we can store them against each created quotes.
 */
class QueryStringInterceptor {

    def marketingService

    QueryStringInterceptor(){
        match(controller: ~/(funnel|homeInsurance|health|travel|travelQuote|groupHealth|LifeLanding)/, action:~/(index|quotes|vehicle|details)/)
        match(controller: 'life', action:'getPreVisitedQuoteInfo')
        match(controller: 'checkout', action: 'index', controllerNamespace: 'car')
        match(controller: 'pet', action:~/(addPet|saveQuote|quotes)/, controllerNamespace: 'apiV1')
        match(controller: 'travel', action:~/(saveQuote|quotes)/, controllerNamespace: 'apiV1')
        match(controller: 'car', action:~/(createQuote|quotes)/, controllerNamespace: 'apiV2')
        match(controller: 'health|travel', action: 'createQuote', controllerNamespace: 'apiV2')
    }

    boolean before() {
        String queryString = request.queryString
        String requestURL = request.requestURL.toString()
        URI requestURI = new URI(requestURL)
        String requestDomainName = requestURI.getHost()
        if (requestDomainName == "yallacompare.com") {
            StringBuilder newURIBuilder =  new StringBuilder(requestURL.replace(requestDomainName, "app.${requestDomainName}"))
            if (queryString) {
                newURIBuilder.append("?${queryString}")
            }
            log.info("QueryStringInterceptor.before - redirecting from ${requestURL} to ${newURIBuilder.toString()}")
            redirect(uri: newURIBuilder.toString())
            return false
        }
        boolean hasMarketTrackingCodes = false
        log.info("inside QueryStringInterceptor - request.queryString:${request.queryString}")
        if (queryString) {
            //Query string exceeding 512 chars are chopped off
            session[IConstant.INSURANCE_QUERY_STRING] = request.queryString.take(512)
        }

        String utmSource = request.getParameter("utm_source") ?: request.JSON.utm_source
        if (utmSource) {
            session[IConstant.INSURANCE_UTM_SOURCE] = utmSource
            ((JSONObject)request.JSON).remove("utm_source")
            hasMarketTrackingCodes = true
        }

        String utmMedium = request.getParameter("utm_medium") ?: request.JSON.utm_medium
        if (utmMedium) {
            session[IConstant.INSURANCE_UTM_MEDIUM] = utmMedium
            ((JSONObject)request.JSON).remove("utm_medium")
            hasMarketTrackingCodes = true
        }

        String utmCampaign = request.getParameter("utm_campaign") ?: request.JSON.utm_campaign
        if (utmCampaign) {
            session[IConstant.INSURANCE_UTM_CAMPAIGN] = utmCampaign
            ((JSONObject)request.JSON).remove("utm_campaign")
            hasMarketTrackingCodes = true
        }
        if (request.getParameter("gclid")) {
            session[IConstant.INSURANCE_GCLID] = request.getParameter("gclid")
            hasMarketTrackingCodes = true
        }
        if (request.getParameter("fbclid")) {
            session[IConstant.INSURANCE_FBCLID] = request.getParameter("fbclid")
            hasMarketTrackingCodes = true
        }
        if (request.getParameter("utm_content") || request.getParameter("utm_term")) {
            hasMarketTrackingCodes = true
        }

        if (hasMarketTrackingCodes && ((params.controller == 'checkout' && params.action == 'index') ||
            params.action == 'quotes' || params.action == 'getPreVisitedQuoteInfo')) {

            String quoteId = params.encQuoteId ?: params.id
            if (params.controller == 'checkout' && params.action == 'index' && params.encryptedQuoteId) {
                quoteId = params.encryptedQuoteId
            }
            marketingService.saveTrackingCodes(quoteId, request.getHeader("Referer"), request.requestURI,
                request.queryString, request.parameterMap)
        }

        true
    }

    boolean after() { true }

    void afterView() {
        // no-op
    }
}
