package com.cover.watch;

import com.safeguard.*
import com.safeguard.watch.*
import com.cover.watch.WatchQuoteService

class WatchController {
    static namespace = 'watch'
    def watchQuoteService

    def index() {
        if(params.country == 'uae') {
            if(request.method == 'POST') {
                watchQuoteService.saveQuote(params)
                redirect mapping: "watchThankyou", params: [lang: params.lang, country: params.country]
                return
            }else {
                render view: '/watch/index', model: [lang: params.lang, country: params.country]
                return
            }
        } else {
            render (status: 404, view: '/common/notFound')
        }
    }

    def thankyou() {
        if(params.country == 'uae') {
            render view: '/watch/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render (status: 404, view: '/common/notFound')
            return
        }
    }

}
