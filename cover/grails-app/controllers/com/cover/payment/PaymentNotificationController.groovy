package com.cover.payment

import com.c4m.payfort.util.PayfortCommandEnum
import com.cover.common.CommonQuoteService
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CodPaymentOptionEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.EmailSgService
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentService
import com.safeguard.PaymentStatusEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.User
import com.safeguard.UserPaymentCardService
import com.safeguard.car.CarQuote
import com.safeguard.car.EtisalatApiService
import com.safeguard.car.EtisalatSmilesPayment
import com.safeguard.checkoutPsp.CheckoutPspResponseCodeEnum
import com.safeguard.checkoutPsp.CheckoutPspService
import com.safeguard.payment.PaymentCardDetails
import com.safeguard.payment.PaymentInvoice
import com.safeguard.payment.PaymentOperationResponse
import com.safeguard.payment.PaymentResponseDto
import com.safeguard.payment.QuoteAdditionalPayment
import com.safeguard.payment.RecurringPayment
import com.safeguard.payment.RecurringPaymentService
import com.safeguard.util.AESCryption
import com.safeguard.util.CommonUtilService
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import grails.util.Environment
import groovy.json.JsonSlurper

/**
 * Checkout Payment Notification handler
 */
class PaymentNotificationController {

    def checkoutService
    CheckoutPspService checkoutPspService
    CommonQuoteService commonQuoteService
    CommonUtilService commonUtilService
    EmailSgService emailSgService
    EtisalatApiService etisalatApiService
    PaymentService paymentService
    def sessionService
    RecurringPaymentService recurringPaymentService
    UserPaymentCardService userPaymentCardService

    /**
     * Handle all notifications from Checkout PSP
     * Includes approved (authorized), capture, disputes*
     *
     */
    def checkoutPspNotification() {
        log.info("paymentNotification.checkoutPspNotification - entering with [params:$params]")
        log.info("headers:" + request.getHeader("Cko-Signature"));

        //TODO: Move this to Environment properties file
        String secretKey = "062624b9-641a-48fa-8f36-b4e74348b1cf"; //gateway.checkoutPsp.api.hashingKey

        def requestBody = request.reader.text
        log.info("paymentNotification.checkoutPspNotification - requestBody:$requestBody")
        JsonSlurper jsonSlurper = new JsonSlurper()
        Map<String, Object> notification = (Map<String, Object>) jsonSlurper.parseText(requestBody)

        String generatedHmac256 = paymentService.generateHmac256(requestBody.toString(), secretKey);
        log.info("generatedHmac256:" + generatedHmac256);

        if (generatedHmac256.equalsIgnoreCase(request.getHeader("Cko-Signature"))) {
            log.info("paymentNotification.checkoutPspNotification - signature matched")
            Thread.sleep(5000L) //Wait just in case the quote is still under process

            def quote = null
            PaymentInvoice invoice = PaymentInvoice.findByMerchantRef(notification.data.reference)
            if (invoice != null) {
                quote = invoice.getRecurringPayment().getSourceQuote()
            } else if (notification.data.reference != null) {
                quote = commonUtilService.getQuoteFromMerchantRef(notification.data.reference)
            }

            paymentService.savePaymentResponse(quote, requestBody);

            if (quote && notification.type == "payment_approved") {

                if (invoice) {
                    //No need to save card token in case of invoice, as the card details are already stored.
                    render "OK"
                    return
                }

                PaymentCardDetails paymentCardDetail =
                    PaymentCardDetails.findByMerchantRefAndTokenNameAndActive(notification.data.reference,
                    notification.data.source.id, true)

                if (!paymentCardDetail) {
                    PaymentResponseDto.CardDetailDto cardDetailDto = new PaymentResponseDto.CardDetailDto()
                    cardDetailDto.cardHolderName = notification.data.source.name
                    cardDetailDto.maskedCardNumber = notification.data.source.bin.concat("******").concat(notification.data.source.last_4)
                    cardDetailDto.expiryMonth = notification.data.source.expiry_month
                    cardDetailDto.expiryYear = notification.data.source.expiry_year
                    cardDetailDto.tokenName = notification.data.source.id
                    cardDetailDto.merchantRef = notification.data.reference
                    cardDetailDto.paymentGateway = PaymentGatewayEnum.CHECKOUT

                    paymentService.savePaymentCardDetailsV2(quote.userId, cardDetailDto)

                    if (quote.hasProperty('token')) {
                        quote.token = cardDetailDto.tokenName
                        quote.save()
                    }
                }

                render "OK"
                return

            }
            else if (quote && notification.type == "payment_captured") {

                if (invoice) {
                    //TODO:If Invoice, then check if invoice is not paid, then mark as paid. and in all invoice cases, return
                    render "OK"
                    return
                }

                QuoteAdditionalPayment quoteAdditionalPayment = QuoteAdditionalPayment.findByMerchantRef(notification.data.reference)
                if (quoteAdditionalPayment) {
                    //If Quote additional payment, then mark it as paid
                    quoteAdditionalPayment = paymentService.handlePaymentNotificationOnCheckoutPaymentLink(notification)

                    //TODO: Add a check to decide when to do the quote payment.. like p_full_payment=true
                    if (notification.data.metadata && notification.data.metadata.p_full_payment == "true") {
                        //Full policy payment has been received, update to COD and mark as paid
                        quote.paymentMethod = PaymentMethodEnum.COD
                        quote.codPaymentOption = CodPaymentOptionEnum.LINKS
                        quote.save()

                        def customParams = [:]
                        customParams = addCheckoutMetadata(customParams, notification.data.metadata)
                        paymentService.changePaymentStatus(quote, PaymentStatusEnum.PAID)

                        Country country = quote.quoteCountry

                        String discountCode
                        if (country) {
                            //Referral only for UAE
                            if (country.code == CountryEnum.UAE.isoAlpha2Code) {
                                discountCode = checkoutService.getOrCreateDiscountCode(quote.productType.value(), country, quote.user, quote.id)
                            }
                        } else {
                            log.warn("Could not find a Country object corresponding to country code = $params.country. Discount code " +
                                "can not be created without the country information, skipping that step.")
                        }

                        String hash = AESCryption.encrypt(quote.id.toString())
                        String brand = sessionService.getBrandCode()

                        notify(
                            AsyncEventConstants.CAR_QUOTE_PURCHASED,
                            [quoteId: quote.id, country: CountryEnum.findCountryById(country.id),
                             lang: quote.lang, discountCode: discountCode,
                             hash: hash, brand: brand, serverName: request.serverName]
                        )

                    }
                    render "OK"
                    return
                }

                if (quote.paidDate != null) {
                    log.info("paymentNotification.checkoutPspNotification - Quote: ${quote.id} is already paid, " +
                        "no need for further process and returing.")
                    render "OK"
                    return
                }

                def customParams = [:]
                customParams.paymentGatewayEnum = PaymentGatewayEnum.CHECKOUT

                PaymentResponseDto ckoResponseDto = new PaymentResponseDto()
                ckoResponseDto.merchantReference = notification.data.reference
                ckoResponseDto.pgPaymentId = notification.data.id
                //ckoResponseDto.cardToken = notification.data.source.id //Source isnt received in payment_captured event
                ckoResponseDto.paymentMerchantId = null
                ckoResponseDto.paymentStatus = notification.data.status
                ckoResponseDto.numberOfInstallments = null
                ckoResponseDto.authorizedAmount = checkoutPspService.getAuthorizedAmount(notification.data.balances)
                //Cannot take total_captured as its the total amount we have captured so far and doesnt include refund
                ckoResponseDto.capturedAmount = checkoutPspService.getCapturedAmount(notification.data.balances)

                ckoResponseDto.paymentResponseJson = requestBody
                ckoResponseDto.paymentGatewayEnum = PaymentGatewayEnum.CHECKOUT

                customParams = addCheckoutMetadata(customParams, notification.data.metadata)

                paymentService.paidV2(ckoResponseDto, customParams, customParams.p_sales_person_id != null)

                if (quote && quote instanceof CarQuote && quote.requestSource == RequestSourceEnum.ETISALAT_SMILES) {
                    boolean isEtisalatSuccess = processEtisalatPayment(quote, ckoResponseDto.pgPaymentId)

                    if (!isEtisalatSuccess) {
                        log.error("paymentNotification.checkoutPspNotification - Error processing ETS Payment. notification:$notification")
                    }
                }

                render "OK"
                return

            }
            else if (notification.type.indexOf("dispute_") > -1) {
                log.info("paymentNotification.checkoutPspNotification - Payment Dispute Event Received. notification:$notification")

                String subject = "YallaCompare - Payment Dispute Event Received"
                List toAddresses = []
                List ccAddresses = []

                if (Environment.PRODUCTION == Environment.current) {
                    toAddresses.add("<EMAIL>")
                    toAddresses.add("<EMAIL>")
                    ccAddresses.add("<EMAIL>")
                } else {
                    toAddresses.add("<EMAIL>")
                    subject = "<" + Environment.current + "> " + subject
                }
                String body = "Hi, <br/><br/>" +
                    "We have received a payment dispute event from Checkout Payment Gateway, with below detail for reference.\n" +
                    "Type: " + notification.type + ", <br/>" +
                    "Payment ID: " + notification.data.payment_id + ", <br/>" +
                    "Category: " + notification.data.category + ", <br/>" +
                    "Amount in fills: " + notification.data.amount + ". <br/>" +
                    "Kindly contact Tech Team or Checkout Payment Gateway for further detail."

                emailSgService.sendEmailViaSES(null, toAddresses, ccAddresses, subject, body, null)
                render "OK"
                return
            }
            else if (quote && notification.type in ["payment_declined", "payment_voided"]) {
                //For now, we are already storing the responses, we dont need to handle it nor notify dev.
                render "OK"
                return
            }
        }

        //Send alert Email to dev team about a notification which is not handled.
        String subject = "YallaCompare - Unknown notification event received"
        subject = "<" + Environment.current + "> " + subject
        List toAddresses = ["<EMAIL>"]

        String body = "<html><body>" +
            "Hi, <br/>" +
            "We have received a payment event: <br/><br/>" +
            "$notification" +
            "</body></html>"

        emailSgService.sendEmailViaSES(null, toAddresses, null, subject, body, null)
        render "OK"
        return

    }

    /**
     * Handle Checkout PSP Failure
     *
     */
    def checkoutPspPaymentFailure(String insuranceType, String id) {
        log.info("paymentNotification.checkoutPspPaymentFailure - entering with params:[$params]")

        List<Map<String, Object>> paymentActions = checkoutPspService.getPaymentActions(params."cko-payment-id")

        Map<String, Object> authorizationPaymentAction = paymentActions.find { it.type == "Authorization"}

        String paymentStatus = authorizationPaymentAction.response_summary

        String country = CountryEnum.UAE.code
        String lang = "en"
        ProductTypeEnum insuranceTypeEnum = ProductTypeEnum.findByName(insuranceType)

        if (insuranceTypeEnum != ProductTypeEnum.userPaymentCard) {
            def quote = commonUtilService.getQuoteFromMerchantRef(authorizationPaymentAction.reference)
            country = CountryEnum.findCountryById(quote.quoteCountryId).code
            lang = quote.lang

            notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message: paymentStatus, quoteId: quote.id]
            session[IConstant.CHECKOUT_QUOTE_ID] = quote.id
            log.error "#### ERROR #### -> ${paymentStatus} for ${quote.id} #### ERROR ####"
        }
        flash.error = paymentStatus

        String mapping = getPaymentPageMapping(insuranceTypeEnum, params.source)

        redirect mapping:mapping, params:[country: country, lang: lang, id: id, insuranceType: insuranceType, d:params.d],
            base: sessionService.getBaseUrl()

        return
    }


    /**
     * Handle Checkout PSP Cancel
     *
     */
    def checkoutPspPaymentCancel(String insuranceType, String id) {
        log.info("paymentNotification.checkoutPspPaymentCancel - entering with params:[$params]")
        Long quoteUserId = Long.parseLong(AESCryption.decrypt(id))
        String country = CountryEnum.UAE.code
        String lang = "en"
        ProductTypeEnum insuranceTypeEnum = ProductTypeEnum.findByName(insuranceType)

        if (insuranceTypeEnum != ProductTypeEnum.userPaymentCard) {
            def quote = commonQuoteService.getQuote(quoteUserId, insuranceTypeEnum)
            country = CountryEnum.findCountryById(quote.quoteCountryId).code
            lang = quote.lang
        }

        flash.error = "Cancelled"

        String mapping = getPaymentPageMapping(insuranceTypeEnum, params.source)

        redirect mapping:mapping,
            params:[country: country, lang: lang, id: id, insuranceType: insuranceType, d:params.d],
            base: sessionService.getBaseUrl()

        return
    }

    /**
     * Handle Checkout PSP Success
     *
     */
    def checkoutPspPaymentSuccess(String insuranceType, String id) {
        log.info("paymentNotification.checkoutPspPaymentSuccess - entering with params:[$params]")
        ProductTypeEnum insuranceTypeEnum = ProductTypeEnum.findByName(insuranceType)
        String country = CountryEnum.UAE.code
        String lang = "en"
        Long quoteUserId = Long.parseLong(AESCryption.decrypt(id))

        String ckoPaymentId = params."cko-payment-id"

        def quote

        if (insuranceTypeEnum != ProductTypeEnum.userPaymentCard) {
            quote = commonQuoteService.getQuote(quoteUserId, insuranceTypeEnum)

            country = CountryEnum.findCountryById(quote.quoteCountryId).code
            lang = quote.lang

            if (quote && !quote.isNotProcessed()) {
                redirect mapping: getSuccessPageMapping(insuranceTypeEnum, params.source),
                    params: [country: country, lang: lang, id: id, insuranceType: insuranceType], base: sessionService.getBaseUrl()
                return
            }
        }

        if (ckoPaymentId == null) {
            session[IConstant.CHECKOUT_QUOTE_ID] = quoteUserId
            Thread.sleep(10000L) //Allow notification handler to handle the quote response in the meantime

            redirect mapping: getPaymentPageMapping(insuranceTypeEnum, params.source),
                params: [country: country, lang: lang, id: id, insuranceType: insuranceType, d:params.d],
                base: sessionService.getBaseUrl()
            return
        }

        def paymentStatusResponse = checkoutPspService.getCheckoutPaymentStatus(ckoPaymentId)

        String paymentStatus = paymentStatusResponse.status

        if (insuranceTypeEnum == ProductTypeEnum.userPaymentCard && paymentStatus == "Voided") {
            //Payment Already voided
            PaymentCardDetails paymentCardDetail =
                PaymentCardDetails.findByMerchantRefAndTokenNameAndActive(paymentStatusResponse.reference,
                paymentStatusResponse.source.id, true)

            if (paymentCardDetail) {
                //Card detail already stored
                redirect mapping: 'apiUserPaymentCardThankyou', params: [country      : country, lang: lang, id: id,
                                                                         insuranceType: insuranceType], base: sessionService.getBaseUrl()
                return
            }
            //else continue to store the card
        } else if (insuranceTypeEnum != ProductTypeEnum.userPaymentCard && paymentStatus == "Authorized") {
            //GET actions to know if capture was done or was it failed and for what reason
            List<Map<String, Object>> paymentActions = checkoutPspService.getPaymentActions(ckoPaymentId)
            log.info("paymentNotification.checkoutPspPaymentSuccess - paymentActions: ${paymentActions} for ckoPaymentId:$ckoPaymentId")

            Map<String, Object> capturePaymentAction = paymentActions.find { it.type == "Capture"}

            String requestMessage = ""
            if (capturePaymentAction != null) {
                requestMessage = capturePaymentAction.response_summary
            } else {
                requestMessage = "Unable to Capture for unknown reason"
            }

            notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message: requestMessage, quoteId: quoteUserId]

            flash.error = requestMessage + ". Please contact YallaCompare Support"
            log.error "paymentNotification.checkoutPspPaymentSuccess - " + "#### ERROR #### -> ${paymentStatus} for ${id} #### ERROR ####"

            redirect mapping: getPaymentPageMapping(insuranceTypeEnum, params.source),
                params: [country: country, lang: lang, id: id, insuranceType: insuranceType, d:params.d],
                base: sessionService.getBaseUrl()
            return

        } else if ((insuranceTypeEnum != ProductTypeEnum.userPaymentCard && paymentStatus != "Captured") ||
            (insuranceTypeEnum == ProductTypeEnum.userPaymentCard && (paymentStatus != "Authorized" && paymentStatus != "Captured"))) {
            flash.error = paymentStatus
            log.error "paymentNotification.checkoutPspPaymentSuccess - " + "#### ERROR #### -> ${paymentStatus} for ${id} #### ERROR ####"

            notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message: paymentStatus, quoteId: quoteUserId]

            redirect mapping: getPaymentPageMapping(insuranceTypeEnum, params.source),
                params: [country: country, lang: lang, id: id, insuranceType: insuranceType, d:params.d],
                base: sessionService.getBaseUrl()
            return
        }

        def customParams = [:]
        customParams.paymentGatewayEnum = PaymentGatewayEnum.CHECKOUT

        PaymentResponseDto.CardDetailDto cardDetailDto = new PaymentResponseDto.CardDetailDto()
        cardDetailDto.cardHolderName = paymentStatusResponse.source.name
        cardDetailDto.maskedCardNumber = paymentStatusResponse.source.bin.concat("******").concat(paymentStatusResponse.source.last4)
        cardDetailDto.expiryMonth = paymentStatusResponse.source.expiry_month
        cardDetailDto.expiryYear = paymentStatusResponse.source.expiry_year
        cardDetailDto.tokenName = paymentStatusResponse.source.id
        cardDetailDto.merchantRef = paymentStatusResponse.reference
        cardDetailDto.paymentGateway = PaymentGatewayEnum.CHECKOUT

        if (insuranceTypeEnum != ProductTypeEnum.userPaymentCard) {
            PaymentResponseDto ckoResponseDto = new PaymentResponseDto()
            ckoResponseDto.merchantReference = paymentStatusResponse.reference
            ckoResponseDto.pgPaymentId = paymentStatusResponse.id
            ckoResponseDto.cardToken = paymentStatusResponse.source.id
            ckoResponseDto.paymentMerchantId = null
            ckoResponseDto.paymentStatus = paymentStatusResponse.status
            ckoResponseDto.numberOfInstallments = null
            if (paymentStatusResponse.status.equals("Captured") && paymentStatusResponse.approved == true) {
                ckoResponseDto.authorizedAmount = paymentStatusResponse.amount / 100
                ckoResponseDto.capturedAmount = ckoResponseDto.authorizedAmount
            }
            ckoResponseDto.paymentResponseJson = paymentStatusResponse
            ckoResponseDto.paymentGatewayEnum = PaymentGatewayEnum.CHECKOUT
            ckoResponseDto.cardDetailDto = cardDetailDto

            customParams = addCheckoutMetadata(customParams, paymentStatusResponse.metadata)

            paymentService.paidV2(ckoResponseDto, customParams, params.source == "api")

            WhiteLabelBrandEnum brandEnum = sessionService.getBrand()

            if (quote && quote instanceof CarQuote && brandEnum && brandEnum == WhiteLabelBrandEnum.ETISALAT_SMILES) {
                boolean isEtisalatSuccess = processEtisalatPayment(quote, paymentStatusResponse.id)

                if (!isEtisalatSuccess) {
                    def errorDetails = []

                    errorDetails.add([title: "Transaction Id", message: "${session['etisalatTransactionId']}"])
                    errorDetails.add([title: "Loyality ID", message: "${session['etisalatloyaltyId']}"])
                    errorDetails.add([title: "Partner Transaction Id", message: "${quote.id}"])

                    def errorMessage = g.message(message: "Sorry something went wrong while accruing points. Payment has been rolled back")
                    render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                    return
                }
            }

        } else if (insuranceTypeEnum == ProductTypeEnum.userPaymentCard) {

            try {

                PaymentCardDetails paymentCard = paymentService.addPaymentCardAndRefund(quoteUserId, cardDetailDto, paymentStatusResponse.id)

                //TODO: switch cards of all pending subscriptions...
                if (paymentCard) {
                    String paramsData = params.d ? AESCryption.decrypt(params.d) : null
                    def extraParams = commonUtilService.convertQueryStringToMap(paramsData)
                    if (extraParams.switch && extraParams.switch == "all") {
                        //Update default card and switch all pending payments to new card
                        User user = User.read(quoteUserId)
                        userPaymentCardService.makeDefaultPaymentCard(user, paymentCard)
                    }
                }
            } catch (Exception exp) {
                log.error("Exception:", exp)
                String message = g.message(code:'checkout.general.error')

                redirect mapping: 'apiUserPaymentCardError',
                    params: [insuranceType: ProductTypeEnum.userPaymentCard.toString(), id: id,
                             country: country, lang: lang, message: message], base: sessionService.getBaseUrl()
                return
            }
        }

        log.debug("paymentNotification.checkoutPspPaymentSuccess - Redirect user to thankyou for merchantRef: ${paymentStatusResponse.reference}")

        redirect mapping: getSuccessPageMapping(insuranceTypeEnum, params.source),
            params:[country:country, lang:lang, id:id, insuranceType:insuranceType], base: sessionService.getBaseUrl()

        return
    }

    private def addCheckoutMetadata(def customParams, def metadata) {
        customParams.p_total_price = metadata.p_total_price
        customParams.p_discount_id = metadata.p_discount_id
        customParams.p_discount = metadata.p_discount
        customParams.p_product_id = metadata.p_product_id
        customParams.p_excess = metadata.p_excess ?
            AESCryption.encrypt(metadata.p_excess) : null
        customParams.p_addon_price = metadata.p_addon_price
        customParams.p_policy_price = metadata.p_policy_price
        customParams.p_c4me_fee = metadata.p_c4me_fee
        customParams.p_pprice_vat = metadata.p_pprice_vat
        customParams.p_c4me_vat = metadata.p_c4me_vat
        customParams.p_addon_vat = metadata.p_addon_vat
        customParams.p_addon_id = metadata.p_addon_id && metadata.p_addon_id.size() > 0 ? metadata.p_addon_id.split(",") : []
        customParams.p_sales_person_id = metadata.p_sales_person_id
        customParams.p_act_sum_ins = metadata.p_act_sum_ins

        return customParams
    }

    private String getSuccessPageMapping(ProductTypeEnum insuranceType, String source) {
        String mapping = 'apiCheckoutThankyou'
        if (source == "api") {
            return mapping
        }
        switch (insuranceType) {
            case ProductTypeEnum.CAR:
                mapping = 'carCheckoutThankyou'
                break
            case ProductTypeEnum.HEALTH:
                mapping = 'healthCheckoutThankyou'
                break
            case ProductTypeEnum.HOME:
                mapping = 'homeInsuranceCheckoutThankyou'
                break
            case ProductTypeEnum.GENERAL:
                mapping = 'generalCheckoutThankyou'
                break
            case ProductTypeEnum.userPaymentCard:
                mapping = 'apiUserPaymentCardThankyou'
                break
        }
    }

    private String getPaymentPageMapping(ProductTypeEnum insuranceType, String source) {
        String mapping = 'apiCheckoutPaynow'
        if (source == "api") {
            return mapping
        }

        switch (insuranceType) {
            case ProductTypeEnum.CAR:
                mapping = 'carCheckoutPaynow'
                break
            case ProductTypeEnum.HEALTH:
                mapping = 'healthCheckoutPaynow'
                break
            case ProductTypeEnum.HOME:
                mapping = 'homeInsuranceCheckoutPaynow'
                break
            case ProductTypeEnum.GENERAL:
                mapping = 'generalQuoteCheckout'
                break
            case ProductTypeEnum.userPaymentCard:
                mapping = 'apiUserPaymentCardPaynow'
                break
        }
        return mapping
    }

    private boolean processEtisalatPayment(CarQuote quote, String pgPaymentId) {

        EtisalatSmilesPayment payment = EtisalatSmilesPayment.findByQuote(quote)

        PaymentMethodEnum paymentMethod = payment.moneyAmount ? PaymentMethodEnum.ETISALAT_SMILES_MIX : PaymentMethodEnum.CREDITCARD
        def moneyAmount = payment.moneyAmount ? payment.moneyAmount : quote.totalPrice
//                        def etisalatSmilesPaymentId = session['EtisalatSmilesPaymentId'] ? session['EtisalatSmilesPaymentId'] : null
        def etisalatSmilesPaymentId, isSuccessful
        def (tokenType, token, authToken) = etisalatApiService.authApisWrapper(payment.transactionId, payment.etisalatUser.loyalityId)

        if (paymentMethod == PaymentMethodEnum.ETISALAT_SMILES_MIX) {
            log.info("Payment Methods --- ${paymentMethod}")
            log.info("Etisalat User ID --- ${session['etisalatUserId']}")
            log.info("Mix Points AMount --- ${session['MixPointsAmount']}")
            log.info("Payment Methods --- ${paymentMethod}")

            etisalatSmilesPaymentId = etisalatApiService.callRedeemPointsForPartner(quote.id.toString(),
                String.valueOf((int) payment.pointsAmount), PaymentMethodEnum.ETISALAT_SMILES_MIX.name(),
                session['etisalatUserId'], payment.transactionId, payment.etisalatUser.loyalityId, authToken,
                tokenType, token)

            if (etisalatSmilesPaymentId) {
                session['EtisalatSmilesPaymentId'] = etisalatSmilesPaymentId
            } else {
                //TODO send void authorize
                paymentService.process(params, PayfortCommandEnum.VOID_AUTHORIZATION, null, null)
                paymentService.changePaymentStatus(quote, PaymentStatusEnum.CANCEL)
                payment.pointsAmount = null
                payment.moneyAmount = null
                payment.save(flash: true)
                def errorDetails = []

                errorDetails.add([title: "Transaction Id", message: "${payment.transactionId}"])
                errorDetails.add([title: "Loyality ID", message: "${payment.etisalatUser.loyalityId}"])
                errorDetails.add([title: "Partner Transaction Id", message: "${quote.id}"])

                def errorMessage = g.message(message: "Sorry something went wrong while redeeming points. Payment has been rolled back")
                render view: "/common/errorMessage", model: [errorMessage: errorMessage, errorDetails: errorDetails, country: params.country]
                return
            }
        }

        quote.paymentMethod = paymentMethod
        quote.save(failOnError: true, flush: true)

        log.info("Etisalat Quote Id --- ${quote.id.toString()}")
        log.info("Etisalat User Id --- ${payment.etisalatUser.userId}")
        log.info("Etisalat Loyalty ID --- ${payment.etisalatUser.loyalityId}")
        log.info("Etisalat Token --- ${session['etisalatToken']}")
        log.info("Etisalat Token Type--- ${session['etisalatTokenType']}")
        log.info("Etisalat Auth Token --- ${session['etisalatAuthToken']}")

        boolean etisalatPointsAccrued = etisalatApiService.callAccrualForPartner(quote.id.toString(), moneyAmount.toString(),
            paymentMethod.name(), session['etisalatUserId'], payment.transactionId, payment.etisalatUser.loyalityId,
            authToken, tokenType, token, etisalatSmilesPaymentId.toString())

        if (!etisalatPointsAccrued) {
            PaymentOperationResponse paymentOperationResponse = checkoutPspService.revertPaymentAmount(pgPaymentId, moneyAmount)
            if (paymentOperationResponse.status != CheckoutPspResponseCodeEnum.APPROVED.toString()) {
                log.error("paymentNotificationController.processEtisalatPayment - unable to void the payment [pgPaymentId:$pgPaymentId]")
            }
            paymentService.changePaymentStatus(quote, PaymentStatusEnum.CANCEL)
            payment.pointsAmount = null
            payment.moneyAmount = null
            payment.save(flash: true)

            return false
        }

        return true
    }
}
