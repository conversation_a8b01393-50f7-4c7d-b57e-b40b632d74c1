package com.cover.payment

import com.cover.util.IConstant
import com.safeguard.CountryEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.payment.PaymentCardDetails
import com.safeguard.payment.PaymentInvoice
import com.safeguard.payment.PaymentResponseDto
import com.safeguard.tapPaymentPsp.TapPaymentCommand
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.util.Environment
import groovy.json.JsonSlurper
import org.springframework.http.HttpStatus

/**
 * Tap Payment Notification Handler
 */
class TapPaymentNotificationController {

    def commonUtilService
    def commonQuoteService
    def configurationService
    def emailSgService
    def paymentService
    def tapPaymentService
    def tapPaymentApiService

    def tokenize() {
        log.info(".tokenize - params:$params")
        Long quoteId
        if (params.encQuoteId) {
            quoteId = Long.parseLong(AESCryption.decrypt(params.encQuoteId))
            session[IConstant.CHECKOUT_QUOTE_ID] = quoteId
        }

        if (!quoteId) {
            render status: 404
            return
        }

        def quote = commonQuoteService.getQuote(quoteId, ProductTypeEnum.findByName(params.productType))

/*        if (quote && !quote.isNotProcessed()) {
            render view: '/car/funnel/quoteProcessed', model: [country: params.country]
            return
        }*/

        if (quote && !quote.isNotProcessed()) {
            log.warn "tokenize - Quote is already processed for quote id: ${quote.id}"
            redirect mapping: getSuccessPageMapping(quote.productType, params.insuranceType),
                params: [country: params.country, lang: params.lang, id: params.encQuoteId, insuranceType: params.insuranceType], base: getBaseUrl()
            return
        }

        /*
        String providerMerchantId = grailsApplication.config.tapPaymentPsp.providersMID."${quote.product.provider.code}"
        if (providerMerchantId == null) {
            providerMerchantId = grailsApplication.config.tapPaymentPsp.providersMID.test
        }

        String server = grailsApplication.config.getProperty('server.cover')
        String postUrl = "https://b783-91-75-121-179.ngrok-free.app/insurance/payments/tapPayment/notification"
        String redirectUrl = "${server}/payments/tapPayment/redirect"

        TapPaymentCommand.Customer tapCustomer = new TapPaymentCommand.Customer()
        tapCustomer.email = quote.email
        tapCustomer.firstName = quote.user.firstName
        tapCustomer.lastName = quote.user.lastNames
        tapCustomer.phoneCountryCode = CountryEnum.UAE.callingCode
        tapCustomer.phoneLocalNumber = commonUtilService.extractPhoneNumber(quote.mobile).substring(1)

        String merchantRef = quote.encodedrMerchantRef()
        TapPaymentCommand.Reference tapReference = new TapPaymentCommand.Reference()
        tapReference.transaction = merchantRef
        tapReference.order = merchantRef

        TapPaymentCommand command = new TapPaymentCommand()
        command.sourceId = params.tapPaymentCardToken
        command.amount = quote.totalPrice
        command.currency = quote.currency
        command.redirectUrl = redirectUrl
        command.postUrl = postUrl
        command.providerMerchantId = providerMerchantId
        command.reference = tapReference
        command.description = paymentService.getOrderDescription(quote, 120)
        command.customer = tapCustomer
        command.receipt = new TapPaymentCommand.Receipt()
        command.encryptedQuoteId = params.encQuoteId
        command.saveCard = false
        command.metaData = []

        def createChargeResp = tapPaymentApiService.callCreateChargeApi(command)*/

        JsonSlurper jsonSlurper = new JsonSlurper()
        Map<String, Object> tapPaymentCardDetail = (Map<String, Object>) jsonSlurper.parseText(params.tapPaymentCardDetail)


        PaymentResponseDto.CardDetailDto cardDetailDto = new PaymentResponseDto.CardDetailDto()
        cardDetailDto.cardHolderName = tapPaymentCardDetail.name
        cardDetailDto.maskedCardNumber = tapPaymentCardDetail.first_six.concat("******").concat(tapPaymentCardDetail.last_four)
        cardDetailDto.expiryMonth = tapPaymentCardDetail.exp_month
        cardDetailDto.expiryYear = tapPaymentCardDetail.exp_year
        cardDetailDto.cardId = tapPaymentCardDetail.id
        //cardDetailDto.merchantRef = notification.reference.order
        cardDetailDto.paymentGateway = PaymentGatewayEnum.TAP_PAYMENT

        PaymentCardDetails paymentCardDetails = paymentService.savePaymentCardDetailsV2(quote.userId, cardDetailDto)


        def createChargeResp = tapPaymentService.initializeCharge(quote, params.tapPaymentCardToken, params.source)
        paymentCardDetails.paymentMerchantId = createChargeResp.merchant.id
        paymentCardDetails.merchantRef = createChargeResp.reference.order
        paymentCardDetails.save()

        if (createChargeResp.threeDSecure) {
            String mapping = params.source == 'api' ? '/apiV1/checkout/redirect' : '/car/checkout/_redirect'

            render view: mapping, model: [url : createChargeResp.transaction.url,
                                quote: quote, country: params.country, lang: params.lang,
                                conversionRate: configurationService.getValue('etisalatSmiles.api.conversionRate')
            ]
            return
        }

        //redirect createChargeResp.transaction.url
        //redirect "https://acceptance.sandbox.tap.company/gosell/v2/payment/tap_process.aspx?chg=w%2fPIU2Jhegw%2fSvP7WqMG9eJrQS3SvhjNgwrprKT5WOo%3d"
    }

    /**
     * Initiate Tabby payment
     * @return
     */
    def initiateTabby() {
        log.info(".initiateTabby - params:$params")

        // Enhanced debugging for blank page issue
        log.info(".initiateTabby - tapPaymentService available: ${tapPaymentService != null}")
        log.info(".initiateTabby - commonQuoteService available: ${commonQuoteService != null}")

        Long quoteId
        if (params.encQuoteId) {
            try {
                quoteId = Long.parseLong(AESCryption.decrypt(params.encQuoteId))
                session[IConstant.CHECKOUT_QUOTE_ID] = quoteId
                log.info(".initiateTabby - Successfully decrypted quoteId: ${quoteId}")
            } catch (Exception e) {
                log.error(".initiateTabby - Failed to decrypt encQuoteId: ${params.encQuoteId}", e)
                render status: 400, text: "Invalid quote ID"
                return
            }
        }

        if (!quoteId) {
            log.error(".initiateTabby - No quoteId provided")
            render status: 404, text: "Quote ID required"
            return
        }

        def quote = commonQuoteService.getQuote(quoteId, ProductTypeEnum.findByName(params.productType))
        if (!quote) {
            log.error(".initiateTabby - Quote not found for ID: ${quoteId}, productType: ${params.productType}")
            render status: 404, text: "Quote not found"
            return
        }

        log.info(".initiateTabby - Found quote: ${quote.id}, productType: ${quote.productType}")

        // Check if tapPaymentService is available
        if (!tapPaymentService) {
            log.error(".initiateTabby - tapPaymentService is null - dependency injection failed")
            render status: 500, text: "Payment service unavailable"
            return
        }

        try {
            log.info(".initiateTabby - Calling tapPaymentService.initializeTabbyCharge with quote: ${quote.id}, source: ${params.source}")
            def createChargeResp = tapPaymentService.initializeTabbyCharge(quote, params.source)
            log.info(".initiateTabby - Received response: ${createChargeResp}")

            if (createChargeResp?.status == "INITIATED" && createChargeResp.transaction?.url &&
                createChargeResp.transaction.url.contains("tap.company")) {
                log.info(".initiateTabby - Redirecting to Tabby URL: ${createChargeResp.transaction.url}")
                // Redirect to Tabby payment page
                redirect url: createChargeResp.transaction.url
                return
            } else {
                log.error(".initiateTabby - Unexpected response status: ${createChargeResp?.status}, response: ${createChargeResp}")
                flash.error = message(code: 'payment.tabby.error.generic', default: 'Unable to process Tabby payment. Please try again.')
                redirect mapping: getCheckoutPageMapping(quote.productType),
                    params: [country: params.country, lang: params.lang, id: params.encQuoteId]
                return
            }
        } catch (IllegalArgumentException e) {
            log.error(".initiateTabby - Validation error: ${e.message}", e)
            flash.error = e.message
            redirect mapping: getCheckoutPageMapping(quote.productType),
                params: [country: params.country, lang: params.lang, id: params.encQuoteId]
            return
        } catch (Exception e) {
            log.error(".initiateTabby - Error initializing Tabby charge", e)
            flash.error = message(code: 'payment.tabby.error.generic', default: 'Unable to process Tabby payment. Please try again.')
            redirect mapping: getCheckoutPageMapping(quote.productType),
                params: [country: params.country, lang: params.lang, id: params.encQuoteId]
            return
        }
    }

    /**
     * Tap Payment Webhook notification about a charge
     * @return
     */
    def notification() {
        log.info(".notification - params:$params")
        log.info(".notification - Headers {} ", request.getHeader("hashstring"))
        Thread.sleep(10000) //Wait for 10 seconds before continuing

        def requestBody = request.reader.text
        log.info(".notification - requestBody:$requestBody")
        JsonSlurper jsonSlurper = new JsonSlurper()
        Map<String, Object> notification = (Map<String, Object>) jsonSlurper.parseText(requestBody)

        def quote = commonUtilService.getQuoteFromMerchantRef(notification.reference.order)

        boolean isHashMatched = isHashMatched(notification, request.getHeader("hashstring"))
        if (!isHashMatched) {
            log.info(".notification - Hash not matched, returning back")

            String subject = "YallaCompare - Tap Payment Mismatched Signature"
            subject = "<" + Environment.current + "> " + subject
            List toAddresses = ["<EMAIL>"]

            String body = "<html><body>" +
                "Hi, <br/>" +
                "We have received a Mismatched signature event with header:${request.getHeader("hashstring")} for <br/><br/>" +
                "$notification" +
                "</body></html>"

            emailSgService.sendEmailViaSES(null, toAddresses, null, subject, body, null)

            response.status == HttpStatus.BAD_REQUEST.value()
            return
        }

        paymentService.savePaymentResponse(quote, requestBody)

        if (notification.status != "CAPTURED") {
            log.info(".notification - Quote: ${quote.id} status received as ${notification.status}, no action required.")

            render "Notification OK"
            return
        }

       /* PaymentCardDetails paymentCardDetail =
            PaymentCardDetails.findByMerchantRefAndActive(notification.reference.order, true)

        if (!paymentCardDetail && notification.card) {
            PaymentResponseDto.CardDetailDto cardDetailDto = new PaymentResponseDto.CardDetailDto()
            //cardDetailDto.cardHolderName = notification.card.name
            cardDetailDto.maskedCardNumber = notification.card.first_six.concat("******").concat(notification.card.last_four)
            //cardDetailDto.expiryMonth = notification.data.source.expiry_month
            //cardDetailDto.expiryYear = notification.data.source.expiry_year
            //cardDetailDto.tokenName = notification.data.source.id
            cardDetailDto.merchantRef = notification.reference.order
            cardDetailDto.paymentGateway = PaymentGatewayEnum.TAP_PAYMENT

            paymentService.savePaymentCardDetailsV2(quote.userId, cardDetailDto)
        }*/

        if (quote.paidDate != null) {
            log.info(".notification - Quote: ${quote.id} is already paid, " +
                "no need for further process and returing.")
            render "OK"
            return
        }

        def customParams = [:]
        customParams.paymentGatewayEnum = PaymentGatewayEnum.CHECKOUT

        PaymentResponseDto ckoResponseDto = new PaymentResponseDto()
        ckoResponseDto.merchantReference = notification.reference.order
        ckoResponseDto.pgPaymentId = notification.id
        //ckoResponseDto.cardToken = notification.data.source.id //Source isnt received in payment_captured event
        ckoResponseDto.paymentMerchantId = notification.merchant.id
        ckoResponseDto.paymentStatus = notification.status
        ckoResponseDto.numberOfInstallments = null
        ckoResponseDto.authorizedAmount = notification.amount
        //Cannot take total_captured as its the total amount we have captured so far and doesnt include refund
        ckoResponseDto.capturedAmount = notification.amount

        ckoResponseDto.paymentResponseJson = requestBody
        ckoResponseDto.paymentGatewayEnum = PaymentGatewayEnum.TAP_PAYMENT

        //customParams = addCheckoutMetadata(customParams, notification.data.metadata)

        paymentService.paidV2(ckoResponseDto, customParams, false)

        render "Notification OK"
        return
    }

    /**
     * Tap Payment redirects to this method with tap_id (charge id)
     * @return
     */
    def redirect() {
        log.info(".redirect - params:$params")

        def chargeResp = tapPaymentApiService.retrieveChargeApi(params.tap_id)
        def quote = commonUtilService.getQuoteFromMerchantRef(chargeResp.reference.order)

        paymentService.savePaymentResponse(quote, chargeResp)

        String countryCode = CountryEnum.findCountryById(quote.quoteCountryId).code
        String encQuoteId = AESCryption.encrypt(quote.id + "")

        if (chargeResp.status == "CAPTURED") {
            if (!quote.paidDate) {
                def customParams = [:]
                customParams.paymentGatewayEnum = PaymentGatewayEnum.CHECKOUT

                PaymentResponseDto ckoResponseDto = new PaymentResponseDto()
                ckoResponseDto.merchantReference = chargeResp.reference.order
                ckoResponseDto.pgPaymentId = chargeResp.id
                //ckoResponseDto.cardToken = notification.data.source.id //Source isnt received in payment_captured event
                ckoResponseDto.paymentMerchantId = chargeResp.merchant.id
                ckoResponseDto.paymentStatus = chargeResp.status
                ckoResponseDto.numberOfInstallments = null
                ckoResponseDto.authorizedAmount = chargeResp.amount
                //Cannot take total_captured as its the total amount we have captured so far and doesnt include refund
                ckoResponseDto.capturedAmount = chargeResp.amount

                ckoResponseDto.paymentResponseJson = chargeResp.toString()
                ckoResponseDto.paymentGatewayEnum = PaymentGatewayEnum.TAP_PAYMENT

                //customParams = addCheckoutMetadata(customParams, notification.data.metadata)

                paymentService.paidV2(ckoResponseDto, customParams, false)

            } else {
                log.info(".redirect - already paid. quoteId:${quote.id}, productType:${quote.productType}")

            }

            redirect mapping: getSuccessPageMapping(quote.productType, params.source),
                params: [country: params.country, lang: params.lang, id: encQuoteId, insuranceType: params.insuranceType], base: getBaseUrl()
            return
        } else {
            log.info(".redirect - payment status is :${chargeResp.status}, for quoteId:${quote.id}, productType:${quote.productType}")

        }

        flash.error = chargeResp.response.message
        String mapping = getPaymentPageMapping(quote.productType, params.source)

        redirect mapping: mapping, params: [country: countryCode, lang: quote.lang, id: encQuoteId,
                                            insuranceType: params.insuranceType], base: getBaseUrl()
        return
    }

    private String getBaseUrl() {
        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]
        String baseUrl = whiteLabelDomain ? whiteLabelDomain.baseUrl : grailsApplication.config.getProperty('yallacompare.baseURL')
        log.info("baseUrl:${baseUrl}")

        baseUrl
    }

    boolean isHashMatched(def charge, String actualHash) {
        // Get the passed data from the post response (charge / authorize / invoice response)

        String id = charge.id
        String amount = charge.amount
        String currency = charge.currency
        String gateway_reference = charge.reference.gateway
        String payment_reference = charge.reference.payment
        //String updated = invoice.updated'
        String status = charge.status
        String created = charge.transaction.created

        // Your Secret API Key Provided by Tap
        String secretAPIKey = grailsApplication.config.getProperty("tapPaymentPsp.api.secretKey")

        // Charges - Create a hashstring from the posted response data + the data that are related to you.
        String toBeHashedString = "x_id${id}x_amount${amount}x_currency${currency}x_gateway_reference${gateway_reference}x_payment_reference${payment_reference}x_status${status}x_created${created}"

        log.info("toBeHashedString:${toBeHashedString}")

        // Create your hashstring by passing concatinated string and your secret API Key
        String myHashString = paymentService.generateHmac256(toBeHashedString, secretAPIKey);
            //hash_hmac('sha256', toBeHashedString, secretAPIKey);
        log.info("myHashString:${myHashString}, actualHash:${actualHash}")

        // Legitimate the post request by comparing the hashstring you computed with the one posted in the header
        return myHashString.equalsIgnoreCase(actualHash)
    }

    private String getSuccessPageMapping(ProductTypeEnum insuranceType, String source) {
        String mapping = 'apiCheckoutThankyou'
        if (source == "api" || source == "apiV1") {
            return mapping
        }
        switch (insuranceType) {
            case ProductTypeEnum.CAR:
                mapping = 'carCheckoutThankyou'
                break
            case ProductTypeEnum.HEALTH:
                mapping = 'healthCheckoutThankyou'
                break
            case ProductTypeEnum.HOME:
                mapping = 'homeInsuranceCheckoutThankyou'
                break
            case ProductTypeEnum.GENERAL:
                mapping = 'generalCheckoutThankyou'
                break
            case ProductTypeEnum.userPaymentCard:
                mapping = 'apiUserPaymentCardThankyou'
                break
        }
    }

    private String getPaymentPageMapping(ProductTypeEnum insuranceType, String source) {
        String mapping = 'apiCheckoutPaynow'
        if (source == "api" || source == "apiV1") {
            return mapping
        }

        switch (insuranceType) {
            case ProductTypeEnum.CAR:
                mapping = 'carCheckoutPaynow'
                break
            case ProductTypeEnum.HEALTH:
                mapping = 'healthCheckoutPaynow'
                break
            case ProductTypeEnum.HOME:
                mapping = 'homeInsuranceCheckoutPaynow'
                break
            case ProductTypeEnum.GENERAL:
                mapping = 'generalQuoteCheckout'
                break
            case ProductTypeEnum.userPaymentCard:
                mapping = 'apiUserPaymentCardPaynow'
                break
        }
        return mapping
    }

    private String getCheckoutPageMapping(ProductTypeEnum productType) {
        switch (productType) {
            case ProductTypeEnum.CAR:
                return 'carInsuranceCheckout'
            case ProductTypeEnum.HOME:
                return 'homeInsuranceCheckout'
            case ProductTypeEnum.HEALTH:
                return 'healthInsuranceCheckout'
            case ProductTypeEnum.TRAVEL:
                return 'travelInsuranceCheckout'
            case ProductTypeEnum.PET:
                return 'petInsuranceCheckout'
            default:
                return 'carInsuranceCheckout'
        }
    }
}
