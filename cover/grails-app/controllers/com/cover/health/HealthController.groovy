package com.cover.health

import com.cover.common.commands.MarketingTrackingCommand
import com.cover.health.commands.*
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CignaCampaign
import com.safeguard.City
import com.safeguard.CityEnum
import com.safeguard.CountryEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.HealthRelationshipEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.health.HealthMember
import com.safeguard.health.HealthQuote
import com.safeguard.util.AESCryption
import org.joda.time.LocalDateTime

class HealthController {

    static namespace = "health"
    def utilService
    def healthQuoteService
    def paymentService
    def discountService
    def sessionService

    def index() {

        def discount = discountService.getApplicableDiscountCode(CountryEnum.findCountry(params.country), ProductTypeEnum.HEALTH.value())
        def discountAmount = null

        if (discount) {
            discountAmount = discount.discount.toBigInteger()
            discountAmount += (discount.hasPercentDiscount) ? '%' : ' AED'
        }

        render view: "/health/index", model: [country: params.country, discountAmount: discountAmount]
    }

    def details(HealthQuoteCommand healthQuoteCommand ) {

        if (request.method == "POST" && healthQuoteCommand.validate()) {
            //create health quote

            MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
            marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
            marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
            marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
            marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
            marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
            marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
            healthQuoteCommand.marketingTracking = marketingTracking

            healthQuoteCommand.countryEnum = utilService.convertToCountry(params.country)
            healthQuoteCommand.source = RequestSourceEnum.WEB.name()
            healthQuoteCommand.viewEbp = session[IConstant.HEALTH_INSURANCE_VIEW_EBP] != null ? Boolean.valueOf(session[IConstant.HEALTH_INSURANCE_VIEW_EBP]) : null
            HealthQuote healthQuote = healthQuoteService.createHealthQuote(healthQuoteCommand)
            session[IConstant.HEALTH_QUOTE_ID] = healthQuote.id
            session[IConstant.HEALTH_INSURANCE_NAME] = healthQuote.name
            session[IConstant.HEALTH_INSURANCE_MOBILE] = healthQuote.mobile

            if (healthQuoteCommand.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
                // send mail to YC support
                notify AsyncEventConstants.HEALTH_EMPLOYEES_QUOTE_CREATED, [quoteId: healthQuote.id, numberOfEmployees: healthQuoteCommand.numberOfEmployees, companyName:healthQuoteCommand.companyName]
                render (view: "/health/employeesThankyou", model: [lang: params.lang, country: params.country])
                return
//                redirect(mapping: 'healthDeclaration', params: [lang: params.lang, country: params.country])
            }

            redirect(mapping: 'healthDeclaration', params: [lang: params.lang, country: params.country, applicationType: healthQuoteCommand.applicationType])

        } else {

            HealthQuote healthQuote = HealthQuote.read(session[IConstant.HEALTH_QUOTE_ID] as String)
            log.info("health.details - healthQuoteCommand.applicationType: ${healthQuoteCommand.applicationType}")
            // if (!healthQuoteCommand.applicationType || healthQuoteCommand.viewEbp == null) {
            //     redirect (mapping: 'healthIndex', params: [country: params.country, lang:params.lang])
            //     return
            // }

            session[IConstant.HEALTH_INSURANCE_VIEW_EBP] = healthQuoteCommand.viewEbp

            render view: "/health/details", model: [healthQuoteCommand: healthQuoteCommand, healthQuote: healthQuote, lang: params.lang, country: params.country]
        }
    }

    def declaration(HealthDeclarationCommand healthDeclarationCommand) {
        HealthQuote healthQuote = HealthQuote.read(session[IConstant.HEALTH_QUOTE_ID])

        if (!healthQuote) {
            log.debug "declaration - redirect to index as healthQuote is null"
            redirect (mapping: 'healthIndex', params: [lang: params.lang, country: params.country])
            return
        }

        if (request.method == "POST") {
            healthQuoteService.updateMembers(healthDeclarationCommand)

            if (!healthQuote.areMembersHealthy()) {
                paymentService.changePaymentStatus(healthQuote, PaymentStatusEnum.NOQUOTE)
                healthQuote.save()
                redirect mapping: 'healthNoQuotes', params: [lang: params.lang, country: params.country, applicationType: healthQuote.applicationType]
                return
            } else if (healthQuote.paymentStatus == PaymentStatusEnum.NOQUOTE) {
                paymentService.changePaymentStatus(healthQuote, PaymentStatusEnum.DRAFT)
                healthQuote.save()
            }

            notify AsyncEventConstants.HEALTH_QUOTE_CREATED, [quoteId: session[IConstant.HEALTH_QUOTE_ID], country: params.country]

            // redirect(mapping: 'healthQuotes',
            //     id: AESCryption.encrypt(session[IConstant.HEALTH_QUOTE_ID].toString()), params: [lang: params.lang, country: params.country, applicationType: healthQuote.applicationType.toString()])
           redirect mapping: 'preHealthQuotes', id: AESCryption.encrypt(healthQuote.id.toString()), base: sessionService.getBaseUrl(),
                    params: [lang: params.lang, country: params.country]
            return
        }


        def qs = healthQuoteService.getQuestions(healthQuote.id)

        render view: "/health/declaration", model: [healthQuote: healthQuote, questions: qs, lang: params.lang, country: params.country]
    }

    def faq() {
        render view: "/health/faq", model: [lang: params.lang, country: params.country]
    }

    def quotes(String id) {
        Long quoteId
        HealthQuote healthQuote

        try {
            if (id) {
                quoteId = Long.parseLong(AESCryption.decrypt(id))
                healthQuote = HealthQuote.read(quoteId)
            } else {
                healthQuote = HealthQuote.read(session[IConstant.HEALTH_QUOTE_ID])
            }
        } catch (e) {
            log.error "Unable to decrypt health quote id ${id}"
        }

        if (!healthQuote) {
            log.info "health.quotes - Quote not found, quoteId:${quoteId}"
            render status: 404
            return
        }

        session[IConstant.HEALTH_QUOTE_ID] = quoteId
        session[IConstant.HEALTH_INSURANCE_NAME] = healthQuote.name
        session[IConstant.HEALTH_INSURANCE_MOBILE] = healthQuote.mobile

        if (healthQuote.isNotProcessed()) {

            /*List<HealthMember> members = HealthMember.findAllByQuote(healthQuote)
            if (members.any { !it.dob }) {
                log.info("healthController.quotes - DOB not found for members in health quote ${healthQuote.id}, redirecting to details action")
                redirect action: "details"
                return
            }*/

            List<HealthRateCommand> quotes = []

            if (validateQuoteDetail(healthQuote)) {
                quotes = healthQuoteService.getRatings(healthQuote)
            }

            //healthQuoteService.saveAllQuotes(quotes,healthQuote.id)

            //adding sorting by premium
            if (quotes) {
                if (healthQuote.isEbp != null) {

                    if (healthQuote.isEbp) {
                        // if customer wants to view EBP products first, then sort by EBP first, then by premium
                        quotes.sort { q1, q2 ->
                            if (q1.providerId.toInteger() == InsuranceProviderEnum.ORIENT.id) {
                                return -1
                            } else if (q2.providerId.toInteger() == InsuranceProviderEnum.ORIENT.id) {
                                return 1
                            } else {
                                (!q1.isEbp) <=> (!q2.isEbp) ?: q1.premium <=> q2.premium
                            }
                        }
                    } else {
                        quotes.sort { q1, q2 -> (q1.isEbp) <=> (q2.isEbp) ?: q1.premium <=> q2.premium }
                    }
                } else {
                    quotes.sort { it.premium }
                }
            }

            render view: '/health/quotes', model: [quotes: quotes, healthQuote: healthQuote, lang: params.lang, country: params.country]
            return
        } else {
            log.info ".quotes HealthQuote not found or already processed: ${id}"
        }

        redirect mapping: 'healthIndex', params: [lang: params.lang, country: params.country]

    }

    def noQuotes() {
        render view: "/health/noQuotes", model: [lang: params.lang, country: params.country]
    }

    def quoteDetails() {

        Long quoteId = params.long('healthQuote')
        log.info "health.quoteDetails - entering with healthQuoteId ${quoteId}"
        HealthQuote healthQuote = HealthQuote.read(quoteId)
        if (healthQuote) {
            HealthQuoteCommand command =  healthQuoteService.toHealthQuoteCommand(healthQuote)
            command.deductibleId = params.int('deductible')
            command.productId = params.int('product')
            command.networkId = params.int('network')

            HealthRateCommand rateCommand = healthQuoteService.getRating(command)

            def currency = utilService.getCountry().currency
            def city = City.read(command.cityId).name

            render view: '/health/quoteDetails', model: [quoteId: healthQuote.id, quote: rateCommand, currency: currency, command: command, city: city, healthQuote: healthQuote, lang: params.lang, country: params.country]
        } else {
            redirect mapping: 'healthIndex', params: [lang: params.lang, country: params.country]
        }
    }


    private boolean validateQuoteDetail(HealthQuote healthQuote) {

        boolean isValid = true

        def healthMembers = healthQuote.healthMembers

        healthMembers.each { HealthMember member ->
            if (member.answers == null) {
                log.info("Health Member has no health declaration -> quoteId: ${healthQuote.id}, memberId: ${member.id}")
                isValid = false
            }
            if (member.nationality == null) {
                log.info("Health Member has no nationality -> quoteId: ${healthQuote.id}, memberId: ${member.id}")
                isValid = false
            }

            if (!healthQuote.applicationType) {
                log.info("Health Member has no applicationType -> quoteId: ${healthQuote.id}, memberId: ${member.id}")
                isValid = false
            }

            if (!member.dob) {
                log.info("Health Member has no DOB -> quoteId: ${healthQuote.id}, memberId: ${member.id}")
                isValid = false
            }
        }

        return isValid
    }

    def preHealthQuotes(String country, String lang, String id) {
        log.info("funnel.preHealthQuotes - entering with [country:$country, lang:$lang, id:$id]")

        String quotesLink = g.createLink(mapping: 'healthQuotes', base: sessionService.getBaseUrl(), params:
            [country: country, id: id, lang: lang])

        Long quoteId
        HealthQuote healthQuote

        try {
            if (id) {
                quoteId = Long.parseLong(AESCryption.decrypt(id))
                healthQuote = HealthQuote.read(quoteId)
            } else {
                healthQuote = HealthQuote.read(session[IConstant.HEALTH_QUOTE_ID])
            }
        } catch (e) {
            log.error "Unable to decrypt health quote id ${id}"
        }

        if (!healthQuote) {
            log.info "health.preQuotes - Quote not found, quoteId:${quoteId}"
            render status: 404
            return
        }

        render view: '/health/preQuotes', model: [quotesLink: quotesLink, quote: healthQuote]
    }

    def cignaCampaign(){

        HealthQuote healthQuote = HealthQuote.findById(session[IConstant.HEALTH_QUOTE_ID])

        CignaCampaign cignaCampaign = new CignaCampaign()
        cignaCampaign.dateCreated = LocalDateTime.now()
        cignaCampaign.healthQuote = healthQuote

        cignaCampaign.save()

        redirect(url: "https://ghb.cigna.com/quiz-comparison")
        return

    }
}
