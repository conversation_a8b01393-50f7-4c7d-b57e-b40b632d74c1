package com.cover.health

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.health.commands.HealthCheckoutCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverageTypeEnum
import com.safeguard.DocumentTypeEnum
import com.safeguard.DonationTypeEnum
import com.safeguard.InsuranceProviderEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.Donation
import com.safeguard.health.HealthMember
import com.safeguard.health.HealthQuote
import com.safeguard.payment.PaymentMethod
import com.safeguard.payment.ProviderPaymentMethod
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import com.safeguard.whitelabel.WhiteLabelDomain
import org.springframework.context.i18n.LocaleContextHolder

/**
 * Created by Minhaj on 3/6/17.
 */
class CheckoutController {

    static namespace = "health"
    static allowedMethods = [index:'POST']

    def utilService
    def healthQuoteService
    def checkoutService
    def paymentService
    def paymentMethodService
    def paymentMethodSgService
    def commonPolicySgService

    /**
     * Checkout process index page
     * Step 1
     */
    def index(HealthCheckoutCommand command) {
        log.info "checkoutHealth.index - entring with quote id ${session[IConstant.HEALTH_QUOTE_ID]}"
        HealthQuote quote = command.healthQuote

        if (quote.isNotProcessed()) {
            if (command.validate()) {
                def (HealthQuote healthQuote, HealthRateCommand rateCommand) = healthQuoteService.updateHealthQuoteRating(command, params)

                // Update donation information
                def donationAmount = params.donationBox
                commonPolicySgService.updateDonation(quote, ProductTypeEnum.HEALTH, donationAmount, DonationTypeEnum.CHARITY)

                session[IConstant.HEALTH_QUOTE_ID] = command.healthQuote.id

                render view: '/health/checkout/index', model: [healthCheckoutCommand: command, command: rateCommand, lang: LocaleContextHolder.locale.language, country: session[IConstant.SITE_COUNTRY]]
                return
            } else {
                flash.error = "Something went wrong, please try again"
            }
        }
        redirect mapping: 'healthIndex', params: [country: params.country, lang: params.lang]
    }

    /**
     * Step 2 - payment page
     * @param command
     * @return
     */
    def updateOrder(HealthCheckoutCommand command) {
        log.info("healthCheckout.updateOrder - command:${command} - params:${params}")

        HealthQuote quote = command.healthQuote

        if (!quote.isNotProcessed()) {
            flash.error = g.message(code: 'error.alreadyProcessed')
            redirect mapping:"healthIndex", params: [lang: params.lang, country: params.country]
            return
        }

        session[IConstant.HEALTH_QUOTE_ID] = command.healthQuote.id

        healthQuoteService.updateHealthQuoteRating(command, params, PaymentStatusEnum.PENDING, true)

        // Update donation information
        def donationAmount = params.donationBox
        commonPolicySgService.updateDonation(quote, ProductTypeEnum.HEALTH, donationAmount, DonationTypeEnum.CHARITY)

        session[IConstant.HEALTH_QUOTE_ID] = command.healthQuote.id

        redirect mapping: 'healthCheckoutPaynow', params: [country: params.country, lang: params.lang]

    }

    def paynow() {
        log.info "checkoutHealth.paynow - entring with quote id ${session[IConstant.HEALTH_QUOTE_ID]}"
        HealthQuote healthQuote = HealthQuote.read(session[IConstant.HEALTH_QUOTE_ID])

        if (healthQuote) {
            if (!healthQuoteService.isEligibleForPayment(healthQuote)) {
                flash.error = g.message(code:'checkout.quote.modified')
                redirect mapping: 'healthIndex', params: [country: params.country, lang: params.lang]
                return
            }

            String encQuoteId = AESCryption.encrypt(healthQuote.id + "")

            String domain = grailsApplication.config.getProperty("cover.domain")
            String country = params.country
            String lang = params.lang

            String quoteVersion = healthQuote.version.toString()

            checkoutService.clearQuoteWithFromPaymentDetail(healthQuote, ProductTypeEnum.HEALTH)

            def creditCardParams, installmentParams
            boolean showMerchantPage = true
            String payfortReturnUrl = "$domain/$country/$lang/${ProductTypeEnum.HEALTH.toString().toLowerCase()}/checkout/payment"

            ProviderPaymentMethod providerPaymentMethod =
                paymentMethodSgService.getProviderPaymentMethod(healthQuote.product, healthQuote.productType)

            if (providerPaymentMethod) {
                log.info("providerPaymentMethod found :${providerPaymentMethod}")
                if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.NOT_AVAILABLE ||
                    (healthQuote.product.providerId.toInteger() in [InsuranceProviderEnum.NOOR_TAKAFUL.id, InsuranceProviderEnum.ORIENT.id] &&
                        healthQuote.product.typeId.toInteger() == CoverageTypeEnum.HEALTH_EBP.value() &&
                        (!healthQuote.salaryOver4k || HealthMember.countByQuoteAndSalaryOver4k(healthQuote, false)))) {
                    notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: healthQuote.id,
                                                                              productId: healthQuote.product.id]

                    paymentService.addPaymentIntent(ProductTypeEnum.HEALTH, healthQuote.id, healthQuote.product.id)

                    redirect mapping: 'healthPreparePaymentPage', params: [country: params.country, lang: params.lang]
                    return
                } else if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.TAP_PAYMENT) {
                    log.info("TAP payment gateway found for provider - including both TAP credit card and Tabby payment options")

                    // Get all TAP_PAYMENT gateway payment methods (both CREDITCARD and TABBY)
                    List<PaymentMethod> tapPaymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountryAndPaymentGateway(
                        true, ProductTypeEnum.HEALTH, Country.load(CountryEnum.findCountryByCode(country).id), PaymentGatewayEnum.TAP_PAYMENT)

                    if (tapPaymentMethods && tapPaymentMethods.size() > 0) {
                        log.info("Found ${tapPaymentMethods.size()} TAP payment methods: ${tapPaymentMethods.collect { it.name }}")

                        render view: "/health/checkout/paymentMethods",
                            model: [paymentMethods   : tapPaymentMethods, healthQuote: healthQuote,
                                    tapMerchantId: "${providerPaymentMethod.merchantId}",
                                    quoteVersion     : quoteVersion, creditCardParams: [:],
                                    installmentParams: [:], country: country,
                                    lang             : lang, showMerchantPage: true
                            ]
                        return
                        /*
                        //For redirection
                        def createChargeResp = tapPaymentService.initializeCharge(quote, params.tapPaymentCardToken)

                        if (createChargeResp.threeDSecure) {
                            render view: '/car/checkout/_redirect', model: [url           : createChargeResp.transaction.url,
                                                                            quote         : quote, country: params.country, lang: params.lang,
                                                                            conversionRate: configurationService.getValue('etisalatSmiles.api.conversionRate')
                            ]
                            return
                        }*/
                    }
                }
            }

            log.info("no provider specific method found, falling back to Checkout.com")
            List<PaymentMethod> paymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountry(true,
                ProductTypeEnum.HEALTH, Country.load(CountryEnum.findCountryByCode(country).id))

            //Fall back to Prepare Payment Page when no payment method found
            if (paymentMethods.size() == 0) {
                notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: healthQuote.id,
                                                                          productId: healthQuote.product.id]

                paymentService.addPaymentIntent(ProductTypeEnum.HEALTH, healthQuote.id, healthQuote.product.id)

                redirect mapping: 'healthPreparePaymentPage', params: [country: params.country, lang: params.lang]
                return
            }

            //Is Credit Card Payment Method Available?
            PaymentMethod creditCardPaymentMethod = paymentMethods.find { it.name == PaymentMethodEnum.CREDITCARD.toString() }

            if (creditCardPaymentMethod) {
                String checkoutSuccessUrl = "${domain}/payments/response/checkoutPsp/health/${encQuoteId}/success"
                String checkoutFailureUrl = "${domain}/payments/response/checkoutPsp/health/${encQuoteId}/failure"
                String checkoutCancelUrl = "${domain}/payments/response/checkoutPsp/health/${encQuoteId}/cancel"

                (creditCardParams, installmentParams, showMerchantPage) =
                    paymentMethodService.getCreditCardPaymentParameters(creditCardPaymentMethod.paymentGateway, healthQuote,
                        healthQuote.user, country, utilService.getClientIp(request),
                        payfortReturnUrl, checkoutSuccessUrl, checkoutFailureUrl, checkoutCancelUrl, null)
            }

            HealthQuoteCommand command =  healthQuoteService.toHealthQuoteCommand(healthQuote)
            HealthRateCommand rateCommand = healthQuoteService.getRating(command)

            render view: "/health/checkout/paymentMethods",
                model: [paymentMethods: paymentMethods,
                        healthQuote: healthQuote,
                        creditCardParams: creditCardParams,
                        installmentParams: installmentParams,
                        showMerchantPage: showMerchantPage,
                        quoteVersion: quoteVersion, rateCommand: rateCommand, lang: LocaleContextHolder.locale.language,
                        country: session[IConstant.SITE_COUNTRY]]

        } else {
            log.warn "health.checkout.paynow health quote not found with id ${session[IConstant.HEALTH_QUOTE_ID]}"
            redirect mapping: 'healthIndex', params:[lang: utilService.getLanguage(), country: CountryEnum.UAE.code]
        }
    }

    /**
     * Payfort redirect to this action upon credit card form submission.
     */
    def payment() {
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        if (isSecured) {

            if (PayfortStatusEnum.INVALID_REQUEST.toString().equals(params.status)) {
                flash.error = params.response_message
                redirect mapping: 'healthCheckoutPaynow', params: [country: params.country, lang: params.lang]
                return
            }

            try {
                HealthQuote healthQuote = HealthQuote.findById(HealthQuote.decodeMerchantRef(params.merchant_reference))

                if (healthQuote.isModified(params.p_quote_v)) {
                    flash.error = g.message(code:'checkout.quote.modified')
                    log.error("health.checkout.payment - quote was modified:${healthQuote.id} ")
                    redirect mapping: 'healthIndex', params: [country: params.country, lang: params.lang]
                    return
                }

                PayfortResponse payfortResponse = paymentService.process(params, PayfortCommandEnum.PURCHASE,
                    utilService.getClientIp(request))

                session[IConstant.HEALTH_QUOTE_ID] = healthQuote.id

                //This is important so that we know incase of failed transaction it was installments
                if (payfortResponse.isInstallments) {
                    paymentService.setInstallments(healthQuote, payfortResponse.numberOfInstallments)
                }

                if (payfortResponse.isThreeDeeSecure) {

                    render view: '/car/checkout/_redirect', model: [url:payfortResponse.threeDeeSecureUrl,
                                                                       quote:healthQuote, lang: LocaleContextHolder.locale.language, country: session[IConstant.SITE_COUNTRY]]
                    return
                } else if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(payfortResponse.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(payfortResponse.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT
                    paymentService.paid(params, customParams)

                    log.debug(".health.checkout.payment Redirect user to thankyou for merchantRef: ${params.merchant_reference}")
                    redirect mapping: 'healthCheckoutThankyou', params: [country: params.country, lang: params.lang]
                    return
                }
                //pushover service sending sms in-case error to dev team
                String failureMessage = payfortResponse.responseMessage  +
                    (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                notify AsyncEventConstants.HEALTH_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage,
                                                                              quoteId: healthQuote.id]

                flash.error = payfortResponse.responseMessage

            } catch (Exception exp) {
                log.error("Exception:", exp)
                flash.error = g.message(code:'checkout.general.error')
            }
        } else {
            log.error(".health.checkout.payment **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code:'checkout.general.error')
        }

        redirect mapping: 'healthCheckoutPaynow', params: [country: params.country, lang: params.lang]
    }

    /**
     * All offline payment method submit to this action.
     * Note: Currently we support COD only. 21.Jun.2016
     */
    def order() {
        String method = params.paymentMethod
        def healthQuoteId = params.quoteId

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.findPaymentMethod(method)

        if (!paymentMethodEnum) {
            log.warn("Invalid paymentmethod passed: ${params.paymentMethod}")
            flash.message = g.message(code:"default.general.error")
            redirect mapping: 'healthIndex', params:[lang: utilService.getLanguage(), country: CountryEnum.UAE.code]
            return
        }

        HealthQuote healthQuote = HealthQuote.read(healthQuoteId)

        if (!healthQuote.isNotProcessed()) {
            log.warn(".checkout.order quote is processed -> ${healthQuote?.id}")
            flash.message = g.message(code:"default.general.error")
            redirect mapping:"healthCheckoutIndex", params: [country: params.country, lang: params.lang]
            return
        }

        if (healthQuote.isModified(params.p_quote_v)) {
            flash.error = g.message(code:'checkout.quote.modified')
            log.error("health.checkout.order - quote was modified:${healthQuote.id} ")
            redirect mapping: 'healthIndex', params: [country: params.country, lang: params.lang]
            return
        }

        checkoutService.offlinePayment(healthQuote, paymentMethodEnum)

        flash.healthQuoteId = healthQuoteId

        redirect mapping: 'healthCheckoutThankyou', params: [country: params.country, lang: params.lang]
    }

    /**
     * 3d Secure returns to this action
     */
    def success() {
        log.info ".health.checkout.controller.success ${params}"
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        def healthQuoteId = HealthQuote.decodeMerchantRef(params.merchant_reference)

        HealthQuote quote = HealthQuote.read(healthQuoteId)

        flash.healthQuoteId = quote.id

        if (isSecured) {
            if (quote && quote.isNotProcessed()) {
                paymentService.savePaymentResponse(quote, params)

                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)

                    log.debug(".health.checkout.success Redirect user to thankyou for quoteId: ${quote.id}")
                    //All good? redirect user to thankyou page
                    redirect mapping: 'healthCheckoutThankyou', params: [country: params.country, lang: params.lang]
                    return
                } else {
                    //pushover service sending sms in-case error to dev team
                    String failureMessage = params.response_message +
                        (params.acquirer_response_message ? " - " + params.acquirer_response_message : "")
                    notify AsyncEventConstants.HEALTH_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage, quoteId: quote.id]
                    session[IConstant.HEALTH_QUOTE_ID] = quote.id
                    flash.error = params.response_message
                    log.error "#### HEALTH ERROR #### -> ${params.response_message} for ${quote.id} with status-> ${params.status} #### ERROR ####"
                }
            } else {
                log.warn(".health.checkout.success quote is isProcessed -> ${quote?.id}")
                log.debug(".health.checkout.success.is.processed still redirecting to thankyou")
                redirect mapping: 'healthCheckoutThankyou', params: [country: params.country, lang: params.lang]
                return
            }

        } else {
            log.error(".honme.checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code:'checkout.general.error')
            redirect mapping: 'healthCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        redirect mapping: 'healthCheckoutPaynow', params: [country: params.country, lang: params.lang]
    }

    /**
     * Payment success action
     */
    def thankyou () {

        def healthQuoteId = session[IConstant.HEALTH_QUOTE_ID]

        log.debug(".checkout.thankyou entering with healthQuoteId: ${healthQuoteId}")

        //if params are null redirect to healthInsurance page
        if (!healthQuoteId) {
            redirect mapping: 'healthCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        HealthQuote healthQuote = HealthQuote.get(healthQuoteId)
        String hash = AESCryption.encrypt(healthQuote.id.toString())

        //Quote is not processed yet, redirect back to quotes srp page
        if (healthQuote.isNotProcessed()) {
            CountryEnum country = CountryEnum.findCountry(params.country) ?: CountryEnum.UAE

            redirect mapping: 'healthQuotes', id: hash,
                params: [lang: utilService.getLanguage(), country: country.code]
            return
        }

        log.debug(".health.checkout.thankyou HealthQuote found with id ${healthQuote.id}")

        notify AsyncEventConstants.HEALTH_QUOTE_PURCHASED, [quoteId: healthQuote.id]

        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain
        List attributeNames = session.attributeNames.toList()
        attributeNames.each { String attribute ->
            session.removeAttribute(attribute)
        }
        session[IConstant.WHITE_LABEL_DOMAIN] = whiteLabelDomain

        def newSession = request.getSession()
        LocaleContextHolder.setLocale(new Locale(params.lang))
        newSession.'org.springframework.web.servlet.i18n.SessionLocaleResolver.LOCALE' = LocaleContextHolder.locale
        newSession[IConstant.SITE_COUNTRY] = CountryEnum.findCountry(params.country)
        newSession[IConstant.HEALTH_QUOTE_ID] = healthQuote.id
        newSession[IConstant.HEALTH_INSURANCE_NAME] = healthQuote.name
        newSession[IConstant.HEALTH_INSURANCE_MOBILE] = healthQuote.mobile

        render view: '/health/checkout/thankyou', model:[healthQuote: healthQuote, session: newSession, hash: hash,
                                                         lang: LocaleContextHolder.locale.language,
                                                         country: newSession[IConstant.SITE_COUNTRY]]
    }

    /**
     * Show Prepare Payment Page
     * @return
     */
    def preparePaymentPage() {
        log.info(".preparePaymentPage - params:$params")

        def quoteId = flash.quoteId

        if (!quoteId) {
            //Try looking into quote parameter
            quoteId = session[IConstant.HEALTH_QUOTE_ID]
        }

        if (quoteId && quoteId instanceof String) {
            quoteId = Long.parseLong(quoteId)
        }

        log.debug(".checkout.preparePaymentPage entering with quoteId: ${quoteId}")

        //if params are null redirect to homeInsurance page
        if (!quoteId) {
            CountryEnum country = CountryEnum.findCountry(params.country) ?: CountryEnum.UAE
            redirect mapping: 'healthIndex', params: [lang: utilService.getLanguage(), country: country.code]
            return
        }

        HealthQuote quote = HealthQuote.get(quoteId)

        String hash = AESCryption.encrypt(quote.id.toString())

        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain

        render view: '/payment/preparePayment', model: [
            quote               : quote,
            lang                   : LocaleContextHolder.locale.language,
            hash                   : hash,
            whitelabel             : whiteLabelDomain.brand
        ]
    }
}
