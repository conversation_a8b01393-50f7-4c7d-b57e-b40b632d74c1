package com.cover.health

import com.cover.common.commands.MarketingTrackingCommand
import com.cover.health.commands.HealthRateCommand
import com.cover.health.groupHealth.commands.GroupHealthHomeCommand
import com.cover.health.groupHealth.commands.GroupHealthQuoteCommand
import com.cover.util.IConstant
import com.safeguard.City
import com.safeguard.Provider
import com.safeguard.health.HealthQuote
import com.safeguard.util.AESCryption

class GroupHealthController {

    static namespace = "health"

    def groupHealthService

    def index(GroupHealthHomeCommand groupHealthHomeCommand) {

        if (request.method == "POST" && !groupHealthHomeCommand.hasErrors()) {

            session[IConstant.NUMBER_OF_EMPLOYEES] = groupHealthHomeCommand.numberOfEmployees
            session[IConstant.COMPANY_NAME] = groupHealthHomeCommand.companyName
            session[IConstant.CITY_ID] = groupHealthHomeCommand.city.id

            redirect mapping: "groupDetails", params: [country: params.country, lang: params.lang]
            return
        }

        render view: "/health/groupHealth/index", model: [country: params.country]
    }

    def details(GroupHealthQuoteCommand groupHealthQuoteCommand) {

        Boolean noParamsInSession = [IConstant.NUMBER_OF_EMPLOYEES, IConstant.COMPANY_NAME, IConstant.CITY_ID].any { String key ->
            !session[key]
        }

        if (noParamsInSession) {
            redirect mapping: "groupIndex", params: [country: params.country, lang: params.lang]
            return
        }

        groupHealthQuoteCommand.numberOfEmployees = session[IConstant.NUMBER_OF_EMPLOYEES]
        groupHealthQuoteCommand.companyName = session[IConstant.COMPANY_NAME]
        groupHealthQuoteCommand.city = City.read(session[IConstant.CITY_ID])

        if (request.method == "POST" && groupHealthQuoteCommand.validate()) {

            MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
            marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
            marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
            marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
            marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
            marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
            marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
            groupHealthQuoteCommand.marketingTracking = marketingTracking

            HealthQuote healthQuote = groupHealthService.createHealthQuote(groupHealthQuoteCommand)
            String encryptedId = AESCryption.encrypt(healthQuote.id.toString())
            redirect mapping: "groupQuotes", params: [country: groupHealthQuoteCommand.country, lang: groupHealthQuoteCommand.lang, id: encryptedId]
            return
        }

        render view: '/health/groupHealth/details', model: [groupHealthQuoteCommand: groupHealthQuoteCommand]
    }

    def quotes(String id) {

        if (!id) {
            redirect mapping: "groupIndex", params: [country: params.country, lang: params.lang]
            return
        }

        Long healthQuoteId = AESCryption.decrypt(id).toLong()
        HealthQuote healthQuote = HealthQuote.read(healthQuoteId)

        if (!healthQuote) {
            redirect mapping: "groupIndex", params: [country: params.country, lang: params.lang]
            return
        }

        List<HealthRateCommand> healthRateCommandList = groupHealthService.quotes()
        List<Provider> providers = healthRateCommandList.collect{ HealthRateCommand healthRateCommand ->
            [
                id      :   healthRateCommand.providerId,
                name    :   healthRateCommand.providerNameEn
            ]
        }.unique {
            it.id
        }

       render view: '/health/groupHealth/quotes', model: [quotes: healthRateCommandList, healthQuote: healthQuote, providers: providers]

    }
}
