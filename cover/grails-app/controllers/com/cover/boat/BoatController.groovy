package com.cover.boat

import com.safeguard.*
import com.safeguard.boat.*
import com.cover.boat.BoatQuoteService

class BoatController {
    static namespace = 'boat'
    def boatQuoteService

    def index() {
        if(params.country == 'uae') {
            if(request.method == 'POST') {
                boatQuoteService.saveQuote(params)
                redirect mapping: "boatThankyou", params: [lang: params.lang, country: params.country]
                return
            }else {
                render view: '/boat/index', model: [lang: params.lang, country: params.country]
                return
            }
        } else {
            render view: '/common/notFound'
        }
    }

    def thankyou() {
        if(params.country == 'uae') {
            render view: '/boat/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render view: '/common/notFound'
            return
        }
    }

}
