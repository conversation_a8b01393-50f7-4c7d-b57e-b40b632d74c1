package com.cover.independent

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.ProductType
import com.safeguard.ProductTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.springframework.http.HttpStatus

class CheckoutController {

    static namespace = "independent"

    static allowedMethods = [
        paynow         : ['GET'],
        discountAmount : ['POST'],
        codOrder       : ['POST'],
        paymentSummary : ['GET'],
        installmentPlans : ['GET']
    ]

    def apiQuoteService
    def checkoutService
    def commonUtilService
    def paymentService
    def utilService
    def discountService

    def paynow() {
        log.info("independent.checkout.paynow - entering with [id:${params.id}, insuranceType:${params.insuranceType}]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(params.id))
        log.info("independent.checkout.paynow - quoteId:${quoteId}")
        String insuranceType = params.insuranceType

        if (quoteId) {
            def quote = apiQuoteService.getQuoteById(quoteId, insuranceType)

            if (!quote) {
                log.info "independent.checkout.paynow - [quoteId:${quoteId}] not found"

                String message = "Quote not found"

                redirect mapping: 'ICheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message, returnUrl:params.returnUrl]
                return
            }

            if (!quote.isNotProcessed()) {
                log.warn("independent.checkout.paynow - quote is processed -> ${quote.id}, instanceof ${quote.class}")

                String message = "Quote already processed"

                redirect mapping: 'iCheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message, returnUrl:params.returnUrl ]

                return
            }

            if (!quote.productId) {
                log.warn("independent.checkout.paynow - no product selected -> ${quote.id}, instanceof ${quote.class}")

                String message = "No Product Selected"

                redirect mapping: 'iCheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message, returnUrl:params.returnUrl]

                return
            }


            def domain = grailsApplication.config.getProperty("cover.domain")
            def country = params.country
            def lang = params.lang


            def model = [id:params.id, quoteId: quoteId, country: country, lang: lang,
                         insuranceType: insuranceType.capitalize()]

            render view: "/icheckout/paynow", model: model

        }
    }

    /**
     * Error page handler
     *
     * @return
     */
    def error() {
        log.info("independent.checkout.error - entering with [params:${params}]")

        render view:"/icheckout/error", model:[error:params.error, message:params.message]
        return
    }

}
