package com.cover.gap
import com.safeguard.*
import com.safeguard.gap.*
import com.cover.gap.GapController


class GapController {
    static namespace = 'gap'
    def gapQuoteService

    def index() {
        if(params.country == 'uae') {
            if(request.method == 'POST') {
                gapQuoteService.saveQuote(params)
                redirect mapping: "gapThankyou", params: [lang: params.lang, country: params.country]
                return
            }else {
                render view: '/gap/index', model: [lang: params.lang, country: params.country]
                return
            }
        } else {
            render (status: 404, view: '/common/notFound')
        }
    }

    def thankyou() {
        if(params.country == 'uae') {
            render view: '/gap/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render (status: 404, view: '/common/notFound')
            return
        }
    }

}
