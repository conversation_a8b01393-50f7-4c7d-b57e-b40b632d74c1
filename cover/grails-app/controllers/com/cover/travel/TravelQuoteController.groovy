package com.cover.travel

import com.cover.util.IConstant
import com.safeguard.CountryEnum
import com.safeguard.travel.*
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.web.RequestParameter
import org.apache.commons.codec.DecoderException
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter

import java.util.stream.Collectors

class TravelQuoteController {

    static namespace = 'travel'

    def travelQuoteSgService
    def messageSource

    def index() {
        render view: '/travel/index', model: [country: params.country]
    }

    def funnel() {
        render view: '/travel/funnel', model: [country: params.country, lang: params.lang]
    }

    def renderQuoteDetailsPage(@RequestParameter("id") String encryptedQuoteId) {

        TravelQuote quote = null
        if (encryptedQuoteId) {
            try {
                long quoteId = AESCryption.decrypt(encryptedQuoteId).toInteger()
                quote = TravelQuote.read(quoteId)
            } catch (DecoderException e) {
                throw new RuntimeException("Failed to decode encrypted id(value = '$encryptedQuoteId') of travel quote.", e)
            }
        }

        render view: '/travel/quotes', model: [country: params.country, quote: quote]
    }

    def getQuoteFromApi(GetQuoteFromApiCommand cmd) {
        if (cmd.hasErrors()) {
            throw new RuntimeException("com.cover.travel.TravelQuoteController#getQuoteFromApi has been called with " +
                "invalid request parameters. Errors: ${cmd.concatenateAllErrors(cmd.errors)}")
        }

        Map<TravelerAgeGroupEnum, Integer> ageGroupToTravelersQuantityMap =
            TravelUtils.getAgeGroupToTravelersQuantityMap(cmd.travelersBirthDatesList, cmd.startDate)

        // Save Travel UTM in sessions
        session[IConstant.INSURANCE_QUERY_STRING] = cmd.queryString
        session[IConstant.INSURANCE_UTM_SOURCE] = cmd.utmSource
        session[IConstant.INSURANCE_UTM_MEDIUM] = cmd.utmMedium
        session[IConstant.INSURANCE_UTM_CAMPAIGN] = cmd.utmCampaign
        session[IConstant.INSURANCE_GCLID] = cmd.gclid
        session[IConstant.INSURANCE_FBCLID] = cmd.fbclid

        TravelDetails travelDetails = new TravelDetails(
            startDate: cmd.startDate,
            endDate: cmd.endDate,
            sourceCountry: CountryEnum.findCountryByIsoAlpha2Code(cmd.sourceCountryCode),
            destinationCountry: TravelUtils.getAnyCountryInZone(TravelDestinationCountryZoneEnum.getById(cmd.destinationCountryZoneId)),
            travelersRelationshipType: TravelersRelationshipTypeEnum.getById(cmd.travelersRelationshipTypeId),
            childTravelersCount: ageGroupToTravelersQuantityMap.get(TravelerAgeGroupEnum.CHILD),
            adultTravelersCount: ageGroupToTravelersQuantityMap.get(TravelerAgeGroupEnum.ADULT),
            seniorTravelersCount: ageGroupToTravelersQuantityMap.get(TravelerAgeGroupEnum.SENIOR),
            travelersBirthDatesList: cmd.travelersBirthDatesList,
            lang: params.lang ?: "en",
            queryString: cmd.queryString,
            utmSource: cmd.utmSource,
            utmMedium: cmd.utmMedium,
            utmCampaign: cmd.utmCampaign,
            gclid: cmd.gclid,
            fbclid: cmd.fbclid
        )

        TravelApiQuotationQueryResult queryResult =
            travelQuoteSgService.getAndSaveQuoteFromApi(travelDetails, cmd.userName, cmd.userEmail, cmd.userMobile, session[IConstant.INSURANCE_QUERY_STRING] as String, session[IConstant.INSURANCE_UTM_SOURCE] as String, session[IConstant.INSURANCE_UTM_MEDIUM] as String, session[IConstant.INSURANCE_UTM_CAMPAIGN] as String, session[IConstant.INSURANCE_GCLID] as String, session[IConstant.INSURANCE_FBCLID] as String, cmd.agreeToPrivacyPolicy)

        TravelQuote quote = queryResult.quote
        TravelApiQueryResultStatusEnum resultStatus = queryResult.status

        /** IMPORTANT! It is mandatory to check the result status here. For more info check return type javadoc for {@link TravelQuoteSgService#getAndSaveQuoteFromApi} */
        if (resultStatus != TravelApiQueryResultStatusEnum.SUCCESS || !travelQuoteSgService.validateQuoteStrippingOffInvalidData(quote)) {
            render([offerList: []] as JSON)
        } else {
            DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd")

            render([
                id  : AESCryption.encrypt(quote.id.toString()),
                startDate: quote.startDate.toString(),
                endDate: quote.endDate.toString(),
                sourceCountryCode: quote.sourceCountry.code,
                destinationCountryZoneId: quote.destinationCountry.id,
                travelersRelationshipTypeId: quote.travelersRelationshipTypeId,
                offerList: quote.offerList.collect({ TravelOffer offer ->
                    [
                        id          : offer.id,
                        price       : offer.price,
                        currency    : offer.currency,
                        name        : offer.name,
                        coverageList: offer.coverageList.collect({ TravelCoverage coverage ->
                            [
                                title: coverage.title,
                                value: coverage.value,
                                content: coverage.content
                            ]
                        }),
                        tacUrl      : offer.tacUrl
                    ]
                }),
                travelerList: quote.travelerList
                    .stream()
                    .map { Traveler traveler ->
                    [id: traveler.id, birthDate: fmt.print(traveler.birthDate)]
                }.collect(Collectors.toList())
            ] as JSON)
        }
    }

    def getQuoteFromApiBySavedQuoteInfo(@RequestParameter("id") String encryptedQuoteId) {
        TravelQuote quote = null
        if (encryptedQuoteId) {
            try {
                long quoteId = AESCryption.decrypt(encryptedQuoteId).toInteger()
                quote = TravelQuote.read(quoteId)
            } catch (DecoderException e) {
                throw new RuntimeException("Failed to decode encrypted id(value = '$encryptedQuoteId') of travel quote.", e)
            }
        }

        TravelDetails travelDetails = new TravelDetails(
            startDate: quote.startDate,
            endDate: quote.endDate,
            sourceCountry: CountryEnum.findCountryById(quote.sourceCountry.id),
            destinationCountry: CountryEnum.findCountryById(quote.destinationCountry.id),
            travelersRelationshipType: TravelersRelationshipTypeEnum.getById(quote.travelersRelationshipTypeId),
            childTravelersCount: quote.childTravelersCount,
            adultTravelersCount: quote.adultTravelersCount,
            seniorTravelersCount: quote.seniorTravelersCount,
            travelersBirthDatesList: quote.getTravelerList()*.birthDate,
            lang: params.lang ?: "en"
        )

        TravelApiQuotationQueryResult queryResult =
            travelQuoteSgService.getAndSaveQuoteFromApi(travelDetails, quote.user.name, quote.user.email, quote.user.mobile, session[IConstant.INSURANCE_QUERY_STRING] as String, quote.agreeToPrivacyPolicy)

        if (queryResult.status != TravelApiQueryResultStatusEnum.SUCCESS ||
            !travelQuoteSgService.validateQuoteStrippingOffInvalidData(queryResult.quote)) {

            render([offerList: []] as JSON)

        } else {
            DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd")

            render([
                id: AESCryption.encrypt(queryResult.quote.id.toString()),
                startDate: travelDetails.startDate.toString(),
                endDate: travelDetails.endDate.toString(),
                sourceCountryCode: travelDetails.sourceCountry.code,
                destinationCountryZoneId: travelDetails.destinationCountry.id,
                travelersRelationshipTypeId: travelDetails.travelersRelationshipType.id,
                offerList: queryResult.quote.offerList.collect({ TravelOffer offer ->
                    [
                        id          : offer.id,
                        price       : offer.price,
                        currency    : offer.currency,
                        name        : offer.name,
                        coverageList: offer.coverageList.collect({ TravelCoverage coverage ->
                            [
                                title: coverage.title,
                                value: coverage.value,
                                content: coverage.content
                            ]
                        }),
                        tacUrl      : offer.tacUrl
                    ]
                }),
                travelerList: queryResult.quote.travelerList
                    .stream()
                    .map { Traveler traveler ->
                    [id: traveler.id, birthDate: fmt.print(traveler.birthDate)]
                }.collect(Collectors.toList())
            ] as JSON)
        }
    }

    def makePurchase(MakePurchaseCommand cmd) {
        if (cmd.hasErrors()) {
            throw new RuntimeException("com.cover.travel.TravelQuoteController#makePurchase has been called with " +
                "invalid request parameters. Errors: ${cmd.concatenateAllErrors(cmd.errors)}")
        }

        long quoteId = AESCryption.decrypt(cmd.quoteId).toInteger()


        TravelQuote quote = TravelQuote.read(quoteId)

        TravelApiQueryResultStatusEnum resultStatus = travelQuoteSgService.makePurchase(quoteId, cmd.offerId, cmd.travelerList, cmd.paymentCardDetails)

        render([status: resultStatus.toString(), statusCode: resultStatus.id, policyNo: quote.policyNo] as JSON)
    }

    def faq() {
        render view: '/travel/faq', model: [country: params.country]
    }
}
