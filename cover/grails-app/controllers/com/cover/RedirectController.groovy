package com.cover

/**
 * Permanent and Temporary redirects can be added through url mappings
 */
class RedirectController {

    def permanent() {
        String url = "${params.destination}${request.queryString ? '?' + request.queryString : ''}"
        redirect(url: url, permanent: true)
    }

    def temporary() {
        String url = "${params.destination}${request.queryString ? '?' + request.queryString : ''}"
        redirect(url: url)
    }
}
