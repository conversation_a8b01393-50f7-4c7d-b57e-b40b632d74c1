package com.cover

import com.cover.common.commands.GeneralInsuranceCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.ProductSubTypeEnum
import com.safeguard.ProductType
import com.safeguard.Provider
import com.safeguard.QuoteCreatorEnum
import com.safeguard.general.GeneralQuote
import com.safeguard.ProductTypeEnum
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelDomain

class GeneralInsuranceController {

    def checkoutService
    def generalInsuranceService
    def messageSource

    def checkout(GeneralInsuranceCommand command) {
        log.info("general.checkout - command:${command}, queryString:${params.toQueryString()}")
        String insuranceType = ProductTypeEnum.GENERAL.toString().toLowerCase()

        Integer productId = command.productId
        Product product = command.productId ? Product.read(command.productId) : null
        if (!product || product.productTypeId.intValue() != ProductTypeEnum.GENERAL.value()) {
            productId = null
            product = null
        }

        // If this is a form submission
        if (request.method == "POST") {
            if (command.validate()) {

                command.queryString = params.toQueryString()
                GeneralQuote quote = generalInsuranceService.createGeneralQuote(command)

                WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN]
                log.info("general.checkout - whiteLabelDomain${whiteLabelDomain}")

                notify AsyncEventConstants.GENERAL_QUOTE_CREATED, [quoteId: quote.id, lang: params.lang,
                                                               baseUrl: whiteLabelDomain.baseUrl,
                                                               creator: QuoteCreatorEnum.WEBSITE]
                log.info("product.productSubType.id.intValue() :${product.productSubType.id.intValue() }")

                if (product.productSubType.id.intValue() in [ProductSubTypeEnum.BUNDLE.value(),
                                                             ProductSubTypeEnum.DENTAL_CARD.value()]) {
                    String quoteId = AESCryption.encrypt(quote.id.toString())

                    def domain = grailsApplication.config.getProperty("cover.domain")
                    String returnUrl = "${domain}/${params.country}/${params.lang}/$insuranceType/$quoteId/checkout?productId=${command.productId}"

                    redirect(mapping: 'apiCheckoutPaynow',
                        params: [insuranceType: insuranceType, id: quoteId, lang: params.lang, country: params.country,
                                 returnUrl    : returnUrl])
                } else {
                    render view: "/generalInsurance/checkout/submissionSuccess", model: [policyReference:quote.policyReference]
                }
                return
            }

            render view: '/generalInsurance/generalInsuranceForm', model: [command: command, product:product]
            return
        }

        if (params.transaction && params.transaction == 'success') {

            redirect (mapping: 'generalCheckoutThankyou',
                params: [id: params.id, lang: params.lang, country: params.country])
            return
        }

        //Load values from session if available otherwise initialize with empty value
        command = new GeneralInsuranceCommand()
        command.productId = productId
        command.utmMedium = params.utm_medium
        command.utmSource = params.utm_source
        command.utmCampaign = params.utm_campaign
        command.gclid = params.gclid
        command.fbclid = params.fbclid
        command.firstName = session[IConstant.GENERAL_INSURANCE_FIRST_NAME] ?: ''
        command.lastName = session[IConstant.GENERAL_INSURANCE_LAST_NAME] ?: ''
        command.email = session[IConstant.GENERAL_INSURANCE_EMAIL] ?: ''
        command.mobile = session[IConstant.GENERAL_INSURANCE_MOBILE] ?: ''
        command.emiratesId = session[IConstant.GENERAL_INSURANCE_EMIRATES_ID] ?: ''

        log.info("form...")
        List<Provider> providers = Provider.findAllByCountry(Country.read(CountryEnum.UAE.id), [sort: "nameEn", order: "asc"])
        List<Product> generalProducts = Product.findAllByProductType(ProductType.read(ProductTypeEnum.GENERAL.value()))
        log.info("form...generalProducts:${generalProducts}")

        render view: '/generalInsurance/generalInsuranceForm', model: [command: command, product:product,
                                                                       products:generalProducts, providers: providers]
    }

    def thankyou() {
        log.info("generalInsurance.thankyou - params:$params")

        GeneralQuote quote = GeneralQuote.findById(AESCryption.decrypt(params.id))
        if (quote && quote.paymentStatus == PaymentStatusEnum.PAID) {

            String orderSummaryTemplate = '/generalInsurance/checkout/order_summary'

            def model = [quote: quote, orderSummaryTemplate: orderSummaryTemplate, donation: 0]
            def orderSummaryModel = checkoutService.getUAEOrderSummary(quote)
            model.putAll(orderSummaryModel)

            render view: "/generalInsurance/checkout/thankyou", model: model
            return
        }

        render view: "/apiV1/checkout/error", model: [quote: quote,
                              message: messageSource.getMessage('default.general.error', null, new Locale(params.lang))]
        return
    }
}
