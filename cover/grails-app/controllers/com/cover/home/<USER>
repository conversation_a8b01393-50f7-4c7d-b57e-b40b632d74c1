package com.cover.home

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.DonationTypeEnum
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.Donation
import com.safeguard.home.HomeQuote
import com.safeguard.payment.PaymentMethod
import com.safeguard.payment.ProviderPaymentMethod
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelDomain
import org.springframework.context.i18n.LocaleContextHolder

/**
 * <AUTHOR>
 */
class CheckoutController {
    static namespace = 'homeInsurance'

    def homeQuoteService
    def checkoutService
    def paymentService
    def paymentMethodService
    def paymentMethodSgService
    def utilService
    def commonPolicySgService

    /**
     * Update addons and redirect to pay now page
     */
    def updateOrder() {
        HomeQuote quote = HomeQuote.get(params.homeQuoteId)

        if (!quote.isNotProcessed()) {
            flash.error = g.message(code: 'error.alreadyProcessed')
            redirect mapping: "homeInsuranceIndex", params: [lang: params.lang, country: params.country]
            return
        }

        def donationAmount = params.donationAmount ?: 0

        commonPolicySgService.updateDonation(quote, ProductTypeEnum.HOME, donationAmount, DonationTypeEnum.CHARITY)


        HomeQuote homeQuote = homeQuoteService.updateHomeQuote(params)

        session[IConstant.HOME_CHECKOUT_QUOTE_ID] = homeQuote.id

        redirect mapping: 'homeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]

    }

    def paynow() {
        def homeQuoteId = session[IConstant.HOME_CHECKOUT_QUOTE_ID]

        if (homeQuoteId) {
            HomeQuote homeQuote = HomeQuote.read(homeQuoteId)

            if (!homeQuoteService.isEligibleForPayment(homeQuote)) {
                flash.error = g.message(code:'checkout.quote.modified')
                redirect mapping: 'homeInsuranceCheckoutIndex',
                    params: [country:params.country, lang:params.lang,
                             productId:homeQuote.productId, homeQuoteId:homeQuote.id]
                return
            }

            checkoutService.clearQuoteWithFromPaymentDetail(homeQuote, ProductTypeEnum.HOME)

            String domain = grailsApplication.config.getProperty("cover.domain")
            String country = params.country
            String lang = params.lang
            String encQuoteId = AESCryption.encrypt(homeQuote.id + "")

            String quoteVersion = homeQuote.version.toString()

            ProviderPaymentMethod providerPaymentMethod =
                paymentMethodSgService.getProviderPaymentMethod(homeQuote.product, homeQuote.productType)

            if (providerPaymentMethod) {
                log.info("providerPaymentMethod found :${providerPaymentMethod}")
                if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.NOT_AVAILABLE) {
                    notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: homeQuote.id,
                                                                              productId: homeQuote.product.id]

                    paymentService.addPaymentIntent(ProductTypeEnum.HOME, homeQuote.id, homeQuote.product.id)

                    redirect mapping: 'homePreparePaymentPage', params: [country: params.country, lang: params.lang]
                    return
                } else if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.TAP_PAYMENT) {
                    log.info("TAP payment gateway found for provider - including both TAP credit card and Tabby payment options")

                    // Get all TAP_PAYMENT gateway payment methods (both CREDITCARD and TABBY)
                    List<PaymentMethod> tapPaymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountryAndPaymentGateway(
                        true, ProductTypeEnum.HOME, Country.load(CountryEnum.findCountryByCode(country).id), PaymentGatewayEnum.TAP_PAYMENT)

                    if (tapPaymentMethods && tapPaymentMethods.size() > 0) {
                        log.info("Found ${tapPaymentMethods.size()} TAP payment methods: ${tapPaymentMethods.collect { it.name }}")

                        render view: "/homeInsurance/checkout/paymentMethods",
                            model: [paymentMethods   : tapPaymentMethods,
                                    homeQuote: homeQuote,
                                    showMerchantPage: false,
                                    quoteVersion: quoteVersion, country: session[IConstant.SITE_COUNTRY]]
                        return
                    }
                }
            }

            def creditCardParams, installmentParams
            boolean showMerchantPage = true
            String payfortReturnUrl = "$domain/$country/$lang/${ProductTypeEnum.HOME.toString().toLowerCase()}/checkout/payment"


            List<PaymentMethod> paymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountry(true,
                ProductTypeEnum.HOME, Country.load(CountryEnum.findCountryByCode(country).id))

            //Fall back to Prepare Payment Page when no payment method found
            if (paymentMethods.size() == 0) {
                notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: quote.id,
                                                                          productId: quote.product.id]

                paymentService.addPaymentIntent(ProductTypeEnum.HOME, quote.id, quote.product.id)

                redirect mapping: 'homePreparePaymentPage', params: [country: params.country, lang: params.lang]
                return
            }

            //Is Credit Card Payment Method Available?
            PaymentMethod creditCardPaymentMethod = paymentMethods.find { it.name == PaymentMethodEnum.CREDITCARD.toString() }

            if (creditCardPaymentMethod) {
                String checkoutSuccessUrl = "${domain}/payments/response/checkoutPsp/home/<USER>/success"
                String checkoutFailureUrl = "${domain}/payments/response/checkoutPsp/home/<USER>/failure"
                String checkoutCancelUrl = "${domain}/payments/response/checkoutPsp/home/<USER>/cancel"

                (creditCardParams, installmentParams, showMerchantPage) =
                    paymentMethodService.getCreditCardPaymentParameters(creditCardPaymentMethod.paymentGateway, homeQuote,
                        homeQuote.user, country, utilService.getClientIp(request),
                        payfortReturnUrl, checkoutSuccessUrl, checkoutFailureUrl, checkoutCancelUrl, null)
            }

            render view: "/homeInsurance/checkout/paymentMethods",
                model: [paymentMethods: paymentMethods,
                        homeQuote: homeQuote,
                        creditCardParams: creditCardParams,
                        installmentParams: installmentParams, showMerchantPage: showMerchantPage,
                        quoteVersion: quoteVersion, country: session[IConstant.SITE_COUNTRY]]
        }
    }

    /**
     * Payfort redirect to this action upon credit card form submission.
     */
    def payment() {
        log.info("checkout.payment - entering with params:$params")
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        if (isSecured) {

            HomeQuote homeQuote = HomeQuote.findById(HomeQuote.decodeMerchantRef(params.merchant_reference))

            if (PayfortStatusEnum.INVALID_REQUEST.toString().equals(params.status)) {
                flash.error = params.response_message
                redirect mapping: 'homeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]
                return
            }

            try {

                if (homeQuote.isModified(params.p_quote_v)) {
                    flash.error = g.message(code:'checkout.quote.modified')
                    log.error("home.checkout.payment - quote was modified:${homeQuote.id} ")
                    redirect mapping: 'homeInsuranceCheckoutIndex',
                        params: [country:params.country, lang:params.lang,
                                 productId:homeQuote.productId, homeQuoteId:homeQuote.id]
                    return
                }

                PayfortResponse payfortResponse = paymentService.process(params, PayfortCommandEnum.AUTHORIZATION,
                    utilService.getClientIp(request))


                session[IConstant.HOME_CHECKOUT_QUOTE_ID] = homeQuote.id

                //This is important so that we know incase of failed transaction it was installments
                if (payfortResponse.isInstallments) {
                    paymentService.setInstallments(homeQuote, payfortResponse.numberOfInstallments)
                }

                if (payfortResponse.isThreeDeeSecure) {
                    render view: '/car/checkout/_redirect', model: [url:payfortResponse.threeDeeSecureUrl,
                                                                    quote:homeQuote,
                                                                    lang: LocaleContextHolder.locale.language,
                                                                    country: session[IConstant.SITE_COUNTRY]]
                    return
                } else if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(payfortResponse.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(payfortResponse.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)
                    log.debug(".checkout.payment Redirect user to thankyou for merchantRef: ${params.merchant_reference}")
                    redirect mapping: 'homeInsuranceThankyou', params: [country: params.country, lang: params.lang]
                    return
                }
                //pushover service sending sms in-case error to dev team
                String failureMessage = payfortResponse.responseMessage  +
                    (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                notify AsyncEventConstants.HOME_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage,
                                                                         quoteId: homeQuote.id]

                flash.error = payfortResponse.responseMessage

            } catch (Exception exp) {
                log.error("Exception:", exp)
                flash.error = g.message(code:'checkout.general.error')
            }
        } else {
            log.error(".checkout.payment **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code:'checkout.general.error')
        }

        redirect mapping: 'homeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]
    }

    /**
     * All offline payment method submit to this action.
     * Note: Currently we support COD only. 21.Jun.2016
     */
    def order() {
        String method = params.paymentMethod
        def homeQuoteId = params.getLong('quoteId')

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.findPaymentMethod(method)

        if (!paymentMethodEnum) {
            log.warn("Invalid paymentmethod passed: ${params.paymentMethod}")
            flash.message = g.message(code:"default.general.error")
            redirect mapping: 'homeInsuranceCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        HomeQuote homeQuote = HomeQuote.read(homeQuoteId)

        if (!homeQuote.isNotProcessed()) {
            log.warn(".checkout.order quote is processed -> ${homeQuote?.id}")
            flash.message = g.message(code:"default.general.error")
            redirect mapping:"homeInsuranceCheckoutIndex", params: [country: params.country, lang: params.lang]
            return
        }

        if (homeQuote.isModified(params.p_quote_v)) {
            flash.error = g.message(code:'checkout.quote.modified')
            log.error("home.checkout.order - quote was modified:${homeQuote.id} ")
            redirect mapping: 'homeInsuranceCheckoutIndex',
                params: [country:params.country, lang:params.lang,
                         productId:homeQuote.productId, homeQuoteId:homeQuote.id]
            return
        }

        checkoutService.offlinePayment(homeQuote, paymentMethodEnum)

        flash.homeQuoteId = homeQuoteId

        redirect mapping: 'homeInsuranceCheckoutThankyou', params: [country: params.country, lang: params.lang]
    }

    /**
     * Payment success action
     */
    def thankyou () {

        def homeQuoteId = session[IConstant.HOME_INSURANCE_QUOTE_ID]

        log.debug(".checkout.thankyou entering with homeQuoteId: ${homeQuoteId}")

        //if params are null redirect to homeInsurance page
        if (!homeQuoteId) {
            redirect mapping: 'homeInsuranceCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        HomeQuote homeQuote = HomeQuote.get(homeQuoteId)

        //Quote is not processed yet, redirect back to quotes srp page
        if (homeQuote.isNotProcessed()) {
            CountryEnum country = CountryEnum.findCountry(params.country) ?: CountryEnum.UAE

            redirect mapping: 'homeQuotes', id: AESCryption.encrypt(homeQuote.id.toString()),
                params: [lang: utilService.getLanguage(), country: country.code]
            return
        }

        log.debug(".checkout.thankyou CarQuote found with id ${homeQuote.id}")
        notify AsyncEventConstants.EMAIL_HOME_QUOTE_PURCHASED, [quoteId: homeQuote.id, lang: LocaleContextHolder.locale.language]

        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain
        //Instead of invalidating session, remove all attributes and add only required attributes, similar to what we did in other insurances
        List attributeNames = session.attributeNames.toList()
        attributeNames.each { String attribute ->
            session.removeAttribute(attribute)
        }
        session[IConstant.WHITE_LABEL_DOMAIN] = whiteLabelDomain
        LocaleContextHolder.setLocale(new Locale(params.lang))
        session.'org.springframework.web.servlet.i18n.SessionLocaleResolver.LOCALE' = LocaleContextHolder.locale
        session[IConstant.SITE_COUNTRY] = CountryEnum.findCountry(params.country)
        session[IConstant.HOME_INSURANCE_FULL_NAME] = homeQuote.name
        session[IConstant.HOME_INSURANCE_MOBILE] = homeQuote.mobile
        session[IConstant.HOME_INSURANCE_QUOTE_ID] = homeQuote.id

        render view: '/homeInsurance/checkout/thankyou',
            model:[homeQuote:homeQuote, country: session[IConstant.SITE_COUNTRY]]
    }

    /**
     * 3d Secure returns to this action
     */
    def success() {
        log.info ".home.checkout.controller.success ${params}"
        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        def homeQuoteId = HomeQuote.decodeMerchantRef(params.merchant_reference)

        HomeQuote quote = HomeQuote.read(homeQuoteId)

        flash.homeQuoteId = quote.id

        if (isSecured) {
            if (quote && quote.isNotProcessed()) {
                paymentService.savePaymentResponse(quote, params)

                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status)) {
                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)

                    log.debug(".home.checkout.success Redirect user to thankyou for quoteId: ${quote.id}")
                    //All good? redirect user to thankyou page
                    redirect mapping: 'homeInsuranceCheckoutThankyou', params: [country: params.country, lang: params.lang]
                    return
                } else {
                    //pushover service sending sms in-case error to dev team
                    String failureMessage = params.response_message +
                        (params.acquirer_response_message ? " - " + params.acquirer_response_message : "")
                    notify AsyncEventConstants.HOME_PUSHOVER_FAILED_TRANSACTION, [message:failureMessage, quoteId: quote.id]
                    session[IConstant.HOME_CHECKOUT_QUOTE_ID] = quote.id
                    flash.error = params.response_message
                    log.error "#### HOME ERROR #### -> ${params.response_message} for ${quote.id} with status-> ${params.status} #### ERROR ####"
                }
            } else {
                log.warn(".home.checkout.success quote is isProcessed -> ${quote?.id}")
                log.debug(".home.checkout.success.is.processed still redirecting to thankyou")
                redirect mapping: 'homeInsuranceCheckoutThankyou', params: [country: params.country, lang: params.lang]
                return
            }

        } else {
            log.error(".honme.checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            flash.error = g.message(code:'checkout.general.error')
            redirect mapping: 'homeInsuranceCheckoutIndex', params: [country: params.country, lang: params.lang]
            return
        }

        redirect mapping: 'homeInsuranceCheckoutPaynow', params: [country: params.country, lang: params.lang]
    }

    /**
     * Show Prepare Payment Page
     * @return
     */
    def preparePaymentPage() {
        log.info(".preparePaymentPage - params:$params")

        def quoteId = flash.quoteId

        if (!quoteId) {
            //Try looking into quote parameter
            quoteId = session[IConstant.HOME_CHECKOUT_QUOTE_ID]
        }

        if (quoteId && quoteId instanceof String) {
            quoteId = Long.parseLong(quoteId)
        }

        log.debug(".checkout.preparePaymentPage entering with quoteId: ${quoteId}")

        //if params are null redirect to homeInsurance page
        if (!quoteId) {
            CountryEnum country = CountryEnum.findCountry(params.country) ?: CountryEnum.UAE
            redirect mapping: "homeInsuranceIndex", params: [lang: params.lang, country: params.country]
            return
        }

        HomeQuote quote = HomeQuote.get(quoteId)

        String hash = AESCryption.encrypt(quote.id.toString())

        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain

        render view: '/payment/preparePayment', model: [
            quote               : quote,
            lang                   : LocaleContextHolder.locale.language,
            hash                   : hash,
            whitelabel             : whiteLabelDomain.brand
        ]
    }
}
