package com.cover.home

import com.cover.common.commands.MarketingTrackingCommand
import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.cover.homeInsuranceCommands.IndexCommand
import com.cover.homeInsuranceCommands.CustomerDetailsCommand
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.car.DiscountCode
import com.safeguard.util.AESCryption
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.home.HomeQuote
import org.joda.time.DateTime

class HomeInsuranceController {

    static namespace = "homeInsurance"

    def homeQuoteService
    def utilService
    def discountService
    def sessionService

    def index(IndexCommand command) {

        // If this is a form submission
        if (request.method == "POST") {
            if (command.validate()) {
                session[IConstant.HOME_INSURANCE_CATEGORY] = command.coverageType

                redirect(mapping: 'homeInsuranceCustomerDetails', params: [lang: params.lang, country: params.country])
                return
            }

            redirect(action: 'index', params: [country: params.country])
            return
        }
        // home-insurance product type id is 2
        def discount = discountService.getApplicableDiscountCode(CountryEnum.findCountry(params.country), ProductTypeEnum.HOME.value())
        def discountAmount = null

        if (discount) {
            discountAmount = discount.discount.toBigInteger()
            discountAmount += (discount.hasPercentDiscount) ? '%' : ' AED'
        }

        def model = [
            selectedCoverageType: session[IConstant.HOME_INSURANCE_CATEGORY] ?: 1,
            country: params.country,
            discountAmount: discountAmount
        ]

        render view: '/homeInsurance/index', model: model
    }

    /**
     * Page to capture home insurance data
     * Home insurance Step 1.
     * @return
     */
    def details() {

        CustomerDetailsCommand command = new CustomerDetailsCommand()
        command.homeInsuranceCategoryId = session[IConstant.HOME_INSURANCE_CATEGORY]
        command.homeContentId = session[IConstant.HOME_INSURANCE_HOME_CONTENTS_VALUE]
        command.homePersonalBelongingId = session[IConstant.HOME_INSURANCE_PERSONAL_BELONGINGS_VALUE]
        command.buildingValue = session[IConstant.HOME_INSURANCE_BUILDING_VALUE]
        command.name = session[IConstant.HOME_INSURANCE_FULL_NAME]
        command.mobile = session[IConstant.HOME_INSURANCE_MOBILE]
        command.email = session[IConstant.HOME_INSURANCE_EMAIL]

        DateTime policyStartDateObj = DateTime.now()

        try {
            if (session[IConstant.HOME_INSURANCE_POLICY_START_DATE]) {
                policyStartDateObj = new DateTime(session[IConstant.HOME_INSURANCE_POLICY_START_DATE])
            }
        } catch (Exception e) {
            log.error("Error while parsing policy start date")
        }

        if (policyStartDateObj.isBeforeNow()) {
            policyStartDateObj = DateTime.now()
        }

        command.policyStartDate = policyStartDateObj.toDate()


        render view: '/homeInsurance/details', model: [command:command, country: params.country]
    }

    def save (CustomerDetailsCommand command) {

        command.countryEnum = utilService.convertToCountry(params.country)
        if (!command.validate()) {
            log.error("Customer details command validation error")
            command.errors.allErrors.each {
                log.error("code-> ${it.code}")
            }
            redirect(action: 'details', params: [country: params.country])
            return
        }

        session[IConstant.HOME_INSURANCE_CATEGORY] = command.homeInsuranceCategoryId
        session[IConstant.HOME_INSURANCE_HOME_CONTENTS_VALUE] = command.homeContentId
        session[IConstant.HOME_INSURANCE_PERSONAL_BELONGINGS_VALUE] = command.homePersonalBelongingId
        session[IConstant.HOME_INSURANCE_BUILDING_VALUE] = command.buildingValue
        session[IConstant.HOME_INSURANCE_POLICY_START_DATE] = command.policyStartDate
        session[IConstant.HOME_INSURANCE_FULL_NAME] = command.name
        session[IConstant.HOME_INSURANCE_MOBILE] = command.mobile
        session[IConstant.HOME_INSURANCE_EMAIL] = command.email
        command.source = RequestSourceEnum.WEB.name()

        MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
        marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
        marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
        marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
        marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
        marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
        marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
        command.marketingTracking = marketingTracking

        HomeQuote homeQuote = homeQuoteService.createHomeQuote(command)

        session[IConstant.HOME_INSURANCE_QUOTE_ID] = homeQuote.id

        String encryptedId = AESCryption.encrypt(homeQuote.id.toString());
        String recipient = homeQuote.mobile
        if (!recipient.contains("+971")) {
            recipient = "+971".concat(recipient)
        }
        //sending home quote email...
        notify AsyncEventConstants.EMAIL_HOME_QUOTE_CREATED, [quoteId: homeQuote.id, lang: params.lang, country: params.country]

        notify AsyncEventConstants.WHATSAPP_NOTIFICATION_TRIGGER, [templateName:"home_quote_update",
                                                                   quoteId:encryptedId,
                                                                   lang:homeQuote.lang,
                                                                   country:command.countryEnum.code,
                                                                   recipient:recipient,
                                                                   type: "home"]


        // redirect(mapping:'homeQuotes', params: [lang: params.lang, id: encryptedId, country: params.country])
        redirect mapping: 'preHomeQuotes', id: AESCryption.encrypt(homeQuote.id.toString()), base: sessionService.getBaseUrl(),
                    params: [lang: params.lang, country: params.country]
    }

    def quotes(String id) {

        Integer homeQuoteId

        try {
            if (id) {
                homeQuoteId = Integer.parseInt(AESCryption.decrypt(id))
            } else {
                homeQuoteId = session[IConstant.HOME_INSURANCE_QUOTE_ID]
            }
        }catch (e) {
            log.error "Unable to decrypt home quote id ${id}"
            homeQuoteId = session[IConstant.HOME_INSURANCE_QUOTE_ID]
        }

        HomeQuote homeQuote = HomeQuote.read(homeQuoteId)

        if (!homeQuote) {
            redirect mapping: 'homeInsuranceCustomerDetails', params: [lang: params.lang, country: params.country]
            return
        }

        session[IConstant.HOME_INSURANCE_CATEGORY] = homeQuote.homeInsuranceCategoryId
        session[IConstant.HOME_INSURANCE_HOME_CONTENTS_VALUE] = homeQuote.homeContentId
        session[IConstant.HOME_INSURANCE_PERSONAL_BELONGINGS_VALUE] = homeQuote.homePersonalBelongingId
        session[IConstant.HOME_INSURANCE_BUILDING_VALUE] = homeQuote.buildingValue
        session[IConstant.HOME_INSURANCE_FULL_NAME] = homeQuote.name
        session[IConstant.HOME_INSURANCE_MOBILE] = homeQuote.mobile
        session[IConstant.HOME_INSURANCE_EMAIL] = homeQuote.email

        List<HomeRateCommand> quotes = homeQuoteService.getRatings(homeQuoteService.toHomeQuoteCommand(homeQuote))

        //adding sorting by premium
        if (quotes) {
            quotes.sort { it.premium }
        }

        render view: '/homeInsurance/quotes', model: [quotes: quotes, homeQuote: homeQuote, country: params.country]
    }

    def quoteDetails() {
        if (params.productId && params.homeQuoteId) {
            HomeQuote homeQuote = HomeQuote.read(params.long("homeQuoteId"))
            HomeQuoteCommand homeQuoteCommand = homeQuoteService.toHomeQuoteCommand(homeQuote)
            homeQuoteCommand.productId = params.int("productId")

            def quote = homeQuoteService.getRating(homeQuoteCommand)
            def currency = utilService.getCountry().currency

            render view: '/homeInsurance/quoteDetails', model: [quoteId: homeQuote.id, quote: quote, currency: currency, country: params.country]
            return
        }

        redirect(mapping: 'homeInsuranceCustomerDetails', params: [country: params.country, lang: params.lang])
    }

    def checkout() {
        if (params.productId && params.homeQuoteId) {
            HomeQuote homeQuote = HomeQuote.get(params.long("homeQuoteId"))

            if (homeQuote.isNotProcessed()) {
                HomeQuoteCommand homeQuoteCommand = homeQuoteService.toHomeQuoteCommand(homeQuote)
                homeQuoteCommand.productId = params.int("productId")
                HomeRateCommand rate = homeQuoteService.getRating(homeQuoteCommand)

                def totalPrice = rate.premium

                if(params.domesticHelper) {
                    session[IConstant.HOME_INSURANCE_DOMESTIC_HELPER_COVER] = params.domesticHelperValue
                    totalPrice = totalPrice.add(Double.parseDouble(params.domesticHelperValue))
                } else {
                    session[IConstant.HOME_INSURANCE_DOMESTIC_HELPER_COVER] = null
                }

                homeQuoteService.setProduct(homeQuote, rate)


                String currency = utilService.getCountry().currency

                render view: '/homeInsurance/checkout/index', model: [totalPrice: totalPrice, homeQuote: homeQuote, rateCommand: rate, currency: currency, country: session[IConstant.SITE_COUNTRY]]
                return

            }
        }

        redirect(mapping: 'homeInsuranceCustomerDetails', params: [country: params.country, lang: params.lang])
    }

    def payment(HomeQuoteCommand quoteCommand) {
        def quote = homeQuoteService.getRating(quoteCommand)
        def quoteId = 50
        def currency = utilService.getCountry().currency
        def addonList = [[
                             label: 'Domestic Helper Service',
                             price: '75'
                         ]]

        render view: '/homeInsurance/payment', model: [quoteId: quoteId, quote: quote, currency: currency,
                                                       addonList: addonList, country: params.country]
    }

    def faq() {
        render view: '/homeInsurance/faq', model: [country: params.country]
    }

    def preHomeQuotes(String country, String lang, String id) {
        log.info("funnel.preHomeQuotes - entering with [country:$country, lang:$lang, id:$id]")

        String quotesLink = g.createLink(mapping: 'homeQuotes', base: sessionService.getBaseUrl(), params:
            [country: country, id: id, lang: lang])

        render view: '/homeInsurance/preQuotes', model: [quotesLink: quotesLink]
    }
}
