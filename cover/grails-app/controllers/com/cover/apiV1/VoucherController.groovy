package com.cover.apiV1

import com.safeguard.RequestSourceEnum
import com.safeguard.User
import com.safeguard.VoucherEventTypeEnum
import grails.converters.JSON
import org.joda.time.*
import org.json.JSONObject

class VoucherController {

    static namespace = "apiV1"

    static allowedMethods = [
        list         : ["GET"],
        vouchecrEvent: ["POST"],
    ]

    def springSecurityService
    def voucherService


    def list(String s) {

        log.info("apiV1.vouchers.index")

        User currentUser = springSecurityService.currentUser

        RequestSourceEnum requestSourceEnum
        if (s == "w") {
            requestSourceEnum = RequestSourceEnum.WEB
        }
        def hasActivePolicy = voucherService.hasActivePolicy(currentUser)
        if (hasActivePolicy) {
            def voucherList = voucherService.getVouchers()
            def vouchers = voucherList.collect {
                [
                    id           : it.id,
                    filename     : "${it.filename}",
                    fullPath     : "${it.fullPath}",
                    daysRemaining: it.expireDate ? "${(Days.daysBetween(LocalDateTime.now(), it.expireDate).getDays())}" : null,
                    logoFullPath : "${it.logoFullPath}"
                ]
            }
            render([vouchers: vouchers, totalCount: vouchers.size()] as JSON)
        } else {
            render([vouchers: [], totalCount: 0] as JSON)
        }
    }

    def voucherEvent() {
        log.debug("==========================")
        log.debug(request.JSON.toString())
        log.debug("==========================")
        def json = request.JSON
        User currentUser = springSecurityService.currentUser

        def voucherEvent = voucherService.saveVoucherEvent(currentUser, (VoucherEventTypeEnum) json.eventType, (json.voucherId).toLong())
        if (voucherEvent) {
            def voucher = voucherEvent.voucher
            def voucherModel = new JSONObject()
            def jsonObj = new JSONObject()
            jsonObj.put("id", voucher.id)
            jsonObj.put("filename", "${voucher.filename}")
            jsonObj.put("fullPath", "${voucher.fullPath}")
            jsonObj.put("daysRemaining", "${voucher.expireDate ? (Days.daysBetween(LocalDateTime.now(), voucher.expireDate).getDays()) : null}")
            jsonObj.put("logoFullPath", "${voucher.logoFullPath}")
            voucherModel.put("voucher", jsonObj)

            render voucherModel
        } else {
            return response.sendError(404)
        }

    }
}
