package com.cover.apiV1

import com.cover.car.commands.QuoteDetailsCommand
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.pet.commands.*
import com.cover.util.IConstant
import com.safeguard.*
import com.safeguard.pet.*
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.springframework.http.HttpStatus

class PetController {

    static namespace = "apiV1"

    static allowedMethods = [
        petBreeds   : ['GET', 'HEAD'],
        saveQuote   : ['POST'],
        quotes      : ['GET'],
        addPet      : ['POST'],
        updatePet   : ['POST'],
        removePet   : ['DELETE'],
        pets        : ['GET'],
        savePetPhoto: ['POST'],
        editPetQuoteDetails: ['POST']
    ]

    def utilService
    def petService
    def petQuote
    def petQuoteService
    def petRatingService
    def petQuoteSgService
    def springSecurityService
    def commonUtilService
    def documentUploadService

    /**
     * Get all pet breeds
     * @return petBreed as JSON
     */
    def petBreeds() {
        log.info("apiV1.pet.petBreed - getting all pet breeds")

        def obj = [:]
        List<PetBreed> petBreedList = petService.getPetBreedList()
        obj['cat'] = petBreedList.findAll {it.petType.id == 1}
        obj['dog'] = [:]
        obj['dog']['pure'] = petBreedList.findAll {!it.name.toUpperCase().contains("MIXED") && it.petType.id == 2}

        List<PetBreed> mixedDogBreed = petBreedList.findAll {it.name.toUpperCase().contains("MIXED") && it.petType.id == 2}.sort{it.id}
        mixedDogBreed.each {
            it.name = it.name.replace(' (', '<br>(')
        }
        obj['dog']['mixed'] = mixedDogBreed

        def model = [breeds: obj]

        render model as JSON
    }

    /**
     * Save quote
     *
     * @param params
     * @return Encrypted Quote Id as JSON
     */
    def saveQuote(def params) {
        log.info("apiV1.pet.saveQuote - entering with [params:${params.toString()}]")


        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        def resp = [:]
        def petQuote = petQuoteService.createQuote(params)
        if (petQuote) {

            petQuote = saveUTM(petQuote, params)

            petQuoteService.saveQuote(petQuote)

            String encryptedId = AESCryption.encrypt(petQuote.id.toString())
            //sending pet quote email
            notify AsyncEventConstants.PET_QUOTE_CREATED, [quoteId: petQuote.id,
                                                           lang   : petQuote.lang,
                                                           country: countryEnum.code]
            //sending pet quote WhatsApp
            String recipient = petQuote.user.mobile
            if (!recipient.contains("+971")) {
                recipient = "+971".concat(recipient)
            }
            notify AsyncEventConstants.WHATSAPP_NOTIFICATION_TRIGGER, [templateName: "pet_insurance_quote",
                                                                       quoteId     : encryptedId,
                                                                       lang        : petQuote.lang,
                                                                       country     : countryEnum.code,
                                                                       recipient   : recipient,
                                                                       type        : "pet-insurance"]
            response.status = HttpStatus.OK.value()
            resp = [
                id    : encryptedId,
                petId : AESCryption.encrypt(petQuote.pet.id.toString()),
                status: response.status
            ]

        } else {
            response.status = HttpStatus.NOT_ACCEPTABLE.value()
            resp = [
                message: "You have an active policy for this pet!",
                status : response.status
            ]
        }

        render resp as JSON

    }

    /**
     * Get Pet quotes (ratings)
     *
     * @param id ( Quote id )
     * @return Quotes as JSON
     */
    List<PetRateCommand> quotes(String id) {
        log.info("apiV1.pet.quotes - entering with params:[id:$id]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        log.info("apiV1.pet.quotes - quoteId:${quoteId}")

        def resp = [:]

        if (quoteId) {
            PetQuote petQuote = PetQuote.findById(quoteId)

            if (petQuote && petQuote.isNotProcessed()) {

                List<PetRateCommand> petRatings = petRatingService.getRatings(petQuote)

                if (!petRatings) {
                    log.error("apiV1.pet.quotes - No rattings avalable for quoteId:${quoteId}")

                    resp.message = g.message(code: 'noquote.sorry')
                    resp.message1 = 'No rattings avalable'
                    resp.message1 = g.message(code: 'noquote.manualQuotation')

                    render resp as JSON
                    return
                }

                def model = [petRatings: petRatings]

                render model as JSON
                return
            } else if (!petQuote) {
                log.warn "quotes - Quote is not found "
                response.status = HttpStatus.NOT_FOUND.value()
                render resp as JSON
                return
            } else {
                log.warn "quotes - Quote is already processed for quote id: ${petQuote?.id}"

                resp.message = g.message(code: 'error.alreadyProcessed')
            }
            render resp as JSON
            return
        }

        response.status = HttpStatus.NOT_FOUND.value()
        render resp as JSON
        return
    }


    /**
     * Get Pet quote Details
     *
     * @param id ( Quote id )
     * @return Quote details as JSON
     */
    def quoteDetail(String id) {
        log.info("apiV1.pet.quoteDetail - entering with params:[id:$id]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        log.info("apiV1.pet.quoteDetail - quoteId:${quoteId}")

        def resp = [:]

        if (quoteId) {
            PetQuote petQuote = PetQuote.findById(quoteId)

            if (petQuote && petQuote.isNotProcessed()) {

                def model = [petQuote: petQuote]
                render model as JSON

                return
            } else if (!petQuote) {
                log.warn "quotes - Quote is not found "
                response.status = HttpStatus.NOT_FOUND.value()
                render resp as JSON
                return
            } else {
                log.warn "quotes - Quote is already processed for quote id: ${petQuote?.id}"

                resp.message = g.message(code: 'error.alreadyProcessed')
            }
            render resp as JSON
            return
        }

        response.status = HttpStatus.NOT_FOUND.value()
        render resp as JSON
        return
    }

    /**
     * Update Pet quote price
     *
     * @param id ( Quote id )
     * @param id ( Rating id )
     * @param paymentPlan ( ANNUAL or MONTHLY )
     * @return Quote details as JSON
     */
    def updatePetQuote(String id, String selectedRatingId, String paymentPlan) {
        log.info("pet.updatePetQuote - [params:$params]")
        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        Integer rateId = Integer.parseInt(selectedRatingId)
        log.info("apiV1.pet.updatePetQuote - quoteId:${quoteId}")

        def resp = [:]

        if (quoteId) {
            PetQuote petQuote = PetQuote.findById(quoteId)
            PetRating petRating = PetRating.findById(rateId)

            if (petQuote && petQuote.isNotProcessed()) {

                petQuote = petQuoteService.updatePetQuote(petQuote, petRating, paymentPlan)

                def model = [success: true]
                render model as JSON
                return

            } else if (!petQuote) {
                log.warn "quotes - Quote is not found "
                response.status = HttpStatus.NOT_FOUND.value()
                render resp as JSON
                return
            } else {
                log.warn "quotes - Quote is already processed for quote id: ${petQuote?.id}"

                resp.message = g.message(code: 'error.alreadyProcessed')
            }
            render resp as JSON
            return
        }

        response.status = HttpStatus.NOT_FOUND.value()
        render resp as JSON
        return
    }


    /**
     * Edit Pet quote From Summary
     *
     * @param petQuote ( Pet quote details )
     * @return Quote details as JSON
     */
    def editPetQuoteDetails(PetQuoteCommand petQuoteCmd) {
         log.info("apiV1.pet.editPetQuoteDetails - petQuote:${petQuoteCmd.toString()}")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(petQuoteCmd.cryptedId))

        def resp = [:]

        if (quoteId) {
            PetQuote petQuote = PetQuote.findById(quoteId)
            Pet pet = Pet.find( petQuote.pet)

            def updatedPet = petService.updatePet(petQuoteCmd.toUpdatePetCommand(pet.id))
            def updatepetQuote = petQuoteService.updatePetQuoteDetails(petQuoteCmd)

            resp = [
                petQuote: petQuote,
                id    : AESCryption.encrypt(petQuote.id.toString()),
                petId : AESCryption.encrypt(petQuote.pet.id.toString()),
                status: response.status
            ]

        } else {
                log.warn "quotes - Quote with id: ${quoteId} not found"

                resp.message = "quotes - Quote with id: ${quoteId} not found"
            }

        render resp as JSON
        return

    }



    public uploadPolicyDocument(def params) {
        def obj = [status: 200, message: "uploaded"]
        log.info "all data : ${params}"
        Integer petQuoteId = Integer.parseInt(AESCryption.decrypt(params.quoteId))
        log.info "quoteId: ${petQuoteId}"
        InputStream inputStream = new BufferedInputStream(params.file.getInputStream());
        PetQuote petQuote = PetQuote.findById(petQuoteId)

        PetPolicyDocument petPolicyDocument = PetPolicyDocument.findByPetQuote(petQuote)
        if (petPolicyDocument) {
            log.warn(".upload: policy document already uploaded for petQuote [id: ${petQuote.id}]")
            obj['status'] = 400
            obj['message'] = "Policy document is already uploaded for this policy"
        }

        DocumentType doc = DocumentType.findByCode(DocumentTypeEnum.INSURANCE_POLICY.code)
        def fileName = "${petQuote.policyReference}-${DocumentTypeEnum.INSURANCE_POLICY.title}.pdf"
        try {
            Map result = documentUploadService.savePolicyDocument(fileName, inputStream, doc, petQuote, ProductTypeEnum.PET, false, false, null)
            PetPolicyDocument policyDocument = result.policyDocument
            if (policyDocument.validate()) {
                petQuote.policyNo = params.policyNo
                petQuote.save(flush: true)

                obj['id'] = policyDocument.id
                obj['documentType'] = doc.code
                obj['fullPath'] = policyDocument.fullPath
            } else {
                obj['status'] = 400
                String errors = ""
                policyDocument.errors.allErrors.each {
                    errors = "${errors}${g.message(error: it)}\n"
                }
                obj['message'] = errors
            }
        } catch (Exception e) {
            log.error(".upload: Error while uploading file in petQuote [id: ${petQuote.id}]", e)
            obj['status'] = 400
            obj['message'] = g.message(code: "file.upload.failed")
        }

        render obj as JSON
        return
    }

    //region My Account APIs

    /**
     * Add pet from my account
     * @param addPetCommand
     * @return Json with petId, message and errors
     */
    def addPet() {
        Map result
        AddPetCommand addPetCommand = new AddPetCommand(request.JSON)

        if (!addPetCommand.validate()) {

            response.status = HttpStatus.BAD_REQUEST.value()

            List cmdErrors = []
            addPetCommand.errors.allErrors.each { error ->
                cmdErrors.push(error.defaultMessage)
            }
            result = [
                message: "Validation errors",
                errors : cmdErrors
            ]
            render result as JSON
            return
        }
        User user = springSecurityService.currentUser
        if (!user) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["User doesn't exist"]
            ]
            render result as JSON
            return
        }

        def pet = Pet.findByPetNameAndUserAndPetTypeAndPetBreed(addPetCommand.petName, user, PetType.read(addPetCommand.petType), PetBreed.read(addPetCommand.petBreed))
        if (pet && pet.isDeleted) {
            pet.isDeleted = false
            pet.save(flush: true)
        }
        if (pet) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["Your ${pet.petType.name.toLowerCase()} ${pet.petName.capitalize()} already Exist."]
            ]
            render result as JSON
            return
        }
        Pet newPet = petService.createPet(addPetCommand, user)

        if (!newPet) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["Failed to add your pet ${addPetCommand.petName.capitalize()}."]
            ]
            render result as JSON
            return
        }

        response.status = HttpStatus.OK.value()
        result = [
            petId  : AESCryption.encrypt(newPet.id.toString()),
            message: "Added your ${newPet.petType.name.toLowerCase()} ${newPet.petName.capitalize()} successfully.",
            errors : []
        ]
        render result as JSON
        return
    }

    /**
     * Update pet from my account
     * @param updatePetCommand
     * @return Json with message and errors
     */
    def updatePet(UpdatePetCommand updatePetCommand) {
        Map result

        if (!updatePetCommand.validate()) {

            response.status = HttpStatus.BAD_REQUEST.value()

            List cmdErrors = []
            updatePetCommand.errors.allErrors.each { error ->
                cmdErrors.push(error.defaultMessage)
            }
            result = [
                message: "Validation errors",
                errors : cmdErrors
            ]
            render result as JSON
            return
        }

        User user = springSecurityService.currentUser
        if (!user) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["User doesn't exist"]
            ]
            render result as JSON
            return
        }

        def pet = petService.updatePet(updatePetCommand)

        if (!pet) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["Your ${updatePetCommand.petName.capitalize()} not found."]
            ]
            render result as JSON
            return
        }

        response.status = HttpStatus.OK.value()
        result = [
            message: "Updated your ${pet.petType.name.toLowerCase()} ${pet.petName.capitalize()} successfully.",
            errors : []
        ]
        render result as JSON
        return
    }

    /**
     * Mark pet as deleted from my account
     * @return Json with message and errors
     */
    def removePet() {
        Map result
        String petId = params.id

        if (!petId) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Validation errors",
                errors : ["petId cannot be null"]
            ]
            render result as JSON
            return
        }

        User user = springSecurityService.currentUser
        if (!user) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["User doesn't exist"]
            ]
            render result as JSON
            return
        }

        Pet pet = Pet.read(AESCryption.decrypt(petId))
        if (!pet) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["Your pet not found."]
            ]
            render result as JSON
            return
        }

        def removeResult = petService.removePet(pet)

        if (!removeResult) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed to remove",
                errors : ["Cannot remove your ${pet.petType} ${pet.petName} with active policy at the moment!"]
            ]
            render result as JSON
            return
        }
        /*
        //sending pet quote email
        notify AsyncEventConstants.EMAIL_PET_REMOVED, [user_name    : pet.user.name,
                                                       user_email   : pet.user.email,
                                                       pet_name     : pet.petName]
        */

        response.status = HttpStatus.OK.value()
        result = [
            message: "Removed your ${pet.petType.name.toLowerCase()} ${pet.petName.capitalize()} successfully.",
            errors : []
        ]
        render result as JSON
        return
    }

    /**
     * fetch all user pets along with all active pet quotes
     * @return list of pets and active policies as JSON
     */
    def pets() {
        log.info ".pets - currentUser: ${springSecurityService.currentUser}"
        Map result
        User user = springSecurityService.currentUser
        if (!user) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["User doesn't exist"]
            ]
            render result as JSON
            return
        }
        def pets = petService.listPetsAndPolicies(user)

        if (!pets) {
            response.status = HttpStatus.OK.value()
            result = [
                message: "You have no pets",
                errors : []
            ]
            render result as JSON
            return
        }
        response.status = HttpStatus.OK.value()
        render pets as JSON
        return
    }

    /**
     * upload pet photo to s3 and save it in the pet domain
     * @param petPhotoCommand
     * @return S3UploadResult as JSON
     */
    def savePetPhoto(PetPhotoCommand petPhotoCommand) {
        log.info(".savePetPhoto -  petId: ${petPhotoCommand.petId}")
        Map result
        if (!petPhotoCommand.validate()) {
            response.status = HttpStatus.BAD_REQUEST.value()
            List cmdErrors = []
            petPhotoCommand.errors.allErrors.each { error ->
                cmdErrors.push(error.defaultMessage)
            }
            result = [
                message: "Validation errors",
                errors : cmdErrors
            ]
            render result as JSON
            return
        }
        Pet pet = Pet.read(AESCryption.decrypt(petPhotoCommand.petId))

        if (!pet) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Failed",
                errors : ["Your pet not found."]
            ]
            render result as JSON
            return
        }
        if (!commonUtilService.isDocValid(petPhotoCommand.file)) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Invalid photo",
                errors : ["Your photo is not valid!"]
            ]
            render result as JSON
            return
        }

        def uploadResult = petService.savePetPhoto(petPhotoCommand.file, pet)

        if (!uploadResult) {
            response.status = HttpStatus.BAD_REQUEST.value()
            result = [
                message: "Upload Failed",
                errors : ["Your photo couldn't be uploaded, try again later!"]
            ]
            render result as JSON
            return
        }
        response.status = HttpStatus.OK.value()
        render uploadResult as JSON
        return
    }
    //endregion

    def petQuotePaymentHistory(String encryptedQuoteId) {
        String quoteId = AESCryption.decrypt(encryptedQuoteId)
        PetQuote petQuote = PetQuote.read(AESCryption.decrypt(encryptedQuoteId))
        log.info("pet.petQuotePaymentHistory - encrypted id: ${encryptedQuoteId} ; quote id: ${quoteId}")
        List payments = petQuoteSgService.getPetQuotePaymentHistory(petQuote)

        Map paymentsList = [
            payments   : payments,
            paymentPlan: petQuote.paymentPlan ? petQuote.paymentPlan.name() : ""
        ]

        render paymentsList as JSON
    }

    /**
     * Get Pet Details using quote id
     *
     * @param id ( Quote id )
     * @return Pet details as JSON
     */
    def petDetailFromQuote(String id, String queryString, String utmSource,
                           String utmMedium, String utmCampaign, String gclid, String fbclid) {
        log.info("apiV1.pet.petDetailFromQuote - entering with params:[id:$id]")

        Long quoteId = Long.parseLong(AESCryption.decrypt(id))
        log.info("apiV1.pet.petDetailFromQuote - quoteId:${quoteId}")

        def resp = [:]

        if (quoteId) {
            PetQuote petQuote = PetQuote.findById(quoteId)

            if (petQuote) {

                petQuote.queryString = queryString != 'null' ? queryString : petQuote.queryString
                petQuote.utmSource = utmSource != 'null' ? utmSource : petQuote.utmSource
                petQuote.utmMedium = utmMedium != 'null' ? utmMedium : petQuote.utmMedium
                petQuote.utmCampaign = utmCampaign != 'null' ? utmCampaign : petQuote.utmCampaign
                petQuote.gclid = gclid != 'null' ? gclid : petQuote.gclid
                petQuote.fbclid = fbclid != 'null' ? fbclid : petQuote.fbclid
                petQuote.save(flush: true, failOnError: true)

                Pet pet = Pet.findById(petQuote.pet.id)
                PetDetailsCommand petDetailsCommand = new PetDetailsCommand()
                petDetailsCommand.id = pet.id
                petDetailsCommand.petName = pet.petName
                petDetailsCommand.petType = pet.petType.name
                petDetailsCommand.petBreed = pet.petBreed.id
                petDetailsCommand.petAge = pet.petAge
                petDetailsCommand.petGender = pet.petGender
                petDetailsCommand.microchip = pet.microchip
                petDetailsCommand.emiratesIdNumber = petQuote.emiratesIdNumber
                petDetailsCommand.userName = petQuote.user.firstName + ' ' + petQuote.user.lastNames
                petDetailsCommand.userEmail = petQuote.user.email
                petDetailsCommand.isMixed = petQuote.pet.isMixed
                petDetailsCommand.userMobile = petQuote.user.mobile
                petDetailsCommand.policyStartDate = petQuote.policyStartDate.toString()

                def model = [petDetails: petDetailsCommand]

                render model as JSON
                return
            } else {
                log.warn "quotes - Quote is not found "
                response.status = HttpStatus.NOT_FOUND.value()
                render resp as JSON
                return
            }
            render resp as JSON
            return
        }

        response.status = HttpStatus.NOT_FOUND.value()
        render resp as JSON
        return
    }


    /**
     * Save the utm params to the pet quote
     * @return petQuote
     */
    PetQuote saveUTM(PetQuote petQuote, def params) {

        petQuote.queryString = params.queryString
        petQuote.utmSource = params.utmSource
        petQuote.utmMedium = params.utmMedium
        petQuote.utmCampaign = params.utmCampaign
        petQuote.gclid = params.gclid
        petQuote.fbclid = params.fbclid

        return petQuote

    }
}
