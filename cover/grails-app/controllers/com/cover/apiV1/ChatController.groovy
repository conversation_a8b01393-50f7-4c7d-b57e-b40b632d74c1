package com.cover.apiV1

import com.safeguard.User

class ChatController {

    static namespace = "apiV1"
    def springSecurityService

    static allowedMethods = [
        index     : ['GET']
    ]

    def index(String name, String mobile, Long userId) {
        log.info("apiV1.chat.index - entering with [params:$params]")

        if(!name && !mobile) {
            User user = springSecurityService.currentUser
            if(user) {
                name = user.name
                mobile = user.mobile
                userId = user.id

            }
        }
        Map model = [ name : name, mobile : mobile, userId : userId ]
        render view:"/apiV1/chat/index", model: model
    }

    def script() {
        render view:'/apiV1/chat/script'
    }

    def closebutton() {
        render view:'/apiV1/chat/closebutton'
    }
}
