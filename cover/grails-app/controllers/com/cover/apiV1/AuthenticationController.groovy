package com.cover.apiV1

import com.cover.api.AuthenticationCommand
import com.cover.api.UserProfile
import com.safeguard.Country
import com.safeguard.CountryEnum
import grails.converters.JSON
import org.pac4j.core.context.J2EContext
import org.pac4j.core.context.WebContext
import org.springframework.http.HttpStatus

/**
 * Perform authentication of user
 *
 * For now only social media authentication is made
 *
 */
class AuthenticationController {

    static namespace = "apiV1"

    static allowedMethods = [
        login     : ['POST']
    ]

    def profileService
    def utilService

    /**
     * Login User with social media accounts
     * @param command
     * @return
     */
    def login(AuthenticationCommand command) {
        log.info("authentication.login - command:${command}")

        String lang = utilService.convertToLocale(params.lang)
        CountryEnum countryEnum = CountryEnum.findCountry(command.country)
        Country country = Country.get(countryEnum.id)
        Locale locale = new Locale(lang)

        WebContext context = new J2EContext(request, response)

        UserProfile profile = profileService.getUserProfile(command, context)

        log.info("profile:${profile}")
        if (!profile || !profile.email) {
            response.status = HttpStatus.UNAUTHORIZED.value()

            Map resp = [:]
            if (profile && !profile.email) {
                resp.message = g.message(code: "myAccount.login.email.required")
            } else {
                resp.message = "User not found"
            }

            render resp
            return
        }

        def authResponse = profileService.loginSocialUser(profile, locale, country)

        if (!authResponse.success) {
            response.status = HttpStatus.UNAUTHORIZED.value()
        }

        render authResponse as JSON

    }
}
