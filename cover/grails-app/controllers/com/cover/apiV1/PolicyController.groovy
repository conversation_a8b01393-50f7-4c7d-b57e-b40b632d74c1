package com.cover.apiV1

import com.safeguard.CrmStatusEnum
import com.safeguard.HealthPolicyDocument
import com.safeguard.HomePolicyDocument
import com.safeguard.PolicyDocument
import com.safeguard.ProductTypeEnum
import com.safeguard.QuoteCreatorEnum
import com.safeguard.Renewal
import com.safeguard.RequestSourceEnum
import com.safeguard.User
import com.safeguard.car.CarQuote
import com.safeguard.travel.TravelPolicyDocument
import com.safeguard.util.AESCryption
import com.safeguard.car.CarQuoteDetail
import grails.converters.JSON
import grails.web.RequestParameter
import groovy.transform.CompileStatic
import org.springframework.http.HttpStatus

class PolicyController {

    static namespace = "apiV1"

    static allowedMethods = [
        list               : ["GET"],
        requestRenewal     : ["POST"],
        requestCancellation: ["POST"],
        emergencyInfo      : ["GET"],
    ]

    def quoteService
    def springSecurityService
    def renewalService
    def commonQuoteService
    def apiQuoteService
    def commonUtilService
    def utilService

    def list(String type, String s) {

        log.info("apiV1.policies.index - type: $type")

        User currentUser = springSecurityService.currentUser

        RequestSourceEnum requestSourceEnum
        if (s == "w") {
            requestSourceEnum = RequestSourceEnum.WEB
        }
        ProductTypeEnum productType = ProductTypeEnum.findByName(type.toUpperCase())

        def policyList = currentUser.getPolicies(productType, params)
        def view = "${productType.name().toLowerCase()}PolicyList"


        policyList.each { def policy ->
            policy.metaClass.requestSource = requestSourceEnum
            policy.lang = params.lang
        }

        render view: view, model: [
            policyList  : policyList
        ]
    }

    def document(String id, String type) {

        log.info("apiV1.policies.document - encryptedId : $id, type: $type")

        Long documentId

        try {
            documentId = AESCryption.decrypt(id).toLong()
        } catch (e) {
            log.error("Unable to decrypt documentId : $id")
            render status: HttpStatus.NOT_FOUND, message: "document not found"
            return
        }

        ProductTypeEnum productType = ProductTypeEnum.findByName(type)
        def document = commonUtilService.getPolicyDocument(documentId, productType)

        User quoteUser

        if(document instanceof PolicyDocument) {
            quoteUser = document.carQuote.user
        } else if(document instanceof HealthPolicyDocument) {
            quoteUser = document.healthQuote.user
        } else if(document instanceof HomePolicyDocument) {
            quoteUser = document.homeQuote.user
        } else if(document instanceof TravelPolicyDocument) {
            quoteUser = document.travelQuote.user
        }

        if(quoteUser == springSecurityService.currentUser) {
            // Fixing full path for old documents
            String fileName = document.filename
            String urlEncodedFileName = URLEncoder.encode(fileName,"UTF-8")
            String docFullPath = document.fullPath
            boolean fileNameHasUrlUnsafeCharacters = fileName != urlEncodedFileName
            if (fileNameHasUrlUnsafeCharacters && docFullPath.contains("/${documentId}-${fileName}")) {
                docFullPath = docFullPath.replace("/${documentId}-${fileName}", "/${documentId}-${urlEncodedFileName}")
            }

            URL url = new URL(docFullPath.replace("http:", "https:"))
            URLConnection connection = url.openConnection()

            InputStream inputStream = connection.inputStream

            response.setHeader("Content-disposition", "attachment; filename=${fileName}")
            response.contentType = "application/octet-stream"
            response.outputStream << inputStream
            response.outputStream.flush()

            inputStream.close()

        } else {
            log.warn("apiV1.policy.document - Unauthorized access to document [id : $documentId] by user [id: ${springSecurityService.currentUser?.id}]")
            render status: HttpStatus.UNAUTHORIZED, message: "Unauthorized access"
        }
    }

    /**
     * Create Renewal for Car only
     *
     * @param type
     * @param id
     * @return
     */
    def requestRenewal(String type, Long id) {
        log.info("apiV1.policies.requestRenewal - type: $type, id: $id")

        def quote = commonQuoteService.getQuote(id, ProductTypeEnum.findByName(type))

        def resp = [:]

        if (quote instanceof CarQuote && renewalService.validateRenewalQuote(quote)) {

            Renewal renewal = renewalService.createRenewalEntry(quote)

            renewalService.createRenewalQuote(renewal, null, QuoteCreatorEnum.API)

            renewalService.notifyByEmail(renewal)

            resp.message = "Renewal requested"

        } else {

            if (quote instanceof CarQuote && Renewal.findByOldCarQuote(quote)) {
                resp.message = "Renewal Request already submitted"
            } else {
                resp.message = "Not valid for renewal"
            }
        }

        render resp as JSON
    }

    def requestCancellation(@RequestParameter('type') String productTypeStr, @RequestParameter('id') Long quoteId) {
        log.info("com.cover.apiV1.PolicyController#requestCancellation called with params: $params")

        productTypeStr = productTypeStr.trim()

        validateCancellationRequest(productTypeStr, quoteId)

        ProductTypeEnum productType = ProductTypeEnum.findByName(productTypeStr.toUpperCase())
        def quote = commonQuoteService.getQuote(quoteId, productType)

        apiQuoteService.handlePolicyCancellationRequest(productType, quote, params.country, params.lang)

        String responseMsg = 'Success'
        if (productType == ProductTypeEnum.CAR) {
            CrmStatusEnum crmStatus = CarQuoteDetail.findByQuote(quote).crmStatus
            String userEmail = quote.user.email

            responseMsg = crmStatus == CrmStatusEnum.CANCELLATION_DOCS_PENDING ? "We've sent an email to $userEmail. Please check it to " +
                "upload your documents or withdraw the cancellation request." : "We've sent an email to $userEmail. Please " +
                "check it for more info."
        }

        render([message: responseMsg] as JSON)
    }

    def emergencyInfo(String type, Long id) {

        def quote = quoteService.getQuoteById(id, type)

        String insurerNumber = quote.product.provider.phone
        String roadsideNumber = quote.product.provider.roadSideNumber

//        switch (quote.product.provider.id) {
//            case 1: //union
//                insurerNumber = "800 84248"
//                roadsideProvider = "Derby Auto Assistance"
//                roadsideNumber = "***********"
//                break
//            case 2: //oman
//                insurerNumber = "800 4746"
//                roadsideProvider = "Oman Insurance Company"
//                roadsideNumber = "8000 973 0048"
//                break
//            case 3: //i-insured / qatar
//                insurerNumber = "800 4742"
//                roadsideProvider = "AAA - Arabian Automobile Association"
//                roadsideNumber = "800 4900"
//                break
//            case 4: //noor
//                insurerNumber = "800 6667"
//                roadsideProvider = "IMC - International Motor Club"
//                roadsideNumber = "800 4101"
//                break
//            case 6: //watania
//                insurerNumber = "************"
//                roadsideProvider = "IMC - International Motor Club"
//                roadsideNumber = "800 4101"
//                break
//            case 7: //dubai
//                insurerNumber = "***********"
//                roadsideProvider = "IMC - International Motor Club"
//                roadsideNumber = "800 4101"
//                break
//            case 8: //al sagr
//                insurerNumber = "047 028 500"
//                roadsideProvider = "AMAN ASHARQ"
//                roadsideNumber = "800 2239"
//                break
//            case 9: //salama
//                insurerNumber = "800 725 262"
//                roadsideProvider = "Salama"
//                roadsideNumber = "800 725 262"
//                break
//            case 10: //wathba
//                insurerNumber = "600 544 040"
//                roadsideProvider = "IMC - International Motor Club"
//                roadsideNumber = "800 4101"
//                break
//            case 12: //adamjee
//                insurerNumber = "042 752 300"
//                roadsideProvider = "IMC - International Motor Club"
//                roadsideNumber = "800 4101"
//                break
//            case 17: // Al Khazna
//                insurerNumber = "800 22297"
//                roadsideProvider = "AAA - Arabian Automobile Association"
//                roadsideNumber = "800 4900"
//                break
//            case 19: // RSA
//                insurerNumber = "800 462 372"
//                roadsideProvider = "IMC - International Motor Club"
//                roadsideNumber = "800 4101"
//                break
//        }

        render([
                insurerNumber   : insurerNumber,
                roadsideNumber  : roadsideNumber
        ] as JSON)
    }

    @CompileStatic
    private void validateCancellationRequest(String productTypeStr, Long quoteId) throws RuntimeException {
        ProductTypeEnum productType = ProductTypeEnum.findByName(productTypeStr.toUpperCase())
        if (!productType) {
            String errMsg = "Api policy cancellation request contains invalid product type - '$productTypeStr'"
            log.error(errMsg)
            throw new RuntimeException(errMsg)
        }

        if (!quoteId) {
            String errMsg = "Api policy cancellation request misses the 'id' parameter"
            log.error(errMsg)
            throw new RuntimeException(errMsg)
        } else {
            def quote

            switch (productType) {
                case ProductTypeEnum.CAR:
                    quote = CarQuote.get(quoteId)
                    break
                case ProductTypeEnum.HOME:
                    quote = CarQuote.get(quoteId)
                    break
                case ProductTypeEnum.HEALTH:
                    quote = CarQuote.get(quoteId)
                    break
            }

            if (!quote) {
                String errMsg = "Api policy cancellation request contains a parameter 'id' = $quoteId, but there is no " +
                    "a ${productType.toString()} quote corresponding to that id."
                log.error(errMsg)
                throw new RuntimeException(errMsg)
            }
        }
    }
}
