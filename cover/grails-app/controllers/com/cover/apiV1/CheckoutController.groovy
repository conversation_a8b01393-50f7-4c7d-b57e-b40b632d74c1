package com.cover.apiV1

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.cover.ProviderPaymentGatewayCommand
import com.cover.api.smartdubai.SmartDubaiCancelRequestCommand
import com.cover.api.smartdubai.SmartDubaiConfirmRequestCommand
import com.cover.api.smartdubai.SmartDubaiInitiateRequestCommand
import com.cover.car.GigRateV2Service
import com.cover.checkout.DiscountCodeException
import com.cover.util.IConstant
import com.fasterxml.jackson.databind.ObjectMapper
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.DonationTypeEnum
import com.safeguard.GenderEnum
import com.safeguard.PaLead
import com.safeguard.PaymentGatewayEnum
import com.safeguard.PaymentLinkReasonEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.PaymentTransaction
import com.safeguard.PersonTitleEnum
import com.safeguard.ProductType
import com.safeguard.ProductTypeEnum
import com.safeguard.Donation
import com.safeguard.car.CarQuote
import com.safeguard.car.DiscountCode
import com.safeguard.checkoutPsp.PaymentResponseData
import com.safeguard.general.GeneralQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.life.LifeQuote
import com.safeguard.pa.PersonalAccidentService
import com.safeguard.payment.PaymentMethod
import com.safeguard.payment.ProviderPaymentMethod
import com.safeguard.payment.QuoteAdditionalPayment
import com.safeguard.pet.PetQuote
import com.safeguard.smartDubai.SmartDubaiStatusCodeEnum
import com.safeguard.travel.TravelCover
import com.safeguard.travel.TravelProductCover
import com.safeguard.travel.TravelQuote
import com.safeguard.travel.TravelQuoteCountry
import com.safeguard.travel.Traveler
import com.safeguard.util.AESCryption
import com.safeguard.whitelabel.WhiteLabelBrand
import com.safeguard.whitelabel.WhiteLabelBrandEnum
import com.safeguard.whitelabel.WhiteLabelDomain
import grails.converters.JSON
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONElement
import org.joda.time.LocalDateTime
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.http.HttpStatus

class CheckoutController {

    static namespace = "apiV1"

    static allowedMethods = [
        paynow         : ['GET'],
        discountAmount : ['POST'],
        codOrder       : ['POST'],
        paymentSummary : ['GET'],
        installmentPlans : ['GET'],
        initiateTransaction: ['POST'],
        confirmTransaction: ['POST'],
        cancelTransaction: ['POST']
    ]

    def apiQuoteService
    def checkoutService
    def commonQuoteService
    def commonUtilService
    def groovyPageRenderer
    def paymentService
    def paymentMethodService
    def paymentMethodSgService
    def payfortService
    def smartDubaiService
    def utilService
    def discountService
    def gigApiService

    def paynow() {
        log.info("apiV1.checkout.paynow - entering with [id:${params.id}, insuranceType:${params.insuranceType}]")
        String q = AESCryption.decrypt(params.id)
        Integer quoteId = Integer.parseInt(q)
        log.info("apiV1.checkout.paynow - quoteId:${quoteId}")
        ProductTypeEnum insuranceType = ProductTypeEnum.findByName(params.insuranceType)

        def resp = [:]
        def model = [:]

        if (quoteId) {
            def quote = commonQuoteService.getQuote(quoteId, insuranceType)
            log.info("quote:${quote.id} version:${quote.version}")
            if (!quote) {
                log.info "apiV1.checkout.paynow - [quoteId:${quoteId}] not found"

                String message = "Quote not found"

                redirect mapping: 'apiCheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message]
                return
            }

            if (!quote.isNotProcessed()) {
                log.warn("apiV1.checkout.paynow - quote is processed -> ${quote.id}, instanceof ${quote.class}")

                String message = "Quote already processed"

                redirect mapping: 'apiCheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message]

                return
            }

            if (!quote.productId) {
                log.warn("apiV1.checkout.paynow - no product selected -> ${quote.id}, instanceof ${quote.class}")

                String message = "No Product Selected"

                redirect mapping: 'apiCheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message]

                return
            }

            if (!apiQuoteService.isEligibleForPayment(quote)) {
                log.warn("apiV1.checkout.paynow - quote not elgibile for payment -> ${quote.id}, instanceof ${quote.class}")

                String message =  g.message(code: 'checkout.quote.modified')

                redirect mapping: 'apiCheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message]

                return
            }

            checkoutService.clearQuoteWithFromPaymentDetail(quote, insuranceType)

            String domain = grailsApplication.config.getProperty("cover.domain")

            CountryEnum countryEnum = utilService.convertToCountry(params.country)
            def lang = params.lang

            String quoteVersion = quote.version.toString()

            if (quote.product.providerId.intValue() == GigRateV2Service.PROVIDER_ID.intValue() &&
                quote.providerPolicyReference) {
                String gigReturnUrl = "${domain}/v1/$insuranceType/checkout/$countryEnum.code/$lang/${params.id}/payment"

                ProviderPaymentGatewayCommand providerPaymentGatewayCommand =
                    gigApiService.getAuthorizedPaymentLink(quote.id, quote.providerPolicyReference, gigReturnUrl)
                log.info("providerPaymentGatewayCommand:$providerPaymentGatewayCommand")
                if (providerPaymentGatewayCommand.paymentPageHtml) {
                    log.info("rendering Payment gateway page")
                    render providerPaymentGatewayCommand.paymentPageHtml
                    return
                }
            }

            String orderSummaryTemplate

            if (quote instanceof HomeQuote) {
                orderSummaryTemplate = '/homeInsurance/checkout/order_summary'
            } else if (quote instanceof HealthQuote) {
                orderSummaryTemplate = '/health/checkout/order_summary'
            } else if (quote instanceof TravelQuote) {
                orderSummaryTemplate = '/travel/checkout/order_summary'
            } else if (quote instanceof PetQuote) {
                orderSummaryTemplate = '/pet/checkout/order_summary'
            } else if (quote instanceof PaLead) {
                orderSummaryTemplate = '/pa/checkout/order_summary'
            } else if (quote instanceof CarQuote) {
                //other than UAE
                orderSummaryTemplate = '/car/checkout/order_summary'
            } else if (quote instanceof GeneralQuote) {
                //other than UAE
                orderSummaryTemplate = '/generalInsurance/checkout/order_summary'
            }

            def orderSummaryModel = checkoutService.getUAEOrderSummary(quote)
            model.orderSummaryTemplate = orderSummaryTemplate
            model.putAll(orderSummaryModel)


            ProviderPaymentMethod providerPaymentMethod =
                paymentMethodSgService.getProviderPaymentMethod(quote.product, quote.productType)

            if (providerPaymentMethod) {

                if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.NOT_AVAILABLE) {
                    notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: quote.id,
                                                                              productId: quote.product.id]

                    paymentService.addPaymentIntent(quote.productType, quote.id, quote.product.id)

                    String preparePaymentPageMapping = getPreparePaymentPageMapping(quote.productType)
                    session[IConstant.CHECKOUT_QUOTE_ID] = quote.id
                    redirect mapping: preparePaymentPageMapping, params: [insuranceType:params.insuranceType,
                                                                          country: params.country, lang: params.lang]//, base: getBaseUrl()
                    return
                } else if (providerPaymentMethod.paymentGateway == PaymentGatewayEnum.TAP_PAYMENT) {
                    log.info("TAP payment gateway found for provider - including both TAP credit card and Tabby payment options")

                    // Get all TAP_PAYMENT gateway payment methods (both CREDITCARD and TABBY)
                    List<PaymentMethod> tapPaymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountryAndPaymentGateway(
                        true, quote.productType, Country.load(countryEnum.id), PaymentGatewayEnum.TAP_PAYMENT)

                    if (tapPaymentMethods && tapPaymentMethods.size() > 0) {
                        log.info("Found ${tapPaymentMethods.size()} TAP payment methods: ${tapPaymentMethods.collect { it.name }}")

                        model.putAll([paymentMethods   : tapPaymentMethods,
                                      tapMerchantId: "${providerPaymentMethod.merchantId}",
                                      quote: quote,
                                      showMerchantPage: false,
                                      quoteVersion: quoteVersion, country: session[IConstant.SITE_COUNTRY]])

                        render view: "/apiV1/checkout/paymentMethods", model: model
                        return
                    }
                }
            }

            List<PaymentMethod> paymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountry(true,
                insuranceType, Country.load(countryEnum.id))

            //Fall back to Prepare Payment Page when no payment method found
            if (paymentMethods.size() == 0) {
                notify AsyncEventConstants.CUSTOMER_PRODUCT_FOR_PAYMENT, [quoteId: quote.id,
                                                                          productId: quote.product.id]

                paymentService.addPaymentIntent(quote.productType, quote.id, quote.product.id)
                String preparePaymentPageMapping = getPreparePaymentPageMapping(quote.productType)

                session[IConstant.CHECKOUT_QUOTE_ID] = quote.id

                redirect mapping: preparePaymentPageMapping, params: [insuranceType:params.insuranceType,
                                                                      country: params.country, lang: params.lang]
                return
            }

            def creditCardParams, installmentParams
            boolean showMerchantPage = true

            String payfortReturnUrl = null

            if (countryEnum.code == CountryEnum.UAE.code) {
                payfortReturnUrl = "${domain}/v1/$insuranceType/checkout/$countryEnum.code/$lang/${params.id}/payment"
            } else {
                //Redirection
                payfortReturnUrl = "$domain/v1/$insuranceType/checkout/$countryEnum.code/$lang/${params.id}/success"
            }

            //Is Credit Card Payment Method Available?
            PaymentMethod creditCardPaymentMethod = paymentMethods.find { it.name == PaymentMethodEnum.CREDITCARD.toString()}
            if (creditCardPaymentMethod) {
                String checkoutSuccessUrl = "${domain}/payments/response/checkoutPsp/$insuranceType/${params.id}/success?source=api"
                String checkoutFailureUrl = "${domain}/payments/response/checkoutPsp/$insuranceType/${params.id}/failure?source=api"
                String checkoutCancelUrl = "${domain}/payments/response/checkoutPsp/$insuranceType/${params.id}/cancel?source=api"

                (creditCardParams, installmentParams, showMerchantPage) =
                    paymentMethodService.getCreditCardPaymentParameters(creditCardPaymentMethod.paymentGateway, quote,
                        quote.user, countryEnum.code, utilService.getClientIp(request),
                        payfortReturnUrl, checkoutSuccessUrl, checkoutFailureUrl, checkoutCancelUrl, null)
            }

            model.putAll([paymentMethods  : paymentMethods, quote: quote,
                         quoteVersion    : quoteVersion,
                         creditCardParams: creditCardParams,
                         installmentParams: installmentParams,
                         country: countryEnum.code, lang: lang,
                         insuranceType: insuranceType.toString().capitalize(),
                         showMerchantPage:showMerchantPage, source:"api"])

            //model.donation = donationAmount
            model.returnUrl = params.returnUrl
            model.quotePaymentOnly = params.quotePaymentOnly

            if (params.quotePaymentOnly) {
                model.returnUrl = "${grailsApplication.config.grails.serverURL}/v1/${params.insuranceType}/checkout/${countryEnum.code}/${lang}/${params.id}/thankyouSummary?quotePaymentOnly=true"
            }

            render view: "/apiV1/checkout/paymentMethods", model: model

        }
    }

    /**
     * Handle COD orders
     *
     * @return
     */
    def codOrder() {
        String method = params.paymentMethod
        def quoteId = params.getLong('quoteId')

        def resp = [:]

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.findPaymentMethod(method)

        if (!paymentMethodEnum) {
            paymentMethodEnum = PaymentMethodEnum.COD
        }

        ProductTypeEnum productTypeEnum = ProductTypeEnum[params.insuranceType.toUpperCase()]
        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)

        if (!quote.isNotProcessed()) {
            log.warn("apiV1.checkout.codOrder - quote is processed -> ${quote?.id}, instanceof ${quote.class}")

            String message = "Quote already processed"

            redirect mapping: 'apiCheckoutError',
                params: [insuranceType:params.insuranceType, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }

        if (quote.isModified(params.p_quote_v)) {
            log.error("apiV1.checkout.codOrder - quote was modified:${quote.id} ")

            String message = g.message(code:'checkout.quote.modified')

            redirect mapping: 'apiCheckoutError',
                params: [insuranceType:params.insuranceType, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }

        checkoutService.offlinePayment(quote, paymentMethodEnum)

        flash.quoteId = quoteId

        redirect mapping: 'apiCheckoutThankyou',
            params: [insuranceType:params.insuranceType, id:params.id, country:params.country, lang:params.lang,
                     paidAmount:quote.totalPrice, paymentMethod: quote.paymentMethod
            ]
    }

    /**
     * Payfort redirect to this action upon credit card form submission.
     */
    def payment() {
        log.info("apiV1.checkout.payment - enterig with params:${params}")

        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        Long quoteId = utilService.decodeMerchantRef(params.merchant_reference)
        if (!quoteId) {
            String message = "Quote not found"

            redirect mapping: 'apiCheckoutError',
                params: [insuranceType:params.insuranceType, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }

        String encryptedQuoteId = AESCryption.encrypt(quoteId.toString())
        ProductTypeEnum productTypeEnum = ProductTypeEnum[params.insuranceType.toUpperCase()]
        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)

        if (isSecured) {

            if (PayfortStatusEnum.INVALID_REQUEST.toString().equals(params.status)) {
                flash.error = params.response_message
                redirect mapping:'apiCheckoutPaynow',
                    params:[country:params.country, lang:params.lang, id:encryptedQuoteId, insuranceType:params.insuranceType]
                return
            }

            try {

                if (quote.isModified(params.p_quote_v)) {
                    flash.error = g.message(code:'checkout.quote.modified')
                    log.error("apiV1.checkout.payment - quote was modified:${quote.id} ")
                    String message = g.message(code:'checkout.quote.modified')

                    redirect mapping: 'apiCheckoutError',
                        params: [insuranceType:params.insuranceType, id:params.id,
                                 country:params.country, lang:params.lang, message:message]

                    return
                }

                params.source = "api"

                PayfortResponse payfortResponse = paymentService.process(params,
                    PayfortCommandEnum.PURCHASE, utilService.getClientIp(request))

                //This is important so that we know incase of failed transaction it was installments
                if (payfortResponse.isInstallments) {
                    paymentService.setInstallments(quote, payfortResponse.numberOfInstallments)
                }

                if (payfortResponse.isThreeDeeSecure) {
                    render view: '/apiV1/checkout/redirect',
                        model: [url:payfortResponse.threeDeeSecureUrl, quote:quote, country:params.country,
                                lang: params.lang, insuranceType:params.insuranceType]
                    return

                } else if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(payfortResponse.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(payfortResponse.status)) {

                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams)
                    log.debug(".checkout.payment Redirect user to thankyou for merchantRef: ${params.merchant_reference}")
                    redirect mapping:'apiCheckoutThankyou',
                        params:[country:params.country, lang:params.lang, id:encryptedQuoteId,
                                insuranceType:params.insuranceType, paidAmount:quote.totalPrice]
                    return
                }

                if (quote instanceof CarQuote) {
                    String failureMessage = payfortResponse.responseMessage +
                        (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                    notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message: failureMessage,
                                                                             quoteId: quote.id]
                } else if (quote instanceof HomeQuote) {
                    String failureMessage = payfortResponse.responseMessage  +
                        (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                    notify AsyncEventConstants.HOME_PUSHOVER_FAILED_TRANSACTION, [message: failureMessage,
                                                                             quoteId: quote.id]
                } else if (quote instanceof HealthQuote) {
                    String failureMessage = payfortResponse.responseMessage  +
                        (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                    notify AsyncEventConstants.HEALTH_PUSHOVER_FAILED_TRANSACTION, [message: failureMessage,
                                                                                  quoteId: quote.id]
                } else if (quote instanceof TravelQuote) {
                    String failureMessage = payfortResponse.responseMessage  +
                        (payfortResponse.acquirerResponseMessage ? + ' - ' + payfortResponse.acquirerResponseMessage : '')
                    notify AsyncEventConstants.TRAVEL_PUSHOVER_FAILED_TRANSACTION, [message: failureMessage,
                                                                                    quoteId: quote.id]
                }

                flash.error = payfortResponse.responseMessage

            } catch (Exception exp) {
                log.error("Exception:", exp)
                String message = g.message(code:'checkout.general.error')

                redirect mapping: 'apiCheckoutError',
                    params: [insuranceType:params.insuranceType, id:params.id,
                             country:params.country, lang:params.lang, message:message]

                return
            }
        } else {
            log.error(".checkout.payment **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            String message = g.message(code:'checkout.general.error')

            redirect mapping: 'apiCheckoutError',
                params: [insuranceType:params.insuranceType, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }

        redirect mapping:'apiCheckoutPaynow',
            params:[country:params.country, lang:params.lang, id:encryptedQuoteId, insuranceType:params.insuranceType]
        return
    }

    /**
     * 3d Secure returns to this action
     */
    def success() {
        log.info "apiV1.checkout.success - entering with [params:${params}]"

        def customParams = checkoutService.getCustomParams(params)

        boolean isSecured = checkoutService.isSecured(params)

        Long quoteId = utilService.decodeMerchantRef(params.merchant_reference)
        if (!quoteId) {
            def resp = [:]
            String message = "Quote not found"

            redirect mapping: 'apiCheckoutError',
                params: [insuranceType:params.insuranceType, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }

        String encryptedQuoteId = AESCryption.encrypt(quoteId.toString())
        ProductTypeEnum productTypeEnum = ProductTypeEnum[params.insuranceType.toUpperCase()]
        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)

        if (isSecured) {
            if (quote && quote.isNotProcessed()) {
                paymentService.savePaymentResponse(quote, params)

                if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status)) {

                    customParams.paymentGatewayEnum = PaymentGatewayEnum.PAYFORT

                    paymentService.paid(params, customParams, true)

                    log.debug("apiv1.checkout.success Redirect user to thankyou for quoteId: ${quote.id}")
                    //All good? redirect user to thankyou page
                    redirect mapping:'apiCheckoutThankyou',
                        params:[country:params.country, lang:params.lang, id:encryptedQuoteId,
                                insuranceType:params.insuranceType, paidAmount:quote.totalPrice,
                                paymentMethod:quote.paymentMethod]
                    return
                } else {
                    String failureMessage = params.response_message +
                        (params.acquirer_response_message ? " - " + params.acquirer_response_message : "")
                    notify AsyncEventConstants.PUSHOVER_FAILED_TRANSACTION, [message:failureMessage, quoteId: quote.id]
                    session[IConstant.CHECKOUT_QUOTE_ID] = quote.id
                    flash.error = params.response_message
                    log.error "#### ERROR #### -> ${params.response_message} for ${quote.id} with status-> ${params.status} #### ERROR ####"
                }
            } else {
                log.warn(".checkout.success quote is isProcessed -> ${quote?.id}")
                log.debug(".checkout.success.is.processed still redirecting to thankyou")
                redirect mapping:'apiCheckoutThankyou',
                    params:[country:params.country, lang:params.lang, id:encryptedQuoteId,
                            insuranceType:params.insuranceType, paidAmount:quote.totalPrice,
                            paymentMethod:quote.paymentMethod]
                return
            }

        } else {
            log.error(".checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            def resp = [:]
            resp.message = g.message(code:'checkout.general.error')
            response.status = HttpStatus.UNAUTHORIZED.value()
            render resp as JSON
            return
        }

        redirect mapping:'apiCheckoutPaynow',
            params:[country:params.country, lang:params.lang, id:encryptedQuoteId, insuranceType:params.insuranceType]
        return
    }

    /**
     * Payment success action
     */
    def thankyou() {
        log.info("apiV1.checkout.thankyou - entering with [id:${params.id}, insuranceType:${params.insuranceType}]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(params.id))
        String insuranceType = params.insuranceType

        log.info("apiV1.checkout.paynow - quoteId:${quoteId}")

        ProductTypeEnum productTypeEnum = ProductTypeEnum[insuranceType.toUpperCase()]
        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)

        log.debug("apiV1.checkout.thankyou - quote found with id ${quote.id}")

        if (quote instanceof CarQuote) {

            Country country = Country.get(CountryEnum.findCountry(params.country)?.getId())

            String discountCode
            if (country) {
                discountCode = checkoutService.getOrCreateDiscountCode(ProductType.CAR, country, quote.user, quoteId)
            } else {
                log.warn("Could not find a Country object corresponding to country code = $params.country. Discount code " +
                    "can not be created without the country information, skipping that step.")
            }

            def hash = AESCryption.encrypt(quote.id.toString())

            notify(
                AsyncEventConstants.CAR_QUOTE_PURCHASED,
                [quoteId: quote.id, country: CountryEnum.findCountry(params.country), lang: params.lang, discountCode: discountCode,
                 hash: hash]
            )

        } else if (quote instanceof HealthQuote) {
            notify AsyncEventConstants.HEALTH_QUOTE_PURCHASED, [quoteId: quote.id]
        } else if (quote instanceof HomeQuote) {
            notify AsyncEventConstants.EMAIL_HOME_QUOTE_PURCHASED, [quoteId: quote.id, lang: params.lang]
        } else if (quote instanceof TravelQuote) {
            notify AsyncEventConstants.EMAIL_TRAVEL_QUOTE_PURCHASED, [quoteId: quote.id, lang: params.lang]
        } else if (quote instanceof PetQuote) {
            //String encryptedId = AESCryption.encrypt(quote.id.toString())
            CountryEnum countryEnum = utilService.convertToCountry(params.country)

            //sending pet quote email
            notify AsyncEventConstants.EMAIL_PET_QUOTE_PURCHASED, [quoteId:quote.id,
                                                           lang   :quote.lang,
                                                           country:countryEnum.code]

            //sending quote information to salama email
            notify AsyncEventConstants.EMAIL_PET_QUOTE_PURCHASED_SALAMA, [quoteId:quote.id,
                                                           lang   :quote.lang,
                                                           country:countryEnum.code]

            /*
            //sending pet quote WhatsApp
            String recipient = quote.user.mobile
            if (!recipient.contains("+971")) {
                recipient = "+971".concat(recipient)
            }
            notify AsyncEventConstants.WHATSAPP_NOTIFICATION_TRIGGER, [templateName:"pet_insurance_quote",
                                                                       quoteId:encryptedId,
                                                                       lang:quote.lang,
                                                                       country:countryEnum.code,
                                                                       recipient:recipient,
                                                                       type: "pet-insurance"]
          */
        } else if (quote instanceof PaLead) {
            notify AsyncEventConstants.PA_YOUTH_SALE, [merchantReference: quote.merchantRef]
        } else if (quote instanceof GeneralQuote) {
            notify AsyncEventConstants.GENERAL_QUOTE_PURCHASED, [quoteId: quote.id, lang: params.lang]
        }

        def model = [:]
        model.quote = quote

        render view: "/apiV1/checkout/success", model: model

        return
    }

    /**
     * Return payment detail, includes order summary and thankyou text
     *
     * @return
     */
    def thankyouSummary() {
        log.info("apiV1.checkout.thankyouSummary - entering with [id:${params.id}, insuranceType:${params.insuranceType}]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(params.id))
        String insuranceType = params.insuranceType

        log.info("apiV1.checkout.thankyouSummary - quoteId:${quoteId}")

        ProductTypeEnum productTypeEnum = ProductTypeEnum[insuranceType.toUpperCase()]
        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)
        log.debug("apiV1.checkout.thankyouSummary - quote found with id ${quote.id}")

        Donation donation = Donation.findByQuoteIdAndQuoteTypeIdAndDonationType(quote.id, productTypeEnum.value(), DonationTypeEnum.CHARITY)

        def model = [quote:quote, lang: params.lang]
        model.donation = donation ? donation.amount : 0
        String orderSummaryTemplate

        if (!(quote instanceof CarQuote) || quote.isUAEQuote()) {
            if (quote instanceof HomeQuote) {
                orderSummaryTemplate = '/homeInsurance/checkout/order_summary'
            } else if (quote instanceof HealthQuote) {
                orderSummaryTemplate = '/health/checkout/order_summary'
            } else if (quote instanceof LifeQuote) {
                orderSummaryTemplate = '/life/checkout/order_summary'
            } else if (quote instanceof TravelQuote) {
                orderSummaryTemplate = '/travel/checkout/order_summary'
            } else if (quote instanceof PetQuote) {
                orderSummaryTemplate = '/pet/checkout/order_summary'
            } else if (quote instanceof PaLead) {
                orderSummaryTemplate = '/pa/checkout/order_summary'
            } else if (quote instanceof GeneralQuote) {
                orderSummaryTemplate = '/generalInsurance/checkout/order_summary'
            } else if (quote instanceof CarQuote) {
                orderSummaryTemplate = '/car/checkout/order_summary'

                Country country = Country.get(CountryEnum.findCountry(params.country)?.getId())
                model.discountCode = checkoutService.getOrCreateDiscountCode(ProductType.CAR, country, quote.user, quoteId)
            }

            def orderSummaryModel = checkoutService.getUAEOrderSummary(quote)
            model.putAll(orderSummaryModel)

        } else {
            orderSummaryTemplate = "/car/checkout/order_summary_${quote.quoteCountry.code.toLowerCase()}"

            def orderSummaryModel = checkoutService.getLBNOrderSummary(quote)
            model.putAll(orderSummaryModel)
        }

        model.orderSummaryTemplate = orderSummaryTemplate

        def resp = [:]
        resp.paidAmount = quote.totalPrice
        resp.currency = quote.currency
        if (quote instanceof PetQuote) {
            resp.pet = [pet_name: quote.pet.petName, policy_reference:quote.policyReference]
        } else {
            resp.htmlMessage = groovyPageRenderer.render([view: "/apiV1/checkout/thankyou", model:model])//.replaceAll("\n", "")
        }

        if (params.quotePaymentOnly) {
            render resp.htmlMessage
            return
        }

        render resp as JSON
    }

    /**
     *
     * @param discountCode
     * @param amount
     * @param productTypeId
     * @param productId
     */
    def discountAmount( String country, String insuranceType ) {

        Map model
        def jsonParams = request.JSON
        String discountCode = jsonParams.discountCode
        Double amount = jsonParams.amount
        ProductTypeEnum productTypeEnum = ProductTypeEnum[insuranceType.toUpperCase()]
        Long productId = jsonParams.productId

        log.info "apiV1.checkout.discount - entering with discountCode: $discountCode, amount: $amount, productType: ${productTypeEnum.name()}, productId: $productId"

        try {
            List discountList = checkoutService.getDiscount(discountCode, amount, productTypeEnum.value(), productId, country)
            model = [discountAmount: discountList[0]]
        } catch (DiscountCodeException discountException) {
            log.warn(".discount DiscountCode exception(${discountException.messageKey}) while getting discount for code:${discountCode} and amount:${amount}")
            model = [error:true, message:g.message(code:discountException.messageKey, args: discountException.args)]
        }

        render model as JSON

    }

    /**
     * Error page handler
     *
     * @return
     */
    def error() {
        log.info("apiV1.checkout.error - entering with [params:${params}]")

        if (params.id.toString() != null && params.insuranceType != null){

            Integer quoteId = Integer.parseInt(AESCryption.decrypt(params.id.toString()))
            ProductTypeEnum insuranceType = ProductTypeEnum.findByName(params.insuranceType)
            def quote = commonQuoteService.getQuote(quoteId, insuranceType)

            if (quote != null){

                log.info("apiV1.checkout.error - entering with [params:${params}, quote:${quote}]")
                render view: "/apiV1/checkout/error", model: [message: params.message,
                                                              quote: quote, params: params,
                                                              lang: params.lang, country: params.country]
                return
            }

            log.info("apiV1.checkout.error - entering with [params:${params}]")

            render "error"
            return
        }

        render "error"
        return
    }

    /**
     * Cancel payment page handler
     *
     * @return
     */
    def cancelPayment () {
        log.info("apiV1.checkout.cancelPayment - entering with [params:${params}]")

        render view:"/apiV1/checkout/cancel"
        return
    }

    /**
     * Make sure you think 10 times before calling this API.
     * This will send purchase email to customer
     *
     * @return
     */
    def sendPurchaseEmail() {
        log.info("apiV1.checkout.sendPurchaseEmail - params:${params}")
        if (params.secret == "Y4llaComp@43") {
            if (params.insuranceType == "car") {
                if (params.quoteId) {
                    /*notify AsyncEventConstants.CAR_QUOTE_PURCHASED, [quoteId: params.quoteId,
                                                                 country: CountryEnum.findCountry(params.country),
                                                                 lang   : params.lang,
                                                                 hash   : AESCryption.encrypt(params.quoteId)]*/

                   /* notify AsyncEventConstants.CAR_QUOTE_TPL_RENEWAL_REMINDER, [
                        quoteId: params.quoteId
                    ]*/

                    notify AsyncEventConstants.CAR_QUOTE_TPL_RENEWAL_REMINDER, [
                        quoteId: params.quoteId, paymentLink: "https://pay.sandbox.checkout.com/link/pl_gRLjB9DEiIwn"
                    ]

                } else if (params.renewalId) {
                    notify AsyncEventConstants.CAR_QUOTE_RENEWAL_UPLOADED, [renewalId: Long.parseLong(params.renewalId)]
                }
            } else if (params.insuranceType == "health") {
                notify AsyncEventConstants.HEALTH_QUOTE_PURCHASED, [quoteId: Long.parseLong(params.quoteId + "")]
            }

            render "Sending email for quote:${params.quoteId}"
        } else {
            render "Not Allowed to send email"
        }
    }

    /**
     * Generate quote payment summary
     *
     */
    def paymentSummary() {
        log.info("apiV1.checkout.paymentSummary - entering with [type:${params.insuranceType}, id:${params.id}]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(params.id))

        ProductTypeEnum productTypeEnum = ProductTypeEnum.findByName(params.insuranceType)
        log.info("quoteId:$quoteId, product:$productTypeEnum")
        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)

        if (!quote) {
            def resp = [:]
            String message = "Quote not found"

            redirect mapping: 'apiCheckoutError',
                params: [insuranceType:params.insuranceType, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }

        def summaryMap = checkoutService.getUAEOrderSummary(quote)

        def model = [:]

        if (summaryMap.provider) {
            model.provider = summaryMap.provider
        }

        if (quote instanceof CarQuote) {
            model.carQuote = quote

            model.bulletServiceAddon = summaryMap.bulletServiceAddon ?: [:]
            model.homeInsuranceAddon = summaryMap.homeInsuranceAddon ?: [:]
            model.lifeInsuranceAddon = summaryMap.lifeInsuranceAddon ?: [:]
            model.bulletServiceAddonVat = summaryMap.bulletServiceAddonVat ?: 0.0
            model.homeInsuranceAddonVat = summaryMap.homeInsuranceAddonVat ?: 0.0
            model.lifeInsuranceAddonVat = summaryMap.lifeInsuranceAddonVat ?: 0.0
            model.carQuoteAddonList = summaryMap.carQuoteAddonList ?: []

            model.policyStartDate = g.formatDate(date: quote.policyStartDate.toDate(), format: 'dd-MM-yyyy')
            if (!quote.isUAEQuote()) {
                model.policyEndDate = g.formatDate(date: quote.policyStartDate.plusMonths(12).toDate(), format: 'dd-MM-yyyy')
            } else {
                model.policyEndDate = g.formatDate(date: quote.policyStartDate.plusMonths(13).toDate(), format: 'dd-MM-yyyy')
            }
            model.subTotalVat = summaryMap.subTotalVat ?: 0.0
            model.c4meFeeVATWithoutDiscount = commonUtilService.getVATAmount(quote.c4meFee)

        } else if (quote instanceof HealthQuote) {
            model.healthQuote = quote
            model.c4meFeeVATWithoutDiscount = commonUtilService.getVATAmount(quote.c4meFee)

        } else if (quote instanceof  HomeQuote) {
            model.homeQuote = quote
            model.addonList = summaryMap.addonList

        } else if (quote instanceof TravelQuote) {
            model.travelQuote = quote
            model.travelers = []

            if (quote.travelerList?.size()) {
                model.travelers = quote.travelerList?.collect { Traveler traveler ->
                    [
                        title         : traveler.titleId ? PersonTitleEnum.getById(traveler.titleId)?.displayValue : null,
                        firstName     : traveler.firstName,
                        lastName      : traveler.lastName,
                        dob           : traveler.birthDate?.toString("dd-MM-yyyy"),
                        gender        : traveler.genderId ? GenderEnum.getById(traveler.genderId)?.displayValue : null,
                        passportNumber: traveler.identificationNumber,
                        isPolicyHolder: traveler.isPolicyHolder
                    ]
                }
            }
            model.travelingCountries = []
            if (quote.travellingCountries?.size()) {
                model.travelingCountries = quote.travellingCountries?.collect { TravelQuoteCountry quoteCountry ->
                    [
                        name: quoteCountry.country.name
                    ]
                }
            }


            if (quote.product) {
                List<TravelProductCover> productCovers = TravelProductCover.findAllByProduct(quote.product)

                LinkedHashMap covers = productCovers.collectEntries { TravelProductCover productCover ->
                    TravelCover cover = productCover.travelCover

                    [
                        "${cover.slug}": [
                            "name"  : "${cover.coverEn}",
                            "value" : productCover.value,
                            "parent": cover.parent ? ["slug": cover.parent.slug, "name": cover.parent.coverEn] : null
                        ]
                    ]
                }
                model.covers = covers

            }

        }

        model.subTotal = summaryMap.subTotal ?: 0.0
        model.discountCode = ""
        DiscountCode globalDiscountCode = discountService.getApplicableDiscountCode(CountryEnum.findCountry(params.country), ProductTypeEnum.findByName(params.insuranceType).value(), null, null, null, null, WhiteLabelBrand.read(WhiteLabelBrandEnum.YALLACOMPARE.id))
        // verified the working for this change through postman. its checking if global discount is applied then dont send discountCode code on mobile app
        if (quote.discountCode && quote.discountCode.id != globalDiscountCode?.id) {
            model.discountCode = quote.discountCode.code
        }

        log.info("apiV1.checkout.paymentSummary - model:${model}")

        render view:"/apiV1/${params.insuranceType}/_summary", model: model
    }

    /**
     * Get Installment plans
     * @param country
     * @param lang
     * @param totalAmount
     */
    def installmentPlans(String insuranceType, String country, String lang, String currency) {
        BigDecimal totalAmount = new BigDecimal(params.totalAmount)
        String bin = params.bin
        log.info("apiV1.checkout.installmentPlans - " +
            "entering with [insuranceType:$insuranceType, country:$country, lang:$lang, amount:$totalAmount]")

        def installmentPlans = checkoutService.getInstallmentPlans(country, lang, totalAmount, bin, currency)

        render installmentPlans as JSON
    }

    /**
     * Update installment plan against the quote
     *
     * @param insuranceType
     * @param country
     * @param lang
     * @param planCode
     * @param issuerCode
     * @param quoteId
     */
    def updateInstallmentPlan(String insuranceType, String issuerCode, String planCode, String id) {
        log.info("api.checkout.updateInstallmentPlan - " +
            "entering with [insuranceType:$insuranceType, planCode:$planCode, issuerCode:$issuerCode, id:$id]")

        Long quoteId = Long.parseLong(AESCryption.decrypt(id))
        String numberOfInstallments = params.noOfInstallments

        ProductTypeEnum productTypeEnum = ProductTypeEnum.findByName(insuranceType)
        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)

        if (quote.isNotProcessed()) {

            def quoteDetail = commonQuoteService.getQuoteDetail(quote, productTypeEnum)
            checkoutService.updateInstallmentPlan(quote, quoteDetail, issuerCode, planCode, numberOfInstallments)

            render "ok"
        } else {
            render "already processed"
        }
    }

    /**
     * Initiate transaction against the quote. This initializes transaction and return merchant reference (unique
     * transaction reference everytime)
     *
     */
    def sdgInitiateTransaction() {
        log.info("api.checkout.initiateTransaction - entering with [params:$params, JSON:${request.JSON}]")

        if (!request.JSON || !request.JSON.initiateServiceRequest ||
            !request.JSON.initiateServiceRequest.properties ||
            !request.JSON.initiateServiceRequest.properties.property ||
            !request.JSON.initiateServiceRequest.messageDigest) {

            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            render view:"initiateTransaction", model:[merchantReference: "",
                                                      statusCode: SmartDubaiStatusCodeEnum.FAILED.getCode(),
                                                      statusDescription: "Invalid Request",
                                                      transactionTime: "",
                                                      transactionAmount: ""]
            return

        }

        JSONArray quoteProperties = request.JSON.initiateServiceRequest.properties.property
        String hash = request.JSON.initiateServiceRequest.messageDigest
        String statusCode = ""
        String statusDescription = ""
        String transactionTime = LocalDateTime.now().toString("yyyy-MM-dd'T'HH:mm:ss")

        SmartDubaiInitiateRequestCommand initiateRequest =
            smartDubaiService.getInitiateRequest(quoteProperties, request.JSON)

        try {
            //TODO: Validate and return missing request parameter error

            boolean isRequestValid = smartDubaiService.validateHash(quoteProperties, hash)

            //CheckSum failed?
            if (!isRequestValid) {
                log.info("api.checkout.initiateTransaction - Checksum failed to match. quoteProperties:${quoteProperties}")

                statusCode = SmartDubaiStatusCodeEnum.CHECKSUM_FAILED.getCode()
                statusDescription = g.message(code:"smartDubai.checksum.failed")

                render view:"initiateTransaction", model:[merchantReference:"",
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: ""]
                return
            }

            Integer quoteId = Integer.parseInt(AESCryption.decrypt(initiateRequest.quoteId))

            CarQuote carQuote = commonQuoteService.getQuote(quoteId, ProductTypeEnum.CAR)

            if (!carQuote || !carQuote.isNotProcessed()) {
                log.info("api.checkout.initiateTransaction - Quote already processed, quoteProperties:${quoteProperties}")
                statusCode = SmartDubaiStatusCodeEnum.FAILED.getCode()

                if (!carQuote) {
                    statusDescription = g.message(code: "smartDubai.quote.notFound")
                } else {
                    statusDescription = g.message(code: "smartDubai.quote.alreadyProcessed")
                }

                render view:"initiateTransaction", model:[merchantReference: "",
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: ""]
                return
            }

            if (!carQuote.product) {
                log.info("api.checkout.initiateTransaction - No Product is associated. carQuote:${carQuote.id}, " +
                    "quoteProperties:${quoteProperties}")

                statusCode = SmartDubaiStatusCodeEnum.FAILED.getCode()
                statusDescription = g.message(code: "smartDubai.quote.product.notFound")

                render view:"initiateTransaction", model:[merchantReference: "",
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: ""]
                return
            }

            //Could be string or number
            BigDecimal requestTransactionAmount = quoteProperties.find { it.name == "transaction-amount" }?.value
            if (!requestTransactionAmount ||
                carQuote.totalPrice != requestTransactionAmount) {
                log.info("api.checkout.initiateTransaction - Payment amount not matched. carQuote:${carQuote.id}, " +
                    "quoteProperties:${quoteProperties}")

                statusCode = SmartDubaiStatusCodeEnum.FAILED.getCode()
                statusDescription = g.message(code: "smartDubai.quote.amount.notMatched")

                render view:"initiateTransaction", model:[merchantReference: "",
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: ""]
                return
            }

            paymentService.savePaymentResponse(carQuote, request.JSON.toString())

            //TODO: Validate with Quote is ready for payment? Product id available!

            PaymentTransaction paymentTransaction = checkoutService.initializePaymentTransaction(carQuote, initiateRequest)

            statusCode = SmartDubaiStatusCodeEnum.SUCCESS.getCode()
            statusDescription = g.message(code: "smartDubai.request.success")

            render view:"initiateTransaction", model:[merchantReference: paymentTransaction.merchantReference,
                                                      statusCode: statusCode, statusDescription: statusDescription,
                                                      transactionTime: transactionTime,
                                                      transactionAmount: paymentTransaction.amount]

        }  catch (Exception e) {
            log.error("api.checkout.confirmTransaction - Unable to process. quoteProperties:${quoteProperties}", e)

            statusCode = SmartDubaiStatusCodeEnum.SERVER_ERROR.getCode()
            statusDescription = g.message(code: "smartDubai.server.error")

            render view:"initiateTransaction", model:[merchantReference: "",
                                                      statusCode: statusCode, statusDescription: statusDescription,
                                                      transactionTime: transactionTime,
                                                      transactionAmount: initiateRequest.transactionAmount]
        }
    }

    /**
     * Confirm transaction. Mark as paid if transaction status is success
     *
     */
    def sdgConfirmTransaction() {
        log.info("api.checkout.confirmTransaction - entering with params:[$params, JSON:${request.JSON}]")

        if (!request.JSON || !request.JSON.confirmServiceRequest ||
            !request.JSON.confirmServiceRequest.properties ||
            !request.JSON.confirmServiceRequest.properties.property ||
            !request.JSON.confirmServiceRequest.messageDigest) {

            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            render view:"confirmTransaction", model:[merchantReference: "",
                                                      statusCode: SmartDubaiStatusCodeEnum.FAILED.getCode(),
                                                      statusDescription: "Invalid Request",
                                                      transactionTime: "",
                                                      transactionAmount: ""]
            return

        }

        JSONArray quoteProperties = request.JSON.confirmServiceRequest.properties.property
        String hash = request.JSON.confirmServiceRequest.messageDigest
        String statusCode = ""
        String statusDescription = ""
        String transactionTime = LocalDateTime.now().toString("yyyy-MM-dd'T'HH:mm:ss")

        SmartDubaiConfirmRequestCommand confirmRequest =
            smartDubaiService.getConfirmRequest(quoteProperties, request.JSON)

        try {

            //TODO: Validate and return missing request parameter error

            boolean isRequestValid = smartDubaiService.validateHash(quoteProperties, hash)

            //CheckSum failed?
            if (!isRequestValid) {
                log.info("api.checkout.initiateTransaction - Checksum failed to match. quoteProperties:${quoteProperties}")

                statusCode = SmartDubaiStatusCodeEnum.CHECKSUM_FAILED.getCode()
                statusDescription = g.message(code:"smartDubai.checksum.failed")

                render view:"confirmTransaction", model:[merchantReference: confirmRequest.merchantReference,
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: confirmRequest.epayAmount]
                return
            }

            PaymentTransaction paymentTransaction = PaymentTransaction.findByMerchantReference(confirmRequest.merchantReference)
            CarQuote carQuote = paymentTransaction?.carQuote

            if (!paymentTransaction || !carQuote || !carQuote.isNotProcessed()) {
                log.info("api.checkout.confirmTransaction - Quote already processed. quoteProperties:${quoteProperties}")
                statusCode = SmartDubaiStatusCodeEnum.FAILED.getCode()

                if (!paymentTransaction) {
                    statusDescription = g.message(code: "smartDubai.transaction.notFound")
                } else if (!carQuote) {
                    statusDescription = g.message(code: "smartDubai.quote.notFound")
                } else if (carQuote.paidDate &&
                    carQuote.paymentStatus in [PaymentStatusEnum.PAID, PaymentStatusEnum.RECEIVED,
                                               PaymentStatusEnum.VALIDATION_REQUIRED, PaymentStatusEnum.REFERRAL,
                                               PaymentStatusEnum.ISSUED]) {
                    //If Already Paid, then send success response
                    statusCode = SmartDubaiStatusCodeEnum.SUCCESS.getCode()
                    statusDescription = g.message(code: "smartDubai.request.success")
                } else {
                    statusDescription = g.message(code: "smartDubai.quote.alreadyProcessed")
                }

                render view:"confirmTransaction", model:[merchantReference: confirmRequest.merchantReference,
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: confirmRequest.epayAmount]
                return
            }

            if (!confirmRequest.epayAmount ||
                confirmRequest.epayAmount && carQuote.totalPrice != new BigDecimal(confirmRequest.epayAmount)) {
                log.info("api.checkout.confirmTransaction - Payment amount not matched. carQuote:${carQuote.id}, " +
                    "quoteProperties:${quoteProperties}")

                statusCode = SmartDubaiStatusCodeEnum.FAILED.getCode()
                statusDescription = g.message(code: "smartDubai.quote.amount.notMatched")

                render view:"confirmTransaction", model:[merchantReference: confirmRequest.merchantReference,
                                                         statusCode: statusCode, statusDescription: statusDescription,
                                                         transactionTime: transactionTime,
                                                         transactionAmount: confirmRequest.epayAmount]
                return
            }

            paymentService.savePaymentResponse(carQuote, request.JSON.toString())

            paymentTransaction = checkoutService.confirmPaymentTransaction(carQuote, confirmRequest)

            //Notify YC team for payment from SmartDubai
            notify AsyncEventConstants.QUOTE_PAYMENT_RECEIVED_EMAIL_TO_YC, [
                quote: carQuote,
                recipientEmail: grailsApplication.config.smartDubai.ycEmailForSmartDubaiPaymentNotification
            ]

            notify AsyncEventConstants.QUOTE_PAYMENT_RECEIVED_SMS_TO_YC, [carQuoteId: carQuote.id]


            statusCode = SmartDubaiStatusCodeEnum.SUCCESS.getCode()
            statusDescription = g.message(code: "smartDubai.request.success")

            render view:"confirmTransaction", model:[merchantReference: paymentTransaction.merchantReference,
                                                      statusCode: statusCode, statusDescription: statusDescription,
                                                      transactionTime: transactionTime,
                                                      transactionAmount: paymentTransaction.epayAmount]
        } catch (Exception e) {
            log.error("api.checkout.confirmTransaction - Unable to process. quoteProperties:${quoteProperties}", e)

            statusCode = SmartDubaiStatusCodeEnum.SERVER_ERROR.getCode()
            statusDescription = g.message(code: "smartDubai.server.error")

            render view:"confirmTransaction", model:[merchantReference: confirmRequest.merchantReference,
                                                      statusCode: statusCode, statusDescription: statusDescription,
                                                      transactionTime: transactionTime,
                                                      transactionAmount: confirmRequest.epayAmount]
        }

    }

    /**
     * Mark on the transaction as Cancel. Quote remains in DRAFT status
     *
     */
    def sdgCancelTransaction() {
        log.info("api.checkout.cancelTransaction - entering with params:[$params]")

        if (!request.JSON || !request.JSON.cancelServiceRequest ||
            !request.JSON.cancelServiceRequest.properties ||
            !request.JSON.cancelServiceRequest.properties.property ||
            !request.JSON.cancelServiceRequest.messageDigest) {

            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            render view:"cancelTransaction", model:[merchantReference: "",
                                                     statusCode: SmartDubaiStatusCodeEnum.FAILED.getCode(),
                                                     statusDescription: "Invalid Request",
                                                     transactionTime: "",
                                                     transactionAmount: ""]
            return

        }

        JSONArray quoteProperties = request.JSON.cancelServiceRequest.properties.property
        String hash = request.JSON.cancelServiceRequest.messageDigest
        String statusCode = ""
        String statusDescription = ""
        String transactionTime = LocalDateTime.now().toString("yyyy-MM-dd'T'HH:mm:ss")

        SmartDubaiCancelRequestCommand cancelRequest =
            smartDubaiService.getCancelRequest(quoteProperties, request.JSON)

        try {

            //TODO: Validate and return missing request parameter error

            boolean isRequestValid = smartDubaiService.validateHash(quoteProperties, hash)

            //CheckSum failed?
            if (!isRequestValid) {
                log.info("api.checkout.cancelTransaction - Checksum failed to match. quoteProperties:${quoteProperties}")

                statusCode = SmartDubaiStatusCodeEnum.CHECKSUM_FAILED.getCode()
                statusDescription = g.message(code:"smartDubai.checksum.failed")

                render view:"cancelTransaction", model:[merchantReference: cancelRequest.merchantReference,
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: cancelRequest.epayAmount]
                return
            }

            CarQuote carQuote = PaymentTransaction.findByMerchantReference(cancelRequest.merchantReference)?.carQuote

            if (!carQuote || !carQuote.isNotProcessed() ) {
                log.info("api.checkout.cancelTransaction - Quote already processed. quoteProperties:${quoteProperties}")
                statusCode = SmartDubaiStatusCodeEnum.FAILED.getCode()

                if (!carQuote) {
                    statusDescription = g.message(code: "smartDubai.transaction.notFound")
                } else {
                    statusDescription = g.message(code: "smartDubai.quote.alreadyProcessed")
                }

                render view:"cancelTransaction", model:[merchantReference: cancelRequest.merchantReference,
                                                          statusCode: statusCode, statusDescription: statusDescription,
                                                          transactionTime: transactionTime,
                                                          transactionAmount: cancelRequest.epayAmount]
                return
            }

            paymentService.savePaymentResponse(carQuote, request.JSON.toString())

            PaymentTransaction paymentTransaction = checkoutService.cancelPaymentTransaction(carQuote, cancelRequest)

            statusCode = SmartDubaiStatusCodeEnum.SUCCESS.getCode()
            statusDescription = g.message(code: "smartDubai.request.success")

            render view:"cancelTransaction", model:[merchantReference: paymentTransaction.merchantReference,
                                                      statusCode: statusCode, statusDescription: statusDescription,
                                                      transactionTime: transactionTime,
                                                      transactionAmount: paymentTransaction.epayAmount]
        } catch (Exception e) {
            log.error("api.checkout.cancelTransaction - Unable to process. quoteProperties:${quoteProperties}", e)

            statusCode = SmartDubaiStatusCodeEnum.SERVER_ERROR.getCode()
            statusDescription = g.message(code: "smartDubai.server.error")

            render view:"cancelTransaction", model:[merchantReference: cancelRequest.merchantReference,
                                                      statusCode: statusCode, statusDescription: statusDescription,
                                                      transactionTime: transactionTime,
                                                      transactionAmount: cancelRequest.epayAmount]
        }

    }

    /**
     * Show Prepare Payment Page
     * @return
     */
    def preparePaymentPage() {
        log.info(".preparePaymentPage - params:$params")

        def quoteId = flash.quoteId

        if (!quoteId) {
            //Try looking into quote parameter
            quoteId = session[IConstant.CHECKOUT_QUOTE_ID]
        }

        if (quoteId && quoteId instanceof String) {
            quoteId = Long.parseLong(quoteId)
        }

        log.debug(".checkout.preparePaymentPage entering with quoteId: ${quoteId}")

        //if params are null redirect to homeInsurance page
        if (!quoteId) {
            String message = "Quote not found"

            redirect mapping: 'apiCheckoutError',
                params: [insuranceType:params.insuranceType, id:params.id,
                         country:params.country, lang:params.lang, message:message]
            return
        }

        ProductTypeEnum productTypeEnum = ProductTypeEnum.findByName(params.insuranceType)

        def quote = commonQuoteService.getQuote(quoteId, productTypeEnum)

        String hash = AESCryption.encrypt(quote.id.toString())

        WhiteLabelDomain whiteLabelDomain = session[IConstant.WHITE_LABEL_DOMAIN] as WhiteLabelDomain

        render view: '/payment/preparePayment', model: [
            quote               : quote,
            lang                   : LocaleContextHolder.locale.language,
            hash                   : hash,
            whitelabel             : whiteLabelDomain.brand
        ]
    }

    /**
     * Get Prepare payment page mapping based on product type enum
     *
     * @param productType
     * @return
     */
    private String getPreparePaymentPageMapping(ProductTypeEnum productType) {
        String mapping = 'apiV1PreparePaymentPage'

        switch (productType) {
            case ProductTypeEnum.CAR:
                mapping = 'carPreparePaymentPage'
                break
            case ProductTypeEnum.HEALTH:
                mapping = 'healthPreparePaymentPage'
                break
            case ProductTypeEnum.HOME:
                mapping = 'homePreparePaymentPage'
                break
        }
        return mapping
    }

}
