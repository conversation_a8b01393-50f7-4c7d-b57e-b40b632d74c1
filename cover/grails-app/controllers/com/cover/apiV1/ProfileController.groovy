package com.cover.apiV1

import com.cover.api.CreateProfileCommand
import com.cover.api.ResetPasswordCommand
import com.cover.api.UpdateProfileCommand
import com.safeguard.CountryEnum
import com.safeguard.PaLead
import com.safeguard.PaymentPlanEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.User
import com.safeguard.life.LifeQuote
import com.safeguard.payment.RecurringPayment
import com.safeguard.pet.PetQuote
import com.safeguard.util.AESCryption
import grails.converters.JSON
import grails.plugins.rest.client.RestBuilder
import grails.plugins.rest.client.RestResponse
import org.grails.web.json.JSONElement
import org.joda.time.LocalDate
import org.joda.time.LocalDateTime
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.springframework.http.HttpStatus
import org.joda.time.Days

class ProfileController {

    static namespace = "apiV1"

    static allowedMethods = [
        signIn                      : ["POST", "OPTIONS"],
        create                      : ["POST"],
        view                        : ["GET"],
        update                      : ["POST"],
        requestVerificationEmail    : ["GET"],
        requestOTP                  : ["GET"],
        verifyEmail                 : ["GET"],
        forgotPassword              : ["POST"],
        notifications               : ["GET"],
        unread                      : ["GET"],
        checkUserResetPasswordStatus: ["GET"],
        listPendingRecurringPayments: ["GET"]
    ]

    def commonUtilService
    def profileService
    def recurringPaymentService
    def springSecurityService

    def create(CreateProfileCommand cmd) {

        log.info("apiV1.profile.create - name: ${cmd.name}, email: ${cmd.email}, mobile: ${cmd.mobile}")

        if (!cmd.validate()) {

            def errors = []
            cmd.errors.fieldErrors.each { errors.push(g.message(error: it) as String) }

            response.status = 400
            render view: "/apiV1/profile/create", model: [
                message: "Validation errors",
                errors : errors
            ]
            return
        }

        def result = profileService.create(cmd)

        response.status = result.responseStatus
        render view: "/apiV1/profile/create", model: [
            id     : result.userId,
            message: result.message
        ]
    }

    def view() {
        log.info("apiV1.profile.show")

        User user = springSecurityService.currentUser
        DateTimeFormatter formatter = DateTimeFormat.forPattern("dd-MM-yyyy")
        Long callingCode = null

        CountryEnum country = CountryEnum.UAE
        if (user.country) {
            country = CountryEnum.findCountryById(user.country.id)
            callingCode = country.callingCode
        }

        String formattedPhone = commonUtilService.extractPhoneNumber(user.mobile, country)

        Integer passwordExpiryInDays = 0

        if(user.lastPasswordChangeDate != null) {
            passwordExpiryInDays = Days
                .daysBetween(LocalDateTime.now(), user.lastPasswordChangeDate.plusMonths(3)).getDays()
        }

        if (passwordExpiryInDays < 1) {
           profileService.expirePassword(user.id)
        }

        render view: "/apiV1/profile/view", model: [user     : user, userDetails: user.userDetails, callingCode: callingCode,
                                                    formatter: formatter, formattedPhone: formattedPhone, passwordExpiryInDays: passwordExpiryInDays]
    }

    /**
     * Update profile of logged in user
     * @param cmd
     * @return
     */
    def update(UpdateProfileCommand cmd) {

        log.info("apiV1.profile.update - name: ${cmd.name}, email: ${cmd.email}, Date of Birth: ${cmd.dob}," +
            " mobile: ${cmd.mobile}, weeklyNewsletter: ${cmd.weeklyNewsletter} ")

        User currentUser = springSecurityService.currentUser

        if (cmd.validate()) {
            profileService.update(currentUser, cmd)

            log.info("apiV1.user.save - user [${currentUser.id}] saved")

            response.status = HttpStatus.OK.value()

            def resp = [
                "success": true,
                "message": "Profile saved"
            ]

            render resp as JSON
        } else {
            response.status = HttpStatus.BAD_REQUEST.value()

            List cmdErrors = []
            cmd.errors.allErrors.each { error ->
                cmdErrors.push(error.defaultMessage)
            }
            Map model = [
                message: "Validation errors",
                errors : cmdErrors
            ]
            render model as JSON
        }
    }

    def requestVerificationEmail() {
        User currentUser = springSecurityService.currentUser
        log.info("profile.requestVerificationEmail - Sending verification email to ${currentUser.email}")
        profileService.sendVerificationEmail(currentUser)

        response.status = HttpStatus.OK.value()
        render([status: "Email sent"] as JSON)
    }

    def requestOTP() {
        User currentUser = springSecurityService.currentUser
        //todo send sms

        response.status = HttpStatus.OK.value()
        render([status: "SMS sent"] as JSON)
    }

    def verifyEmail() {
        User user = User.findByEmail(params.email as String)
        log.info("profile.verifyEmail - Verifying email ${params.email}")

        def msg = profileService.verifyEmail(user, params.hash as String)
        log.info("profile.verifyEmail - $msg [email: ${params.email}]")

        String country = user.country ? CountryEnum.findCountryById(user.country.id).code : CountryEnum.UAE.code
        render view: "/apiV1/profile/verifyEmail", model: [message: msg, lang: "en", country: country]
    }

    def forgotPassword() {
        //todo: send reset password email
        // render code: HttpStatus.OK
        String email = request.JSON.email
        log.info("profile.forgotPassword - Sending email for forgot password to ${email}")
        Boolean result = profileService.sendForgotPasswordEmail(email)

        String msg = "User not found"
        if (result) {
            msg = "Password Reset mail sent to $email"
            log.info("profile.forgotPassword - $msg")
        } else {
            log.warn("profile.forgotPassword - $msg for $email")
        }
        Map model = [msg: msg]
        render model as JSON
    }

    def verifyForgotPasswordEmail(String email, String hash) {
        log.info("profile.verifyForgotPasswordEmail - Verifying forgot password email, params: ${params}")
        User verifiedUser = profileService.verifyForgotPasswordEmail(email, hash)
        if (verifiedUser) {
            String country = verifiedUser.country ? CountryEnum.findCountryById(verifiedUser.country.id).code : CountryEnum.UAE.code
            render view: "/apiV1/profile/forgotPassword", model: [email: email, hash: hash, country: country, lang: "en"]
        } else {
            log.warn("profile.verifyForgotPasswordEmail - Invalid email: ${email}")
            render view: "/apiV1/profile/invalidEmail", model: [lang: "en", country: CountryEnum.UAE.code]
        }
    }

    def resetPassword(ResetPasswordCommand resetPasswordCommand) {
        log.info("profile.resetPassword - Resetting password for user ${resetPasswordCommand.email}")

        resetPasswordCommand.profileService = profileService

        if (resetPasswordCommand.validate()) {
            User user = profileService.resetPassword(resetPasswordCommand)
            flash.message = g.message(code: "forgotPassword.success")
            String country = user.country ? CountryEnum.findCountryById(user.country.id).code : CountryEnum.UAE.code
            log.info("profile.resetPassword - Reset password done for user ${resetPasswordCommand.email}")
            render view: "/apiV1/profile/resetPasswordSuccess", model: [country: country, lang: "en"]
        } else {
            User user = User.findByEmail(resetPasswordCommand.email)
            String country = user?.country ? CountryEnum.findCountryById(user.country.id).code : CountryEnum.UAE.code
            log.warn("profile.resetPassword - No password reset for user ${resetPasswordCommand.email}")
            render view: "/apiV1/profile/forgotPassword", model: [resetPasswordCommand: resetPasswordCommand,
                                                                  email               : resetPasswordCommand.email,
                                                                  hash                : resetPasswordCommand.hash,
                                                                  country             : country, lang: "en"]
        }
    }

    def notifications() {
        User currentUser = springSecurityService.currentUser
//      currentUser.getNotifications()
        //todo: remember to update last read date

        render([] as JSON)
    }

    def unread() {
        //todo: check against date of last time notifications were read
        render([unread: 4] as JSON)
    }

    /**
     * Get all applications of logged in user
     * @param lang
     * @param max
     * @param offset
     * @return
     */
    def applications(String lang, Integer max, Integer offset) {

        User currentUser = springSecurityService.currentUser
        RestBuilder rest = new RestBuilder()
        String url = "${grailsApplication.config.banking}/lead/allApplicationsByLead?email=${currentUser.email}"
        if (lang) {
            url += "&lang=$lang"
        }
        if (max) {
            url += "&max=$max"
        }
        if (offset) {
            url += "&offset=$offset"
        }
        RestResponse resp = rest.get(url)

        JSONElement jsonBody = JSON.parse(resp.body)
        render jsonBody as JSON
    }

    Boolean checkUserResetPasswordStatus(String encryptedUserId) {
        User user = User.findById(AESCryption.decrypt(encryptedUserId))
        render([passwordReset: user.passwordReset] as JSON)
    }

    /**
     * Get All Subscriptions (Recurring Payments) and related used Card
     *
     * @return
     */
    def subscriptions() {
        log.info("apiV1.profile.subscriptions - entering")

        User user = springSecurityService.currentUser

        List<RecurringPayment> recurringPayments = recurringPaymentService.getRecurringPayments(user)

        def resp = [:]
        Map<String, List> subscriptions = [:]
        recurringPayments.each { RecurringPayment it ->
            def sourceQuote = it.sourceQuote
            String productType = sourceQuote.productType.toString().toLowerCase()

            if (subscriptions[productType] == null) {
                subscriptions[productType] = []
            }
            Map subscription = [
                encRecurringPaymentId: AESCryption.encrypt(it.id + ""),
                nextPaymentDate: it.nextPaymentDate.toString("dd-MM-YYYY"),
                //merchantRefernce: it.merchantRef,
                isActive: it.active,

                paymentAmount: it.paymentAmount,
                paymentPlan: it.isMonthly ? PaymentPlanEnum.MONTHLY.toString() : PaymentPlanEnum.ANNUAL.toString(),
                paymentCard: [
                    encPaymentCardId: AESCryption.encrypt(it.paymentCard.id + ""),
                    cardHolderName: it.paymentCard.cardHolderName,
                    expiry: it.paymentCard.expiryYear + "/" + String.format("%02d", it.paymentCard.expiryMonth),
                    maskedCardNumber: it.paymentCard.maskedCardNumber
                ],
                encQuoteId: AESCryption.encrypt(sourceQuote.id + ""),
                policyNumber: sourceQuote.policyNo,
                expiryDate: sourceQuote.actualExpiryDate ? sourceQuote.actualExpiryDate.toString("dd-MM-YYYY") : null,
                isExpired: sourceQuote.actualDaysRemaining <= 0 ? true : false
            ]

            if (sourceQuote instanceof PetQuote) {
                subscription["petName"] = sourceQuote.pet.petName
            } else if (sourceQuote instanceof PaLead) {
                subscription["policyHolderName"] = sourceQuote.guardianName
            } else if (sourceQuote instanceof LifeQuote) {
                subscription["policyHolderName"] = sourceQuote.name
            }

            subscriptions[productType].add(subscription)
        }
        resp.subscriptions = subscriptions

        response.status = HttpStatus.OK.value()
        render resp as JSON
        return
    }

}
