package com.cover.apiV1.calls

import com.safeguard.User
import com.safeguard.autodialer.CallDispositionLog
import grails.converters.JSON
import org.springframework.http.HttpStatus

class CallController {

    static namespace = "apiV1"
    def callService

    /**
     * This is called by Ameyo API when a call is ended
     * Currently used for testing only
     * @param
     * @return
     */
    def handleCallDetails(String key) {

        log.info("call.handleCallDetails - handling call disposition from autodialer - In handleCallDetails with params: ${params}")

        callService.saveCallDisposition(params)

        Map responseParams = [ success : true ]
        render responseParams as JSON
    }
}
