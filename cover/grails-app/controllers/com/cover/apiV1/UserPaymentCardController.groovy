package com.cover.apiV1

import com.c4m.payfort.util.PayfortCommandEnum
import com.c4m.payfort.util.PayfortResponse
import com.c4m.payfort.util.PayfortStatusEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CurrencyEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.User
import com.safeguard.payment.PaymentCardDetails
import com.safeguard.payment.PaymentMethod
import com.safeguard.payment.RecurringPayment
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.joda.time.LocalDate
import org.springframework.http.HttpStatus

class UserPaymentCardController {

    static namespace = "apiV1"

    static allowedMethods = [
        paynow             : ['GET'],
        listCards          : ['GET'],
        deleteCard         : ['DELETE']
    ]

    static String INSURANCE_TYPE = "userPaymentCard"

    def checkoutService
    def paymentService
    def paymentMethodService
    def recurringPaymentService
    def springSecurityService
    def userPaymentCardService
    def utilService

    /**
     * Returns JSON with list of payment cards without Token
     *
     * @return JSON response with list of payment cards
     */
    def listCards() {
        log.info("apiV1.userPaymentCard.listCards - entering")

        User user = springSecurityService.currentUser

        def paymentCards = userPaymentCardService.listPaymentCards(user)

        def resp = [:]
        List cardDetails = []
        paymentCards.each {
            def cardDetail = [
                isDefault: it.isDefault,
                encPaymentCardId: AESCryption.encrypt(it.id + ""),
                cardHolderName: it.cardHolderName,
                expiry: it.expiryYear + "/" + String.format("%02d", it.expiryMonth),
                maskedCardNumber: it.maskedCardNumber,
                paymentCardNetwork: it.maskedCardNumber.startsWith("4") ? "VISA" : (it.maskedCardNumber.startsWith("5") ? "MASTERCARD" : "")
            ]
            cardDetails.add(cardDetail)
        }
        resp.paymentCards = cardDetails
        resp.count = cardDetails.size()

        response.status = HttpStatus.OK.value()
        render resp as JSON
        return
    }

    /**
     * Deletes a Payment Card after ownership verification
     *
     *  @return JSON response with success or error message
     */
    def deleteCard(String encPaymentCardId) {
        log.info("apiV1.userPaymentCard.deleteCard - entering with [id:${encPaymentCardId}]")

        User loggedUser = springSecurityService.currentUser

        Long paymentCardId = AESCryption.decrypt(encPaymentCardId).toLong()
        if (!paymentCardId) {
            response.status = HttpStatus.NOT_FOUND.value()
            return
        }

        PaymentCardDetails paymentCard = PaymentCardDetails.get(paymentCardId)
        if (!paymentCard || !paymentCard.isActive()) {
            response.status = HttpStatus.NOT_FOUND.value()
            return
        }
        log.info("paymentCard.user ${paymentCard.id}, :${paymentCard.user.id}, loggedUser:${loggedUser.id}")
        if (paymentCard.user != loggedUser) {
            response.status = HttpStatus.UNAUTHORIZED.value()
            return
        }

        Integer countOfRunningPayments = recurringPaymentService.getCountOfPendingRecurringPaymentsByPaymentCard(paymentCard)
        if (countOfRunningPayments) {
            def resp = [:]
            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            resp.message = "Cannot delete card. There are pending payments linked to this card."
            render resp as JSON
            return
        }

        userPaymentCardService.deletePaymentCard(loggedUser, paymentCard)

        def resp = [:]
        resp.message = "Success"
        render resp as JSON
        return
    }

    /**
     * Make the card as default of all active recurring payments
     *
     */
    def defaultCard() {
        log.info("userPaymentCard.defaultCard - entering with [JSON:${request.JSON}]")
        String encNewPaymentCardId = request.JSON.encNewPaymentCardId

        User loggedUser = springSecurityService.currentUser

        def resp = [:]

        //TODO: Validate if new Payment card id belongs to same user
        Long paymentCardId = Long.parseLong(AESCryption.decrypt(encNewPaymentCardId))
        PaymentCardDetails paymentCardDetails = PaymentCardDetails.get(paymentCardId)
        if (paymentCardDetails.user != loggedUser) {
            log.error("userPaymentCard.defaultCard - PaymentCard Ownership Check Failed - cardId:$encNewPaymentCardId]")
            response.status = HttpStatus.UNAUTHORIZED.value()
            resp.message = "Payment Card does not belong to user"
            render resp as JSON
            return
        }

        userPaymentCardService.makeDefaultPaymentCard(loggedUser, paymentCardDetails)

        resp.message = "Success"
        render resp as JSON
        return
    }
    /**
     * Replaces payment card of a pending payment with another one
     * @return
     */
    def switchPaymentCard() {
        log.info("userPaymentCard.switchPaymentCard - entering with [JSON:${request.JSON}]")
        String encRecurringPaymentId = request.JSON.encRecurringPaymentId
        String encNewPaymentCardId = request.JSON.encNewPaymentCardId

        User loggedUser = springSecurityService.currentUser

        def resp = [:]

        //TODO: Validate if new Payment card id belongs to same user
        Long paymentCardId = Long.parseLong(AESCryption.decrypt(encNewPaymentCardId))
        PaymentCardDetails paymentCardDetails = PaymentCardDetails.read(paymentCardId)
        if (paymentCardDetails.user != loggedUser) {
            log.error("userPaymentCard.switchPaymentCard - PaymentCard Ownership Check Failed - paymentId:$encRecurringPaymentId, cardId:$encNewPaymentCardId]")
            response.status = HttpStatus.UNAUTHORIZED.value()
            resp.message = "Payment Card does not belong to user"
            render resp as JSON
            return
        }

        //TODO: Validate if Recurring payment id is still pending and belongs to user
        Long recurringPaymentId = Long.parseLong(AESCryption.decrypt(encRecurringPaymentId))
        RecurringPayment recurringPayment = RecurringPayment.findById(recurringPaymentId)

        boolean isPendingPaymentOwner = recurringPaymentService.checkRecurringPaymentOwnership(recurringPayment, loggedUser)
        if (!isPendingPaymentOwner) {
            log.error("userPaymentCard.switchPaymentCard - Payment ownership failed - paymentId:$encRecurringPaymentId, cardId:$encNewPaymentCardId]")
            response.status = HttpStatus.UNAUTHORIZED.value()
            resp.message = "Pending Payment doesnt not belong to user"
            render resp as JSON
            return
        }
        if (!recurringPayment.active || (recurringPayment.endDate && recurringPayment.endDate < LocalDate.now())) {
            log.error("userPaymentCard.switchPaymentCard - Recurring payment is not pending - paymentId:$encRecurringPaymentId, cardId:$encNewPaymentCardId]")
            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            resp.message = "Payment is not pending. Cannot switch payment"
            render resp as JSON
            return
        }

        if (recurringPayment.paymentCard == paymentCardDetails) {
            log.error("userPaymentCard.switchPaymentCard - Cannot switch with same card - paymentId:$encRecurringPaymentId, cardId:$encNewPaymentCardId]")
            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            resp.message = "Payment is using same card, cannot switch"
            render resp as JSON
            return
        }

        //TODO: Replace Recurring Payment and its pending invoices
        userPaymentCardService.changePaymentCard(recurringPayment, paymentCardDetails)

        resp.message = "Success"
        render resp as JSON
        return
    }

    def paynow() {
        log.info("apiV1.userPaymentCard.paynow - entering with [params:${params}]")

        Integer userId = Integer.parseInt(AESCryption.decrypt(params.id))
        log.info("apiV1.userPaymentCard.paynow - userId:${userId}")

        if (userId) {
            User user = User.read(userId)

            if (!user) {
                log.info "apiV1.userPaymentCard.paynow - [userId:${userId}] not found"

                String message = "User not found"

                redirect mapping: 'apiUserPaymentCardError',
                    params: [insuranceType:INSURANCE_TYPE, id:params.id,
                             country      :params.country, lang:params.lang, message:message]
                return
            }

            def domain = grailsApplication.config.getProperty("cover.domain")
            String country = params.country
            String lang = params.lang

            def creditCardParams, installmentParams
            boolean showMerchantPage = true

            String payfortReturnUrl = "${domain}/v1/$INSURANCE_TYPE/checkout/$country/$lang/${params.id}/payment"
            log.info("apiV1.userPaymentCard.paynow - after adding domain, returnUrl:${payfortReturnUrl}")

            List<PaymentMethod> paymentMethods = PaymentMethod.findAllByActiveAndProductTypeAndCountry(true,
                ProductTypeEnum.CAR, Country.load(CountryEnum.findCountryByCode(country).id))

            //Is Credit Card Payment Method Available?
            PaymentMethod creditCardPaymentMethod = paymentMethods.find { it.name == PaymentMethodEnum.CREDITCARD.toString()}
            if (creditCardPaymentMethod) {
                String extraParams = params.d ? "&d=${params.d}" : ""
                String checkoutSuccessUrl = "${domain}/payments/response/checkoutPsp/$INSURANCE_TYPE/${params.id}/success?source=api${extraParams}"
                String checkoutFailureUrl = "${domain}/payments/response/checkoutPsp/$INSURANCE_TYPE/${params.id}/failure?source=api${extraParams}"
                String checkoutCancelUrl = "${domain}/payments/response/checkoutPsp/$INSURANCE_TYPE/${params.id}/cancel?source=api${extraParams}"

                (creditCardParams, installmentParams, showMerchantPage) =
                    paymentMethodService.getCreditCardPaymentParameters(creditCardPaymentMethod.paymentGateway, null, user,
                         country, utilService.getClientIp(request),
                        payfortReturnUrl, checkoutSuccessUrl, checkoutFailureUrl, checkoutCancelUrl, 1)
            }


            def quoteWrapper = [
                currency: CurrencyEnum.AED.toString(),
                totalPrice: 1,
                discount: null,
                product: [
                    nameEn: "Card",
                    isCreditCardOffline: false,
                    provider: [
                        nameEn: "Payment"
                    ]
                ]
            ]

            def model = [paymentMethods  : paymentMethods, quote: quoteWrapper,
                         creditCardParams: creditCardParams,
                         country: country, lang: lang,
                         insuranceType: INSURANCE_TYPE.toString().capitalize(),
                         showMerchantPage:showMerchantPage]

            model.returnUrl = params.returnUrl

            render view: "/apiV1/checkout/paymentMethods", model: model
        }
    }

    /**
     * Payfort redirect to this action upon credit card form submission.
     */
    def payment() {
        log.info("apiV1.userPaymentCard.payment - enterig with params:${params}")

        boolean isSecured = checkoutService.isSecured(params)

        Long userId = utilService.decodeMerchantRef(params.merchant_reference)
        log.info("userId:${userId}")
        if (!userId) {
            String message = "User not found"

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country      :params.country, lang:params.lang, message:message]
            return
        }

        User user = User.read(userId)
        if (!user) {
            String message = "User not found"

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country      :params.country, lang:params.lang, message:message]

            return
        }

        if (isSecured) {

            if (PayfortStatusEnum.INVALID_REQUEST.toString().equals(params.status)) {
                flash.error = params.response_message
                redirect mapping:'apiUserPaymentCardPaynow',
                    params:[country:params.country, lang:params.lang, id:params.id, insuranceType:INSURANCE_TYPE]
                return
            }

            try {
                params.source = "api"
                params.insuranceType = INSURANCE_TYPE

                PayfortResponse payfortResponse = paymentService.process(params,
                    PayfortCommandEnum.AUTHORIZATION, utilService.getClientIp(request), 1)

                if (payfortResponse.isThreeDeeSecure) {
                    render view: '/apiV1/checkout/redirect',
                        model: [url:payfortResponse.threeDeeSecureUrl, country:params.country,
                                lang: params.lang, insuranceType:INSURANCE_TYPE]
                    return

                } else if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(payfortResponse.status) ||
                    PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(payfortResponse.status)) {

                    //TODO: Save Payment Card against user
                    log.info("apiV1.userPaymentCard.payment - Saving card details")
                    paymentService.savePaymentCardDetails(userId, params)

                    params.insuranceType = INSURANCE_TYPE
                    //TODO: Void Auth the amount
                    log.info("apiV1.userPaymentCard.payment - Void Auth Amount")
                    paymentService.process(params, PayfortCommandEnum.VOID_AUTHORIZATION, null, null)


                    //paymentService.paid(params, customParams)
                    log.debug(".checkout.payment Redirect user to thankyou for merchantRef: ${params.merchant_reference}")
                    redirect mapping:'apiUserPaymentCardThankyou',
                        params:[country      :params.country, lang:params.lang, id:params.id,
                                insuranceType:INSURANCE_TYPE, paidAmount:quote.totalPrice]
                    return
                }

                flash.error = payfortResponse.responseMessage

            } catch (Exception exp) {
                log.error("Exception:", exp)
                String message = g.message(code:'checkout.general.error')

                redirect mapping: 'apiUserPaymentCardError',
                    params: [insuranceType:INSURANCE_TYPE, id:params.id,
                             country      :params.country, lang:params.lang, message:message]

                return
            }
        } else {
            log.error(".checkout.payment **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")
            String message = g.message(code:'checkout.general.error')

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country      :params.country, lang:params.lang, message:message]

            return
        }

        redirect mapping:'apiUserPaymentCardThankyou',
            params:[country:params.country, lang:params.lang, id:params.id, insuranceType:INSURANCE_TYPE]
        return
    }

    /**
     * 3d Secure returns to this action
     */
    def success() {
        log.info "apiV1.userPaymentCard.success - entering with [params:${params}]"

        boolean isSecured = checkoutService.isSecured(params)

        Long userId = utilService.decodeMerchantRef(params.merchant_reference)
        log.info("userId:${userId}")
        if (!userId) {
            String message = "User not found"

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country      :params.country, lang:params.lang, message:message]

            return
        }
        User user = User.read(userId)
        if (!user) {
            String message = "User not found"

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country      :params.country, lang:params.lang, message:message]

            return
        }

        if (isSecured) {
            paymentService.savePaymentResponse(null, params)

            if (PayfortStatusEnum.PURCHASE_SUCCESS.toString().equals(params.status) ||
                PayfortStatusEnum.AUTHORIZATION_SUCCESS.toString().equals(params.status)) {

                //TODO: Save Payment Card against user
                log.info("apiV1.userPaymentCard.success - Saving card details")
                paymentService.savePaymentCardDetails(userId, params)

                params.insuranceType = INSURANCE_TYPE
                //TODO: Void Auth the amount
                log.info("apiV1.userPaymentCard.success - Void Auth amount")
                paymentService.process(params, PayfortCommandEnum.VOID_AUTHORIZATION, null, null)

                //paymentService.paid(params, customParams)

                log.debug("apiV1.userPaymentCard.success Redirect user to thankyou for user: ${user.id}")
                //All good? redirect user to thankyou page
                redirect mapping:'apiUserPaymentCardThankyou',
                    params:[country      :params.country, lang:params.lang, id:params.id,
                            insuranceType:INSURANCE_TYPE, paidAmount: Integer.parseInt(params.amount)/100,
                            paymentMethod:PaymentMethodEnum.CREDITCARD]
                return

            } else {
                //session[IConstant.CHECKOUT_QUOTE_ID] = quote.id
                //flash.error = params.response_message
                log.error "#### ERROR #### -> ${params.response_message} for user ${user.id} with status-> ${params.status} #### ERROR ####"

                redirect mapping: 'apiUserPaymentCardError',
                    params: [insuranceType:INSURANCE_TYPE, id:params.id,
                             country      :params.country, lang:params.lang, message: params.response_message]
                return
            }

        } else {
            log.error(".checkout.success **** Alert!! SECURITY SIGNATURE CHECK IS NOT PASSED ****")

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country      :params.country, lang:params.lang, message: g.message(code:'checkout.general.error')]
            return
        }

        redirect mapping:'apiUserPaymentCardThankyou',
            params:[country:params.country, lang:params.lang, id:params.id, insuranceType:INSURANCE_TYPE]
        return
    }

    /**
     * Payment success action
     */
    def thankyou() {
        log.info("apiV1.userPaymentCard.thankyou - entering with [params:${params}, insuranceType:${INSURANCE_TYPE}]")

        Integer userId = Integer.parseInt(AESCryption.decrypt(params.id))

        if (!userId) {
            String message = "User not found"
            log.info("apiV1.userPaymentCard.thankyou - user id not encrypted")

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }
        User user = User.read(userId)
        if (!user) {
            log.info("apiV1.userPaymentCard.thankyou - user not found by id")
            String message = "User not found"

            redirect mapping: 'apiUserPaymentCardError',
                params: [insuranceType:INSURANCE_TYPE, id:params.id,
                         country:params.country, lang:params.lang, message:message]

            return
        }

        log.info("apiV1.userPaymentCard.thankyou - userId:${userId}")

        render view: "/apiV1/checkout/success", model: [:]

        return
    }

    /**
     * Error page handler
     *
     * @return
     */
    def error() {
        log.info("apiV1.userPaymentCard.error - entering with [params:${params}]")

        render "error"
        return
    }

    /**
     * Cancel payment page handler
     *
     * @return
     */
    def cancelPayment () {
        log.info("apiV1.userPaymentCard.cancelPayment - entering with [params:${params}]")

        render view:"/apiV1/checkout/cancel"
        return
    }

    def paymentMethodComplete() {
        log.info("apiV1.userPaymentCard.paymentMethodComplete - entering with [params:${params}]")

        render view:"/apiV1/checkout/paymentMethodComplete", model:[transaction:params.transaction]
        return
    }
}
