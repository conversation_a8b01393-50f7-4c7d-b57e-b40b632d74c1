package com.cover.apiV1

import com.cover.AuthenticationErrorCodeEnum
import com.safeguard.CountryEnum
import grails.util.Holders
import groovy.json.JsonBuilder
import groovy.transform.CompileStatic
import groovy.util.logging.Slf4j
import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContext
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.security.authentication.AccountExpiredException
import org.springframework.security.authentication.CredentialsExpiredException
import org.springframework.security.authentication.DisabledException
import org.springframework.security.authentication.LockedException
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.authentication.AuthenticationFailureHandler
import sun.util.locale.LocaleUtils

import javax.servlet.ServletException
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

@Slf4j
@CompileStatic
class CustomRestAuthenticationFailureHandler implements AuthenticationFailureHandler {

    /**
     * Configurable status code, by default: conf.rest.login.failureStatusCode?:HttpServletResponse.SC_FORBIDDEN
     */
    Integer statusCode

    MessageSource messageSource

    /**
     * Called when an authentication attempt fails.
     * @param request the request during which the authentication attempt occurred.
     * @param response the response.
     * @param exception the exception which was thrown to reject the authentication request.
     */
    void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        String lang = request.getParameter('lang') ?: LocaleContextHolder.getLocale().getLanguage()
        String country = request.getParameter('country') ? (CountryEnum.findCountry(request.getParameter('country'))?.dfp ?: LocaleContextHolder.getLocale().getCountry()) : LocaleContextHolder.getLocale().getCountry()
        Locale locale = new Locale(lang, country)
        String errorMessage
        Integer errorCode

        response.setStatus(statusCode)
        response.addHeader('WWW-Authenticate', Holders.config.get("grails.plugin.springsecurity.rest.token.validation.headerName").toString())
        if (exception instanceof AccountExpiredException) {
            errorMessage = messageSource.getMessage(AuthenticationErrorCodeEnum.ACCOUNT_EXPIRED_EXCEPTION.key(), null as Object[], locale)
            errorCode = AuthenticationErrorCodeEnum.ACCOUNT_EXPIRED_EXCEPTION.code()
        } else if (exception instanceof CredentialsExpiredException) {
            errorMessage = messageSource.getMessage(AuthenticationErrorCodeEnum.CREDENTIALS_EXPIRED_EXCEPTION.key(), null as Object[], locale)
            errorCode = AuthenticationErrorCodeEnum.CREDENTIALS_EXPIRED_EXCEPTION.code()
        } else if (exception instanceof DisabledException) {
            errorMessage = messageSource.getMessage(AuthenticationErrorCodeEnum.DISABLED_EXCEPTION.key(), null as Object[], locale)
            errorCode = AuthenticationErrorCodeEnum.DISABLED_EXCEPTION.code()
        } else if (exception instanceof LockedException) {
            errorMessage = messageSource.getMessage(AuthenticationErrorCodeEnum.LOCKED_EXCEPTION.key(), null as Object[], locale)
            errorCode = AuthenticationErrorCodeEnum.LOCKED_EXCEPTION.code()
        } else {
            errorMessage = messageSource.getMessage(AuthenticationErrorCodeEnum.FAILED_EXCEPTION.key(), null as Object[], locale)
            errorCode = AuthenticationErrorCodeEnum.FAILED_EXCEPTION.code()
        }
        PrintWriter out = response.getWriter()
        response.setCharacterEncoding("UTF-8")
        response.setContentType("application/json;charset=UTF-8")
        response.setLocale(locale)
        response.addHeader('Error-Code', errorCode.toString())
        out.print(new JsonBuilder([error_code: errorCode, message: errorMessage]).toString())
        out.flush();
    }
}
