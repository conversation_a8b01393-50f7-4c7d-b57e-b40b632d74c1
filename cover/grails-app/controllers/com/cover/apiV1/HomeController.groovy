package com.cover.apiV1

import com.cover.api.ApiResponseCodeEnum
import com.cover.api.response.ErrorResponse
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.home.commands.HomeQuoteCommand
import com.cover.home.commands.HomeRateCommand
import com.cover.homeInsuranceCommands.CustomerDetailsCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CountryEnum
import com.safeguard.home.HomeContent
import com.safeguard.home.HomeInsuranceCategory
import com.safeguard.home.HomePersonalBelonging
import com.safeguard.home.HomeQuote
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus

class HomeController {

    static namespace = "apiV1"

    static allowedMethods = [
        ddl         : ['GET'],
        saveQuote   : ['POST'],
        quotes      : ['GET'],
        quoteDetail : ['GET'],
        checkout    : ['PUT']
    ]

    def apiQuoteService
    def homeQuoteService
    def messageSource
    def utilService

    /**
     * Get list of static data required for home insurance
     * @return list of static data as JSON
     */
    def ddl() {
        log.info("apiV1.home.ddl - entering with params:[${params}]")

        List coverageTypes = HomeInsuranceCategory.getActiveList().list().collect {
            [id:it.id, value:it.name]
        }

        List homeContentValues = HomeContent.getActiveList().list().collect {
            [id:it.id, value:it.name]
        }

        List personalBelongingValues = HomePersonalBelonging.getActiveList().list().collect {
            [id:it.id, value:it.name]
        }

        def model = [:]
        model.coverageTypes = coverageTypes
        model.homeContentValues = homeContentValues
        model.personalBelongingValues  = personalBelongingValues

        render view:"ddl", model:model
    }

    /**
     * Save Home Quote
     *
     */
    def saveQuote(CustomerDetailsCommand command) {
        log.info("apiV1.home.saveQuote - entering with:[command:$command, lang:${params.lang}, country:${params.country}]")

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)
        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        command.countryEnum = countryEnum

        if (!command.validate()) {
            log.error("Customer details command validation error")

            def errors = utilService.generateErrorResponse(command.errors, locale, "home.quote")
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON
            return
        }

        MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
        marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
        marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
        marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
        marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
        marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
        marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
        command.marketingTracking = marketingTracking

        HomeQuote homeQuote = homeQuoteService.createHomeQuote(command)
        //sending home quote email...
        notify AsyncEventConstants.EMAIL_HOME_QUOTE_CREATED, [quoteId:homeQuote.id, lang:lang, country:countryEnum.code]

        String encryptedId = AESCryption.encrypt(homeQuote.id.toString())
        String recipient = homeQuote.mobile
        if (!recipient.contains("+971")) {
            recipient = "+971".concat(recipient)
        }

        notify AsyncEventConstants.WHATSAPP_NOTIFICATION_TRIGGER, [templateName:"home_quote_update",
                                                                   quoteId:encryptedId,
                                                                   lang:homeQuote.lang,
                                                                   country:countryEnum.code,
                                                                   recipient:recipient,
                                                                   type: "home"]
        def resp = [id: encryptedId]
        render resp as JSON
    }

    /**
     * Get Home quotes (ratings)
     *
     * @return Quotes as JSON
     */
    def quotes(String id) {
        log.info("apiV1.home.quotes - entering with params:[id:$id]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        log.info("apiV1.home.quotes - quoteId:${quoteId}")

        def resp = [:]

        if (quoteId) {
            HomeQuote homeQuote = HomeQuote.read(quoteId)

            if (!homeQuote) {
                redirect mapping: 'homeInsuranceCustomerDetails', params: [lang: params.lang, country: params.country]
                return
            }

            List<HomeRateCommand> quotes = homeQuoteService.getRatings(homeQuoteService.toHomeQuoteCommand(homeQuote))

            //adding sorting by premium
            if (quotes) {
                quotes.sort { it.premium }
            }

            String vatExclusiveMsg = g.message(code: "vatMessage.exclusive", locale: new Locale(params.lang))

            render view:"/apiV1/home/<USER>", model:[homeQuote:homeQuote, ratings:quotes, vatExclusiveMsg: vatExclusiveMsg]
            return
        }
    }

    /**
     * Get Home Insurance Detail against provided product
     *
     * Checkout step1
     *
     * @return
     */
    def quoteDetail(String id) {
        log.info "apiV1.home.quoteDetail - entering with quote id [${id}]"

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        log.info("apiV1.home.quoteDetail - quoteId:${quoteId}")

        HomeQuote homeQuote

        if (quoteId) {
            homeQuote  = HomeQuote.read(quoteId)
        }

        def resp = [:]

        if (homeQuote && !homeQuote.isNotProcessed()) {
            log.warn "apiV1.home.quoteDetail - Quote is already processed for quote id: ${homeQuote.id}"
            resp.message = g.message(code:'error.alreadyProcessed')
            render resp as JSON
            return
        }
        if (!params.productId) {
            String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.key()
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.code()
            error.developerMessage = key
            error.moreInfo = code
            error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }

        Integer productId = params.int("productId")

        if (homeQuote) {
            HomeQuoteCommand homeQuoteCommand = homeQuoteService.toHomeQuoteCommand(homeQuote)
            homeQuoteCommand.productId = productId

            HomeRateCommand rateCommand = homeQuoteService.getRating(homeQuoteCommand)

            def addonCheckboxList = homeQuoteService.getAddons(rateCommand, false)

            render view:"/apiV1/home/<USER>", model:[homeQuote:homeQuote, rating:rateCommand,
                                                           addonCheckboxList:addonCheckboxList]

            return

        } else {
            resp.message = "Quote not found or no product selected"
            response.setStatus(HttpStatus.NOT_FOUND.value())
            render resp as JSON
            return
        }
    }

    /**
     * Update Home quote, prepare for checkout. Update Addon and discount if any, pricing and vat
     * @param id
     * @return
     */
    def checkout(String id) {
        log.info "apiV1.home.checkout - entering with quote id [${id}]"

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        log.info "apiV1.home.checkout - id:${id}, quoteId:${quoteId}"

        def resp = [:]

        HomeQuote quote = HomeQuote.get(quoteId)

        if (!quote.isNotProcessed()) {
            log.warn "apiV1.home.checkout - Quote is already processed for quote id: ${quote.id}"
            resp.message = g.message(code:'error.alreadyProcessed')
            render resp as JSON
            return
        }

        if (!request.JSON.productId) {
            String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.key()
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.code()
            error.developerMessage = key
            error.moreInfo = code
            error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }

        Integer productId = request.JSON.productId

        def requestParams = request.JSON
        requestParams.homeQuoteId = quoteId

        apiQuoteService.updateHomeQuote(requestParams, productId)

        render view:"/apiV1/home/<USER>", model:[success:true, homeQuote: quote]
        return
    }

}
