package com.cover.apiV1

import com.cover.api.ApiResponseCodeEnum
import com.cover.api.response.ErrorResponse
import com.cover.lifeInsuranceCommands.DetailsCommand
import com.cover.lifeInsuranceCommands.LifeDetailCommand
import com.cover.lifeInsuranceCommands.LifeRateCommand
import com.cover.util.IConstant
import com.safeguard.CountryEnum
import com.safeguard.Product
import com.safeguard.life.LifeQuote
import com.safeguard.life.LifeRating
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.springframework.http.HttpStatus
import com.safeguard.life.LifeDetail

import java.text.SimpleDateFormat


class LifeController {

    static namespace = "apiV1"

    static allowedMethods = [
        saveQuote   : ['POST'],
        getCheapestQuoteValue : ['GET']
    ]

    def lifeQuoteService
    def messageSource
    def utilService
    def lifeUtilService

    def saveQuote(LifeDetailCommand lifeDetailCommand) {
        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)
        if (!lifeDetailCommand.validate()) {

            log.error("Life quote command validation error")

            def errorList = utilService.generateErrorResponse(lifeDetailCommand.errors, locale, "life.quote")
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())

            def errors = [errors: errorList]
            render errors as JSON
            return
        }

        DetailsCommand comm = new DetailsCommand()
        comm.name = lifeDetailCommand.name
        comm.beneficiaryName = lifeDetailCommand.beneficiaryName == lifeDetailCommand.name ? null : lifeDetailCommand.beneficiaryName
        comm.mobile = lifeDetailCommand.mobile
        comm.email = lifeDetailCommand.email

        if (lifeDetailCommand.dob) {
            comm.dob = LocalDate.parse(lifeDetailCommand.dob, DateTimeFormat.forPattern('yyyy-MM-dd'))
        }

        comm.source = lifeDetailCommand.source
        comm.salaryEnum = lifeDetailCommand.salaryEnum

        if (lifeDetailCommand.policyStartDate) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd")
            comm.policyStartDate = format.parse(lifeDetailCommand.policyStartDate)
        }



        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        comm.countryEnum = countryEnum


        LifeQuote lifeQuote = lifeQuoteService.createLifeQuote(comm)

        def resp = [id: AESCryption.encrypt(lifeQuote.id.toString())]
        render resp as JSON
    }

    //Method added to get the cheapest quote value among all the quotes
    def getCheapestQuoteValue(String id) {
        Integer lifeQuoteId

        Locale locale = new Locale(params.lang)

        if (id) {
            try {
                lifeQuoteId = Integer.parseInt(AESCryption.decrypt(id))
            }
         catch (Exception ex ) {
            log.error "Unable to decrypt life quote id ${id}"

            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.LIFE_QUOTE_NOT_FOUND.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.LIFE_QUOTE_NOT_FOUND.key(), [].toArray(), "Life Quote ID not valid", locale)

            def errors = [errors: [error]]

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON
             return
        }
    }


        def resp

        if(lifeQuoteId) {
            LifeQuote lifeQuote = LifeQuote.read(lifeQuoteId)
            if(lifeQuote) {
                List<LifeRateCommand> quotes = lifeQuoteService.getRatings(lifeQuoteService.toLifeQuoteCommand(lifeQuote))
                if(quotes.size() > 0) {
                    resp = [premium: quotes.first().totalPrice]
                    render resp as JSON
                }
                else {
                    ErrorResponse error = new ErrorResponse()
                    error.errorCode = ApiResponseCodeEnum.LIFE_CHEAPEST_QUOTE_NOT_FOUND.code()
                    error.moreInfo = ""
                    error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.LIFE_CHEAPEST_QUOTE_NOT_FOUND.key(), [].toArray(), "Cheapest quote not found", locale)
                    def errors = [errors:[error]]
                    response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                    render errors as JSON

                    log.info "Unable to find ratings for the  life quote on id ${lifeQuoteId}"

                    return
                }
            } else {
                ErrorResponse error = new ErrorResponse()
                error.errorCode = ApiResponseCodeEnum.LIFE_QUOTE_NOT_FOUND.code()
                error.moreInfo = ""
                error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.LIFE_QUOTE_NOT_FOUND.key(), [].toArray(), "Life Quote ID not valid", locale)
                def errors = [errors:[error]]
                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                render errors as JSON

                log.info "Unable to find life quote on id ${lifeQuoteId}"

                return
            }
        }
    }

    def getMetlifeProducts() {
        List<LifeRating> ratings = lifeQuoteService.getMetLifeProducts()

        def ratingsByProduct = ratings.groupBy { it.product.name }

        def ratingsList = []
        ratingsByProduct.each {
            Product product = Product.read(it.value[0].product.id)
            Map<String, Object> ratingsMap = ["product": [id: product.id, name: product.name], "ratings": it.value]
            ratingsList.add(ratingsMap)
        }

        def model = [products: ratingsList]

        render model as JSON
    }
}

