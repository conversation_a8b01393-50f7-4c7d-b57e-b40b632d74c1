package com.cover.apiV1

import com.cover.api.ApiResponseCodeEnum
import com.cover.api.response.ErrorResponse
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.health.commands.HealthCheckoutCommand
import com.cover.health.commands.HealthDeclarationCommand
import com.cover.health.commands.HealthMemberCommand
import com.cover.health.commands.HealthQuoteCommand
import com.cover.health.commands.HealthRateCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.Product
import com.safeguard.RequestSourceEnum
import com.safeguard.health.HealthDeductible
import com.safeguard.health.HealthGender
import com.safeguard.health.HealthQuote
import com.safeguard.health.HealthRelationship
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.joda.time.LocalDate
import org.springframework.http.HttpStatus

class HealthController {

    static namespace = "apiV1"

    static allowedMethods = [
        ddl              : ['GET'],
        members          : ['POST'],
        declarations     : ['GET'],
        saveDeclarations : ['POST'],
        quoteDetail      : ['GET'],
        checkout         : ['PUT']
    ]

    def apiQuoteService
    def healthQuoteService
    def messageSource
    def paymentService
    def utilService

    /**
     * Get list of static data required for health insurance
     * @return list of static data as JSON
     */
    def ddl() {
        log.info("apiV1.health.ddl - entering with params:[${params}]")

        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)

        List cities = utilService.getCities(country, true).collect {
            [id:it.id, name:it.name]
        }

        List genders = HealthGender.findAll(sort:"sortOrder").collect {
            [id:it.id, name:it.name]
        }

        List relationships = HealthRelationship.findAll(sort:"sortOrder").collect {
            [id:it.id, name:it.name]
        }

        def model = [:]
        model.cities = cities
        model.genders = genders
        model.relationships = relationships
        model.applicationTypes = HealthApplicationTypeEnum.findAll()

        render view:"ddl", model:model
    }

    /**
     * Save members detail
     *
     */
    def members(HealthQuoteCommand healthQuoteCommand) {
        log.info("apiV1.health.members - healthQuoteCommand:${healthQuoteCommand}")

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        def resp = [:]

        //Validate Quote and member detail
        boolean validationPassed = true
        if (!healthQuoteCommand.validate()) {
            validationPassed = false
        }

        healthQuoteCommand.members.each { HealthMemberCommand memberCommand ->
            memberCommand.source = healthQuoteCommand.source
            if (!memberCommand.validate()) {
                validationPassed = false
            }

            //if(!memberCommand.validate(healthQuoteCommand.getSource()))
            //{
            //    validationPassed = false
            //}
            /*
            if(memberCommand.getAge() == null &&  memberCommand.getDob() == null)
            {
                validationPassed = false
            }
            */
        }

        if (validationPassed) {
            //create health quote

            MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
            marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
            marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
            marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
            marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
            marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
            marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
            healthQuoteCommand.marketingTracking = marketingTracking

            healthQuoteCommand.countryEnum = utilService.convertToCountry(params.country)
            if (healthQuoteCommand.healthPolicyStartDate) {
                healthQuoteCommand.policyStartDate = healthQuoteCommand.healthPolicyStartDate.toDate()
            } else if (!healthQuoteCommand.healthPolicyStartDate && !healthQuoteCommand.policyStartDate) {
                healthQuoteCommand.policyStartDate = LocalDate.now().plusDays(1).toDate()
            }
            HealthQuote healthQuote = healthQuoteService.createHealthQuote(healthQuoteCommand)

            if (healthQuoteCommand.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
                resp.htmlMessage = g.message(code:"health.quote.employee.contact.message", locale: locale)
            } else {
                resp.quoteId = healthQuote.id
            }

            render resp as JSON

        } else {

            def errorList = []

            errorList.addAll(utilService.generateErrorResponse(healthQuoteCommand.errors, locale, "health.quote"))

            healthQuoteCommand.members.each { HealthMemberCommand memberCommand ->
                errorList.addAll(utilService.generateErrorResponse(memberCommand.errors, locale, "health.quote.member"))
            }

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())

            def errors = [errors:errorList]
            render errors as JSON
            return
        }
    }

    def updateQuote() {

    }

    /**
     * Get declarations against the provided quote id
     *
     * @param id
     * @return declaration questions as JSON
     */
    def declarations(Long id) {
        log.info("apiV1.health.declarations - id:${id}")

        def questions = healthQuoteService.getQuestions(id)

        def resp = [:]
        resp.questions = questions

        render questions as JSON
    }

    /**
     * Save declarations questions against the provided health quote id
     *
     * @param id HealthQuote id
     * @param healthDeclarationCommand
     * @return
     */
    def saveDeclarations(Integer id, HealthDeclarationCommand healthDeclarationCommand) {
        log.info("apiV1.health.saveDeclarations - [id:$id, command:$healthDeclarationCommand, params:$params]")

        HealthQuote healthQuote = HealthQuote.get(id)

        def resp = [:]

        if (!healthQuote) {
            log.debug "declaration - redirect to index as healthQuote is null"
            response.status = HttpStatus.NOT_FOUND.value()
            resp.message = "Not Found"
            render resp as JSON
            return
        }

        if (!healthDeclarationCommand.validate()) {
            log.error("apiV1.health.saveDeclarations - errors:${healthDeclarationCommand.errors}")
        }

        healthQuoteService.updateMembers(healthDeclarationCommand)

        if (!healthQuote.areMembersHealthy()) {
            paymentService.changePaymentStatus(healthQuote, PaymentStatusEnum.NOQUOTE)

            resp.message = g.message(code: 'healthInsurance.noQuotes.heading')
            resp.message1 = g.message(code: 'healthInsurance.noQuotes.subheading')
            resp.message2 = g.message(code: 'outOfRange.alternatively')

            render resp as JSON
            return

        } else if (healthQuote.paymentStatus == PaymentStatusEnum.NOQUOTE) {

            paymentService.changePaymentStatus(healthQuote, PaymentStatusEnum.DRAFT)

        }

        CountryEnum countryEnum = utilService.convertToCountry(params.country)

        notify AsyncEventConstants.HEALTH_QUOTE_CREATED, [quoteId: healthQuote.id, country: countryEnum.code]

        resp.message = "Success"
        resp.id = AESCryption.encrypt(healthQuote.id.toString())

        render resp as JSON
        return
    }

    /**
     * Get Quotes by Id
     *
     * @param id
     * @return
     */
    def quotes(String id) {
        log.info("apiV1.health.quotes - [id:$id]")

        HealthQuote healthQuote
        try {
            if (id) {
                Long quoteId = Long.parseLong(AESCryption.decrypt(id))
                healthQuote = HealthQuote.read(quoteId)
            }
        } catch (e) {
            log.error "Unable to decrypt health quote id ${id}"
        }

        def resp = [:]

        if (healthQuote && healthQuote.isNotProcessed()) {
            List<HealthRateCommand> quotes = healthQuoteService.getRatings(healthQuote)

            //adding sorting by premium
            if (quotes) {
                quotes.sort { it.premium }
            }
            String vatExclusiveMsg = g.message(code: "vatMessage.exclusive", locale: new Locale(params.lang))

            render view:"/apiV1/health/ratings", model:[ratings:quotes, healthQuote:healthQuote, vatExclusiveMsg: vatExclusiveMsg]

        } else {
            response.status = HttpStatus.NOT_FOUND.value()
            resp.message = "Health Quote not found or already processed"

            render resp as JSON
        }

    }

    /**
     * Get quote detail against product
     * This will update quote with product, reset discount, vat and pricing
     *
     * @param id
     * @param productId
     * @return
     */
    def quoteDetail(String id) {
        log.info("apiV1.health.quoteDetail - [id:$id, json:${request.JSON}]")

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        Integer productId = params.getInt('productId')
        Integer deductibleId = params.getInt("deductibleId")

        Long quoteId = Long.parseLong(AESCryption.decrypt(id))
        HealthQuote healthQuote = HealthQuote.get(quoteId)

        if (!productId || !deductibleId) {
            def errorList = []

            if (!productId) {
                String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.key()
                String code = "productId"

                ErrorResponse error = new ErrorResponse()
                error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.code()
                error.developerMessage = key
                error.moreInfo = code
                error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)
                errorList.add(error)
            }

            if (!deductibleId) {
                String key = ApiResponseCodeEnum.HEALTH_QUOTE_DEDUCTIBLEID_NULLABLE.key()
                String code = "deductibleId"

                ErrorResponse error = new ErrorResponse()
                error.errorCode = ApiResponseCodeEnum.HEALTH_QUOTE_DEDUCTIBLEID_NULLABLE.code()
                error.developerMessage = key
                error.moreInfo = code
                error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)
                errorList.add(error)
            }

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:errorList]
            render errors as JSON
            return
        }

        def resp = [:]

        if (healthQuote && healthQuote.isNotProcessed()) {
            HealthQuoteCommand command =  healthQuoteService.toHealthQuoteCommand(healthQuote)
            command.deductibleId = deductibleId
            command.productId = productId

            HealthRateCommand rateCommand = healthQuoteService.getRating(command)

            resp.currency = healthQuote.currency
            resp.quoteId = healthQuote.id
            resp.rating = rateCommand
            resp.command = command

            //render resp as JSON
            render view: '/apiV1/health/detail', model: resp
        } else {
            response.status = HttpStatus.NOT_FOUND.value()
            resp.message = "Health Quote not found or already processed"

            render resp as JSON
        }

    }

    /**
     * Update Health quote, prepare for checkout. Update discount if any, pricing and vat
     * @param id
     * @return
     */
    def checkout(String id) {
        log.info "apiV1.health.checkout - entering with quote id [${id}]"

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))

        def resp = [:]

        HealthQuote healthQuote = HealthQuote.get(quoteId)

        if (!healthQuote.isNotProcessed()) {
            log.warn "apiV1.health.checkout - Quote is already processed for quote id: ${healthQuote.id}"
            resp.message = g.message(code:'error.alreadyProcessed')
            render resp as JSON
            return
        }

        def requestParams = request.JSON
        Integer productId = requestParams.productId
        Integer deductibleId = requestParams.deductibleId

        Product product = Product.get(productId)
        HealthDeductible deductible = HealthDeductible.get(deductibleId)

        if (!product || !deductible) {
            def errorList = []

            if (!product) {
                String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.key()
                String code = "productId"

                ErrorResponse error = new ErrorResponse()
                error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.code()
                error.developerMessage = key
                error.moreInfo = code
                error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)
                errorList.add(error)
            }

            if (!deductible) {
                String key = ApiResponseCodeEnum.HEALTH_QUOTE_DEDUCTIBLEID_NULLABLE.key()
                String code = "deductibleId"

                ErrorResponse error = new ErrorResponse()
                error.errorCode = ApiResponseCodeEnum.HEALTH_QUOTE_DEDUCTIBLEID_NULLABLE.code()
                error.developerMessage = key
                error.moreInfo = code
                error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)
                errorList.add(error)
            }

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:errorList]
            render errors as JSON
            return
        }

        HealthCheckoutCommand checkoutCommand = new HealthCheckoutCommand()
        checkoutCommand.healthQuote = healthQuote
        checkoutCommand.product = Product.get(productId)
        checkoutCommand.deductible = healthQuote.deductible
        checkoutCommand.discountCode = requestParams.discountCode

        apiQuoteService.updateHealthQuote(checkoutCommand, params)

        resp.success = true

        render resp as JSON
    }

}
