package com.cover.apiV1

import com.safeguard.*
import com.safeguard.car.CarPcr
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteDetail
import com.safeguard.pa.PaLeadCommand
import com.safeguard.pa.PaLeadDetailsCommand
import com.safeguard.pa.PaRating
import com.safeguard.pa.PaSchools
import com.safeguard.security.AuthenticationToken
import com.safeguard.util.AESCryption
import com.sun.xml.internal.bind.v2.TODO
import grails.converters.JSON
import org.springframework.http.HttpStatus

class PaLeadController {

    static namespace = "apiV1"

    static allowedMethods = [
        ddl         : ['GET', 'HEAD'],
        paLeadlist  : ['GET', 'HEAD'],
        createPaLead: ['POST']
    ]

    def personalAccidentService
    def springSecurityService
    def utilService
    def cookieService

    /**
     * Get all schools
     * @return schoolsList as JSON
     */
    def ddl() {
        log.info("apiV1.PaLead.schoolsList - getting all schools")

        def obj = [:]
        List<PaSchools> paSchools = PaSchools.findAll().sort { it.name }

        if (paSchools) {
            obj['schoolsList'] = paSchools
        } else {
            obj['errors'] = [:]
            obj['errors']['developerMessage'] = "No result fount"
            obj['errors']['errorCode'] = 4
            obj['errors']['userMessage'] = "No result fount"
        }

        render obj as JSON
    }


    /**
     * Save PaLead
     *
     * @param PaLeadCommand
     * @return Encrypted PaLead Id as JSON
     */
    def createPaLead(PaLeadCommand command) {
        log.info("apiV1.PaLead..saveLead - [$command]")

        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)

        String lang = utilService.convertToLocale(params.lang)
        command.requestSource = RequestSourceEnum.WEB

        def obj = [:]
        command.validate()
        if (command.hasErrors()) {

            response.status = HttpStatus.BAD_REQUEST.value()

            List cmdErrors = []
            command.errors.allErrors.each { error ->
                log.info("error:$error")
                cmdErrors.push(error.code)
            }
            obj = [
                message: "Validation errors",
                errors : cmdErrors
            ]
            render obj as JSON
            return
        }

        def paLead
        try {

            paLead = personalAccidentService.saveLeadAndUser(command)
            obj['message'] = "PA Lead successfully created"
            obj['id'] = AESCryption.encrypt(paLead.id.toString())
            obj['totalPrice'] = paLead.totalPrice
            log.info("PA lead, quote:${paLead.id}, version:${paLead.version}")

            //Sending email to customer after saving PaLead to getting back to payment page
            notify AsyncEventConstants.PA_YOUTH_CREATED, [paLeadId: paLead.id,
                                                          lang    : lang,
                                                          country : countryEnum.code]

        } catch (e) {

            log.error("apiV1.PaLead..saveLead - [$e]")
            obj['errors'] = [:]
            obj['errors']['developerMessage'] = e.message
            obj['errors']['errorCode'] = 4
            obj['errors']['userMessage'] = "Bad Request Please try again"

        }

        render obj as JSON

    }


    /**
     * Get all PaLead
     * @return schoolsList as JSON
     */
    def paLeadList(String s) {
        log.info("apiV1.PaLead.paLeadlist")

        Locale locale = new Locale(params.lang)
        String errorMessage = null
        User currentUser = springSecurityService.currentUser
        if (!currentUser) {
            response.status = HttpStatus.UNAUTHORIZED.value()
            errorMessage = g.message(code:"cancellation.unauthorized", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        }
        RequestSourceEnum requestSourceEnum
        if (s == "w") {
            requestSourceEnum = RequestSourceEnum.WEB
        }

        List<PaLeadDetailsCommand> paLeadList = personalAccidentService.getUserPaLeads(currentUser)

        def obj = [:]
        obj['paLeadList'] = paLeadList
        obj['totalCount'] = paLeadList.size()
        //log.info("paLeadList  [$paLeadList.]")
        render obj as JSON
    }


    /**
     * Create a request and also confirms the pa policy cancellation.
     * Amalgamation of requestPaCancellation & confirmPaCancellation service
     * @param encryptedpaeId
     * @return
     */
    def requestConfirmPaCancellation(String paId) {
        log.info("PaLead.requestConfirmPaCancellation - entering with paId:${paId}")

        Locale locale = new Locale(params.lang)

        String accessToken = cookieService.getCookie('access_token')

        User user = null
        String errorMessage = null

        if (accessToken) {
            AuthenticationToken authenticationToken = AuthenticationToken.findByToken(accessToken)
            if (authenticationToken) {
                user = User.findByEmail(authenticationToken.username)
            }
        }

        if (!accessToken || !user) {
            response.status = HttpStatus.UNAUTHORIZED.value()
            errorMessage = g.message(code:"cancellation.unauthorized", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        }

        PaLead paLead = null
        if (paId) {
            Long id = AESCryption.decrypt(paId)?.toLong()
            if (id) {
                paLead = PaLead.read(id)
            }
        }

        if (!paLead) {
            response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
            errorMessage = g.message(code:"cancellation.quote.notFound", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        } else if (paLead.user != user) {
            response.status = HttpStatus.UNAUTHORIZED.value()
            errorMessage = g.message(code:"cancellation.unauthorized", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        }

        //Make sure the pa lead is not already requested or processed for cancellation
        String cancellationRequestUUID = personalAccidentService.handlePaCancellationRequest(paLead)
        log.info("paLead.requestConfirmPaCancellation - user:${user?.id}, paLead:${paLead?.id}, cancellationRequestUUID: ${cancellationRequestUUID}")
        //Possibly, pa lead is already requested for cancellation or processed.
        if (!cancellationRequestUUID) {
            //Possibly pa lead is already processsed and hence is not valid for cancellation
            errorMessage = g.message(code:"cancellation.notValid", locale:locale)
            render view: "/common/errorMessage", model: [errorMessage:errorMessage]
            return
        } else {
            render view: "/common/policyCancelled", model: [title: "Personal Accident Policy Cancelled"]
            return
        }

        response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
        render view: "/common/errorMessage", model: [errorMessage:errorMessage]
        return

    }



}
