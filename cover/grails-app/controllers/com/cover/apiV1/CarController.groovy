package com.cover.apiV1

import com.cover.api.ApiResponseCodeEnum
import com.cover.api.CarQuoteCommand
import com.cover.api.FileUploadCommand
import com.cover.api.UserDocumentsCommand
import com.cover.api.response.ErrorResponse
import com.cover.car.GigRateV2Service
import com.cover.car.commands.ConditionalQuestionsCommand
import com.cover.car.commands.ConditionalQuestionsCommandApi
import com.cover.car.commands.DriverCommand
import com.cover.car.commands.QuoteCommand
import com.cover.car.commands.RateCommand
import com.cover.car.commands.UpdatePolicyCommand
import com.cover.car.commands.UpdatePolicyDocumentsApiCommand
import com.cover.car.commands.ValuationCommand
import com.cover.car.commands.VehicleCommand
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.CarConditionalDocsQuestionEnum
import com.safeguard.CarConditionalDocument
import com.safeguard.CarPlateCategoryEnum
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverPreferenceEnum
import com.safeguard.DocumentType
import com.safeguard.DocumentTypeEnum
import com.safeguard.PaymentMethodEnum
import com.safeguard.PaymentStatusEnum
import com.safeguard.PolicyDocument
import com.safeguard.ProductTypeEnum
import com.safeguard.QuoteCreatorEnum
import com.safeguard.RepairTypeEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.User
import com.safeguard.car.CarFinanceInstitution
import com.safeguard.car.CarQuote
import com.safeguard.car.CarQuoteConditionalQuestionsValues
import com.safeguard.car.DocApiCommand
import com.safeguard.car.vehicle.Make
import com.safeguard.car.vehicle.Model
import com.safeguard.util.AESCryption
import com.safeguard.util.ResultWrapper
import grails.converters.JSON
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.springframework.http.HttpStatus

class CarController {

    static namespace = "apiV1"

    static allowedMethods = [
        makes                   : ['GET', 'HEAD'],
        models                  : ['GET'],
        trims                   : ['GET'],
        valuation               : ['GET'],
        countries               : ['GET'],
        saveQuote               : ['POST'],
        quotes                  : ['GET'],
        detail                  : ['GET'],
        checkout                : ['PUT'],
        sendQuoteCreatedV2Email : ['GET'],
        documentsData           : ['POST'],
        requiredDocumentsData   : ['GET'],
        uploadPolicyDocs        : ['POST'],
        deletePolicyDocs        : ['DELETE'],
        updatePolicyDocs        : ['PUT']
    ]

    def apiQuoteService
    def lbnQuoteService
    def messageSource
    def policyService
    def quoteService
    def utilService
    def valuationService
    def vehicleService
    def commonUtilService
    def documentUploadService
    def policySgService
    def kwtQuoteService
    def egyQuoteService
    /**
     * Get list of static data required for car insurance
     * @return list of static data as JSON
     */
    def ddl() {
        log.info("apiV1.car.ddl - entering with params:[${params}]")

        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)
        log.info("country:${country}, countryEnum:$countryEnum")
        List cities = utilService.getCities(country, false).collect {
            [id:it.id, name:it.name]
        }

        List drivingExperience = utilService.getDrivingExperienceList().collect {
            [id:it.id, name:it.name]
        }

        List ncd = utilService.getNoClaimDiscountList().collect {
            [id:it.id, name:it.name]
        }

        def model = [:]
        model.carYears = commonUtilService.getCarYears()
        model.registrationCities = cities
        model.drivingExperience = drivingExperience
        model.ncd = ncd
        model.carPlateCategories = [
            [id: CarPlateCategoryEnum.PRIVATE.getValue(), name: CarPlateCategoryEnum.PRIVATE.getName()],
            [id: CarPlateCategoryEnum.PUBLIC.getValue(), name: CarPlateCategoryEnum.PUBLIC.getName()],
            [id: CarPlateCategoryEnum.RENT_A_CAR.getValue(), name: CarPlateCategoryEnum.RENT_A_CAR.getName()],
            [id: CarPlateCategoryEnum.TRAILER.getValue(), name: CarPlateCategoryEnum.TRAILER.getName()],
            [id: CarPlateCategoryEnum.MOTOR_CYCLE.getValue(), name: CarPlateCategoryEnum.MOTOR_CYCLE.getName()],
            [id: CarPlateCategoryEnum.COMMERCIAL.getValue(), name: CarPlateCategoryEnum.COMMERCIAL.getName()],
        ]
        model.financeInstitutions = CarFinanceInstitution.findAllByActive(true, [sort: "name", order: "asc"])

        render view:"ddl", model:model
    }

    /**
     * Get Car Makes
     * @return Makes as JSON
     */
    def makes(String country) {
        log.info("apiV1.car.make - entering with params:[${params}]")

        Country countryObj
        if (country) {
            CountryEnum countryEnum = utilService.convertToCountry(country)
            countryObj = Country.get(countryEnum.id)
        }

        List<Make> makeList = vehicleService.getMakeList(countryObj)

        render view:"makes", model:[makeList:makeList]
    }

    /**
     * Get Car Models by year and make id
     * @param year
     * @param make
     * @return Models as JSON
     */
    def models(int year, int make, String country) {
        log.info("apiV1.car.model - entering with params:[year:$year, make:$make]")

        Country countryObj
        if (country) {
            CountryEnum countryEnum = utilService.convertToCountry(country)
            countryObj = Country.get(countryEnum.id)
        }

        def modelMasterList = vehicleService.getModelMasterList("en", make, year, countryObj)

        render view:"modelMasters", model:[modelMasterList:modelMasterList]
    }

    /**
     * Get Car trims by year, make and model id
     * @param year
     * @param make
     * @return Trim data as JSON
     */
    def trims(int year, int make, String country) {
        log.info("apiV1.car.trims - entering with params:[$params]")

        int modelMaster = 0
        if (params.modelMaster) {
            modelMaster = params.int('modelMaster')
        }

        //Supporting new parameter name convention
        if (!params.modelMaster && params.model) {
            modelMaster = params.int('model')
        }

        Country countryObj
        if (country) {
            CountryEnum countryEnum = utilService.convertToCountry(country)
            countryObj = Country.get(countryEnum.id)
        }

        def trimDetail = vehicleService.getModelList("en", year, make, modelMaster, params.rtaModelId, countryObj)

        render view:"trims", model:[trimList: trimDetail.modelList, make: trimDetail.make,
                                    modelMaster: trimDetail.modelMaster]
    }

    /**
     * Get Car Valuation by year, model
     *
     * @param country
     * @param model
     * @param year
     * @param isBrandNew
     * @return Valuation data as JSON
     */
    def valuation(String country, ValuationCommand command) {
        log.info("apiV1.car.valuation - entering with params:[country:$country, command:${command.toString()}]")

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        CountryEnum countryEnum = utilService.convertToCountry(country)
        Country countryObj = Country.get(countryEnum.id)

        //Supporting new parameter name convention
        if (!command.model && params.trim) {
            command.model = params.int('trim')
        }

        if (!command.validate()) {
            log.error("apiV1.car.valuation - Customer details command validation error - command:$command")

            def errorList = utilService.generateErrorResponse(command.errors, locale, "car.quote")
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())

            def errors = [errors:errorList]
            render errors as JSON
            return
        }

        def valuation = valuationService
            .getValuation(command.model, command.year, command.isBrandNew, countryObj, command.chassisNumber)

        log.info("car.valuation - command:${command.toString()}, valuation:${valuation}")

        render view: "valuation",
            model: [currency: valuation.currency, minimum: valuation.minimum, maximum: valuation.maximum]

    }

    /**
     * Get list of countries (along with nationality)
     * @return Countries as JSON
     */
    def countries() {
        log.info("apiV1.car.countries - entering with params:[$params]")

        List<Country> countries = utilService.getCountries('nameEn')

        //render view:"countries", model:[countries:countries]

        List decoratedCountries = []
        countries.each {
            decoratedCountries.add([id: it.id,
             name: it.name,
             nationality: it.nationality,
             isoCode: it.isoCode])
        }

        Map resp = ["counties": decoratedCountries]

        render resp as JSON

    }

    /**
     * Save quote
     *
     * @param vehicleCommand
     * @param driverCommand
     * @return Encrypted Quote Id as JSON
     */
    def saveQuote(CarQuoteCommand carQuoteCommand) {
        log.info("apiV1.car.saveQuote - entering with [carQuoteCommand:${carQuoteCommand.toString()}]")

        carQuoteCommand.country = params.country
        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        //Set default values
        if (carQuoteCommand.year && !carQuoteCommand.firstRegistrationDate) {
            String defaultDate = "01-01-${carQuoteCommand.year}"
            carQuoteCommand.firstRegistrationDate = carQuoteCommand.firstRegistrationDate ?: defaultDate
        }
        carQuoteCommand.internationalExperience = carQuoteCommand.internationalExperience ?: 1 //id:1

        if (!carQuoteCommand.validate()) {
            log.error("apiV1.car.saveQuote - validation error - command:$carQuoteCommand")

            def errorList = utilService.generateErrorResponse(carQuoteCommand.errors, locale, "car.quote")
            def errors = [errors:errorList]

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON
            return
        }

        VehicleCommand vehicleCommand = carQuoteCommand.toVehicleCommand()
        DriverCommand driverCommand = carQuoteCommand.toDriverCommand()

        log.info("apiV1.car.saveQuote - carQuoteCommand is Valid:${carQuoteCommand.validate()}")

        DateTimeFormatter formatter = DateTimeFormat.forPattern("dd-MM-yyyy")

        Date policyStartDate = LocalDate.parse(carQuoteCommand.policyStartDate, formatter).toDate()
        Date firstRegistrationDate = LocalDate.parse(carQuoteCommand.firstRegistrationDate, formatter).toDate()
        Date oldPolicyExpiryDate =  carQuoteCommand.oldPolicyExpiryDate ?
            LocalDate.parse(carQuoteCommand.oldPolicyExpiryDate, formatter).toDate() : null
        Date purchaseDate = carQuoteCommand.purchaseDate ?
            LocalDate.parse(carQuoteCommand.purchaseDate, formatter).toDate() : null
        Date dob = LocalDate.parse(carQuoteCommand.dob, formatter).toDate()

        vehicleCommand.oldPolicyExpiryDate = oldPolicyExpiryDate
        vehicleCommand.purchaseDate = purchaseDate
        driverCommand.policyStartDate = policyStartDate
        driverCommand.dob = dob
        vehicleCommand.firstRegistrationDate = firstRegistrationDate

        log.info("policyStartDate:${policyStartDate}, firstRegistrationDate:$firstRegistrationDate, " +
            "oldPolicyExpiryDate:${oldPolicyExpiryDate}, purchaseDate:$purchaseDate, dob:$dob")

        def valuation = [:]

        log.info("car.saveQuote - carQuoteCommand:${carQuoteCommand.toString()}, valuation:${valuation}")

        //TODO: App isn't handling this error, so only restricting for SmartDubai
        if (carQuoteCommand.source == RequestSourceEnum.SMARTDUBAI.toString() &&
            valuation && (vehicleCommand.insuredValue < valuation.minimum ||
            vehicleCommand.insuredValue > valuation.maximum)) {

            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.CAR_QUOTE_insuredValue_EXCEEDED.code()
            error.moreInfo = "insuredValue"
            error.userMessage = messageSource
                .getMessage(ApiResponseCodeEnum.CAR_QUOTE_insuredValue_EXCEEDED.key(), [].toArray(), "", locale)

            def errors = [errors:[error]]

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON
            return
        }

        def quote
        Boolean newUser = false

        MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
        marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
        marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
        marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
        marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
        marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
        marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
        driverCommand.marketingTracking = marketingTracking

        CarQuote duplicatedFromQuote = carQuoteCommand.duplicatedFromQuoteId ? CarQuote.read(carQuoteCommand.duplicatedFromQuoteId) : null
        User createdBySalesPerson = carQuoteCommand.createdBySalesPersonId ? User.read(carQuoteCommand.createdBySalesPersonId) : null
        (quote, newUser) = quoteService.createCarQuote(driverCommand, vehicleCommand.year,
            vehicleCommand.model, vehicleCommand.makeModelTrim, vehicleCommand.city,
            vehicleCommand.firstRegistrationDate, vehicleCommand.insuredValue,
            vehicleCommand.isNonGcc, vehicleCommand.isExpiredPolicy,
            vehicleCommand.isThirdParty, vehicleCommand.isOldAgency,
            vehicleCommand.purchaseDate, vehicleCommand.isNotBoughtYet,
            vehicleCommand.isBrandNew, vehicleCommand.isFirstCar,
            vehicleCommand.isBuyingUsedCar, carQuoteCommand.queryString,
            valuation?.minimum, valuation?.maximum, vehicleCommand.oldPolicyExpiryDate, countryEnum, lang,
            carQuoteCommand.chassisNumber, carQuoteCommand.plateNumber,
            carQuoteCommand.plateCode, carQuoteCommand.plateCategory, valuation?.valuationSource,
            carQuoteCommand.financeInstitutionId, carQuoteCommand.financeInstitutionName,
            carQuoteCommand.source, null, null, null, null, null, false,
            duplicatedFromQuote, createdBySalesPerson, carQuoteCommand.autodataSpec, carQuoteCommand.admeId,
            carQuoteCommand.subSource)

        valuation = valuationService.getEdataValuation(quote.id, quote.modelId, quote.year, quote.isBrandNew, !quote.isNonGcc)

        if (valuation == null) {
            valuation = valuationService.getValuation(quote.modelId, quote.year, quote.isBrandNew, quote.quoteCountry,
                quote.chassisNumber, !quote.isNonGcc, true)
        }
        if (valuation) {
            log.info("apiV1.car.saveQuote - saving valuation")
            quote.insuredValueMin = valuation.minimum
            quote.insuredValueMax = valuation.maximum
            quote.valuationSource = valuation.valuationSource
            quote.save(flush: true)
        }

        notify AsyncEventConstants.CAR_QUOTE_CREATED, [quoteId: quote.id, lang: lang,
                                                       creator: QuoteCreatorEnum.API]

        String encQuoteId = AESCryption.encrypt(quote.id.toString())
        if (carQuoteCommand.returnQuotes) {
            return quotes(encQuoteId)
        }

        def resp = [id: encQuoteId]
        render resp as JSON
    }

    /**
     * Get Car quotes (ratings)
     *
     * @return Quotes as JSON
     */
    def quotes(String id) {
        log.info("apiV1.car.quotes - entering with params:[id:$id]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        log.info("apiV1.car.quotes - quoteId:${quoteId}")

        def resp = [:]

        if (quoteId) {
            CarQuote carQuote = CarQuote.findById(quoteId)

            if (carQuote && carQuote.isAncient()) {
                log.info ".quotes Policy [quoteId:${quoteId}] created on ${carQuote.dateCreated} so redirecting to 404"
                response.status = HttpStatus.NOT_FOUND.value()
                resp.message = "Not Found"

            } else if (carQuote && carQuote.isNotProcessed()) {

                //def (List<RateCommand> ratings, quote) = quoteService.getRatings(quoteId)

                List<RateCommand> ratings
                def quote

                if (params.country == CountryEnum.UAE.code) {
                    (ratings, quote) = quoteService.getRatings(quoteId)
                } else if (params.country == CountryEnum.LBN.code) {
                    (ratings, quote) = lbnQuoteService.getRatings(quoteId)
                }

                if(!ratings) {
                    quoteService.setNoQuote(carQuote)
                    notify AsyncEventConstants.SALESFORCE_CAR_QUOTE_SYNC, carQuote.id

                    resp.message = g.message(code: 'noquote.sorry')
                    resp.reasons = []
                    resp.reasons.add(g.message(code: 'noquote.veryHighValue'))
                    resp.reasons.add(g.message(code: 'noquote.youngDriver'))
                    if (params.country == CountryEnum.UAE.code) {
                        resp.reasons.add(g.message(code: 'noquote.modified'))
                    } else if (params.country == CountryEnum.KWT.code) {
                        resp.reasons.add(g.message(code: 'noquote.modified.kwt'))
                    }
                    resp.message1 = g.message(code: 'noquote.manualQuotation')

                    render resp as JSON
                    return
                }

                if (carQuote.coverPreference == CoverPreferenceEnum.THIRD_PARTY) {
                    ratings.sort { a, b ->
                        if (a.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return -1
                        } else if (b.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return 1
                        } else {
                            // Compare by type and then by premium
                            b.coverageTypeId <=> a.coverageTypeId ?: a.premium <=> b.premium
                        }
                    }
                } else {
                    // here you can add other sorting on comprehensive policies
                    ratings.sort { a, b ->
                        if (a.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return -1
                        } else if (b.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return 1
                        } else {
                            // Compare by type and then by premium
                            // a.isAgencyRepair() <=> b.isAgencyRepair() ?: a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
                            a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
                        }
                    }
                }

                // sort list based on isOfflineQuotes
                ratings.sort { rate1, rate2 ->
                    (rate1.isOfflineQuotes ? 1 : 0) <=> (rate2.isOfflineQuotes ? 1 : 0)
                }

                def lowestComprehensiveQuote = ratings?.findAll() { it.coverageTypeId == 1 }?.min() { it.premium }

                String vatExclusiveMsg = g.message(code: "vatMessage.exclusive", locale: new Locale(params.lang))

                render view:"/apiV1/car/ratings", model:[carQuote:carQuote, ratings:ratings, vatExclusiveMsg: vatExclusiveMsg,
                                                         lowestComprehensiveQuote:lowestComprehensiveQuote]
                return

            } else if (!carQuote) {
                log.warn "quotes - Quote is not found "
                response.status = HttpStatus.NOT_FOUND.value()
                render resp as JSON
                return

            } else {
                log.warn "quotes - Quote is already processed for quote id: ${carQuote?.id}"

                resp.message = g.message(code:'error.alreadyProcessed')
            }

            render resp as JSON
            return
        }

        response.status = HttpStatus.NOT_FOUND.value()
        render resp as JSON
        return
    }

    /**
     * Sending quote created email with the new template
     * @param quoteId
     * @return
     */
    def sendQuoteCreatedV2Email(Long quoteId) {
        if (!quoteId) {
            log.info("apiV1.car.sendQuoteCreatedV2Email - received send quote created email with no quote Id")
            render status: HttpStatus.BAD_REQUEST
            return
        }

        log.info("apiV1.car.sendQuoteCreatedV2Email - entering with quote id: ${quoteId}")

        CarQuote carQuote = CarQuote.read(quoteId)
        ResultWrapper resultWrapper = policyService.sendQuoteCreatedV2Email(carQuote)

        if (!resultWrapper.success) {
            render status: HttpStatus.UNPROCESSABLE_ENTITY, text: resultWrapper.message
            return
        }

        Map response = [
                success :   true
        ]
        render response as JSON
    }

    /**
     * Get car quote against product and get Car quote details for checkout
     *
     * Checkout step 1
     *
     * @param id
     * @return
     */
    def detail(String id) {
        log.info "apiV1.car.detail - entering with [quote id:${id}, params:${params}]"

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        RepairTypeEnum selectedRepairType = RepairTypeEnum.findByName(params.selectedRepairType)

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        CarQuote quote = CarQuote.get(quoteId)
        log.info("apiV1.car.detail - found Car quote, quote:${quote?.id}")
        def salesPersonName = User.get(quote.user.id)?.phoneLead?.salesPerson?.name

        def model = [:]

        if (!quote) {
            response.status = HttpStatus.NOT_FOUND.value()
            model.message = "Not Found"
            return
        }

        if (quote && !quote.isNotProcessed() && (!params.export || params.boolean('export') == false)) {
            log.warn "apiV1.car.detail - Quote is already processed for quote id: ${quote.id}"
            model.message = g.message(code:'error.alreadyProcessed')
            render model as JSON
            return
        }

        if (!params.productId) {
            String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.key()
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.code()
            error.developerMessage = key
            error.moreInfo = code
            error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }


        Integer productId = params.getInt("productId")
        RateCommand rateCommand
        if (session[IConstant.STAND_ALONE_DISCOUNT_CODE]) {
            if (quote.email == (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[0]) {
                String standAloneDiscountCode = (session[IConstant.STAND_ALONE_DISCOUNT_CODE] as String).split("-")[1]
                rateCommand = quoteService.getRating(quote, productId, selectedRepairType, standAloneDiscountCode)
            } else {
                session[IConstant.STAND_ALONE_DISCOUNT_CODE] = null
            }
        } else if (session[IConstant.STAND_ALONE_DISCOUNT_GROUP]) {
            if (quote.email == (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[0] ) {
                String standAloneDiscountCodeRule = AESCryption.encrypt( (session[IConstant.STAND_ALONE_DISCOUNT_GROUP] as String).split("-")[1] )
                rateCommand = quoteService.getRating(quote, productId, selectedRepairType, null, standAloneDiscountCodeRule)
            }
        } else {
            rateCommand = quoteService.getRating(quote, productId, selectedRepairType, null, null, true)
        }

        if (!rateCommand) {
            String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_NOT_APPLICABLE.key()
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_NOT_APPLICABLE.code()
            error.developerMessage = key
            error.moreInfo = code
            error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }
        model.quote = quote
        model.command = rateCommand
        model.salesPersonName = salesPersonName

        //Get all applicable addons
        Map addons = quoteService.getAddOns(rateCommand, quote.model, quote.requestSource, [:], quote.providerInsuredValue, quote)

        //Filter addons not applicable to Smart Dubai
        if (quote.requestSource == RequestSourceEnum.SMARTDUBAI) {
            Set filteredCheckboxAddons = []
            Set filteredDropdownAddons = []

            addons.checkboxList?.each { addon ->
                if (!(addon.name in ["bulletService", "homeInsurance"])) {
                    filteredCheckboxAddons.add(addon)
                }
            }
            addons.dropdownList?.each { addon ->
                if (!(addon.name in ["bulletService", "homeInsurance"])) {
                    filteredDropdownAddons.add(addon)
                }
            }
            model.addons = [checkboxList: filteredCheckboxAddons, dropdownList: filteredDropdownAddons]
        } else {
            model.addons = addons
        }

        render view:"/apiV1/car/detail", model:model

        return
    }

    /**
     * Get Car rating by quote parameters instead of car quote object
     *
     * Checkout step 1
     *
     * @param id
     * @return
     */
    def getRatingByParams(CarQuoteCommand carQuoteCommand) {
        log.info "apiV1.car.getRatingByParams - entering with [carQuoteCommand:${carQuoteCommand}, params:${params}]"

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        carQuoteCommand.country = params.country
        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)

        //Set default values
        if (carQuoteCommand.year && !carQuoteCommand.firstRegistrationDate) {
            String defaultDate = "01-01-${carQuoteCommand.year}"
            carQuoteCommand.firstRegistrationDate = carQuoteCommand.firstRegistrationDate ?: defaultDate
        }

        if (countryEnum.code == CountryEnum.LBN.code) {
            carQuoteCommand.internationalExperience = carQuoteCommand.internationalExperience ?: 1
        }

        if (!carQuoteCommand.validate()) {
            log.error("apiV1.car.getRatingByParams - validation error - command:$carQuoteCommand")

            def errorList = utilService.generateErrorResponse(carQuoteCommand.errors, locale, "car.quote")
            def errors = [errors:errorList]

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON
            return
        }

        VehicleCommand vehicleCommand = carQuoteCommand.toVehicleCommand()
        DriverCommand driverCommand = carQuoteCommand.toDriverCommand()

        log.info("apiV1.car.getRatingByParams - carQuoteCommand is Valid:${carQuoteCommand.validate()}")

        DateTimeFormatter formatter = DateTimeFormat.forPattern("dd-MM-yyyy")

        Date policyStartDate = LocalDate.parse(carQuoteCommand.policyStartDate, formatter).toDate()
        Date firstRegistrationDate = LocalDate.parse(carQuoteCommand.firstRegistrationDate, formatter).toDate()
        Date oldPolicyExpiryDate =  carQuoteCommand.oldPolicyExpiryDate ?
            LocalDate.parse(carQuoteCommand.oldPolicyExpiryDate, formatter).toDate() : null
        Date purchaseDate = carQuoteCommand.purchaseDate ?
            LocalDate.parse(carQuoteCommand.purchaseDate, formatter).toDate() : null
        Date dob = LocalDate.parse(carQuoteCommand.dob, formatter).toDate()

        vehicleCommand.oldPolicyExpiryDate = oldPolicyExpiryDate
        vehicleCommand.purchaseDate = purchaseDate
        driverCommand.policyStartDate = policyStartDate
        driverCommand.dob = dob
        vehicleCommand.firstRegistrationDate = firstRegistrationDate

        Integer productId = params.getInt("productId")
        Model carModel = Model.get(vehicleCommand.model)

        QuoteCommand quoteCommand = quoteService.toQuoteCommand(carModel, vehicleCommand, driverCommand,
            productId, country, lang, carQuoteCommand.source)

        quoteCommand.selectedRepairType = RepairTypeEnum.findByName(params.selectedRepairType)
        quoteCommand.currency = country.currency

        RateCommand rateCommand = quoteService.getRating(quoteCommand)

        if (!rateCommand) {
            String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_NOT_APPLICABLE.key()
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_NOT_APPLICABLE.code()
            error.developerMessage = key
            error.moreInfo = code
            error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }
        Map responseModel = [:]
        responseModel.rating = rateCommand

        //Get all applicable addons
        Map addons = quoteService.getAddOns(rateCommand, carModel, quoteCommand.requestSource, [:], vehicleCommand.insuredValue)

        //Filter addons not applicable to Smart Dubai
        if (quoteCommand.requestSource == RequestSourceEnum.SMARTDUBAI) {
            Set filteredCheckboxAddons = []
            Set filteredDropdownAddons = []

            addons.checkboxList?.each { addon ->
                if (!(addon.name in ["bulletService", "homeInsurance"])) {
                    filteredCheckboxAddons.add(addon)
                }
            }
            addons.dropdownList?.each { addon ->
                if (!(addon.name in ["bulletService", "homeInsurance"])) {
                    filteredDropdownAddons.add(addon)
                }
            }
            responseModel.addons = [checkboxList: filteredCheckboxAddons, dropdownList: filteredDropdownAddons]
        } else {
            responseModel.addons = addons
        }

        render view:"/apiV1/car/_rating", model:responseModel

        return
    }

    /**
     * Update Car Quote
     * @param id
     * @return
     */
    def checkout(String id) {
        log.info "apiV1.car.checkout - entering with quote id [ ${id} ]"

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)
        RepairTypeEnum selectedRepairType = RepairTypeEnum.findByName(params.selectedRepairType)

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))

        def requestParams = request.JSON
        log.info "apiV1.car.checkout - requestParams:${requestParams}"

        CarQuote quote = CarQuote.read(quoteId)

        def resp = [:]

        if (!quote) {
            response.status = HttpStatus.NOT_FOUND.value()
            return
        }

        if (!quote.isNotProcessed()) {
            log.warn "apiV1.car.checkout - Quote is already processed for quote id: ${quote.id}"
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())

            resp.message = g.message(code:'error.alreadyProcessed')
            render resp as JSON
            return
        }

        if (!requestParams.productId) {
            String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.key()
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.code()
            error.developerMessage = key
            error.moreInfo = code
            error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }

        Integer productId = requestParams.productId

        requestParams.quoteId = quote.id

        if (selectedRepairType == null) {
            selectedRepairType = requestParams.selectedRepairType
        }

        RateCommand rateCommand = quoteService.getRating(quote, productId, selectedRepairType)
        if (!rateCommand) {
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.moreInfo = code
            error.userMessage = "Provided data is not applicable"

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }

        apiQuoteService.updateCarQuote(quote, rateCommand, requestParams)

        resp.success = true
        resp.totalPremium = quote.totalPrice

        render resp as JSON
    }

    /**
     * Add user's Digital Documents data required for issuance of policy.
     * Digital Documents are stored against the quote.
     *
     * @param id Encrypted Quote Id
     * @param UserDocumentsCommand Quote documents data
     */
    def documentsData(String id, UserDocumentsCommand documentsCommand) {
        log.info("apiV1.car.documentsData - enterign with [id:$id, documentsCommand:${documentsCommand}]")

        String lang = params.lang
        Locale locale = new Locale(lang)

        if (!documentsCommand.validate()) {
            log.error("apiV1.car.documentsData - Customer details command validation error - id:$id, command:$documentsCommand")

            def errorList = utilService.generateErrorResponse(documentsCommand.errors, locale, "car.quote")
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())

            def errors = [errors:errorList]
            render errors as JSON
            return
        }


        def errorList = []

        if (documentsCommand.getEidDocument() &&
            !documentsCommand.getEidDocument().validate()) {
            errorList.addAll(utilService
                .generateErrorResponse(documentsCommand.getEidDocument().errors, locale, "user.document.emiratesId"))
        }

        if (documentsCommand.getDrivingLicenseDocument() &&
            !documentsCommand.getDrivingLicenseDocument().validate()) {
            errorList.addAll(utilService
                .generateErrorResponse(documentsCommand.getDrivingLicenseDocument().errors, locale, "user.document.license"))
        }

        if (documentsCommand.getVehicleRegistrationDocument() &&
            !documentsCommand.getVehicleRegistrationDocument().validate()) {
            errorList.addAll(utilService
                .generateErrorResponse(documentsCommand.getVehicleRegistrationDocument().errors, locale, "user.document.vehicle"))
        }

        if (errorList) {
            log.error("apiV1.car.documentsData - User Documents command validation error - id:$id, command:$documentsCommand")

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())

            def errors = [errors:errorList]
            render errors as JSON
            return
        }

        def resp = [:]

        try {

            Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))

            def requestParams = request.JSON
            log.info "apiV1.car.checkout - requestParams:${requestParams}"

            CarQuote quote = CarQuote.read(quoteId)

            if (!quote) {
                log.warn "apiV1.car.documentsData - Quote not found, for quoteId: ${quoteId}"
                response.status = HttpStatus.NOT_FOUND.value()
                resp.message = "Quote Not Found"
                render resp as JSON
                return
            }

            if (quote.paymentStatus in [PaymentStatusEnum.ISSUED, PaymentStatusEnum.REFUND, PaymentStatusEnum.CANCEL,
                                         PaymentStatusEnum.DENIED, PaymentStatusEnum.REJECTED]) {
                log.warn "apiV1.car.documentsData - Quote is already processed for quote id: ${quote.id}"

                response.status = HttpStatus.UNPROCESSABLE_ENTITY.value()
                resp.message = g.message(code:'error.alreadyProcessed')
                render resp as JSON
                return
            }

            quoteService.storeUserDocumentsData(quote, documentsCommand)

            //Validates if all required documents are received, then change to RECEIVED and also proceed with ISSUING
            policyService.validateAndChangeStatusToReceived(quote)

            resp.success = true

            render resp as JSON

        } catch (Exception e) {
            log.error("apiV1.car.documentsData - error updating user documents data. documentsCommand:${documentsCommand.toString()}", e)

            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())

            ErrorResponse error = new ErrorResponse()
            error.developerMessage = e.message
            error.userMessage = messageSource.getMessage("default.general.error", [].toArray(), locale)

            def errors = [errors:[error]]
            render errors as JSON
            return
        }

    }


    def requiredDocumentsData(String quoteId, String lang){
        def resp = [:]
        try {
            Long decryptQuoteId
            decryptQuoteId = AESCryption.decrypt(quoteId).toLong()
            log.info("Getting list of required Docs for Quote: $decryptQuoteId")
            CarQuote carQuote = CarQuote.read(decryptQuoteId)
            if (carQuote) {
                if ( !(carQuote.paymentStatus == PaymentStatusEnum.PAID &&
                    (carQuote.paymentMethod == PaymentMethodEnum.CREDITCARD || carQuote.paymentMethod == PaymentMethodEnum.INSTALLMENT))) {
                    log.error("Document Delete Not Allowed due to policy status for: $carQuote.id")
                    response.setStatus(HttpStatus.BAD_REQUEST.value())
                    render "Not Allowed due to policy status: ${carQuote.paymentStatus.toString().toLowerCase()}."
                    return
                }
                List<DocApiCommand> docs = commonUtilService.getRequiredDocsMarkup(carQuote.id, lang)

                // get all the doc list that needs to upload
                resp.statusCode = HttpStatus.OK.value()
                resp.carQuote = carQuote
                resp.requiredDocs = docs
                render view:"/apiV1/car/docUploads", model:resp
                return
            } else {
                log.info("CarQuote not found for the id: $quoteId")
                response.setStatus(HttpStatus.BAD_REQUEST.value())
                resp.statusCode = HttpStatus.BAD_REQUEST.value()
                resp.message = 'No record found for this ID.'
                resp.id = id
            }

        }
        catch(Exception e){
            log.error("Error decrypt Quote ID for car doc uploads: $quoteId", e)
            response.setStatus(HttpStatus.BAD_REQUEST.value())
            resp.statusCode = HttpStatus.BAD_REQUEST.value()
            resp.message = 'Not a valid ID.'
        }

        render resp as JSON
    }

    def uploadPolicyDocs (String quoteId, String lang,FileUploadCommand cmd){
        def resp = [:]

        try {
            cmd.quoteId = AESCryption.decrypt(quoteId)?.toLong()
            if (!cmd.productType) {
                cmd.productType = 'car'
            }

            // make sure files were actually uploaded
            if (!cmd.file) {
                log.info("policy.uploadPolicyDocs - no file received to server, quoteId:${cmd.quoteId}")

                render(status: HttpStatus.BAD_REQUEST.value())
                return
            }

            CarQuote carQuote = CarQuote.get(cmd.quoteId)
            DocumentType docType = DocumentType.findByCode(cmd.docType)

            log.info("Uploading the doc for car insurance policy via API. id: " + quoteId + " and with language: " + lang)

            def result = documentUploadService.savePolicyDocument(cmd.file, docType,
                carQuote, ProductTypeEnum.CAR, true, false, null)

            def policyDocument = result.policyDocument

            log.info("uploaded the doc: ${docType.nameEn} for Quote: $cmd.quoteId - returning policyDocument:${policyDocument}")

            render([
                statusCode: HttpStatus.OK.value(),
                message: "File Uploaded",
                path: policyDocument?.fullPath,
                documentId: AESCryption.encrypt(policyDocument?.id.toString())
            ] as JSON)

            return
        }
        catch (Exception e) {
            log.error("Error decrypt Quote ID for car doc uploads: $quoteId", e)
            response.setStatus(HttpStatus.BAD_REQUEST.value())
            resp.statusCode = HttpStatus.BAD_REQUEST.value()
            resp.message = 'Not a valid ID. Or any other error happened.'
        }

        render resp as JSON

    }

    def deletePolicyDocs (String quoteId) {

        Map resp = [:]

        if (!(params.documentId && quoteId)){
            log.error("document or quote id is not found.")
            response.setStatus(HttpStatus.BAD_REQUEST.value())
            render 'Document id or Quote Id is not present.'
            return
        }
        Long docId
        Long decryptQuoteId
        try {
            docId = Long.parseLong(AESCryption.decrypt(params.documentId))
        }
        catch (Exception e) {
            log.error("Error decrypt documentId ID: $params.docuemntId",e)
            response.setStatus(HttpStatus.BAD_REQUEST.value())
            render 'Bad document Id.'
            return
        }
        try {
            decryptQuoteId = Long.parseLong(AESCryption.decrypt(quoteId))
        }
        catch (Exception e) {
            log.error("Error decrypt quote ID: $quoteId",e)
            response.setStatus(HttpStatus.BAD_REQUEST.value())
            render 'Bad quote Id.'
            return
        }


        log.info("apiV1.car.deletePolicyDocs - quoteId: $decryptQuoteId")

        CarQuote quote = CarQuote.get(decryptQuoteId)

        if ( !(quote.paymentStatus == PaymentStatusEnum.PAID &&
            (quote.paymentMethod == PaymentMethodEnum.CREDITCARD || quote.paymentMethod == PaymentMethodEnum.INSTALLMENT))) {
            log.error("Document Delete Not Allowed due to policy status for: $quote.id")
            response.setStatus(HttpStatus.BAD_REQUEST.value())
            render "Not Allowed due to policy status: ${quote.paymentStatus.toString().toLowerCase()}."
            return
        }

        try {
            PolicyDocument document = PolicyDocument.read(docId)

            //Allow only when document belongs to provided quote
            if (document.carQuote.id == quote.id) {

                documentUploadService.deleteDocumentOnly(docId, quote.id, ProductTypeEnum.CAR)

                render "success"
                return
            } else {
                response.status = HttpStatus.UNAUTHORIZED.value()
                render "unauthorized"
                return
            }

        } catch (Exception e) {
            response.status = HttpStatus.INTERNAL_SERVER_ERROR.value()
            render "Internal Server Error"
            return
        }
    }

    /**
     * Update Policy with Policy Start Date and to Received
     * @param cmd UpdatePolicyCommand object
     * @return
     */
    def updatePolicyDocs(String quoteId, String lang, UpdatePolicyDocumentsApiCommand cmd) {
        log.info("policy.updatePolicy - entering with [cmd:${cmd.toString()}]")

        if (!quoteId) {
            response.status = HttpStatus.BAD_REQUEST.value()
            render "Bad Request"
            return
        }

        try {
            if (!(cmd.requiredDocs) ) {
                log.error("Empty body is not allowed. Quote ID is:  ${quoteId}")
                response.status = HttpStatus.UNAUTHORIZED.value()
                render "Empty body is not allowed."
                return
            }


            Long decryptQuoteId = Long.parseLong(AESCryption.decrypt(quoteId))
            CarQuote quote = CarQuote.read(decryptQuoteId)

            if (quote.paymentStatus == PaymentStatusEnum.PAID &&
                (quote.paymentMethod == PaymentMethodEnum.CREDITCARD || quote.paymentMethod == PaymentMethodEnum.INSTALLMENT)) {

                try {
                    if (!cmd.validate()) {

                        def errors = g.renderErrors(bean: cmd)

                        render errors as JSON
                        return
                    }

                    List<DocumentTypeEnum> missingDocuments = []
                    List<PolicyDocument> documents = PolicyDocument.findAllByCarQuoteAndIsDeleted(quote, false)

                    int idCardFrontCount = documents.findAll {
                        it.documentTypeId == DocumentTypeEnum.ID_CARD_FRONT.id
                    }.size()
    //                int idCardBackCount = documents.findAll { it.documentTypeId == DocumentTypeEnum.ID_CARD_BACK.id }.size()

                    int licenseCount = documents.findAll { it.documentTypeId == DocumentTypeEnum.DRIVING_LICENSE.id }.size()
                    int registrationCount = documents.findAll {
                        it.documentTypeId == DocumentTypeEnum.VEHICLE_REGISTRATION.id
                    }.size()

                    if (!idCardFrontCount) missingDocuments.add(DocumentTypeEnum.ID_CARD_FRONT)
    //                if (!idCardBackCount) missingDocuments.add(DocumentTypeEnum.ID_CARD_BACK)
                    if (!licenseCount) missingDocuments.add(DocumentTypeEnum.DRIVING_LICENSE)
                    if (!registrationCount) missingDocuments.add(DocumentTypeEnum.VEHICLE_REGISTRATION)

                    // check additional documents and make it required
                    List<DocumentTypeEnum> additionalDocs = commonUtilService.getRequiredDocumentsList(quote)
                    additionalDocs.each { addDoc ->
                        int matchedAdditionalDocs = documents.findAll { doc ->
                            doc.documentTypeId == addDoc.id
                        }.size()
                        if (!matchedAdditionalDocs) {
                            missingDocuments.add(addDoc)
                        }
                    }
                    // need to add conditional docs mandatory here on the base of questions

                    List<CarConditionalDocument> conditionalDocsQuestions = commonUtilService.getConditionalDocumentQuestionAndDocumentType(quote)
                    List<CarQuoteConditionalQuestionsValues> carQuoteConditionalQuestionsValues = CarQuoteConditionalQuestionsValues.findAllByCarQuote(quote)


                    List<String> missingTextConditionalQuestions = []

                    conditionalDocsQuestions.each { condDocQuestions ->

                        if (condDocQuestions.question.answerType == 'boolean'  && condDocQuestions.docTypes) {

                            CarQuoteConditionalQuestionsValues storedValue = carQuoteConditionalQuestionsValues.find {
                                it.type == 'boolean' && it.slug == condDocQuestions.question.slug
                            }
                            String defaultVal = condDocQuestions.question.getDefaultSelectedOptionYesOrNo() == 'yes' ? 'true' : "false"

                            ConditionalQuestionsCommandApi question = cmd.requiredDocs.find{
                                it.fieldType == 'boolean' && it.name == condDocQuestions.question.slug
                            }
                            String value = (question?.value == '0') ? 'false' : (question?.value == '1') ? 'true' : question?.value
                            if ( (question && value != defaultVal) || !question){
                                if ( (question || storedValue) && !(storedValue && ((!value && storedValue.value == defaultVal) || (value && value == defaultVal)) ) ) {

                                    condDocQuestions.docTypes.each { docType ->

                                        int thisDocs = documents.findAll {
                                            it.documentTypeId == docType.id
                                        }.size()

                                        if (!thisDocs) {
                                            missingDocuments.add(docType)
                                        }
                                    }
                                }

                            }

                        } else if (condDocQuestions.question.answerType == 'text') {
                            ConditionalQuestionsCommandApi textQuestion = cmd.requiredDocs.find{
                                it.fieldType == 'text' && it.name == condDocQuestions.question.slug
                            }

                            if (!textQuestion) {
                                if ( !(textQuestion && textQuestion.value != '' && textQuestion.value != null ) ) {
                                    // making tcf number optional.
                                    CarQuoteConditionalQuestionsValues storedValue = carQuoteConditionalQuestionsValues.find {
                                        it.type == 'text' && it.slug == condDocQuestions.question.slug
                                    }

                                    if (textQuestion?.name != 'TCF_NUMBER') {
                                        if (!storedValue?.value) {
                                            missingTextConditionalQuestions.add( "${lang == 'ar' ? condDocQuestions.question.label_ar : condDocQuestions.question.label_en}" )
                                        }
                                    }
                                }
                            }

                        } else if (condDocQuestions.question.answerType == 'emailVerificationButton') {
                            // ignoring this
                            // in api this is mandatory here. but in other controller this is not.
                            ConditionalQuestionsCommandApi emailVerificationCheck = cmd.requiredDocs.find{
                                it.fieldType == 'emailVerificationButton' && it.name == condDocQuestions.question.slug
                            }
                            CarQuoteConditionalQuestionsValues storedValue = carQuoteConditionalQuestionsValues.find {
                                it.type == 'emailVerificationButton' && it.slug == condDocQuestions.question.slug
                            }
                            if (!emailVerificationCheck) {
                                if ( !(emailVerificationCheck && emailVerificationCheck?.value == 'false') && !(storedValue && storedValue?.value == 'true' ) ) {
                                    missingTextConditionalQuestions.add( "${lang == 'ar' ? condDocQuestions.question.label_ar : condDocQuestions.question.label_en}" )
                                }
                            }

                        } else{

                            ConditionalQuestionsCommandApi textQuestion = cmd.requiredDocs.find{
                                it.name == condDocQuestions.question.slug
                            }
                            CarQuoteConditionalQuestionsValues storedValue = carQuoteConditionalQuestionsValues.find {
                                it.slug == condDocQuestions.question.slug
                            }

                            if ( !(textQuestion && textQuestion.value != '' && textQuestion.value != null ) ) {
                                // making other mandatory.
                                if (!storedValue?.value) {
                                    missingTextConditionalQuestions.add("${lang == 'ar' ? condDocQuestions.question.label_ar : condDocQuestions.question.label_en}")
                                }
                            }
                        }
                        missingDocuments.unique { a, b ->
                            a.id <=> b.id
                        }
                    }
                    List<CarConditionalDocsQuestionEnum> allQuestions = CarConditionalDocsQuestionEnum.findAll()
                    List<String> allQuestionsSlug = allQuestions*.slug


                    if (cmd.requiredDocs) {

                        cmd.requiredDocs.each {
                            if (  it.name in allQuestionsSlug) {
                                commonUtilService.processConditionalQuestions(quote.id, it.fieldType, it.name, it.value)
                            }
                        }
                    }


                    if (missingDocuments.size() || missingTextConditionalQuestions) {
                        List<String> documentsTitles = lang == 'ar' ? missingDocuments*.titleAr : missingDocuments*.title
                        if (missingTextConditionalQuestions) {
                            documentsTitles.addAll(missingTextConditionalQuestions)
                        }
                        String error = g.message(code: 'doc.upload.missing.documents.api')
                        log.info("policy.updatePolicy - missing documents, errors:${errors}, missing documents:${documentsTitles}, quoteId:${quote.id}")
                        Map resp = [:]
                        resp.message = error
                        resp.requiredDocs = documentsTitles
                        resp.success = false

                        response.status = HttpStatus.BAD_REQUEST.value()
                        render resp as JSON
                        return
                    }

                    if (!cmd.policyStartDate) {
                        cmd.policyStartDate = quote.policyStartDate
                    }


                    policySgService.updatePolicyDocumentsReceived(quote, cmd.policyStartDate, ProductTypeEnum.CAR)

                    Map resp = [:]
                    resp.message = "success"
                    resp.success = true

                    response.status = HttpStatus.OK.value()
                    render resp as JSON
                    return

                } catch (Exception e) {
                    log.error("exception while submitting documents to received, quoteId:${quote.id}", e)
                    response.status = HttpStatus.BAD_REQUEST.value()
                    render "Error while submitting the docs"
                    return
                }

                return

            } else {
                log.error("attempted to upload doc when quote paymentStatus is not allowing to update: ${quote.id}")
                response.status = HttpStatus.UNAUTHORIZED.value()
                render "Not Authorized/Not Allowed due to policy Status: ${quote.paymentStatus.toString().toLowerCase()}."
                return
            }
        }
        catch (Exception e) {
            response.status = HttpStatus.INTERNAL_SERVER_ERROR.value()
            render "Internal Server Error Or quote Id is not valid."
            return
        }
    }

}
