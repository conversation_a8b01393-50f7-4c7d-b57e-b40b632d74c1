package com.cover.apiV1

import com.cover.api.ApiResponseCodeEnum
import com.cover.api.response.ErrorResponse
import com.cover.travel.TravelQuoteCommand
import com.cover.travel.TravelRateCommand
import com.cover.travel.TravellersCommand
import com.safeguard.AsyncEventConstants
import com.safeguard.CountryEnum
import com.safeguard.DonationTypeEnum
import com.safeguard.ProductTypeEnum
import com.safeguard.Donation
import com.safeguard.User
import com.safeguard.travel.TravelDestinationCountryZoneEnum
import com.safeguard.travel.TravelQuote
import com.safeguard.travel.TravelQuoteCountry
import com.safeguard.travel.Traveler
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.joda.time.LocalDate
import org.springframework.http.HttpStatus

class TravelController {

    static namespace = "apiV1"

    def apiQuoteService
    def messageSource
    def travelQuoteService
    def utilService
    def commonPolicySgService

    def saveQuote() {
        TravelQuoteCommand quoteCommand = new TravelQuoteCommand()
        def jsonData = request.JSON

        // Get the list of properties from your command object
        def allowedProps = TravelQuoteCommand.declaredFields*.name - ['metaClass', 'class', 'errors', 'constraints']

        // Filter the JSON to only include allowed properties
        def filteredJson = jsonData.subMap(allowedProps.intersect(jsonData.keySet()))

        // Now bind only the filtered data
        bindData(quoteCommand, filteredJson)

        //TravelQuoteCommand quoteCommand = new TravelQuoteCommand(request.JSON)
        log.info("travel.saveQuote - entering with [quoteCommand:${quoteCommand.toString()}]")

        Locale locale = utilService.convertToLocale(params.lang)
        CountryEnum country = utilService.convertToCountry(params.country)

        Map model = [:]

        try {
            quoteCommand.lang = locale.language
            quoteCommand.country = country.code

            if (!quoteCommand.validate()) {
                //TODO:// Handle error and return
                log.error("travel.saveQuote - Validation Failed. Errors:${quoteCommand.errors}")
                def errorList = utilService.generateErrorResponse(quoteCommand.errors, locale, "travel.quote")
                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())

                def errors = [errors: errorList]
                render errors as JSON
                return
            }

            TravelQuote quote = travelQuoteService.createTravelQuote(quoteCommand)

            model.quoteId = AESCryption.encrypt(quote.id.toString())
            model.quote = formatQuoteForResponse(quote, locale)

            if (params.returnQuotes && params.returnQuotes == 'true') {
                List<TravelRateCommand> travelRatings = travelQuoteService.getRatings(quote)
                model.ratings = formatRatingsForResponse(travelRatings)
            }

            //sending Travel quote email...
            notify AsyncEventConstants.EMAIL_TRAVEL_QUOTE_CREATED, [quoteId:quote.id,
                                                                    lang:quoteCommand.lang,
                                                                    country:quoteCommand.country]

            notify AsyncEventConstants.WHATSAPP_NOTIFICATION_TRIGGER, [templateName:"travel_quote_update_v3",
                                                                       quoteId:model.quoteId,
                                                                       lang:quoteCommand.lang,
                                                                       country:quoteCommand.country,
                                                                       recipient:quoteCommand.userMobile,
                                                                       type: "travel"]

        } catch (IllegalArgumentException iae) {
            log.error("apiV1.travel.saveQuote - error updating saving travel quote. quoteCommand:${quoteCommand.toString()}", iae)

            response.setStatus(HttpStatus.BAD_REQUEST.value())

            ErrorResponse error = new ErrorResponse()
            error.developerMessage = iae.message
            error.userMessage = iae.getMessage()

            def errors = [errors:[error]]
            render errors as JSON
            return

        } catch (Exception e) {
            log.error("apiV1.travel.saveQuote - error updating saving travel quote. quoteCommand:${quoteCommand.toString()}", e)

            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())

            ErrorResponse error = new ErrorResponse()
            error.developerMessage = e.message
            error.userMessage = messageSource.getMessage("default.general.error", [].toArray(), locale)

            def errors = [errors:[error]]
            render errors as JSON
            return
        }

        render model as JSON
    }

    def quotes(String id) {
        log.info("travel.quotes - entering with [id:$id]")

        Locale locale = utilService.convertToLocale(params.lang)
        Long quoteId
        TravelQuote travelQuote

        try {
            if (id) {
                quoteId = Long.parseLong(AESCryption.decrypt(id))
                travelQuote = TravelQuote.read(quoteId)

                if (!travelQuote) {
                    log.info "travel.quotes - Quote not found, quoteId:${quoteId}"

                    ErrorResponse error = new ErrorResponse()
                    error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.code()
                    error.moreInfo = ""
                    error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.key(), [].toArray(), "Travel Quote ID not valid", locale)
                    def errors = [errors:[error]]
                    response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                    render errors as JSON

                    return
                }
            }
        } catch (e) {
            log.error "travel.quotes - Unable to decrypt travel quote id ${id}"

            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.key(), [].toArray(), "Travel Quote ID not valid", locale)
            def errors = [errors:[error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        if (travelQuote.startDate.isBefore(LocalDate.now())) {
            log.warn "travel.quotes - Start date of this quote has already passed: ${travelQuote.id}"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVEL_QUOTE_EXPIRED.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVEL_QUOTE_EXPIRED.key(), [].toArray(), "Start date of this quote has already passed, please create a new quote", locale)
            def errors = [errors:[error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        if (travelQuote.isNotProcessed()) {
            log.info("travel.quotes - getting ratings for quote:${travelQuote.id}")

            List<TravelRateCommand> travelRatings = travelQuoteService.getRatings(travelQuote)

            Map model = [:]
            model.quoteId = id
            model.quote = formatQuoteForResponse(travelQuote, locale)
            model.ratings = formatRatingsForResponse(travelRatings)

            render model as JSON
            return

        } else {
            log.warn "travel.quotes - Quote is already processed for quote id: ${travelQuote.id}"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_ALREADY_PROCESSED.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_ALREADY_PROCESSED.key(), [].toArray(), "Travel Quote is already processed", locale)
            def errors = [errors:[error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

    }

    private List formatRatingsForResponse(List<TravelRateCommand> ratingCommands) {
        List ratings = []

        ratingCommands.each { TravelRateCommand rateCommand ->
            Map rating = formatRatingForResponse(rateCommand)
            ratings.add(rating)
        }

        return ratings
    }

    private Map formatRatingForResponse(TravelRateCommand rateCommand) {
        def covers = rateCommand.covers.collectEntries {
            ["${it.slug}": [
                "name": "${it.cover}",
                "value": it.value,
                "parent": ["slug": it.parent.slug, "name": it.parent.name]
            ]
            ]
        }
        /*List coverParents = rateCommand.covers.collect { it.parent }.unique {
            a, b -> a.slug <=> b.slug
        }
        log.info("coverParents:${coverParents}")*/

        Map rating = [
            product: [
                encId: AESCryption.encrypt(rateCommand.productId.toString()),
                name: rateCommand.productName
            ],
            provider: [
                //id: rateCommand.providerId,
                name: rateCommand.provider,
                image: rateCommand.providerImage
            ],
            covers: covers,
            totalPrice: rateCommand.totalPrice
        ]

        return rating
    }

    private Map formatQuoteForResponse(TravelQuote travelQuote, Locale locale) {
        Map quoteMap = [:]

        quoteMap.startDate = travelQuote.startDate.toString("dd-MM-yyyy")
        quoteMap.endDate = travelQuote.endDate.toString("dd-MM-yyyy")
        //quoteMap.requestSource = travelQuote.requestSource
        User user = travelQuote.user
        quoteMap.userName = user.name
        quoteMap.userEmail = user.email
        quoteMap.userMobile = user.mobile
        quoteMap.travelers = []
        int travelerSize = travelQuote.travelerList.size()

        travelQuote.travelerList.each { Traveler traveller ->
            quoteMap.travelers.add(
                [id:                    traveller.id,
                 //birthDate:             traveller.birthDate.toString("dd-MM-yyyy"),
                 dob:                   traveller.birthDate.toString("dd-MM-yyyy"),
                 title:                 traveller.titleId,
                 firstName:             traveller.firstName,
                 lastName:              traveller.lastName,
                 identificationNumber:  traveller.identificationNumber,
                 isPolicyHolder:        traveller.isPolicyHolder == true ? true : false,
                 gender:                traveller.genderId,
                 email:                 traveller.email]
            )
        }

        def policyHolderTraveler = quoteMap.travelers.find { it.isPolicyHolder}

        if (policyHolderTraveler == null) {
            quoteMap.travelers[0].isPolicyHolder = true
        }

        quoteMap.sourceCountry = travelQuote.sourceCountry.name
        TravelDestinationCountryZoneEnum destinationZone = TravelDestinationCountryZoneEnum.findByCountry(travelQuote.destinationCountryId)
        quoteMap.destinationCountryZone = [id: destinationZone.id, name:destinationZone.getText(locale.language)]
        List travellingCountries = []
        travelQuote.travellingCountries?.each { TravelQuoteCountry travellingCountry ->
            travellingCountries.add([id:travellingCountry.countryId, name: travellingCountry.country.name])
        }
        quoteMap.travellingCountries = travellingCountries

        return quoteMap
    }

    def updateQuote(String id) {
        log.info("travel.updateQuote - entering with [id:$id]")

        Locale locale = utilService.convertToLocale(params.lang)
        Long quoteId
        TravelQuote travelQuote

        try {
            if (id) {
                quoteId = Long.parseLong(AESCryption.decrypt(id))
                travelQuote = TravelQuote.read(quoteId)

                if (!travelQuote) {
                    log.info "travel.quotes - Quote not found, quoteId:${quoteId}"

                    ErrorResponse error = new ErrorResponse()
                    error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.code()
                    error.moreInfo = ""
                    error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.key(), [].toArray(), "Travel Quote ID not valid", locale)
                    def errors = [errors:[error]]
                    response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                    render errors as JSON

                    return
                }
            }
        } catch (e) {
            log.error "Unable to decrypt travel quote id ${id}"

            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.key(), [].toArray(), "Travel Quote ID not valid", locale)
            def errors = [errors:[error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        if (!travelQuote.isNotProcessed()) {

            log.warn "travel.quotes - Quote is already processed for quote id: ${travelQuote.id}"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_ALREADY_PROCESSED.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_ALREADY_PROCESSED.key(), [].toArray(), "Travel Quote is already processed", locale)
            def errors = [errors: [error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        if (!request.JSON.productId) {
            String key = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.key()
            String code = "productId"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.QUOTE_PRODUCTID_MISSING.code()
            error.developerMessage = key
            error.moreInfo = code
            error.userMessage = messageSource.getMessage(key, [].toArray(), "", locale)

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            def errors = [errors:[error]]
            render errors as JSON
            return
        }

        def requestParams = request.JSON

        apiQuoteService.updateTravelQuote(quoteId, requestParams)

        def resp = ["message": "success"]
        render resp as JSON
        return
    }

    def details(String id) {
        log.info("travel.details - entering with [id:$id]")

        Locale locale = utilService.convertToLocale(params.lang)
        Long quoteId
        TravelQuote travelQuote

        try {
            if (id) {
                quoteId = Long.parseLong(AESCryption.decrypt(id))
                travelQuote = TravelQuote.read(quoteId)

                if (!travelQuote) {
                    log.info "travel.details - Quote not found, quoteId:${quoteId}"

                    ErrorResponse error = new ErrorResponse()
                    error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.code()
                    error.moreInfo = ""
                    error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.key(), [].toArray(), "Travel Quote ID not valid", locale)
                    def errors = [errors:[error]]
                    response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
                    render errors as JSON

                    return
                }
            }
        } catch (e) {
            log.error "Unable to decrypt travel quote id ${id}"

            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.key(), [].toArray(), "Travel Quote ID not valid", locale)
            def errors = [errors:[error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        if (!travelQuote.isNotProcessed()) {

            log.warn "travel.details - Quote is already processed for quote id: ${travelQuote.id}"
            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_ALREADY_PROCESSED.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_ALREADY_PROCESSED.key(), [].toArray(), "Travel Quote is already processed", locale)
            def errors = [errors: [error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        TravelQuoteCommand travelQuoteCommand = travelQuoteService.toTravelQuoteCommand(travelQuote)
        TravelRateCommand travelRateCommand = travelQuoteService.getRating(travelQuoteCommand)

        Map model = [:]
        model.quoteId = id
        model.quote = formatQuoteForResponse(travelQuote, locale)
        model.rating = formatRatingForResponse(travelRateCommand)

        render model as JSON
        return
    }

    def updateDetails(TravellersCommand cmd) {
        log.info("travel.updateDetails - entering with [id:${params.id}]")
        cmd.quoteId = params.id
        log.info("cmd.quoteId:${cmd.quoteId}")

        boolean isValidForm = cmd.validate()
        if (!isValidForm) {

            def errors = cmd.errors
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        Long quoteId = Long.parseLong(AESCryption.decrypt(cmd.quoteId))
        TravelQuote travelQuote = TravelQuote.read(quoteId)

        if (!travelQuote) {
            log.info "travel.details - Quote not found, quoteId:${quoteId}"

            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.code()
            error.moreInfo = ""
            error.userMessage = messageSource.getMessage(ApiResponseCodeEnum.TRAVE_QUOTE_NOT_FOUND.key(), [].toArray(), "Travel Quote ID not valid", locale)
            def errors = [errors:[error]]
            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON

            return
        }

        def donationAmount = cmd.donationAmount ?: 0

        // Update donation information
        commonPolicySgService.updateDonation(travelQuote, ProductTypeEnum.TRAVEL, donationAmount, DonationTypeEnum.CHARITY)

        travelQuoteService.updateQuoteAndTravellers(quoteId, cmd.travelerList)

        def resp = ["message": "success"]
        render resp as JSON
        return
    }

}
