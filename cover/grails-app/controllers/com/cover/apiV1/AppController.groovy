package com.cover.apiV1

import com.cover.api.AppDeviceCommand
import com.safeguard.User
import grails.converters.JSON
import grails.plugins.rest.client.RestBuilder

/**
 * Manage Mobile Application device information
 *
 */
class AppController {

    static namespace = "apiV1"

    static allowedMethods = [
        login     : ['POST']
    ]

    def appService
    def springSecurityService

    /**
     * Store app device information
     *
     * @return
     */
    def device(AppDeviceCommand command) {
        log.info("app.device - entering with [command:${command}]")

        User user = springSecurityService.currentUser

        if (command.deviceToken) {

            appService.storeDeviceToken(command, user)

        } else {
            log.error("app.device - deviceToken not available")
        }

        def resp = [success:true]

        render resp as JSO<PERSON>
    }

    def config() {
        log.info("app.config")

        RestBuilder rest = new RestBuilder()

        String configFilePath = "https://assets.yallacompare.com/mobile_app/config/config"
        def resp = rest.get(configFilePath) {
            contentType 'application/json'
            json body as <PERSON>SO<PERSON>
        }

        render resp.json as JSON
    }

}
