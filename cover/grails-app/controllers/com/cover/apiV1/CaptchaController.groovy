package com.cover.apiV1

import com.cover.captchaV3.CaptchaCommand
import com.cover.captchaV3.ICaptchaService
import grails.converters.JSON

class CaptchaController {

    static namespace = "apiV1"

    static allowedMethods = [
        verify: ['POST']
    ]

    ICaptchaService captchaV3Service

    def verify(CaptchaCommand captchaCommand) {
        Map responseMap

        if (!captchaCommand.validate()) {
            def errors = []
            captchaCommand.errors.fieldErrors.each { errors.push(g.message(error: it) as String) }

            log.info ".verify - captchaCommand invalid, reason: ${errors.toString()}"

            responseMap = [
                success: false,
                error  : errors
            ]
        } else {
            try {
                final String response = captchaCommand.response
                captchaV3Service.processResponse(response, captchaCommand.action)
                responseMap = [
                    success: true
                ]
            } catch (Exception e) {
                e.printStackTrace()

                responseMap = [
                    success: false,
                    error  : e.message
                ]
            }
        }
        render responseMap as JSON
    }
}
