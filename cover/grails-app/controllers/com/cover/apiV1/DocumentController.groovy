package com.cover.apiV1

import com.cover.api.FileUploadCommand
import com.safeguard.DocumentType
import com.safeguard.DocumentTypeEnum
import com.safeguard.HealthPolicyDocument
import com.safeguard.HomePolicyDocument
import com.safeguard.PaymentStatusEnum
import com.safeguard.PolicyDocument
import com.safeguard.ProductTypeEnum
import com.safeguard.car.CarQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.life.LifePolicyDocument
import com.safeguard.life.LifeQuote
import com.safeguard.travel.TravelPolicyDocument
import com.safeguard.travel.TravelQuote
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.springframework.http.HttpStatus

class DocumentController {

    static namespace = "apiV1"

    static allowedMethods = [
        types: ["GET"],
        upload : ["POST"]
    ]

    def commonUtilService
    def quoteService
    def utilService
    def documentUploadService
    def springSecurityService

    /**
     * Get All document types that require documents from user
     *
     * @param lang
     * @param quoteId
     * @param productType
     * @return
     */
    def types(String lang, Long quoteId, String productType) {

        ProductTypeEnum productTypeEnum = ProductTypeEnum.findByName(productType ?: "")

        def types = []
        List<DocumentType> mandatoryTypes = []

        if (productTypeEnum == ProductTypeEnum.CAR) {
            CarQuote quote = CarQuote.read(quoteId)

            mandatoryTypes = DocumentType.findAllByIsMandatory(true)

            List<DocumentTypeEnum> otherMandatoryDocuments = commonUtilService.getRequiredDocumentsList(quote)
            otherMandatoryDocuments.each {
                DocumentType documentType = DocumentType.get(it.id)

                mandatoryTypes.add(documentType)
            }

        } else if (productTypeEnum == ProductTypeEnum.HEALTH) {

            mandatoryTypes = DocumentType.findAllByHealth(true)

        } else if (productTypeEnum == ProductTypeEnum.HOME) {

            mandatoryTypes = DocumentType.findAllByHome(true)

        }


        types = mandatoryTypes.collect {
            [
                code: it.code,
                name: (lang == "en") ? it.nameEn : it.nameAr
            ]
        }

        types = types.findAll {
            !(it.code in ["Policy", "other_documents"])
        }

        render(types as JSON)
    }

    def upload(FileUploadCommand cmd) {
        log.info("document.upload - entering with [quoteId:${cmd.quoteId}, productType:${cmd.productType}, docType:${cmd.docType}]")

        def currentUser = springSecurityService.currentUser
        def quote = quoteService.getQuoteById(cmd.quoteId, cmd.productType)

        // make sure this user has permission to upload
        if (!quote.belongsTo(currentUser)) {
            render(status: HttpStatus.UNAUTHORIZED.value())
            return
        }

        def file = params.file

        // make sure files were actually uploaded
        if (!file) {
            render(status: HttpStatus.BAD_REQUEST.value())
            return
        }

        // make sure only 1 file is uploaded
        if (utilService.isCollectionOrArray(file)) { // multiple files
            render(status: HttpStatus.BAD_REQUEST.value())
            return
        }

        def result = documentUploadService.savePolicyDocument(cmd.file, DocumentType.findByCode(cmd.docType), quote,
            ProductTypeEnum.findByName(cmd.productType), true, false, null)

        def policyDocument = result.policyDocument

        log.info("document.upload - returning policyDocument:${policyDocument}")

        render([message: "File Uploaded", path: policyDocument.fullPath] as JSON)

    }

    /**
     * Get Insurance Policy Document against the provided insurance type and id
     *
     * @param insuranceType
     * @param id
     * @param country
     * @param lang
     * @param merchantReference
     * @return
     */
    def policyDocument(String insuranceType, String id, String country, String lang, String merchantReference) {
        log.info("document.policyDocument - entering with [id:$id, country:$country, lang:$lang, mRef:$merchantReference]")

        Long quoteId

        try {

            quoteId = AESCryption.decrypt(id).toLong()

        } catch (e) {
            log.error("Unable to decrypt documentId : $id")
            response.status = HttpStatus.NOT_FOUND.value()

            return
        }

        ProductTypeEnum productType = ProductTypeEnum.findByName(insuranceType)
        DocumentType documentType = DocumentType.get(DocumentTypeEnum.INSURANCE_POLICY.getId())

        def document
        String quoteMerchantReference

        switch (productType) {
            case ProductTypeEnum.CAR:
                CarQuote quote = CarQuote.get(quoteId)
                if (quote && quote.paymentStatus == PaymentStatusEnum.ISSUED) {
                    quoteMerchantReference = quote.merchantRef
                    document = PolicyDocument.findByCarQuoteAndDocumentTypeAndIsDeleted(quote, documentType, false)
                }

                break
            case ProductTypeEnum.HEALTH:
                HealthQuote quote = HealthQuote.get(quoteId)
                if (quote && quote.paymentStatus == PaymentStatusEnum.ISSUED) {
                    quoteMerchantReference = quote.merchantRef
                    document = HealthPolicyDocument.findByHealthQuoteAndDocumentTypeAndIsDeleted(quote, documentType, false)
                }

                break
            case ProductTypeEnum.HOME:
                HomeQuote quote = HomeQuote.get(quoteId)
                if (quote && quote.paymentStatus == PaymentStatusEnum.ISSUED) {
                    quoteMerchantReference = quote.merchantRef
                    document = HomePolicyDocument.findByHomeQuoteAndDocumentTypeAndIsDeleted(quote, documentType, false)
                }

                break
            case ProductTypeEnum.LIFE:
                LifeQuote quote = LifeQuote.get(quoteId)
                if (quote && quote.paymentStatus == PaymentStatusEnum.ISSUED) {
                    quoteMerchantReference = quote.merchantRef
                    document = LifePolicyDocument.findByLifeQuoteAndDocumentTypeAndIsDeleted(quote, documentType, false)
                }

                break

            case ProductTypeEnum.TRAVEL:
                TravelQuote quote = TravelQuote.get(quoteId)
                if (quote && quote.paymentStatus == PaymentStatusEnum.ISSUED) {
                    quoteMerchantReference = quote.merchantRef
                    document = TravelPolicyDocument.findByTravelQuoteAndDocumentTypeAndIsDeleted(quote, documentType, false)
                }

                break
        }

        //Verify merchant reference and if document is available
        if (!document || merchantReference != quoteMerchantReference) {
            log.info("document.policyDocument - no document or reference not matched, id:$id, mRef:$merchantReference")
            response.status = HttpStatus.NOT_FOUND.value()

            return
        }

        URL url = new URL(document.fullPath.replace("http:", "https:"))
        URLConnection connection = url.openConnection()

        InputStream inputStream = connection.inputStream

        response.setHeader("Content-disposition", "attachment; filename=${document.filename}")
        response.contentType = "application/octet-stream"
        response.outputStream << inputStream
        response.outputStream.flush()

        inputStream.close()
    }
}
