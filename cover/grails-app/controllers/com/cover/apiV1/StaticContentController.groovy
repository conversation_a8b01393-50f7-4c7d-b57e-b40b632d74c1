package com.cover.apiV1

import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.car.StaticContent
import org.springframework.http.HttpStatus

class StaticContentController {

    static namespace = "apiV1"

    static allowedMethods = [
        content     : ['GET']
    ]

    /**
     * Get static Content for a page
     * @param country
     * @param lang
     * @param slug
     * @return
     */
    def content(String country, String lang, String slug) {
        log.info "apiV1.staticContent.content - entering with country: $country, lang: $lang, slug: $slug"

        CountryEnum countryEnum = CountryEnum.findCountry(country)

        if(!countryEnum) {
            render status: HttpStatus.NOT_FOUND
            return
        }

        Country c = Country.get(countryEnum.id)
        StaticContent staticContent = StaticContent.findByCountryAndSlug(c, slug)

        if (!staticContent && countryEnum != CountryEnum.UAE) {
            c = Country.get(CountryEnum.UAE.id)
            staticContent = StaticContent.findByCountryAndSlug(c, slug)
        }

        if(!staticContent) {
            render status: HttpStatus.NOT_FOUND
            return
        }

        Locale locale = new Locale(lang)

        Map model = [
            metaDescription : staticContent.metaDescription,
            title           : staticContent.getTitle(locale),
            content         : staticContent.getContent(locale)
        ]

        render view: "/apiV1/staticContent", model: model
    }
}
