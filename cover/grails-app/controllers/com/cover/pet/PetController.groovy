package com.cover.pet

import org.springframework.util.NumberUtils

class PetController {
    static namespace = 'pet'
    def petQuoteService
    def utilService
    def petRatingService
    def petQuote


    def index() {
            redirect(url: "${grailsApplication.config.getProperty('yallacompare.baseURL')}/uae/en/pet-insurance")
    }

    def quotes() {

        if(params.country == 'uae') {
            def country = utilService.getCountry()
            def petRatings = params.petRatings
            log.info("petController.quotes - [petRatings:${petRatings}, petQuote: ${petQuote}]")

            if(request.method == 'POST') {
                log.info("petController.quotes - [params :${params}]")
                petQuote.totalPrice = NumberUtils.parseNumber(params.totalPrice, BigDecimal.class)
                petQuoteService.saveQuote(petQuote)
                redirect mapping: "petThankyou", params: [lang: params.lang, country: params.country]
                return
            }else {
                render view: '/pet/quotes', model: [country: params.country]
                return
            }
        } else {
            render view: '/common/notFound'
        }
    }

    def thankyou() {
        if (params.country == 'uae') {
            render view: '/pet/thankyou', model: [lang: params.lang, country: params.country]
            return
        } else {
            render view: '/common/notFound'
            return
        }
    }

}
