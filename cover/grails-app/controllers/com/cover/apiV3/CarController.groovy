package com.cover.apiV3

import com.cover.api.ApiResponseCodeEnum
import com.cover.api.CarQuoteCommand
import com.cover.api.response.ErrorResponse
import com.cover.car.GigRateV2Service
import com.cover.car.commands.DriverCommand
import com.cover.car.commands.RateCommand
import com.cover.car.commands.VehicleCommand
import com.cover.common.commands.MarketingTrackingCommand
import com.cover.util.IConstant
import com.safeguard.AsyncEventConstants
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverPreferenceEnum
import com.safeguard.QuoteCreatorEnum
import com.safeguard.RequestSourceEnum
import com.safeguard.User
import com.safeguard.car.CarQuote
import com.safeguard.util.AESCryption
import grails.converters.JSON
import org.joda.time.LocalDate
import org.joda.time.format.DateTimeFormat
import org.joda.time.format.DateTimeFormatter
import org.springframework.http.HttpStatus

class CarController {

    static namespace = "apiV3"

    static allowedMethods = [
        saveQuote               : ['POST'],
        quotes                  : ['GET']
    ]

    def messageSource
    def quoteService
    def utilService
    def valuationService

    /**
     * Save quote
     *
     * @param vehicleCommand
     * @param driverCommand
     * @return Encrypted Quote Id as JSON
     */
    def saveQuote(CarQuoteCommand carQuoteCommand) {
        log.info("apiV3.car.saveQuote - entering with [carQuoteCommand:${carQuoteCommand.toString()}]")
        if (carQuoteCommand.trim) {
            carQuoteCommand.model = carQuoteCommand.trim
        }
        carQuoteCommand.country = params.country
        CountryEnum countryEnum = utilService.convertToCountry(params.country)
        Country country = Country.get(countryEnum.id)

        String lang = utilService.convertToLocale(params.lang)
        Locale locale = new Locale(lang)

        //Set default values
        if (carQuoteCommand.year && !carQuoteCommand.firstRegistrationDate) {
            String defaultDate = "01-01-${carQuoteCommand.year}"
            carQuoteCommand.firstRegistrationDate = carQuoteCommand.firstRegistrationDate ?: defaultDate
        }

        carQuoteCommand.internationalExperience = carQuoteCommand.internationalExperience ?: 1

        if (carQuoteCommand.firstLicenseCountry == null) {
            carQuoteCommand.firstLicenseCountry = CountryEnum.UNKNOWN.id.toInteger()
        }

        if (!carQuoteCommand.validate()) {
            log.error("apiV3.car.saveQuote - validation error - command:$carQuoteCommand")

            def errorList = utilService.generateErrorResponse(carQuoteCommand.errors, locale, "car.quote")
            def errors = [errors:errorList]

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON
            return
        }

        VehicleCommand vehicleCommand = carQuoteCommand.toVehicleCommand()
        DriverCommand driverCommand = carQuoteCommand.toDriverCommand()

        log.info("apiV3.car.saveQuote - carQuoteCommand is Valid:${carQuoteCommand.validate()}")

        DateTimeFormatter formatter = DateTimeFormat.forPattern("dd-MM-yyyy")

        Date policyStartDate = LocalDate.parse(carQuoteCommand.policyStartDate, formatter).toDate()
        Date firstRegistrationDate = LocalDate.parse(carQuoteCommand.firstRegistrationDate, formatter).toDate()
        Date oldPolicyExpiryDate =  carQuoteCommand.oldPolicyExpiryDate ?
            LocalDate.parse(carQuoteCommand.oldPolicyExpiryDate, formatter).toDate() : null
        Date purchaseDate = carQuoteCommand.purchaseDate ?
            LocalDate.parse(carQuoteCommand.purchaseDate, formatter).toDate() : null
        Date dob = LocalDate.parse(carQuoteCommand.dob, formatter).toDate()

        vehicleCommand.oldPolicyExpiryDate = oldPolicyExpiryDate
        vehicleCommand.purchaseDate = purchaseDate
        driverCommand.policyStartDate = policyStartDate
        driverCommand.dob = dob
        vehicleCommand.firstRegistrationDate = firstRegistrationDate

        log.info("policyStartDate:${policyStartDate}, firstRegistrationDate:$firstRegistrationDate, " +
            "oldPolicyExpiryDate:${oldPolicyExpiryDate}, purchaseDate:$purchaseDate, dob:$dob")

        def valuation = valuationService.getValuation(vehicleCommand.model, vehicleCommand.year,
            vehicleCommand.isBrandNew, country, vehicleCommand.chassisNumber, !vehicleCommand.isNonGcc)

        log.info("car.saveQuote - carQuoteCommand:${carQuoteCommand.toString()}, valuation:${valuation}")

        //TODO: App isn't handling this error, so only restricting for SmartDubai
        if (carQuoteCommand.source == RequestSourceEnum.SMARTDUBAI.toString() &&
            (vehicleCommand.insuredValue < valuation.minimum ||
                vehicleCommand.insuredValue > valuation.maximum)) {

            ErrorResponse error = new ErrorResponse()
            error.errorCode = ApiResponseCodeEnum.CAR_QUOTE_insuredValue_EXCEEDED.code()
            error.moreInfo = "insuredValue"
            error.userMessage = messageSource
                .getMessage(ApiResponseCodeEnum.CAR_QUOTE_insuredValue_EXCEEDED.key(), [].toArray(), "", locale)

            def errors = [errors:[error]]

            response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value())
            render errors as JSON
            return
        }

        def quote
        Boolean newUser = false

        MarketingTrackingCommand marketingTracking = new MarketingTrackingCommand()
        marketingTracking.queryString = session[IConstant.INSURANCE_QUERY_STRING]
        marketingTracking.utmSource = session[IConstant.INSURANCE_UTM_SOURCE]
        marketingTracking.utmMedium = session[IConstant.INSURANCE_UTM_MEDIUM]
        marketingTracking.utmCampaign = session[IConstant.INSURANCE_UTM_CAMPAIGN]
        marketingTracking.gclid = session[IConstant.INSURANCE_GCLID]
        marketingTracking.fbclid = session[IConstant.INSURANCE_FBCLID]
        driverCommand.marketingTracking = marketingTracking

        CarQuote duplicatedFromQuote = carQuoteCommand.duplicatedFromQuoteId ? CarQuote.read(carQuoteCommand.duplicatedFromQuoteId) : null
        User createdBySalesPerson = carQuoteCommand.createdBySalesPersonId ? User.read(carQuoteCommand.createdBySalesPersonId) : null
        (quote, newUser) = quoteService.createCarQuote(driverCommand, vehicleCommand.year,
            vehicleCommand.model, vehicleCommand.makeModelTrim, vehicleCommand.city,
            vehicleCommand.firstRegistrationDate, vehicleCommand.insuredValue,
            vehicleCommand.isNonGcc, vehicleCommand.isExpiredPolicy,
            vehicleCommand.isThirdParty, vehicleCommand.isOldAgency,
            vehicleCommand.purchaseDate, vehicleCommand.isNotBoughtYet,
            vehicleCommand.isBrandNew, vehicleCommand.isFirstCar,
            vehicleCommand.isBuyingUsedCar, carQuoteCommand.queryString,
            valuation.minimum, valuation.maximum, vehicleCommand.oldPolicyExpiryDate, countryEnum, lang,
            carQuoteCommand.chassisNumber, carQuoteCommand.plateNumber,
            carQuoteCommand.plateCode, carQuoteCommand.plateCategory, valuation.valuationSource,
            carQuoteCommand.financeInstitutionId, carQuoteCommand.financeInstitutionName,
            carQuoteCommand.source, null, null, null, null, null, false,
            duplicatedFromQuote, createdBySalesPerson, carQuoteCommand.autodataSpec, carQuoteCommand.admeId,
            carQuoteCommand.subSource)

        notify AsyncEventConstants.CAR_QUOTE_CREATED, [quoteId: quote.id, lang: lang,
                                                       creator: QuoteCreatorEnum.API]

        String encQuoteId = AESCryption.encrypt(quote.id.toString())
        if (carQuoteCommand.returnQuotes) {
            return quotes(encQuoteId)
        }

        def resp = [id: encQuoteId]
        render resp as JSON
    }

    /**
     * Get Car quotes (ratings)
     *
     * @return Quotes as JSON
     */
    def quotes(String id) {
        log.info("apiV3.car.quotes - entering with params:[id:$id]")

        Integer quoteId = Integer.parseInt(AESCryption.decrypt(id))
        log.info("apiV3.car.quotes - quoteId:${quoteId}")

        def resp = [:]

        if (quoteId) {
            CarQuote carQuote = CarQuote.findById(quoteId)

            if (carQuote && carQuote.isAncient()) {
                log.info ".quotes Policy [quoteId:${quoteId}] created on ${carQuote.dateCreated} so redirecting to 404"
                response.status = HttpStatus.NOT_FOUND.value()
                resp.message = "Not Found"

            } else if (carQuote && carQuote.isNotProcessed()) {

                List<RateCommand> ratings
                def quote

                if (params.country == CountryEnum.UAE.code) {
                    (ratings, quote) = quoteService.getRatings(quoteId)
                }
                log.info("ratings:${ratings}")
                if(!ratings) {
                    quoteService.setNoQuote(carQuote)
                    notify AsyncEventConstants.SALESFORCE_CAR_QUOTE_SYNC, carQuote.id

                    resp.message = g.message(code: 'noquote.sorry')
                    resp.reasons = []
                    resp.reasons.add(g.message(code: 'noquote.veryHighValue'))
                    resp.reasons.add(g.message(code: 'noquote.youngDriver'))
                    if (params.country == CountryEnum.UAE.code) {
                        resp.reasons.add(g.message(code: 'noquote.modified'))
                    } else if (params.country == CountryEnum.KWT.code) {
                        resp.reasons.add(g.message(code: 'noquote.modified.kwt'))
                    }
                    resp.message1 = g.message(code: 'noquote.manualQuotation')

                    render resp as JSON
                    return
                }

                if (carQuote.coverPreference == CoverPreferenceEnum.THIRD_PARTY) {
                    ratings.sort { a, b ->
                        if (a.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return -1
                        } else if (b.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return 1
                        } else {
                            // Compare by type and then by premium
                            b.coverageTypeId <=> a.coverageTypeId ?: a.premium <=> b.premium
                        }
                    }
                } else {
                    // here you can add other sorting on comprehensive policies
                    ratings.sort { a, b ->
                        if (a.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return -1
                        } else if (b.productId == GigRateV2Service.PRODUCT_TPL_ID) {
                            return 1
                        } else {
                            // Compare by type and then by premium
                            // a.isAgencyRepair() <=> b.isAgencyRepair() ?: a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
                            a.coverageTypeId <=> b.coverageTypeId ?: a.premium <=> b.premium
                        }
                    }
                }

                // sort list based on isOfflineQuotes
                ratings.sort { rate1, rate2 ->
                    (rate1.isOfflineQuotes ? 1 : 0) <=> (rate2.isOfflineQuotes ? 1 : 0)
                }

                def lowestComprehensiveQuote = ratings?.findAll() { it.coverageTypeId == 1 }?.min() { it.premium }

                render view:"/apiV3/car/ratings", model:[carQuote:carQuote, ratings:ratings,
                                                         lowestComprehensiveQuote:lowestComprehensiveQuote]
                return

            } else if (!carQuote) {
                log.warn "quotes - Quote is not found "
                response.status = HttpStatus.NOT_FOUND.value()
                render resp as JSON
                return

            } else {
                log.warn "quotes - Quote is already processed for quote id: ${carQuote?.id}"

                resp.message = g.message(code:'error.alreadyProcessed')
            }

            render resp as JSON
            return
        }

        response.status = HttpStatus.NOT_FOUND.value()
        render resp as JSON
        return
    }

}
