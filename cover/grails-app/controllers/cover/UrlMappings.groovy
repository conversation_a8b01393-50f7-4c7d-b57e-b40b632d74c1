package cover

class UrlMappings {

    static mappings = {
        "/$controller/$action?/$id?(.$format)?" {
            constraints {
                // apply constraints here
            }
        }

        "/"(controller: 'funnel', action: 'index')
        "/$country"(controller: 'funnel', action: 'index')
        "/$country/$lang"(controller: 'funnel', action: 'index')

        name oldInsuranceIndex: "/$country/$lang"(namespace: 'static', controller: 'static', action: 'index')
        name insuranceIndex: "/$country/$lang/hub"(namespace: 'static', controller: 'static', action: 'hub')

        "/$country/$lang/insurance-widget"(view: '/iframes/insuranceWidget')

        "/$country/$lang/youth-pa" (controller: "paYouth", action: "index")

        "/ajax/$action"(controller: 'ajax', action: $action)
        "/ajax/confirmNoorRatingsLoaded/$id"(controller: 'ajax', action: "confirmNoorRatingsLoaded")

        "/android-chrome-192x192.png" (uri: "/assets/android-chrome-192x192.png")
        "/android-chrome-256x256.png" (uri: "/assets/android-chrome-256x256.png")
        "/apple-touch-icon.png" (uri: "/assets/apple-touch-icon.png")
        "/browserconfig.xml" (uri: "/assets/browserconfig.xml")
        "/favicon.ico" (uri: "/assets/favicon.ico")
        "/favicon-16x16.png" (uri: "/assets/favicon-16x16.png")
        "/favicon-32x32.png" (uri: "/assets/favicon-32x32.png")
        "/manifest.json" (uri: "/assets/manifest.json")
        "/mstile-150x150.png" (uri: "/assets/mstile-150x150.png")
        "/safari-pinned-tab.svg" (uri: "/assets/safari-pinned-tab.svg")


        //**************
        // API
        "/v1/app/config"(namespace: "apiV1", controller: "app", action: "config", method: "GET")
        "/v1/app/device"(namespace: "apiV1", controller: "app", action: "device", method: "POST")
        "/v1/profile/socialLogin"(namespace: "apiV1", controller: "authentication", action: "login", method: "POST")
        "/v1/profile/create"(namespace: "apiV1", controller: "profile", action: "create", method: "POST")
        "/v1/profile/view" (namespace: "apiV1", controller: "profile", action: "view", method: "GET")
        "/v1/profile/update" (namespace: "apiV1", controller: "profile",  action: "update", method: "POST")
        "/v1/profile/request-verification-email"(namespace: "apiV1", controller: "profile", action: "requestVerificationEmail", method: "GET")
        "/v1/profile/request-otp"(namespace: "apiV1", controller: "profile", action: "requestOTP", method: "GET")
        "/v1/profile/verify-email"(namespace: "apiV1", controller: "profile", action: "verifyEmail", method: "GET")
        "/v1/profile/forgot-password"(namespace: "apiV1", controller: "profile", action: "forgotPassword", method: "POST")
        "/v1/profile/notifications"(namespace: "apiV1", controller: "profile", action: "notifications", method: "GET")
        "/v1/profile/unread"(namespace: "apiV1", controller: "profile", action: "unread", method: "GET")
        "/v1/profile/applications"(namespace: "apiV1", controller: "profile", action: "applications", method: "GET")
        "/v1/profile/forgotPassword"(namespace: "apiV1", controller: "profile", action: "forgotPassword", method: "POST")
        "/v1/profile/verifyForgotPasswordEmail/$hash"(namespace: "apiV1", controller: "profile", action: "verifyForgotPasswordEmail", method: "GET")
        "/v1/profile/resetPassword"(namespace: "apiV1", controller: "profile", action: "resetPassword", method: "POST")
        "/v1/profile/checkUserResetPasswordStatus/$encryptedUserId"(namespace: "apiV1", controller: "profile", action: "checkUserResetPasswordStatus", method: "GET")
        "/v1/profile/subscriptions"(namespace: "apiV1", controller: "profile", action: "subscriptions", method: "GET")

        "/v1/policies/$country/$lang/$type"(namespace:"apiV1", controller:"policy", action:"list", method: "GET")
        "/v1/policies/$country/$lang/$type/$id/request-renewal"(namespace:"apiV1", controller:"policy", action:"requestRenewal", method: "POST")
        "/v1/policies/$country/$lang/$type/$id/request-cancellation"(namespace:"apiV1", controller:"policy", action:"requestCancellation", method: "POST")
        "/v1/policies/$country/$lang/$type/$id/emergency-info"(namespace:"apiV1", controller:"policy", action:"emergencyInfo", method: "GET")
        name viewDocument: "/v1/policies/document/$type/$id"(namespace: "apiV1", controller: "policy", action: "document", method: "GET")

        "/v1/documents/types/$lang"(namespace: "apiV1", controller: "document", action: "types", method: "GET")
        "/v1/documents/upload"(namespace: "apiV1", controller: "document", action: "upload", method: "POST")

        "/v1/$insuranceType/documents/$country/$lang/$id/policy"(controller:"document", action:"policyDocument", namespace:"apiV1", method: "GET")

        "/v1/car/ddl/$country/$lang"(controller:"car", action:"ddl", namespace:"apiV1")
        "/v1/car/makes/$country/$lang"(controller:"car",  action:"makes", namespace:"apiV1")
        "/v1/car/models/$country/$lang"(controller:"car", action:"models", namespace:"apiV1")
        "/v1/car/trims/$country/$lang"(controller:"car", action:"trims", namespace:"apiV1")
        "/v1/car/valuation/$country/$lang"(controller:"car", action:"valuation", namespace:"apiV1")
        "/v1/car/countries/$country/$lang"(controller:"car", action:"countries", namespace:"apiV1")
        "/v1/car/quotes/$country/$lang"(controller:"car", action:"saveQuote", namespace:"apiV1", method:"POST")
        "/v1/car/quotes/$country/$lang/detail"(controller:"car", action:"getRatingByParams", namespace:"apiV1", method:"GET")
        "/v1/car/quotes/$country/$lang/$id"(controller:"car", action:"quotes", namespace:"apiV1", method:"GET")
        name "quoteByProduct": "/v1/car/quotes/$country/$lang/$id/detail"(controller:"car", action:"detail", namespace:"apiV1", method:"GET")
        "/v1/car/quotes/$country/$lang/$id/checkout"(controller:"car", action:"checkout", namespace:"apiV1", method:"PUT")
        "/v1/car/documents/$country/$lang/$id"(controller:"car", action:"documentsData", namespace:"apiV1")

        "/v2/$insuranceType/$country/$lang/$id/updateQuotePayment"(controller:"car", action:"updateQuotePayment", namespace:"apiV2", method:"POST")

        "/v2/car/$country/$lang/ddl"(controller:"car", action:"ddl", namespace:"apiV2")
        "/v2/car/$country/$lang/adValuation"(controller:"car", action:"autoDataSpecsAndValuation", namespace:"apiV2")
        "/v2/car/$country/$lang/createQuote"(controller:"car", action:"createQuote", namespace:"apiV2", method:"POST")
        "/v2/car/$country/$lang/$id/updateQuote"(controller:"car", action:"saveQuoteDetailedData", namespace:"apiV2", method:"PUT")

        "/v3/car/quotes/$country/$lang"(controller:"car", action:"saveQuote", namespace:"apiV3", method:"POST")


        "/v2/travel/$country/$lang/createQuote"(controller: "travel", action: "createQuote", namespace: "apiV2")
        "/v2/health/$country/$lang/createQuote"(controller: "health", action: "createQuote", namespace: "apiV2")
        "/v2/health/$country/$lang/$id/updateQuote"(controller:"health", action:"updateQuote", namespace:"apiV2", method:"PUT")

        "/v1/static/content/$country/$lang/$slug"(controller: "staticContent", action: "content", namespace: "apiV1", method: "GET")

        "/v1/health/create-cross-sale-lead/$country/$lang/$id"(controller: "health", action: "createCrossSaleLead", namespace: "apiV1", method: "POST")
        "/v1/health/ddl/$country/$lang"(controller:"health", action:"ddl", namespace:"apiV1")
        "/v1/health/members/$country/$lang"(controller:"health", action:"members", namespace:"apiV1", method:"POST")
        "/v1/health/declarations/$country/$lang/$id"(controller:"health", action:"declarations", namespace:"apiV1", method:"GET")
        "/v1/health/declarations/$country/$lang/$id"(controller:"health", action:"saveDeclarations", namespace:"apiV1", method:"POST")
        "/v1/health/quotes/$country/$lang/$id"(controller:"health", action:"quotes", namespace:"apiV1", method:"GET")
        "/v1/health/quotes/$country/$lang/$id/detail"(controller:"health", action:"quoteDetail", namespace:"apiV1", method:"GET")
        "/v1/health/quotes/$country/$lang/$id/checkout"(controller:"health", action:"checkout", namespace:"apiV1", method:"PUT")

        "/v1/home/<USER>/$country/$lang"(controller:"home", action:"ddl", namespace:"apiV1")
        "/v1/home/<USER>/$country/$lang"(controller:"home", action:"saveQuote", namespace:"apiV1", method:"POST")
        "/v1/home/<USER>/$country/$lang/$id"(controller:"home", action:"quotes", namespace:"apiV1", method:"GET")
        "/v1/home/<USER>/$country/$lang/$id/detail"(controller:"home", action:"quoteDetail", namespace:"apiV1", method:"GET")
        "/v1/home/<USER>/$country/$lang/$id/checkout"(controller:"home", action:"checkout", namespace:"apiV1", method:"PUT")

        "/v1/life/products/metlife"(controller:"life", action:"getMetlifeProducts", namespace:"apiV1", method:"GET")
        "/v1/life/quotes/$country/$lang"(controller:"life",action:"saveQuote",namespace:"apiV1",method:"POST")
        "/v1/life/quotes/$country/$lang/$id/cheapest"(controller:"life",action:"getCheapestQuoteValue",namespace:"apiV1",method:"GET")

        "/v1/travel/quotes/$country/$lang"(namespace: 'apiV1', controller: 'travel', action: 'saveQuote')
        "/v1/travel/quotes/$country/$lang/$id"(namespace: 'apiV1', controller: 'travel', action: 'quotes')
        "/v1/travel/quotes/$country/$lang/$id"(namespace: 'apiV1', controller: 'travel', action: 'updateQuote', method:"POST")
        "/v1/travel/quotes/$country/$lang/$id/details"(namespace: 'apiV1', controller: 'travel', action: 'details', method:"GET")
        "/v1/travel/quotes/$country/$lang/$id/details"(namespace: 'apiV1', controller: 'travel', action: 'updateDetails', method:"POST")

        "/v1/userPaymentCard/list"(controller: "userPaymentCard", action:'listCards', namespace:'apiV1', method:'GET')
        "/v1/userPaymentCard/delete/$encPaymentCardId"(controller: "userPaymentCard", action:'deleteCard', namespace:'apiV1', method:'DELETE')
        "/v1/userPaymentCard/switch"(controller: "userPaymentCard", action:'switchPaymentCard', namespace:'apiV1', method:'POST')
        "/v1/userPaymentCard/default"(controller: "userPaymentCard", action:'defaultCard', namespace:'apiV1', method:'POST')

        name apiUserPaymentCardPaynow: "/v1/userPaymentCard/checkout/$country/$lang/$id/paynow"(controller: "userPaymentCard", action:'paynow', namespace:'apiV1', method:'GET')
        "/v1/userPaymentCard/checkout/$country/$lang/$id/payment"(controller: "userPaymentCard", action:'payment', namespace:'apiV1')
        name apiUserPaymentCardSuccess: "/v1/userPaymentCard/checkout/$country/$lang/$id/success"(controller: "userPaymentCard", action:'success', namespace:'apiV1')
        name apiUserPaymentCardThankyou: "/v1/userPaymentCard/checkout/$country/$lang/$id/thankyou"(controller: "userPaymentCard", action:'thankyou', namespace:'apiV1')
        name apiUserPaymentCardError: "/v1/userPaymentCard/checkout/$country/$lang/$id/error"(controller: "userPaymentCard", action:'error', namespace:'apiV1')
        name apiUserPaymentCardClose: "/v1/userPaymentCard/checkout/$country/$lang/$id/close"(controller: "userPaymentCard", action:'cancelPayment', namespace:'apiV1')

        "/v1/userPaymentCard/checkout/$country/$lang/$id/complete"(controller: "userPaymentCard", action:'paymentMethodComplete', namespace:'apiV1')

        name discountAmount: "/v1/$insuranceType/checkout/$country/$lang/discountAmount"(namespace: "apiV1", controller: "checkout", action: "discountAmount", method: 'POST')
        name apiCheckoutPaynow: "/v1/$insuranceType/checkout/$country/$lang/$id/paynow"(controller: "checkout", action:'paynow', namespace:'apiV1', method:'GET')
        name apiCheckoutCodOrder: "/v1/$insuranceType/checkout/$country/$lang/$id/codOrder"(controller: "checkout", action:'codOrder', namespace:'apiV1', method:'POST')
        "/v1/$insuranceType/checkout/$country/$lang/$id/payment"(controller: "checkout", action:'payment', namespace:'apiV1')
        name apiCheckoutSuccess: "/v1/$insuranceType/checkout/$country/$lang/$id/success"(controller: "checkout", action:'success', namespace:'apiV1')
        name apiCheckoutThankyou: "/v1/$insuranceType/checkout/$country/$lang/$id/thankyou"(controller: "checkout", action:'thankyou', namespace:'apiV1')
        name apiCheckoutError: "/v1/$insuranceType/checkout/$country/$lang/$id/error"(controller: "checkout", action:'error', namespace:'apiV1')
        name apiV1PreparePaymentPage: "/v1/$insuranceType/checkout/$country/$lang/preparePayment"(controller: "checkout", action:'preparePaymentPage', namespace:'apiV1')

        name apiCheckoutClose: "/v1/$insuranceType/checkout/$country/$lang/$id/close"(controller: "checkout", action:'cancelPayment', namespace:'apiV1')

        "/v1/$insuranceType/checkout/$country/$lang/$id/thankyouSummary"(controller: "checkout", action:'thankyouSummary', namespace:'apiV1')

        "/v1/$insuranceType/checkout/$country/$lang/$id/summary"(controller: "checkout", action:'paymentSummary', namespace:'apiV1', method:'GET')

        "/v1/$insuranceType/checkout/$country/$lang/sendPurchaseEmail"(controller: "checkout", action:'sendPurchaseEmail', namespace:'apiV1')
        name installmentPlans: "/v1/$insuranceType/checkout/$country/$lang/installmentPlans"(controller: "checkout", action:'installmentPlans', namespace:'apiV1')
        name updateInstallmentPlan:"/v1/$insuranceType/checkout/$country/$lang/$id/updateInstallmentPlan"(controller: "checkout", action:'updateInstallmentPlan', namespace:'apiV1')

        "/v1/$insuranceType/chat/$country/$lang"(controller: 'chat', action:'index', namespace:'apiV1')
        "/v1/$insuranceType/chat/script"(controller: 'chat', action:'script', namespace:'apiV1')
        "/v1/$insuranceType/chat/closebutton"(controller: 'chat', action:'closebutton', namespace:'apiV1')

        name closeChatWindow: "/v1/$insuranceType/chat/close"(view: '/apiV1/loading')


        "/v1/$insuranceType/checkout/$country/$lang/sdgInitiate"(controller: "checkout", action:'sdgInitiateTransaction', namespace:'apiV1')
        "/v1/$insuranceType/checkout/$country/$lang/sdgConfirm"(controller: "checkout", action:'sdgConfirmTransaction', namespace:'apiV1')
        "/v1/$insuranceType/checkout/$country/$lang/sdgCancel"(controller: "checkout", action:'sdgCancelTransaction', namespace:'apiV1')

       /* "/v1/$insuranceType/checkout/$country/$lang/$id/pspSuccess"(controller: "checkout", action:'checkoutPspPaymentSuccess', namespace:'apiV1')
        "/v1/$insuranceType/checkout/$country/$lang/$id/pspFailure"(controller: "checkout", action:'checkoutPspPaymentFailure', namespace:'apiV1')
        "/v1/$insuranceType/checkout/$country/$lang/$id/pspCancel"(controller: "checkout", action:'checkoutPspPaymentCancel', namespace:'apiV1')*/

        "/payments/notification/checkoutPsp/"(controller: "paymentNotification", action:"checkoutPspNotification")
        "/payments/response/checkoutPsp/$insuranceType/$id/success"(controller: "paymentNotification", action:"checkoutPspPaymentSuccess")
        "/payments/response/checkoutPsp/$insuranceType/$id/failure"(controller: "paymentNotification", action:"checkoutPspPaymentFailure")
        "/payments/response/checkoutPsp/$insuranceType/$id/cancel"(controller: "paymentNotification", action:"checkoutPspPaymentCancel")

        "/$insuranceType/checkout/$country/$lang/tapPayment/notification" (controller: "tapPaymentNotification", action: "notification")
        "/$insuranceType/checkout/$country/$lang/tapPayment/redirect" (controller: "tapPaymentNotification", action: "redirect")
        "/$insuranceType/checkout/$country/$lang/tapPayment/tokenize" (controller: "tapPaymentNotification", action: "tokenize")
        "/$insuranceType/checkout/$country/$lang/tabby/initiate" (controller: "tapPaymentNotification", action: "initiateTabby") {
            name = "tabbyInitiate"
        }

        "/v1/calls/handle-details/$key"(controller: "call", action: "handleCallDetails", namespace: "apiV1")

        "/v1/vouchers/$country/$lang"(namespace:"apiV1", controller:"voucher", action:"list", method: "GET")
        "/v1/voucherEvent/$country/$lang"(namespace:"apiV1", controller:"voucher", action:"voucherEvent", method: "POST")

        "/v1/captcha/verify"(namespace:"apiV1", controller:"captcha", action:"verify", method: "POST")
//****************************************************************************
// docs api for smart dubai
        "/v1/car/documents/$lang/$quoteId"(controller:"car", namespace:"apiV1"){
            action = [GET:"requiredDocumentsData", POST: "uploadPolicyDocs", DELETE: "deletePolicyDocs", PUT: "updatePolicyDocs"]
        }


//****************************************************************************
// Common

        name robots: "/robots.txt"(view: '/robots')
//        name commonIndex: "/"(controller: 'index', action: 'index')
        "/error"(view: '/common/error')
        name commonAboutUs: "/$country/$lang/about-us"(namespace: 'static', controller: 'static', action: 'aboutUs')
        name commonDisclaimer: "/$country/$lang/disclaimer"(namespace: 'static', controller: 'static', action: 'disclaimer')
        name commonInsurancePartners: "/$country/$lang/insurance-partners"(namespace: 'static', controller: 'static', action: 'insurancePartners')
        name commonPrivacyPolicy: "/$country/$lang/privacy-policy"(namespace: 'static', controller: 'static', action: 'privacyPolicy')
        name commonTermsAndConditions: "/$country/$lang/terms-and-conditions"(namespace: 'static', controller: 'static', action: 'termsAndConditions')
        name commonContactUs: "/$country/$lang/contact-us"(namespace: 'static', controller: 'static', action: 'contactUs')

        name discountCodeUsage: "/$country/$lang/discount-code/usage/$code" (controller: 'discountCode', action: 'usage')

//****************************************************************************
// Etisalat
        name etisalatLandingPage : "/$country/$lang/etisalat"( controller: 'etisalat', 'action': 'index')
        "/$country/$lang/etisalat/quotes"( controller: 'etisalat', 'action': 'quotes')

        name generalQuoteForm: "/$country/$lang/general/checkout"(controller: 'generalInsurance', action: 'checkout')
        name generalQuoteCheckout: "/$country/$lang/general/$id/checkout"(controller: 'generalInsurance', action: 'checkout')
        name generalCheckoutThankyou: "/$country/$lang/general/$id/checkout/thankyou"(controller: 'generalInsurance', action: 'thankyou')

//****************************************************************************
// Car

        //funnel pages
        name carRequestCall: "/$country/$lang/car/request-call"(controller: 'funnel', action: 'requestCall')
        name carIndex: "/$country/$lang/car"(controller: 'funnel', action: 'index')
        name carVehicleDetails: "/$country/$lang/car/vehicle"(controller: 'funnel', action: 'vehicle')
        name cardriverDetails: "/$country/$lang/car/driver"(controller: 'funnel', action: 'driver')
        name carSaveQuote: "/$country/$lang/car/saveQuote"(controller: 'funnel', action: 'saveQuote')
        name preCarQuotes: "/$country/$lang/car/pre-quotes/$id?"(controller: 'funnel', action: 'preCarQuotes')
        //name updateMissingDataCarQuote: "/$country/$lang/car/update-data/$id?"(controller: 'funnel', action: 'updateMissingDataCarQuote')
        name preHomeQuotes: "/$country/$lang/home/<USER>/$id?"(controller: 'homeInsurance', action: 'preHomeQuotes')
        name preHealthQuotes: "/$country/$lang/health/pre-quotes/$id?"(controller: 'health', action: 'preHealthQuotes')
        name carQuotes: "/$country/$lang/car/quotes/$id?"(controller: 'funnel', action: 'quotes')
        name carQuotesOld: "/$lang/car/quotes/$id?"(controller: 'funnel', action: 'quotes')
        name carQuotesLang: "/car/quotes/$id?"(controller: 'funnel', action: 'quotes')
        name carQuoteDetails: "/$country/$lang/car/quotes/details/$id?"(controller: 'funnel', 'action': 'quoteDetails')
        name carQuoteDetailsLang: "/car/quotes/details/$id?"(controller: 'funnel', 'action': 'quoteDetails')
        name carNoQuotes: "/$country/$lang/car/no-quotes"(controller: 'funnel', 'action': 'noQuotes')
        name carOfflineQuotes: "/$country/$lang/car/offline-quotes"(controller: 'funnel', 'action': 'offlineQuotes')
        name carOutOfRange: "/$country/$lang/car/out-of-range/$id"(controller: 'funnel', 'action': 'outOfRange')
        name carFaq: "/$country/$lang/car/faq"(namespace: 'car', controller: 'static', action: 'faq')

        //policy
        name uploadPolicyCancellationDocs: "/$country/$lang/car/policy/upload-cancellation-document"(namespace: 'car', controller: 'policy') {
            action = [GET: 'renderUploadPolicyCancellationDocsPage', POST: 'uploadPolicyCancellationDocs']
        }

        name renderPolicyCancellationDocsUploadedPage: "/$country/$lang/car/policy/cancellation-docs-uploaded"(namespace: 'car', controller: 'policy', action: 'renderPolicyCancellationDocsUploadedPage')

        name withdrawPolicyCancellation: "/$country/$lang/car/policy/withdraw-cancellation"(namespace: 'car', controller: 'policy', action: 'withdrawCancellation')
        name carPolicyCancellationInsurerReview: "/$country/$lang/car/policy/policy-cancellation-insurer-review"(namespace: 'car', controller: 'policy', action: 'renderPolicyCancellationInsurerReviewPage')
        name carPolicyCancellationInsurerReviewResult: "/$country/$lang/car/policy/policy-cancellation-insurer-review-result"(namespace: 'car', controller: 'policy', action: 'policyCancellationInsurerReviewResult')

        name carUploadPolicyDocs: "/$country/$lang/car/policy/upload-policy-docs/$id"(namespace: 'car', controller: 'policy', action: 'uploadPolicyDocs')
        name carSendNCDOnEmail: "/$country/$lang/car/policy/send-ncd-email/$id"(namespace: 'car', controller: 'policy', action: 'processNCDRecordedOnEmail')
        name carNCDVerification: "/$country/$lang/car/policy/ncd-verification/$id"(namespace: 'car', controller: 'policy', action: 'processNCDRecordedOnEmailVerification')
        name carDeletePolicyDocs: "/$country/$lang/car/policy/delete-policy-docs/$id"(namespace: 'car', controller: 'policy', action: 'deletePolicyDocs')
        name carUpdatePolicyStartDate: "/$country/$lang/car/policy/confirm-docs"(namespace: 'car', controller: 'policy', action: 'updatePolicy')

        //Request call back
        name carRequestCallBack:"/$country/$lang/car/policy/requestCallBack/$id"(namespace: 'car', controller: 'policy', action: 'requestCallBack')


        //car guides
        name carDosAndDonts: "/$country/$lang/car/guides/dos-and-donts"(namespace: 'car', controller: 'static', action: 'dosAndDonts')
        name carRepairGuide: "/$country/$lang/car/guides/car-repair-guide"(namespace: 'car', controller: 'static', action: 'carRepairGuide')
        name carTopTips: "/$country/$lang/car/guides/top-tips"(namespace: 'car', controller: 'static', action: 'topTips')
        name carNoClaimsCertificate: "/$country/$lang/car/guides/no-claims-certificate"(namespace: 'car', controller: 'static', action: 'noClaimsCertificate')
        name carComprehensiveCarInsurance: "/$country/$lang/car/guides/comprehensive-car-insurance"(namespace: 'car', controller: 'static', action: 'comprehensiveCarInsurance')
        name carMakeAClaim: "/$country/$lang/car/guides/make-a-claim"(namespace: 'car', controller: 'static', action: 'makeAClaim')

        //survey
        name carSurveySchedulingInit: "/$country/$lang/car/schedule-survey"(namespace: 'car', controller: 'survey', 'action': 'initSurveyPage')
        name carSurveySchedulingSubmit: "/$country/$lang/car/submit-survey"(namespace: 'car', controller: 'survey', 'action': 'scheduleSurvey')

        //checkout
        name carCheckoutIndex: "/$country/$lang/car/checkout"(namespace: 'car', controller: 'checkout', action: 'index')
        name carCheckoutIndexV2: "/$country/$lang/car/checkout/$encryptedQuoteId/$encryptedProductId/$repairType"(namespace: 'car', controller: 'checkout', action: 'index')
        name carCheckoutUpdateOrder: "/$country/$lang/car/checkout/update-order"(namespace: 'car', controller: 'checkout', 'action': 'updateOrder')
        name carCheckoutPaynow: "/$country/$lang/car/checkout/paynow"(namespace: 'car', controller: 'checkout', 'action': 'paynow')
        name carPreparePaymentPage: "/$country/$lang/car/checkout/preparePayment"(namespace: 'car', controller: 'checkout', 'action': 'preparePaymentPage')


        name carCheckoutOrder: "/$country/$lang/car/checkout/order"(namespace: 'car', controller: 'checkout', action: 'order')
        name carCheckoutThankyou: "/$country/$lang/car/checkout/thankyou"(namespace: 'car', controller: 'checkout', 'action': 'thankyou')
        name carCheckoutPayment: "/$country/$lang/car/checkout/payment"(namespace: 'car', controller: 'checkout', 'action': 'payment')
        name carCheckoutKnetPayment: "/$country/$lang/car/checkout/knetpayment"(namespace: 'car', controller: 'checkout', 'action': 'knetPayment')
        name carCheckoutSuccess: "/$country/$lang/car/checkout/success"(namespace: 'car', controller: 'checkout', 'action': 'success')

        name tabby: "/$country/$lang/car/checkout/paynow/tabby"(namespace: 'car', controller: 'static', action: 'tabby')


        "/checkout/knet/response" (namespace: 'car', controller: 'checkout', 'action': 'knetResponse')
        "/checkout/knet/error" (namespace: 'car', controller: 'checkout', 'action': 'knetError')

        "/checkout/cs/redirect" (namespace: 'car', controller: 'checkout', 'action': 'cyberSourceRedirect')
        "/checkout/cs/notification" (namespace: 'car', controller: 'checkout', 'action': 'cyberSourceNotification')


        name iCheckoutPaynow: "/$country/$lang/$insuranceType/icheckout/$id/paynow"(controller: "checkout", action:'paynow', namespace:'independent', method:'GET')
        //name iCheckoutCodOrder: "/$country/$lang/$insuranceType/icheckout/$id/codOrder"(controller: "checkout", action:'codOrder', namespace:'independent', method:'POST')
        //name iCheckoutPayfortCardResponse: "/$country/$lang/$insuranceType/icheckout/$id/payment"(controller: "checkout", action:'payment', namespace:'independent')
        //name iCheckoutSuccess: "/$country/$lang/$insuranceType/icheckout/$id/success"(controller: "checkout", action:'success', namespace:'independent')
        //name iCheckoutThankyou: "/$country/$lang/$insuranceType/icheckout/$id/thankyou"(controller: "checkout", action:'thankyou', namespace:'independent')
        name iCheckoutError: "/$country/$lang/$insuranceType/icheckout/$id/error"(controller: "checkout", action:'error', namespace:'independent')
        //name iCheckoutThankyou: "/$country/$lang/$insuranceType/icheckout/$id/thankyouSummary"(controller: "checkout", action:'thankyouSummary', namespace:'independent')


        name carCheckout: "/checkout/$action" {
            controller = 'checkout'
            namespace = 'car'
        }

        name carCustomerFeedback: "/$country/$lang/car/customer-feedback"(namespace: 'car', controller: 'static', action: 'customerFeedback')

        name carRequestPolicyCancellation: "/$country/$lang/car/quote/request-policy-cancellation"(namespace: 'car', controller: 'quote', action: 'requestPolicyCancellation')
        name carConfirmPolicyCancellation: "/$country/$lang/car/quote/confirm-policy-cancellation"(namespace: 'car', controller: 'quote', action: 'confirmPolicyCancellation')
        name carRequestConfirmPolicyCancellation: "/$country/$lang/car/quote/request-confirm-policy-cancellation"(namespace: 'car', controller: 'quote', action: 'requestConfirmPolicyCancellation')


//        name carThankYouKsa: "/$country/$lang/car/thankyou"(namespace: 'car', controller: 'funnel', action: 'thankyou')

        name carCheckoutPayByEtisalatPoints: "/$country/$lang/car/checkout/smiles-points-payment"(namespace: 'car', controller: 'checkout', 'action': 'payByEtisalatPoints')
        name carCheckoutPayByEtisalatMix: "/$country/$lang/car/checkout/update-price"(namespace: 'car', controller: 'checkout', 'action': 'updatePrice')
        name carCheckoutUpdatedPaymentLink: "/$country/$lang/car/checkout/updated-payment-link"(namespace: 'car', controller: 'checkout', 'action': 'getUpdatedCheckoutPspHostedPageDetail')
//****************************************************************************
// Gap
        name gapIndex: "/$country/$lang/gap" (namespace: "gap", controller: "gap", action: "index")
        name gapThankyou: "/$country/$lang/gap/thankyou" (namespace: "gap", controller: "gap", action: "thankyou")
// Watch
        name watchIndex: "/$country/$lang/watch" (namespace: "watch", controller: "watch", action: "index")
        name watchThankyou: "/$country/$lang/watch/thankyou" (namespace: "watch", controller: "watch", action: "thankyou")
// Business
        name businessIndex: "/$country/$lang/business" (namespace: "business", controller: "business", action: "index")
        name businessThankyou: "/$country/$lang/business/thankyou" (namespace: "business", controller: "business", action: "thankyou")
// Motorbike
        name motorbikeIndex: "/$country/$lang/motorbike" (namespace: "motorbike", controller: "motorbike", action: "index")
        name motorbikeThankyou: "/$country/$lang/motorbike/thankyou" (namespace: "motorbike", controller: "motorbike", action: "thankyou")

//****************************************************************************
// Travel

        name travelIndex: "/$country/$lang/travel"(namespace: 'travel', controller: 'travelQuote', action: 'index')
        name travelOld: "/$country/$lang/travel/funnel"(namespace: 'travel', controller: 'travelQuote', action: 'funnel')
        name travelQuotes: "/$country/$lang/travel/details"(namespace: 'travel', controller: 'travelQuote', action: 'renderQuoteDetailsPage')
        name travelFaq: "/$country/$lang/travel/faq"(namespace: 'travel', controller: 'travelQuote', action: 'faq')
        name travelGetQuoteFromApi: "/$country/$lang/travel/quote"(namespace: 'travel', controller: 'travelQuote', action: 'getQuoteFromApi')
        name travelGetQuoteFromApiBySavedQuoteInfo: "/$country/$lang/travel/quote/saved"(namespace: 'travel', controller: 'travelQuote', action: 'getQuoteFromApiBySavedQuoteInfo')
        name travelMakePurchase: "/$country/$lang/travel/make-purchase"(namespace: 'travel', controller: 'travelQuote', action: 'makePurchase')

        name travelQuotesNew: "/$country/$lang/travel/quotes/$id?"(namespace: 'apiV1', controller: 'travel', action: 'quotes')

//****************************************************************************
// Boat
        name boatIndex: "/$country/$lang/boat" (namespace: "boat", controller: "boat", action: "index")
        name boatThankyou: "/$country/$lang/boat/thankyou" (namespace: "boat", controller: "boat", action: "thankyou")
//****************************************************************************
// Pet
        name petIndex: "/$country/$lang/pet-insurance" (namespace: "pet", controller: "pet", action: "index")
        name petQuotes: "/$country/$lang/pet/quotes" (namespace: "pet", controller: "pet", action: "quotes")
        name petThankyou: "/$country/$lang/pet/thankyou" (namespace: "pet", controller: "pet", action: "thankyou")



 // Pet Api
        "/v1/pet/add" (namespace: "apiV1", controller: "pet", action: "addPet")
        "/v1/pet/update" (namespace: "apiV1", controller: "pet", action: "updatePet")
        "/v1/pet/remove/$id" (namespace: "apiV1", controller: "pet", action: "removePet")
        "/v1/pet/list" (namespace: "apiV1", controller: "pet", action: "pets")
	    "/v1/pet/uploadPetPhoto" (namespace: "apiV1", controller: "pet", action: "savePetPhoto")
        "/v1/pet/petQuotePaymentHistory/$encryptedQuoteId" (namespace: "apiV1", controller: "pet", action: "petQuotePaymentHistory")

        "/v1/pet/getBreeds/$country/$lang"(controller:"pet", action:"petBreeds", namespace:"apiV1", method:"GET") // Save pet Quote
        "/v1/pet/saveQuote/$country/$lang"(controller:"pet", action:"saveQuote", namespace:"apiV1", method:"POST") // Save pet Quote
        "/v1/pet/quotes/$country/$lang/$id"(controller:"pet", action:"quotes", namespace:"apiV1", method:"GET") // Get Ratings
        "/v1/pet/quoteDetail/$country/$lang/$id/detail"(controller:"pet", action:"quoteDetail", namespace:"apiV1", method:"GET") // Get Quote detail by Id
        "/v1/pet/petDetail/$country/$lang/$id/detail"(controller:"pet", action:"petDetailFromQuote", namespace:"apiV1", method:"GET") // Get Pet detail by Quote Id
        "/v1/pet/checkout/$country/$lang/$id/checkout"(controller:"pet", action:"updatePetQuote", namespace:"apiV1", method:"PUT") // Update Quote with selected Product, update pricing
        "/v1/pet/uploadPolicyDocument/$country/$lang"(controller:"pet", action:"uploadPolicyDocument", namespace:"apiV1", method:"POST") // Save pet Quote
        "/v1/pet/updateQuoteDetails/$country/$lang"(controller:"pet", action:"editPetQuoteDetails", namespace:"apiV1", method:"POST") // update pet Quote details


        // PaLead Api
        "/v1/personalAccident/ddl/$country/$lang"(controller:"paLead", action:"ddl", namespace:"apiV1", method:"GET") // Get all schools
        "/v1/personalAccident/create/$country/$lang"(controller:"paLead", action:"createPaLead", namespace:"apiV1", method:"POST") // Save PaLead
        "/v1/personalAccident/paLeadList/$country/$lang"(controller:"paLead", action:"paLeadList", namespace:"apiV1", method:"GET") // Get all PaLead
        "/v1/personalAccident/paLeadList/request-confirm-pa-cancellation/$country/$lang"(controller:"paLead", action:"requestConfirmPaCancellation", namespace:"apiV1", method:"GET") // Get all PaLead

//****************************************************************************
// Home Insurance

        //funnel
        name homeQuotes: "/$country/$lang/home/<USER>/$id?"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'quotes')
        name homeQuotesOld: "/$lang/home/<USER>/$id?"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'quotes')
        name homeInsuranceIndex: "/$country/$lang/home"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'index')
        name homeInsuranceCustomerDetails: "/$country/$lang/home/<USER>"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'details')
        name homeInsuranceCustomerSave: "/$country/$lang/home/<USER>"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'save')
        name homeInsuranceQuoteDetails: "/$country/$lang/home/<USER>/details"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'quoteDetails')
        name homeInsuranceFaq: "/$country/$lang/home/<USER>"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'faq')

        //checkout
        name homeInsuranceCheckoutIndex: "/$country/$lang/home/<USER>"(namespace: 'homeInsurance', controller: 'homeInsurance', action: 'checkout')
        name homeInsuranceCheckoutUpdateOrder: "/$country/$lang/home/<USER>/update-order"(namespace: 'homeInsurance', controller: 'checkout', action: 'updateOrder')
        name homeInsuranceCheckoutPaynow: "/$country/$lang/home/<USER>/paynow"(namespace: 'homeInsurance', controller: 'checkout', action: 'paynow')
        name homeInsuranceCheckoutOrder: "/$country/$lang/home/<USER>/order"(namespace: 'homeInsurance', controller: 'checkout', action: 'order')
        name homeInsuranceCheckoutPayment: "/$country/$lang/home/<USER>/payment"(namespace: 'homeInsurance', controller: 'checkout', 'action': 'payment')
        name homeInsuranceCheckoutSuccess: "/$country/$lang/home/<USER>/success"(namespace: 'homeInsurance', controller: 'checkout', 'action': 'success')
        name homeInsuranceCheckoutThankyou: "/$country/$lang/home/<USER>/thankyou"(namespace: 'homeInsurance', controller: 'checkout', 'action': 'thankyou')
        name homePreparePaymentPage: "/$country/$lang/home/<USER>/preparePayment"(namespace: 'homeInsurance', controller: 'checkout', 'action': 'preparePaymentPage')


//        "/home/<USER>/$action"(namespace: 'homeInsurance', controller: 'checkout')

//****************************************************************************
// Life


        // Life product pages
        name lifeLanding: "/$country/$lang/life"(namespace: 'life', controller: 'lifeLanding', action: 'index')

        // Life Product API
        "/api/life/product/$productSlug"(namespace: 'life-api', controller: 'life', action: 'getFirstForm', method: 'GET')
        "/api/life/product/all"(namespace: 'life-api', controller: 'life', action: 'listProducts', method: 'GET')
        "/api/life/product/$productSlug"(namespace: 'life-api', controller: 'life', action: 'submitFirstForm', method: 'POST')
        "/api/life/product/$productSlug/quote"(namespace: 'life-api', controller: 'life', action: 'submitSelectedQuote', method: 'POST')
        "/api/life/product/$productSlug/additionalDocumentsAndData"(namespace: 'life-api', controller: 'life', action: 'submitDocumentsAndAdditionalData', method: 'POST')
        "/api/life/product/$productSlug/additionalDocumentsAndDataforsp"(namespace: 'life-api', controller: 'life', action: 'submitSponsorProtectDocumentsAndAdditionalData', method: 'POST')
        "/api/life/product/previsitedquote/$encQuoteId"(namespace: 'life-api', controller: 'life', action: 'getPreVisitedQuoteInfo', method: 'GET')
        "/api/life/policy/$policyNumber/confirm"(namespace: 'life-api', controller: 'life', action: 'confirmPolicy', method: 'POST')
        "/api/life/checkout/prepare"(namespace: 'life-api', controller: 'lifeCheckout', action: 'prepareAnnualPaymentAuthorization', method: 'POST')
        "/api/life/checkout/tokenizationresponse"(namespace: 'life-api', controller: 'lifeCheckout', action: 'recieveTokenizationResponse', method: 'POST')
        "/api/life/checkout/paymentsuccessresponse"(namespace: 'life-api', controller: 'lifeCheckout', action: 'recievePaymentSuccess', method: 'POST')
        "/api/life/checkout/authorize"(namespace: 'life-api', controller: 'lifeCheckout', action: 'handleReturnedAnnualPaymentAuthorization', method: 'POST')
        "/api/life/checkout/handlethreedeesecure/$policyNumber"(namespace: 'life-api', controller: 'lifeCheckout', action: 'handleThreeDeeAnnualPaymentAuthorization', method: 'POST')
        "/api/life/checkout/additionalinfo/$policyNumber"(namespace: 'life-api', controller: 'life', action: 'getAdditionalInfoForm', method: 'GET')
        "/api/life/checkout/product/document"(namespace: 'life-api', controller: 'life', action: 'savePolicyDocument', method: 'POST')
        "/api/life/checkout/product/document"(namespace: 'life-api', controller: 'life', action: 'savePolicyDocument', method: 'OPTIONS')
        "/api/life/policy/$policyNumber"(namespace: 'life-api', controller: 'life', action: 'isPolicyExisting', method: 'GET')
        "/api/life/policy/validquote/$policyNumber"(namespace: 'life-api', controller: 'life', action: 'isQuoteValid', method: 'GET')
        "/api/life/policy/initpayment/$policyNumber"(namespace: 'life-api', controller: 'life', action: 'initPayment', method: 'GET')
        "/api/life/policy/completepayment/$policyNumber/$token"(namespace: 'life-api', controller: 'life', action: 'completePayment', method: 'GET')
        "/api/life/policy/policyschedule/$policyNumber"(namespace: 'life-api', controller: 'life', action: 'getPolicySchedule', method: 'GET')

//****************************************************************************
// Gadget
        name gadgetIndex: "/$country/$lang/gadget" (namespace: "gadget", controller: "gadget", action: "index")
        name gadgetThankyou: "/$country/$lang/gadget/thankyou" (namespace: "gadget", controller: "gadget", action: "thankyou")

//****************************************************************************
// Health
        name healthIndex: "/$country/$lang/health" (namespace: "health", controller: "health", action: "index")
        name healthDetails: "/$country/$lang/health/details" (namespace: "health", controller: "health", action: "details")
        name healthDeclaration: "/$country/$lang/health/declaration" (namespace: "health", controller: "health", action: "declaration")
        name healthFaq: "/$country/$lang/health/faq"(namespace: 'health', controller: 'health', action: 'faq')
        name healthQuotes: "/$country/$lang/health/quotes/$id?"(namespace: 'health', controller: 'health', action: 'quotes')
        name healthQuotesOld: "/$lang/health/quotes/$id?"(namespace: 'health', controller: 'health', action: 'quotes')
        name healthNoQuotes: "/$country/$lang/health/no-quotes"(namespace: "health", controller: "health", action: "noQuotes")
        name healthQuoteDetails: "/$country/$lang/health/quote/details/$id?"(namespace: 'health', controller: 'health', action: 'quoteDetails')

        //checkout
        name healthCheckoutIndex: "/$country/$lang/health/checkout" (namespace: 'health', controller: 'checkout', action: 'index')
        name healthCheckoutUpdateOrder: "/$country/$lang/health/checkout/update-order"(namespace: 'health', controller: 'checkout', action: 'updateOrder')
        name healthCheckoutPaynow: "/$country/$lang/health/checkout/paynow"(namespace: 'health', controller: 'checkout', action: 'paynow')
        name healthCheckoutOrder: "/$country/$lang/health/checkout/order"(namespace: 'health', controller: 'checkout', action: 'order')
        name healthCheckoutPayment: "/$country/$lang/health/checkout/payment"(namespace: 'health', controller: 'checkout', 'action': 'payment')
        name healthCheckoutSuccess: "/$country/$lang/health/checkout/success"(namespace: 'health', controller: 'checkout', 'action': 'success')
        name healthCheckoutThankyou: "/$country/$lang/health/checkout/thankyou"(namespace: 'health', controller: 'checkout', 'action': 'thankyou')
        name healthPreparePaymentPage: "/$country/$lang/health/checkout/preparePayment"(namespace: 'health', controller: 'checkout', 'action': 'preparePaymentPage')

        // group health
        name groupIndex: "/$country/$lang/group-health" (namespace: "health", controller: "groupHealth", action: "index")
        name groupDetails: "/$country/$lang/group-health/details" (namespace: "health", controller: "groupHealth", action: "details")
        name groupQuotes: "/$country/$lang/group-health/quotes/$id" (namespace: "health", controller: "groupHealth", action: "quotes")

//        "/health/checkout/$action"(namespace: 'health', controller: 'checkout')
//****************************************************************************
// Fleet
        name fleetIndex: "/$country/$lang/fleet" (namespace: "fleet", controller: "fleet", action: "index")
        name fleetThankyou: "/$country/$lang/fleet/thankyou" (namespace: "fleet", controller: "fleet", action: "thankyou")


        //Cigna Campaign
        name cignaCampaign: "/$country/$lang/health/cigna-campaign" (namespace: "health", controller: "health", action: "cignaCampaign")

//****************************************************************************
// webhook

        name webhook: "/subscription/webhook/$key" (controller: 'subscription', action: 'webhook')
        name parseWebhook: "/policyemail/parse/$key" (controller: 'policyEmail', action: 'parse')

//****************************************************************************
// Car Warranty
        name carWarrantyIndex: "/$country/$lang/car-warranty" (namespace: "carWarranty", controller: "carWarranty", action: "index")
//****************************************************************************

        name competition: "/$country/$lang/competition" (controller: 'competition', action: 'index')


// Redirects

        "/Car/VehicleDetails.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/vehicle"
        }
        "/Docs/CarGuide1.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/guides/dos-and-donts"
        }
        "/Docs/CarGuide2.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/guides/car-repair-guide"
        }
        "/Docs/CarGuide3.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/guides/top-tips"
        }
        "/Docs/CarGuide4.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/guides/no-claims-certificate"
        }
        "/Docs/CarGuide5.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/guides/comprehensive-car-insurance"
        }
        "/Docs/CarGuide6.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/guides/make-a-claim"
        }
        "/Docs/FAQ.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/faq"
        }
        "/Docs/Partners.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/insurance-partners"
        }
        "/Docs/ContactUs.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/contact-us"
        }
        "/Docs/Terms.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/terms-and-conditions"
        }
        "/Docs/TermsConditions.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/terms-and-conditions"
        }
        "/Docs/Disclaimer.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/static/disclaimer"
        }
        "/Docs/AboutUs.aspx" {
            controller = "redirect"
            action = "permanent"
            destination = "/about-us"
        }
        "/index/index" {
            controller = "redirect"
            action = "permanent"
            destination = "/"
        }
//        "/" {
//            controller = "redirect"
//            action = "temporary"
//            destination = "/car"
//        }
        "/car/static/terms-and-conditions" {
            controller = "redirect"
            action = "permanent"
            destination = "/terms-and-conditions"
        }
        "/car/static/privacy-policy" {
            controller = "redirect"
            action = "permanent"
            destination = "/privacy-policy"
        }
        "/car/static/disclaimer" {
            controller = "redirect"
            action = "permanent"
            destination = "/disclaimer"
        }
        "/car/static/about-us" {
            controller = "redirect"
            action = "permanent"
            destination = "/about-us"
        }
        "/car/static/contact-us" {
            controller = "redirect"
            action = "permanent"
            destination = "/contact-us"
        }
        "/car/static/insurance-partners" {
            controller = "redirect"
            action = "permanent"
            destination = "/insurance-partners"
        }
        "/car/static/faq" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/faq"
        }
        "/car/static/guides/dos-and-donts" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/guides/dos-and-donts"
        }
        "/car/static/guides/car-repair-guide" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/guides/car-repair-guide"
        }
        "/car/static/guides/top-tips" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/guides/top-tips"
        }
        "/car/static/guides/no-claims-certificate" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/guides/no-claims-certificate"
        }
        "/car/static/guides/comprehensive-car-insurance" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/guides/comprehensive-car-insurance"
        }
        "/car/static/guides/make-a-claim" {
            controller = "redirect"
            action = "permanent"
            destination = "/car/guides/make-a-claim"
        }
        "/life/static/terms-and-conditions" {
            controller = "redirect"
            action = "permanent"
            destination = "/terms-and-conditions"
        }
        "/life/static/privacy-policy" {
            controller = "redirect"
            action = "permanent"
            destination = "/privacy-policy"
        }
        "/life/static/disclaimer" {
            controller = "redirect"
            action = "permanent"
            destination = "/disclaimer"
        }
        "/life/static/about-us" {
            controller = "redirect"
            action = "permanent"
            destination = "/about-us"
        }
        "/life/static/contact-us" {
            controller = "redirect"
            action = "permanent"
            destination = "/contact-us"
        }
        "/life/static/insurance-partners" {
            controller = "redirect"
            action = "permanent"
            destination = "/insurance-partners"
        }
        "/life/static/faq" {
            controller = "redirect"
            action = "permanent"
            destination = "/life/faq"
        }
        "/life/static/learn-more" {
            controller = "redirect"
            action = "permanent"
            destination = "/life/learn-more"
        }
        "/travel/static/terms-and-conditions" {
            controller = "redirect"
            action = "permanent"
            destination = "/terms-and-conditions"
        }
        "/travel/static/privacy-policy" {
            controller = "redirect"
            action = "permanent"
            destination = "/privacy-policy"
        }
        "/travel/static/disclaimer" {
            controller = "redirect"
            action = "permanent"
            destination = "/disclaimer"
        }
        "/travel/static/about-us" {
            controller = "redirect"
            action = "permanent"
            destination = "/about-us"
        }
        "/travel/static/contact-us" {
            controller = "redirect"
            action = "permanent"
            destination = "/contact-us"
        }
        "/travel/static/insurance-partners" {
            controller = "redirect"
            action = "permanent"
            destination = "/insurance-partners"
        }
        "/travel/static/faq" {
            controller = "redirect"
            action = "permanent"
            destination = "/travel/faq"
        }
        "/en/health-insurance" {
            controller = "redirect"
            action = "permanent"
            destination = "/en/health"
        }
        "/en/health-insurance/" {
            controller = "redirect"
            action = "permanent"
            destination = "/en/health"
        }
        "/ar/health-insurance" {
            controller = "redirect"
            action = "permanent"
            destination = "/en/health"
        }
        "/$country/$lang/travel/funnel" {
            controller = "redirect"
            action = "permanent"
            destination = "/travel/details"
        }
        "/$country/$lang/home-insurance/" {
            controller = "redirect"
            action = "permanent"
            destination = "/home/"
        }
        "/$country/$lang/home-insurance/quotes/$id" {
            controller = "redirect"
            action = "permanent"
            destination = "/home/<USER>/"
        }

        // API
        name apiCarQuote: "/api/car/quotes/$id"(namespace: 'car', controller: 'quote', action: 'index')
        name apiQuoteCreatedEmailV2: "/api/car/sendQuoteCreatedV2Email/$quoteId"(namespace: "apiV1", controller: "car", action: "sendQuoteCreatedV2Email")

        "500"(view: '/common/error')
        "404"(view: '/common/notFound')
        "405"(view: '/common/notFound')
        "403"(view: '/common/notFound')
    }
}
