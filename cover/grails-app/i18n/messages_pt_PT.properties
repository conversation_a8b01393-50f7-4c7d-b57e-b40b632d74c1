#
# <NAME_EMAIL>, based on pt_BR translation by <PERSON>@gmail.com
#

default.doesnt.match.message=O campo [{0}] da classe [{1}] com o valor [{2}] não corresponde ao padrão definido [{3}]
default.invalid.url.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um URL válido
default.invalid.creditCard.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um número válido de cartão de crédito
default.invalid.email.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um endereço de email válido.
default.invalid.range.message=O campo [{0}] da classe [{1}] com o valor [{2}] não está dentro dos limites de valores válidos de [{3}] a [{4}]
default.invalid.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] está fora dos limites de tamanho válido de [{3}] a [{4}]
default.invalid.max.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o valor máximo [{3}]
default.invalid.min.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atinge o valor mínimo [{3}]
default.invalid.max.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o tamanho máximo de [{3}]
default.invalid.min.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atinge o tamanho mínimo de [{3}]
default.invalid.validator.message=O campo [{0}] da classe [{1}] com o valor [{2}] não passou na validação
default.not.inlist.message=O campo [{0}] da classe [{1}] com o valor [{2}] não se encontra nos valores permitidos da lista [{3}]
default.blank.message=O campo [{0}] da classe [{1}] não pode ser vazio
default.not.equal.message=O campo [{0}] da classe [{1}] com o valor [{2}] não pode ser igual a [{3}]
default.null.message=O campo [{0}] da classe [{1}] não pode ser vazio
default.not.unique.message=O campo [{0}] da classe [{1}] com o valor [{2}] deve ser único

default.paginate.prev=Anterior
default.paginate.next=Próximo

# Mensagens de erro em atribuição de valores. Use "typeMismatch.$className.$propertyName" para personalizar(eg typeMismatch.Book.author)
typeMismatch.java.net.URL=O campo {0} deve ser um URL válido.
typeMismatch.java.net.URI=O campo {0} deve ser um URI válido.
typeMismatch.java.util.Date=O campo {0} deve ser uma data válida
typeMismatch.java.lang.Double=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Integer=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Long=O campo {0} deve ser um número valido.
typeMismatch.java.lang.Short=O campo {0} deve ser um número válido.
typeMismatch.java.math.BigDecimal=O campo {0} deve ser um número válido.
typeMismatch.java.math.BigInteger=O campo {0} deve ser um número válido.
