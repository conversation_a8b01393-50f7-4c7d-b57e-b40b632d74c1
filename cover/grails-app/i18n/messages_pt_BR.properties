#
# Translated by <PERSON>@gmail.com
#

default.doesnt.match.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atende ao padrão definido [{3}]
default.invalid.url.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é uma URL válida
default.invalid.creditCard.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um número válido de cartão de crédito
default.invalid.email.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um endereço de email válido.
default.invalid.range.message=O campo [{0}] da classe [{1}] com o valor [{2}] não está entre a faixa de valores válida de [{3}] até [{4}]
default.invalid.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] não está na faixa de tamanho válida de [{3}] até [{4}]
default.invalid.max.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o valor máximo [{3}]
default.invalid.min.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atinge o valor mínimo [{3}]
default.invalid.max.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o tamanho máximo de [{3}]
default.invalid.min.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atinge o tamanho mínimo de [{3}]
default.invalid.validator.message=O campo [{0}] da classe [{1}] com o valor [{2}] não passou na validação
default.not.inlist.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um valor dentre os permitidos na lista [{3}]
default.blank.message=O campo [{0}] da classe [{1}] não pode ficar em branco
default.not.equal.message=O campo [{0}] da classe [{1}] com o valor [{2}] não pode ser igual a [{3}]
default.null.message=O campo [{0}] da classe [{1}] não pode ser vazio
default.not.unique.message=O campo [{0}] da classe [{1}] com o valor [{2}] deve ser único

default.paginate.prev=Anterior
default.paginate.next=Próximo
default.boolean.true=Sim
default.boolean.false=Não
default.date.format=dd/MM/yyyy HH:mm:ss z
default.number.format=0

default.created.message={0} {1} criado
default.updated.message={0} {1} atualizado
default.deleted.message={0} {1} removido
default.not.deleted.message={0} {1} não pode ser removido
default.not.found.message={0} não foi encontrado com o id {1}
default.optimistic.locking.failure=Outro usuário atualizou este [{0}] enquanto você tentou salvá-lo

default.home.label=Principal
default.list.label={0} Listagem
default.add.label=Adicionar {0}
default.new.label=Novo {0}
default.create.label=Criar {0}
default.show.label=Ver {0}
default.edit.label=Editar {0}

default.button.create.label=Criar
default.button.edit.label=Editar
default.button.update.label=Alterar
default.button.delete.label=Remover
default.button.delete.confirm.message=Tem certeza?

# Mensagens de erro em atribuição de valores. Use "typeMismatch.$className.$propertyName" para customizar (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=O campo {0} deve ser uma URL válida.
typeMismatch.java.net.URI=O campo {0} deve ser uma URI válida.
typeMismatch.java.util.Date=O campo {0} deve ser uma data válida
typeMismatch.java.lang.Double=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Integer=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Long=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Short=O campo {0} deve ser um número válido.
typeMismatch.java.math.BigDecimal=O campo {0} deve ser um número válido.
typeMismatch.java.math.BigInteger=O campo {0} deve ser um número válido.
