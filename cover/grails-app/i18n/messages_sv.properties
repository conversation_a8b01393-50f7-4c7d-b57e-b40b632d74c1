default.doesnt.match.message=Attributet [{0}] för klassen [{1}] med värde [{2}] matchar inte mot uttrycket [{3}]
default.invalid.url.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är inte en giltig URL
default.invalid.creditCard.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är inte ett giltigt kreditkortsnummer
default.invalid.email.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är inte en giltig e-postadress
default.invalid.range.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är inte inom intervallet [{3}] till [{4}]
default.invalid.size.message=Attributet [{0}] för klassen [{1}] med värde [{2}] har en storlek som inte är inom [{3}] till [{4}]
default.invalid.max.message=Attributet [{0}] för klassen [{1}] med värde [{2}] överskrider maxvärdet [{3}]
default.invalid.min.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är mindre än minimivärdet [{3}]
default.invalid.max.size.message=Attributet [{0}] för klassen [{1}] med värde [{2}] överskrider maxstorleken [{3}]
default.invalid.min.size.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är mindre än minimistorleken [{3}]
default.invalid.validator.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är inte giltigt enligt anpassad regel
default.not.inlist.message=Attributet [{0}] för klassen [{1}] med värde [{2}] är inte giltigt, måste vara ett av [{3}]
default.blank.message=Attributet [{0}] för klassen [{1}] får inte vara tomt
default.not.equal.message=Attributet [{0}] för klassen [{1}] med värde [{2}] får inte vara lika med [{3}]
default.null.message=Attributet [{0}] för klassen [{1}] får inte vara tomt
default.not.unique.message=Attributet [{0}] för klassen [{1}] med värde [{2}] måste vara unikt

default.paginate.prev=Föregående
default.paginate.next=Nästa
default.boolean.true=Sant
default.boolean.false=Falskt
default.date.format=yyyy-MM-dd HH:mm:ss z
default.number.format=0

default.created.message={0} {1} skapades
default.updated.message={0} {1} uppdaterades
default.deleted.message={0} {1} borttagen
default.not.deleted.message={0} {1} kunde inte tas bort
default.not.found.message={0} med id {1} kunde inte hittas
default.optimistic.locking.failure=En annan användare har uppdaterat det här {0} objektet medan du redigerade det

default.home.label=Hem
default.list.label= {0} - Lista
default.add.label=Lägg till {0}
default.new.label=Skapa {0}
default.create.label=Skapa {0}
default.show.label=Visa {0}
default.edit.label=Ändra {0}

default.button.create.label=Skapa
default.button.edit.label=Ändra
default.button.update.label=Uppdatera
default.button.delete.label=Ta bort
default.button.delete.confirm.message=Är du säker?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=Värdet för {0} måste vara en giltig URL
typeMismatch.java.net.URI=Värdet för {0} måste vara en giltig URI
typeMismatch.java.util.Date=Värdet {0} måste vara ett giltigt datum
typeMismatch.java.lang.Double=Värdet {0} måste vara ett giltigt nummer
typeMismatch.java.lang.Integer=Värdet {0} måste vara ett giltigt heltal
typeMismatch.java.lang.Long=Värdet {0} måste vara ett giltigt heltal
typeMismatch.java.lang.Short=Värdet {0} måste vara ett giltigt heltal
typeMismatch.java.math.BigDecimal=Värdet {0} måste vara ett giltigt nummer
typeMismatch.java.math.BigInteger=Värdet {0} måste vara ett giltigt heltal