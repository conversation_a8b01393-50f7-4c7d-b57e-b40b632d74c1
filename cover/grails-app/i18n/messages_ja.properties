default.doesnt.match.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、[{3}]パターンと一致していません。
default.invalid.url.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、有効なURLではありません。
default.invalid.creditCard.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、有効なクレジットカード番号ではありません。
default.invalid.email.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、有効なメールアドレスではありません。
default.invalid.range.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、[{3}]から[{4}]範囲内を指定してください。
default.invalid.size.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、[{3}]から[{4}]以内を指定してください。
default.invalid.max.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、最大値[{3}]より大きいです。
default.invalid.min.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、最小値[{3}]より小さいです。
default.invalid.max.size.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、最大値[{3}]より大きいです。
default.invalid.min.size.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、最小値[{3}]より小さいです。
default.invalid.validator.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、カスタムバリデーションを通過できません。
default.not.inlist.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、[{3}]リスト内に存在しません。
default.blank.message=[{1}]クラスのプロパティ[{0}]の空白は許可されません。
default.not.equal.message=クラス[{1}]プロパティ[{0}]の値[{2}]は、[{3}]と同等ではありません。
default.null.message=[{1}]クラスのプロパティ[{0}]にnullは許可されません。
default.not.unique.message=クラス[{1}]プロパティ[{0}]の値[{2}]は既に使用されています。

default.paginate.prev=戻る
default.paginate.next=次へ
default.boolean.true=はい
default.boolean.false=いいえ
default.date.format=yyyy/MM/dd HH:mm:ss z
default.number.format=0

default.created.message={0}(id:{1})を作成しました。
default.updated.message={0}(id:{1})を更新しました。
default.deleted.message={0}(id:{1})を削除しました。
default.not.deleted.message={0}(id:{1})は削除できませんでした。
default.not.found.message={0}(id:{1})は見つかりませんでした。
default.optimistic.locking.failure=この{0}は編集中に他のユーザによって先に更新されています。

default.home.label=ホーム
default.list.label={0}リスト
default.add.label={0}を追加
default.new.label={0}を新規作成
default.create.label={0}を作成
default.show.label={0}詳細
default.edit.label={0}を編集

default.button.create.label=作成
default.button.edit.label=編集
default.button.update.label=更新
default.button.delete.label=削除
default.button.delete.confirm.message=本当に削除してよろしいですか?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL={0}は有効なURLでなければなりません。
typeMismatch.java.net.URI={0}は有効なURIでなければなりません。
typeMismatch.java.util.Date={0}は有効な日付でなければなりません。
typeMismatch.java.lang.Double={0}は有効な数値でなければなりません。
typeMismatch.java.lang.Integer={0}は有効な数値でなければなりません。
typeMismatch.java.lang.Long={0}は有効な数値でなければなりません。
typeMismatch.java.lang.Short={0}は有効な数値でなければなりません。
typeMismatch.java.math.BigDecimal={0}は有効な数値でなければなりません。
typeMismatch.java.math.BigInteger={0}は有効な数値でなければなりません。
