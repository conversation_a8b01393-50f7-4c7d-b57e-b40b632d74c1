default.doesnt.match.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] komt niet overeen met het vereiste patroon [{3}]
default.invalid.url.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] is geen geldige URL
default.invalid.creditCard.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] is geen geldig credit card nummer
default.invalid.email.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] is geen geldig e-mailadres
default.invalid.range.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] valt niet in de geldige waardenreeks van [{3}] tot [{4}]
default.invalid.size.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] valt niet in de geldige grootte van [{3}] tot [{4}]
default.invalid.max.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] overschrijdt de maximumwaarde [{3}]
default.invalid.min.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] is minder dan de minimumwaarde [{3}]
default.invalid.max.size.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] overschrijdt de maximumgrootte van [{3}]
default.invalid.min.size.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] is minder dan minimumgrootte van [{3}]
default.invalid.validator.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] is niet geldig
default.not.inlist.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] komt niet voor in de lijst [{3}]
default.blank.message=Attribuut [{0}] van entiteit [{1}] mag niet leeg zijn
default.not.equal.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] mag niet gelijk zijn aan [{3}]
default.null.message=Attribuut [{0}] van entiteit [{1}] mag niet leeg zijn
default.not.unique.message=Attribuut [{0}] van entiteit [{1}] met waarde [{2}] moet uniek zijn

default.paginate.prev=Vorige
default.paginate.next=Volgende
default.boolean.true=Ja
default.boolean.false=Nee
default.date.format=dd-MM-yyyy HH:mm:ss z
default.number.format=0

default.created.message={0} {1} ingevoerd
default.updated.message={0} {1} gewijzigd
default.deleted.message={0} {1} verwijderd
default.not.deleted.message={0} {1} kon niet worden verwijderd
default.not.found.message={0} met id {1} kon niet worden gevonden
default.optimistic.locking.failure=Een andere gebruiker heeft deze {0} al gewijzigd

default.home.label=Home
default.list.label={0} Overzicht
default.add.label=Toevoegen {0}
default.new.label=Invoeren {0}
default.create.label=Invoeren {0}
default.show.label=Details {0}
default.edit.label=Wijzigen {0}

default.button.create.label=Invoeren
default.button.edit.label=Wijzigen
default.button.update.label=Opslaan
default.button.delete.label=Verwijderen
default.button.delete.confirm.message=Weet je het zeker?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=Attribuut {0} is geen geldige URL
typeMismatch.java.net.URI=Attribuut {0} is geen geldige URI
typeMismatch.java.util.Date=Attribuut {0} is geen geldige datum
typeMismatch.java.lang.Double=Attribuut {0} is geen geldig nummer
typeMismatch.java.lang.Integer=Attribuut {0} is geen geldig nummer
typeMismatch.java.lang.Long=Attribuut {0} is geen geldig nummer
typeMismatch.java.lang.Short=Attribuut {0} is geen geldig nummer
typeMismatch.java.math.BigDecimal=Attribuut {0} is geen geldig nummer
typeMismatch.java.math.BigInteger=Attribuut {0} is geen geldig nummer
