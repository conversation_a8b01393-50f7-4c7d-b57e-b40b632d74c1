#
# Translated by <PERSON> - <EMAIL>
#

default.doesnt.match.message=<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [{0}] klasy [{1}] o wartości [{2}] nie pasuje do wymaganego wzorca [{3}]
default.invalid.url.message=<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [{0}] klasy [{1}] o wartości [{2}] jest niepoprawnym adresem URL
default.invalid.creditCard.message=W<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [{0}] klasy [{1}] with value [{2}] nie jest poprawnym numerem karty kredytowej
default.invalid.email.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] nie jest poprawnym adresem e-mail
default.invalid.range.message=Wła<PERSON><PERSON>wo<PERSON>ć [{0}] klasy [{1}] o wartości [{2}] nie zawiera się zakładanym zakresie od [{3}] do [{4}]
default.invalid.size.message=W<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [{0}] klasy [{1}] o wartości [{2}] nie zaw<PERSON> się w zakła<PERSON>ym zakresie rozmiarów od [{3}] do [{4}]
default.invalid.max.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] przekracza maksymalną wartość [{3}]
default.invalid.min.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] jest mniejsza niż minimalna wartość [{3}]
default.invalid.max.size.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] przekracza maksymalny rozmiar [{3}]
default.invalid.min.size.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] jest mniejsza niż minimalny rozmiar [{3}]
default.invalid.validator.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] nie spełnia założonych niestandardowych warunków
default.not.inlist.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] nie zawiera się w liście [{3}]
default.blank.message=Właściwość [{0}] klasy [{1}] nie może być pusta
default.not.equal.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] nie może równać się [{3}]
default.null.message=Właściwość [{0}] klasy [{1}] nie może być null
default.not.unique.message=Właściwość [{0}] klasy [{1}] o wartości [{2}] musi być unikalna

default.paginate.prev=Poprzedni
default.paginate.next=Następny
default.boolean.true=Prawda
default.boolean.false=Fałsz
default.date.format=yyyy-MM-dd HH:mm:ss z
default.number.format=0

default.created.message=Utworzono {0} {1}
default.updated.message=Zaktualizowano {0} {1}
default.deleted.message=Usunięto {0} {1}
default.not.deleted.message={0} {1} nie mógł zostać usunięty
default.not.found.message=Nie znaleziono {0} o id {1}
default.optimistic.locking.failure=Inny użytkownik zaktualizował ten obiekt {0} w trakcie twoich zmian

default.home.label=Strona domowa
default.list.label=Lista {0}
default.add.label=Dodaj {0}
default.new.label=Utwórz {0}
default.create.label=Utwórz {0}
default.show.label=Pokaż {0}
default.edit.label=Edytuj {0}

default.button.create.label=Utwórz
default.button.edit.label=Edytuj
default.button.update.label=Zaktualizuj
default.button.delete.label=Usuń
default.button.delete.confirm.message=Czy jesteś pewien?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=Właściwość {0} musi być poprawnym adresem URL
typeMismatch.java.net.URI=Właściwość {0} musi być poprawnym adresem URI
typeMismatch.java.util.Date=Właściwość {0} musi być poprawną datą
typeMismatch.java.lang.Double=Właściwość {0} musi być poprawnyą liczbą
typeMismatch.java.lang.Integer=Właściwość {0} musi być poprawnyą liczbą
typeMismatch.java.lang.Long=Właściwość {0} musi być poprawnyą liczbą
typeMismatch.java.lang.Short=Właściwość {0} musi być poprawnyą liczbą
typeMismatch.java.math.BigDecimal=Właściwość {0} musi być poprawnyą liczbą
typeMismatch.java.math.BigInteger=Właściwość {0} musi być poprawnyą liczbą
