default.doesnt.match.message=Значение [{2}] поля [{0}] класса [{1}] не соответствует образцу [{3}]
default.invalid.url.message=Значение [{2}] поля [{0}] класса [{1}] не является допустимым URL-адресом
default.invalid.creditCard.message=Значение [{2}] поля [{0}] класса [{1}] не является допустимым номером кредитной карты
default.invalid.email.message=Значение [{2}] поля [{0}] класса [{1}] не является допустимым e-mail адресом
default.invalid.range.message=Значение [{2}] поля [{0}] класса [{1}] не попадает в допустимый интервал от [{3}] до [{4}]
default.invalid.size.message=Размер поля [{0}] класса [{1}] (значение: [{2}]) не попадает в допустимый интервал от [{3}] до [{4}]
default.invalid.max.message=Значение [{2}] поля [{0}] класса [{1}] больше чем максимально допустимое значение [{3}]
default.invalid.min.message=Значение [{2}] поля [{0}] класса [{1}] меньше чем минимально допустимое значение [{3}]
default.invalid.max.size.message=Размер поля [{0}] класса [{1}] (значение: [{2}]) больше чем максимально допустимый размер [{3}]
default.invalid.min.size.message=Размер поля [{0}] класса [{1}] (значение: [{2}]) меньше чем минимально допустимый размер [{3}]
default.invalid.validator.message=Значение [{2}] поля [{0}] класса [{1}] не допустимо
default.not.inlist.message=Значение [{2}] поля [{0}] класса [{1}] не попадает в список допустимых значений [{3}]
default.blank.message=Поле [{0}] класса [{1}] не может быть пустым
default.not.equal.message=Значение [{2}] поля [{0}] класса [{1}] не может быть равно [{3}]
default.null.message=Поле [{0}] класса [{1}] не может иметь значение null
default.not.unique.message=Значение [{2}] поля [{0}] класса [{1}] должно быть уникальным

default.paginate.prev=Предыдушая страница
default.paginate.next=Следующая страница

# Ошибки при присвоении данных. Для точной настройки для полей классов используйте
# формат "typeMismatch.$className.$propertyName" (например, typeMismatch.Book.author)
typeMismatch.java.net.URL=Значение поля {0} не является допустимым URL
typeMismatch.java.net.URI=Значение поля {0} не является допустимым URI
typeMismatch.java.util.Date=Значение поля {0} не является допустимой датой
typeMismatch.java.lang.Double=Значение поля {0} не является допустимым числом
typeMismatch.java.lang.Integer=Значение поля {0} не является допустимым числом
typeMismatch.java.lang.Long=Значение поля {0} не является допустимым числом
typeMismatch.java.lang.Short=Значение поля {0} не является допустимым числом
typeMismatch.java.math.BigDecimal=Значение поля {0} не является допустимым числом
typeMismatch.java.math.BigInteger=Значение поля {0} не является допустимым числом
