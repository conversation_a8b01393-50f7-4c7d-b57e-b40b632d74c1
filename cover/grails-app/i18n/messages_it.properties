default.doesnt.match.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non corrisponde al pattern [{3}]
default.invalid.url.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non è un URL valido
default.invalid.creditCard.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non è un numero di carta di credito valido
default.invalid.email.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non è un indirizzo email valido
default.invalid.range.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non rientra nell'intervallo valido da [{3}] a [{4}]
default.invalid.size.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non rientra nell'intervallo di dimensioni valide da [{3}] a [{4}]
default.invalid.max.message=La proprietà [{0}] della classe [{1}] con valore [{2}] è maggiore di [{3}]
default.invalid.min.message=La proprietà [{0}] della classe [{1}] con valore [{2}] è minore di [{3}]
default.invalid.max.size.message=La proprietà [{0}] della classe [{1}] con valore [{2}] è maggiore di [{3}]
default.invalid.min.size.message=La proprietà [{0}] della classe [{1}] con valore [{2}] è minore di [{3}]
default.invalid.validator.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non è valida
default.not.inlist.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non è contenuta nella lista [{3}]
default.blank.message=La proprietà [{0}] della classe [{1}] non può essere vuota
default.not.equal.message=La proprietà [{0}] della classe [{1}] con valore [{2}] non può essere uguale a [{3}]
default.null.message=La proprietà [{0}] della classe [{1}] non può essere null
default.not.unique.message=La proprietà [{0}] della classe [{1}] con valore [{2}] deve essere unica

default.paginate.prev=Precedente
default.paginate.next=Successivo
default.boolean.true=Vero
default.boolean.false=Falso
default.date.format=dd/MM/yyyy HH:mm:ss z
default.number.format=0

default.created.message={0} {1} creato
default.updated.message={0} {1} aggiornato
default.deleted.message={0} {1} eliminato
default.not.deleted.message={0} {1} non può essere eliminato
default.not.found.message={0} non trovato con id {1}
default.optimistic.locking.failure=Un altro utente ha aggiornato questo {0} mentre si era in modifica

default.home.label=Home
default.list.label={0} Elenco
default.add.label=Aggiungi {0}
default.new.label=Nuovo {0}
default.create.label=Crea {0}
default.show.label=Mostra {0}
default.edit.label=Modifica {0}

default.button.create.label=Crea
default.button.edit.label=Modifica
default.button.update.label=Aggiorna
default.button.delete.label=Elimina
default.button.delete.confirm.message=Si è sicuri?

# Data binding errors. Usa "typeMismatch.$className.$propertyName per la personalizzazione (es typeMismatch.Book.author)
typeMismatch.java.net.URL=La proprietà {0} deve essere un URL valido
typeMismatch.java.net.URI=La proprietà {0} deve essere un URI valido
typeMismatch.java.util.Date=La proprietà {0} deve essere una data valida
typeMismatch.java.lang.Double=La proprietà {0} deve essere un numero valido
typeMismatch.java.lang.Integer=La proprietà {0} deve essere un numero valido
typeMismatch.java.lang.Long=La proprietà {0} deve essere un numero valido
typeMismatch.java.lang.Short=La proprietà {0} deve essere un numero valido
typeMismatch.java.math.BigDecimal=La proprietà {0} deve essere un numero valido
typeMismatch.java.math.BigInteger=La proprietà {0} deve essere un numero valido
