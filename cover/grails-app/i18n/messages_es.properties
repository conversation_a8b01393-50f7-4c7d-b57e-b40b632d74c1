default.doesnt.match.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no corresponde al patrón [{3}]
default.invalid.url.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es una URL válida
default.invalid.creditCard.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es un número de tarjeta de crédito válida
default.invalid.email.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es una dirección de correo electrónico válida
default.invalid.range.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no entra en el rango válido de [{3}] a [{4}]
default.invalid.size.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no entra en el tamaño válido de [{3}] a [{4}]
default.invalid.max.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] excede el valor máximo [{3}]
default.invalid.min.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] es menos que el valor mínimo [{3}]
default.invalid.max.size.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] excede el tamaño máximo de [{3}]
default.invalid.min.size.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] es menor que el tamaño mínimo de [{3}]
default.invalid.validator.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es válido
default.not.inlist.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no esta contenido dentro de la lista [{3}]
default.blank.message=La propiedad [{0}] de la clase [{1}] no puede ser vacía
default.not.equal.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no puede igualar a [{3}]
default.null.message=La propiedad [{0}] de la clase [{1}] no puede ser nulo
default.not.unique.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] debe ser única

default.paginate.prev=Anterior
default.paginate.next=Siguiente
default.boolean.true=Verdadero
default.boolean.false=Falso
default.date.format=yyyy-MM-dd HH:mm:ss z
default.number.format=0

default.created.message={0} {1} creado
default.updated.message={0} {1} actualizado
default.deleted.message={0} {1} eliminado
default.not.deleted.message={0} {1} no puede eliminarse
default.not.found.message=No se encuentra {0} con id {1}
default.optimistic.locking.failure=Mientras usted editaba, otro usuario ha actualizado su {0}

default.home.label=Principal
default.list.label={0} Lista
default.add.label=Agregar {0}
default.new.label=Nuevo {0}
default.create.label=Crear {0}
default.show.label=Mostrar {0}
default.edit.label=Editar {0}

default.button.create.label=Crear
default.button.edit.label=Editar
default.button.update.label=Actualizar
default.button.delete.label=Eliminar
default.button.delete.confirm.message=¿Está usted seguro?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=La propiedad {0} debe ser una URL válida
typeMismatch.java.net.URI=La propiedad {0} debe ser una URI válida
typeMismatch.java.util.Date=La propiedad {0} debe ser una fecha válida
typeMismatch.java.lang.Double=La propiedad {0} debe ser un número válido
typeMismatch.java.lang.Integer=La propiedad {0} debe ser un número válido
typeMismatch.java.lang.Long=La propiedad {0} debe ser un número válido
typeMismatch.java.lang.Short=La propiedad {0} debe ser un número válido
typeMismatch.java.math.BigDecimal=La propiedad {0} debe ser un número válido
typeMismatch.java.math.BigInteger=La propiedad {0} debe ser un número válido