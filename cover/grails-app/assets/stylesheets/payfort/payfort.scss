#paymentForm {

  .container {
    border-radius: 0;
    border: none;
    padding: 0;
    margin: 0;
    width: 100%;
    position: relative;
  }

  .input {
    border-radius: 0;
  }

  button.pay {
    background-color: #E2156B;
    box-shadow: 0 3px #b41d64;
    color: #fff;
    width: 100%;
    font-family: "proxima-nova", "Tahoma", sans-serif;
    font-size: 16px;
    border: none;
    cursor: pointer;
    display: inline-block;
    letter-spacing: 1px;
    outline: none;
    position: relative;
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
    margin: 8px 0 0;
    padding: 9px 0;
    border-radius: 0;
    text-transform: capitalize;
    font-weight: 600;
    top: -5px;
    text-align: center;

    &:hover:not(:active) {
      box-shadow: 0 2px #b41d64;
      top: -4px;
      color: #fff;
      text-decoration: none;
      opacity: 1;
    }

    &:active {
      box-shadow: 0 0 #b41d64;
      top: -2px;
      color: #fff;
      text-decoration: none;
    }
  }

  .wrapper {
    background: transparent;
  }

  [data-tooltip]:before {
    background-color: #36397D;
    left: 0;
    margin-left: -100px;
    width: 127px;
  }

  .popover {
    border: none;
    background: none;
    padding: 0;
    margin: 0;
    position: relative;
    top: -7px;
    color: #f00;
    height: 14px;
    display: block !important;
    visibility: hidden;

    &[style="display: none;"] {
      visibility: hidden !important;
    }

    &[style="display: block;"] {
      visibility: visible !important;
    }
  }
}
