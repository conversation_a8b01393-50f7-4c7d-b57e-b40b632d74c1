/*
* This is a manifest file that'll be compiled into application.css, which will include all the files
* listed below.
*
* Any CSS file within this directory can be referenced here using a relative path.
*
* You're free to add application-wide styles to this file and they'll appear at the top of the
* compiled file, but it's generally better to create a new file per style scope.
*
*= require insurance-icons/css/insurance-embedded.css
*= require select2/select2.min.css
*= require unslider/css/unslider.css
*= require jquery-slick-carousel/slick.css
*= require jquery-slick-carousel/slick-theme.css
*= require dropzone/dropzone.css
*= require jquery-ui.css

*= require_self
*/

@import "/customBootstrap";

//modules -- This folder should contain only variable files that do not output any CSS.
@import "modules/ets/colors";
@import "modules/ets/font-families";
@import "modules/ets/mixins";
@import "modules/ets/paths";
@import "/assets/stylesheets/commonInsurance";


/**********************************************************GENERAL***************************************************************************/
body {
    font-family: $font-fam-etisalat-body !important;
    font-size: 14px !important;
}

h1, h2, h3, h4, h5, strong, .formHeader {
    font-family: $font-fam-etisalat-title !important;
    font-weight: normal;
    font-style: normal;
    font-size: unset !important;
}
h4.text-primary-darken {
    font-size: 14px;
}
p, .srpHeading, .landingPageHero__title {
    font-family: $font-fam-etisalat-body !important;
}
button, .button-accent, .button-primary {
    font-family: $font-fam-etisalat-body !important;
    text-transform: uppercase !important;
    letter-spacing: .14285714em !important;
    //height: 3rem !important;
    font-size: .875rem !important;
}

.smilesPointsSlider {
    margin-right: -20px;

    .slider-selection {
        background: $clr-accent !important;
        box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
        box-sizing: border-box;
        border-radius: 4px;
        height: 100%;
        top: 0;
        bottom: 0;
    }

    .slider.slider-horizontal {
        width: 100% !important;
        height: 25px;
        position: relative;
        top: 20px;
    }

    .slider.slider-horizontal .slider-track {
        height: 5px;
        margin-top: -3px;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        background-image: linear-gradient(to bottom, #f5f5f5 0, #f9f9f9 100%);
        background-repeat: repeat-x;
        position: absolute;
        width: 100%;
    }

    .slider-ghost .slider-handle {
        border: 5px solid darken($clr-accent, 10);
        border-radius: 50%;
        position: absolute;
        top: -10px;
    }

    .slider-handle {
        background-color: #fff !important;
        background-image: none !important;
        -webkit-box-shadow: 1px 1px 24px -2px rgba(0, 0, 0, 0.75) !important;
        -moz-box-shadow: 1px 1px 24px -2px rgba(0, 0, 0, 0.75) !important;
        box-shadow: 1px 1px 24px -2px rgba(0, 0, 0, 0.75) !important;
        position: absolute;
        z-index: 2;
        width: 1.2em;
        height: 1.2em;
        cursor: default;
        -ms-touch-action: none;
        touch-action: none;
    }

    .slider-handle.min-slider-handle.round {
        height: 25px;
        width: 25px;
    }

    .slider-strips .slider-selection {
        background-image: repeating-linear-gradient(-45deg, transparent, transparent 5px, rgba(255, 252, 252, 0.08) 5px, rgba(252, 252, 252, 0.08) 10px) !important;
        background-image: -ms-repeating-linear-gradient(-45deg, transparent, transparent 5px, rgba(255, 252, 252, 0.08) 5px, rgba(252, 252, 252, 0.08) 10px) !important;
        background-image: -o-repeating-linear-gradient(-45deg, transparent, transparent 5px, rgba(255, 252, 252, 0.08) 5px, rgba(252, 252, 252, 0.08) 10px) !important;
        background-image: -webkit-repeating-linear-gradient(-45deg, transparent, transparent 5px, rgba(255, 252, 252, 0.08) 5px, rgba(252, 252, 252, 0.08) 10px) !important;
    }

    .tooltip-inner {
        max-width: 200px;
        padding: 3px 8px;
        color: #bdbdbd !important;
        text-align: center;
        background-color: transparent !important;
        border-radius: 4px;
        top: -10px;
        position: absolute;
        min-width: 100px;
        font-size: 12px;
    }

    .slider .tooltip.tooltip-main {
        position: absolute;
        margin-top: -26px;
    }
}
.priceBreakDownAedPoints {
    margin-top: 40px;

    .totalPrice {

    }

    .points {

    }

    .showBox {
        padding: 6px 15px;
        border: 1px solid $clr-accent;
    }
}
.border-color-accent {
    border-color: $clr-accent;
}
.landingPageHero {
    .landingPageHero__col1 {
        .landingPageHero__title {
            font-weight: normal;
        }
    }

    .landingPageHero__button {
        font-family: 'Neo Tech Alt' !important;
    }
}

.select2-selection--single {
    border: none !important;
    border-bottom: solid 1px #ccc !important;

    &:hover {
        border: none !important;
        box-shadow: none !important;
    }
}
.carQuoteV2 {
    .carQuoteV2__header {
        @media screen and (max-width: 1100px) {
        }

        .carQuoteV2__provider__price {
            width: 100%;
            margin: auto;
            padding: 10px;
            position: relative;
        }

        // .carQuoteV2__provider,
        // .carQuoteV2__product, .carQuoteV2__ets__price {
        //     width: 50%;
        // }

        // .carQuoteV2__ets__price {
        //     // float: right;
        //     font-size: 15px;
        //     // margin-top: 20px;
        //     color: $clr-primary;
        // }

        .carQuoteV2__ets__price {
            color: $clr-primary;
            font-weight: bold;
            font-family: Neo Tech Alt;
        }

        // .carQuoteV2__provider img {
        //     width: 50%;
        //     margin: 10px auto;
        // }

        .carQuoteV2__cta {
            margin-top: 0 !important;
        }
    }

    .etisalatEarn {
        display: block !important;
        padding: 5px 15px;
        margin-top: 20px;
        font-size: 14px !important;
        background-color: #f1f1eb;
        font-family: Neo Tech Alt;

        img {
            height: 25px;
            margin-right: 5px;
        }

        span {
            color: #7f5f96;
            font-weight: bold;
        }
    }
}
.mandatory {
    font-size: 12px;
    color: #810629;
    font-weight: bold;
    margin-top: 10px;
}
.etsDisablePayment {
    .radio-btn {
        opacity: 0.5;
    }

    &:before {
        content: 'This payment method is disabled due to an error, Please try to LOGIN again';
        color: red;
        font-size: 12px;
        font-style: italic;
    }

    label {
        opacity: 0.4;
        filter: alpha(opacity=30); /* msie */
        cursor: none;
        pointer-events: none;
    }

    input[type= radio] {
        pointer-events: none;
        opacity: 0.5;
    }

    form {
        visibility: hidden;
    }
}
.etsInsufficientPayment {
    &:before {
        content: 'This payment method is disabled due to insufficient points';
        color: red;
        font-size: 12px;
        font-style: italic;
    }

    label {
        opacity: 0.4;
        filter: alpha(opacity=30); /* msie */
        cursor: none;
        pointer-events: none;
    }

    input[type= radio] {
        pointer-events: none;
        opacity: 0.5;
    }

    form {
        visibility: hidden;
    }
}
.secure-checkout-section .overview .image {
    max-height: 70px;
}
.promo-icon, .longer-promo-icon {
    height: 40px;
    margin: 15px 0;
}
.carQuoteV2 .carQuoteV2__cta button {
    line-height: 20px;
}
.docPreview {
    //display: block;
    //position: absolute;
    //top: 50%;
    //left: 50%;
    //min-height: 50%;
    //min-width: 50%;
    //transform: translate(-50%, -50%);
    width: auto; // to maintain aspect ratio. You can use 100% if you don't care about that
    height: 90%;
}
.ui-widget-content {
    background-color: transparent !important;
    border: none !important;
}
.ui-widget-header {
    border: 1px solid gray;
    background: gray;
    color: gray;
    font-weight: bold;
}
.ui-widget-overlay {
    background: rgba(0, 0, 0, 0.3) !important;
    opacity: 1 !important;
}
.ui-widget.ui-widget-content {
    box-shadow: none !important;
    border: none !important;
    top: 50px !important;
}
.ui-dialog {
    .ui-dialog-titlebar {
        display: none;
    }

    .ui-dialog-buttonpane {
        position: absolute;
        top: 0;
        right: 0;
    }

    .ui-dialog-buttonpane .ui-dialog-buttonset {
        float: none;
        top: -25px;
        position: absolute;
        right: 12px;
        color: #000;

        button {
            border: none;
            outline: none;
            text-transform: lowercase !important;
            letter-spacing: 0 !important;
            background: #7f5f96;
            color: #fff;
        }
    }
}
.landingPageHero.v2 {
    .innerContentWrapper-Featured {
        background: $clr-accent;
    }

    .discount-container {
        @media screen and (max-width: $screen-md) {
            h1 {
                font-size: 50px !important;
                background-color: transparent;
                background: linear-gradient(45deg, #39e0ca, $clr-primary) !important;
                font-family: $font-fam-etisalat-title !important;

                span {
                    color: #000;
                    -webkit-text-stroke-width: 0;
                    font-size: 20px;
                }
            }
            h2 {
                color: $clr-accent;
                font-size: 20px !important;
                text-transform: uppercase;
                font-weight: bold;
                padding: 10px;
            }
            h3 {
                color: $clr-accent;
                font-size: 20px !important;
            }
            h4 {
                color: $clr-accent;
                line-height: 20px;
            }
            .days-container, .hours-container, .mints-container, .seconds-container {
                background: linear-gradient(45deg, #39e0ca, $clr-primary);
            }
            .days-txt, .hours-txt, .mints-txt, .seconds-txt {
                background-color: $clr-primary !important;
            }
            .hours-container {
                background: linear-gradient(45deg, #9346ca, $clr-accent) !important;
            }
            .hours-txt {
                background-color: $clr-accent !important;
            }
        }
    }
}
.landing-wrapper{
    display: block;
    text-align: center;
    margin-top: 20px;
    .active-services{
        display: block;
        padding: 0 0 20px 0;
        margin: 0 0 20px 0;
        border-bottom: solid 1px #f1f1f1;
        .service{
            .icon{
                display: inline-block;
                width: 200px;
                height: 200px;
                background-image: url("/insurance/assets/ets-active-service-icon-bg.png");
                background-size: contain;
                background-position: center center;
                background-repeat: no-repeat;
                img{
                    height: 150px;
                    margin-top: 25px;
                }
            }
            .name{
                h1{
                    text-transform: uppercase;
                    font-size: 25px !important;
                    color: $clr-accent;
                    margin: 20px 0;
                }
            }
            .buttons{
                a{
                    border: none;
                    color: #fff;
                    font-size: 16px;
                    padding: 10px;
                    text-transform: unset !important;
                    letter-spacing: 0 !important;
                    min-width: 120px;
                    display: inline-block;
                    text-decoration: none;
                    cursor: pointer;
                    &:first-child{
                        margin-right: 10px;
                    }
                    &:hover{
                        background-color: #fff;
                        border: solid 1px $clr-accent;
                        color: #000;
                        text-decoration: none;
                    }
                }
                .myQuotes{
                    background-color: $clr-primary;
                    border: solid 1px $clr-primary;
                }
                .getQuote{
                    background-color: $clr-accent;
                    border: solid 1px $clr-accent;
                }
            }
        }
    }
    .coming-services{
        padding: 0 0 20px 0;
        margin: 0 0 20px 0;
        border-bottom: solid 1px #f1f1f1;
        .title{
            font-size: 20px !important;
            color: $clr-accent;
            margin-bottom: 20px !important;
        }
    }
    .row{
        display: flex;
        margin: 0 !important;
        .col-md-3 {
            width: 25%;
            float: left;
            padding: 0 !important;
            margin-right: 10px !important;
            background-size: 70px;
            background-repeat: no-repeat;
            text-align: center;
            background-position: top;
            &:last-child{
                margin-right: 0 !important;
            }
            &.travel{
                background-image: url("/insurance/assets/ets-travel-icon.png");
            }
            &.health{
                background-image: url("/insurance/assets/ets-health-icon.png");
            }
            &.life{
                background-image: url("/insurance/assets/ets-life-icon.png");
            }
            &.home{
                background-image: url("/insurance/assets/ets-home-icon.png");
            }
            span{
                display: inline-block;
                margin-top: 80px;
                color: #999;
            }
        }
    }
}
.quotes-wrapper{
    display: block;
    text-align: center;
    margin: 20px;
    .btnback{
        display: block;
        width: 70px;
        margin-bottom: 20px;
        padding: 5px 10px 5px 15px;
        font-size: 12px;
        background-color: #fff;
        background-image: url("https://image.flaticon.com/icons/svg/271/271220.svg");
        background-size: 10px;
        background-repeat: no-repeat;
        background-position: 10px center;
        border: solid 1px $clr-accent;
        text-decoration: none;
        color: #000;

    }
    .quote{
        display: block;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 2px;
        box-shadow: 0 2px 6px 0 hsla(0, 0%,0%,0.1);
        .make{
            img{
                height: 30px;
                margin-top: -10px;
            }
            h1{
                font-size: 25px !important;
                margin: 0 0 10px 0 !important;
                color: #000;
                padding-left: 10px;
            }
        }
        .model{
            font-weight: bold;
            margin-bottom: 10px;
        }
        .value{
            .txt{
                color: #000;
                font-size: 12px;
            }
            .date{
                color: #000;
                font-size: 12px;
                color: $clr-primary;
            }
            .num{
                font-family: $font-fam-etisalat-title !important;
                color: $clr-accent;
                font-size: 16px;
            }
        }
        .quote-btn{
            display: inline-block;
            width: 120px;
            padding: 10px 0;
            margin-top: 10px;
            background-color: $clr-accent;
            color: #fff;
            cursor: pointer;
            border: solid 1px $clr-accent;
            &:hover{
                background-color: #fff;
                border: solid 1px $clr-accent;
                text-decoration: none;
                color: #000;
            }
        }
    }
    h1{
        text-transform: uppercase;
        font-size: 25px !important;
        color: $clr-primary;
        margin: 20px 0;
        margin-top: 5vh;
    }
    .buttons{
        margin-top: 10vh;
        a{
            border: none;
            color: #fff;
            font-size: 20px;
            padding: 10px;
            text-transform: unset !important;
            letter-spacing: 0 !important;
            min-width: 200px;
            display: inline-block;
            text-decoration: none;
            cursor: pointer;
            &:first-child{
                margin-right: 10px;
            }
            &:hover{
                background-color: #fff;
                border: solid 1px $clr-accent;
                color: #000;
                text-decoration: none;
            }
        }
        .getQuote{
            background-color: $clr-accent;
            border: solid 1px $clr-accent;
        }
    }
}
.terms{
    display: block;
    margin-top: 20px;
    a{
        margin-bottom: 10px;
        font-size: 12px;
        display: block;
    }
}
.pagination a,
.pagination .currentStep {
    color: #666666;
    display: inline-block;
    margin: 0 0.1em;
    padding: 0.25em 0.7em;
    text-decoration: none;
}
.pagination a:hover, .pagination a:focus,
.pagination .currentStep {
    background-color: $clr-primary;
    color: #ffffff;
    outline: none;
}
.loader-wrapper{
    display: none;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 10;
}
/****** Font sizes *********/
.checkout-promo-text, .checkout-promo {
    line-height: 20px !important;
    font-weight: normal !important;
    font-size: unset !important;
}
.secure-checkout-section .total-amount {
    .label {
        font-size: unset !important;
    }

    .price {
        font-size: unset !important;
    }
}
.v3SrpHead {
    font-size: unset;
    text-align: center !important;
}

.cheapestQuotes_item{
    background: #56b8ab;
    border: none;
    .cheapestQuotes_item__title{
        color: #fff;
    }

    .cheapestQuotes_item__details {
        color: #fff;
        li {
            span {
                background: #56b8ab;
                color: #fff;
            }
            .cheapestQuotes_item__details--price {
                color: #fdea14;
            }
        }
    }

    .cheapestQuotes_item--buy{
        color: #7f5f96;
        background-color: #fff;
    }
    .viewOption{
        color: #fff;
    }
}
