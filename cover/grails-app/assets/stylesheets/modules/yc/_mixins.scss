//BIDIRECTIONAL MARGIN
@mixin bidi-margin($top, $after, $bottom, $before) {
  @at-root [dir="ltr"] & {
    margin: $top $after $bottom $before;
  }

  @at-root [dir="rtl"] & {
    margin: $top $before $bottom $after;
  }
}

//BIDIRECTIONAL MARGIN-LEFT
@mixin bidi-margin-left($before) {
  @at-root [dir="ltr"] & {
    margin-left: $before;
  }

  @at-root [dir="rtl"] & {
    margin-right: $before;
  }
}

//BIDIRECTIONAL MARGIN-RIGHT
@mixin bidi-margin-right($after) {
  @at-root [dir="ltr"] & {
    margin-right: $after;
  }

  @at-root [dir="rtl"] & {
    margin-left: $after;
  }
}

//BIDIRECTIONAL LEFT
@mixin bidi-left($distance) {
  @at-root [dir="ltr"] & {
    left: $distance;
  }

  @at-root [dir="rtl"] & {
    right: $distance;
  }
}

//BIDIRECTIONAL RIGHT
@mixin bidi-right($distance) {
  @at-root [dir="ltr"] & {
    right: $distance;
  }

  @at-root [dir="rtl"] & {
    left: $distance;
  }
}

//BIDIRECTIONAL BORDER-RADIUS
@mixin bidi-border-radius($top-near, $top-far, $bottom-far, $bottom-near) {
  @at-root [dir="ltr"] & {
    border-radius: $top-near $top-far $bottom-far $bottom-near;
  }

  @at-root [dir="rtl"] & {
    border-radius: $top-far $top-near $bottom-near $bottom-far;
  }
}

//BIDIRECTIONAL PADDING
@mixin bidi-padding($top, $after, $bottom, $before) {
  @at-root [dir="ltr"] & {
    padding: $top $after $bottom $before;
  }

  @at-root [dir="rtl"] & {
    padding: $top $before $bottom $after;
  }
}

//BIDIRECTIONAL PADDING-LEFT
@mixin bidi-padding-left($before) {
  @at-root [dir="ltr"] & {
    padding-left: $before;
  }

  @at-root [dir="rtl"] & {
    padding-right: $before;
  }
}

//BIDIRECTIONAL PADDING-RIGHT
@mixin bidi-padding-right($after) {
  @at-root [dir="ltr"] & {
    padding-right: $after;
  }

  @at-root [dir="rtl"] & {
    padding-left: $after;
  }
}

//BIDIRECTIONAL TRANSLATE(X, Y)
@mixin bidi-translate($x, $y) {
  @at-root [dir="ltr"] & {
    -webkit-transform: translate($x, $y);
    -moz-transform: translate($x, $y);
    -ms-transform: translate($x, $y);
    -o-transform: translate($x, $y);
    transform: translate($x, $y);
  }

  @at-root [dir="rtl"] & {
    -webkit-transform: translate(-$x, $y);
    -moz-transform: translate(-$x, $y);
    -ms-transform: translate(-$x, $y);
    -o-transform: translate(-$x, $y);
    transform: translate(-$x, $y);
  }
}

//BIDIRECTIONAL SKEW(DEG)
@mixin bidi-skew($deg) {
    @at-root [dir="ltr"] & {
        -webkit-transform: skew($deg);
        -moz-transform: skew($deg);
        -ms-transform: skew($deg);
        -o-transform: skew($deg);
        transform: skew($deg);
    }

    @at-root [dir="rtl"] & {
        -webkit-transform: skew(-($deg));
        -moz-transform: skew(-($deg));
        -ms-transform: skew(-($deg));
        -o-transform: skew(-($deg));
        transform: skew(-($deg));
    }
}

//BIDIRECTIONAL TRANSLATE CALC(X + OFFSET, Y)
@mixin bidi-calc-translate($x, $offset, $y) {
    @at-root [dir="ltr"] & {
        -webkit-transform: translate(calc(#{$x} + #{$offset}), $y);
        -moz-transform: translate(calc(#{$x} + #{$offset}), $y);
        -ms-transform: translate(calc(#{$x} + #{$offset}), $y);
        -o-transform: translate(calc(#{$x} + #{$offset}), $y);
        transform: translate(calc(#{$x} + #{$offset}), $y);
    }

    @at-root [dir="rtl"] & {
        -webkit-transform: translate(calc(#{-$x} + #{-$offset}), $y);
        -moz-transform: translate(calc(#{-$x} + #{-$offset}), $y);
        -ms-transform: translate(calc(#{-$x} + #{-$offset}), $y);
        -o-transform: translate(calc(#{-$x} + #{-$offset}), $y);
        transform: translate(calc(#{-$x} + #{-$offset}), $y);
    }
}

//BIDIRECTIONAL FLOAT
@mixin bidi-float($direction) {
  @if $direction == "left" {
    @at-root [dir="ltr"] & {
      float: left;
    }

    @at-root [dir="rtl"] & {
      float: right;
    }
  }
  @else {
    @at-root [dir="ltr"] & {
      float: right;
    }

    @at-root [dir="rtl"] & {
      float: left;
    }
  }
}

//BIDIRECTIONAL TEXT-ALIGN
@mixin bidi-text-align($direction) {
  @if $direction == "left" {
    @at-root [dir="ltr"] & {
      text-align: left;
    }

    @at-root [dir="rtl"] & {
      text-align: right;
    }
  }
  @else {
    @at-root [dir="ltr"] & {
      text-align: right;
    }

    @at-root [dir="rtl"] & {
      text-align: left;
    }
  }
}

//BIDIRECTIONAL TRANSLATE3D
@mixin bidi-translate3d($x, $y, $z) {
  @at-root [dir="ltr"] & {
    -webkit-transform: translate3d($x, $y, $z);
    -moz-transform: translate3d($x, $y, $z);
    -ms-transform: translate3d($x, $y, $z);
    -o-transform: translate3d($x, $y, $z);
    transform: translate3d($x, $y, $z);
  }

  @at-root [dir="rtl"] & {
    -webkit-transform: translate3d(-$x, $y, $z);
    -moz-transform: translate3d(-$x, $y, $z);
    -ms-transform: translate3d(-$x, $y, $z);
    -o-transform: translate3d(-$x, $y, $z);
    transform: translate3d(-$x, $y, $z);
  }
}

//BIDIRECTIONAL BACKGROUND-POSITION
@mixin bidi-background-position($leftright, $topbottom) {
  @if $leftright == "left" {
    @at-root [dir="ltr"] & {
      background-position: left $topbottom;
    }

    @at-root [dir="rtl"] & {
      background-position: right $topbottom;
    }
  }
  @else {
    @at-root [dir="ltr"] & {
      background-position: right $topbottom;
    }

    @at-root [dir="rtl"] & {
      background-position: left $topbottom;
    }
  }
}

//BIDIRECTIONAL BACKGROUND-POSITION WITH ARABIC OFFSET
@mixin bidi-background-position-with-offset($leftright, $offset, $topbottom) {
  @if $leftright == "left" {
    @at-root [dir="ltr"] & {
      background-position: left $offset $topbottom;
    }

    @at-root [dir="rtl"] & {
      background-position: right $offset $topbottom;
    }
  }
  @else {
    @at-root [dir="ltr"] & {
      background-position: right $offset $topbottom;
    }

    @at-root [dir="rtl"] & {
      background-position: left $offset $topbottom;
    }
  }
}

//BIDIRECTIONAL BORDER-RIGHT
@mixin bidi-border-right($border) {
  @at-root [dir="ltr"] & {
    border-right: $border;
  }

  @at-root [dir="rtl"] & {
    border-left: $border;
  }
}

//BIDIRECTIONAL BORDER-LEFT
@mixin bidi-border-left($border) {
  @at-root [dir="ltr"] & {
    border-left: $border;
  }

  @at-root [dir="rtl"] & {
    border-right: $border;
  }
}

//BIDIRECTIONAL BORDER-LEFT
@mixin bidi-border-width($border) {
    @at-root [dir="ltr"] & {
        border-left: $border;
    }

    @at-root [dir="rtl"] & {
        border-right: $border;
    }
}

//ICONS
@mixin icon-colors($clr, $bg-clr) {
  background-color: $clr;
  height: .7em;
  width: .7em;
  border-radius: 50%;
  display: inline-block;

  &:before {
    color: $bg-clr;
    bottom: .2em;
    right: .08em;
    position: relative;
  }
}

//RETINA BACKGROUND IMAGES
@mixin hdpi-background-image($standard-res-path, $file-extension) {
  background-image: url("#{$standard-res-path}.#{$file-extension}");

  @media only screen and (-webkit-min-device-pixel-ratio: 1.3),
  only screen and (-o-min-device-pixel-ratio: 13/10),
  only screen and (min-resolution: 120dpi),
  only screen and (min-resolution: 1.3dppx)
  {
    background-image: url("#{$standard-res-path}2x.#{$file-extension}");
  }
}

//CLEARFIX
@mixin clearfix {
    &:after {
        content: " ";
        display: block;
        height: 0;
        clear: both;
    }
}

//Animations
@mixin animation($animate...) {
    $max: length($animate);
    $animations: '';

    @for $i from 1 through $max {
        $animations: #{$animations + nth($animate, $i)};

        @if $i < $max {
            $animations: #{$animations + ", "};
        }
    }
    -webkit-animation: $animations;
    -moz-animation:    $animations;
    -o-animation:      $animations;
    animation:         $animations;
}

//Keyframes
@mixin keyframes($animationName) {
    @-webkit-keyframes #{$animationName} {
        @content;
    }
    @-moz-keyframes #{$animationName} {
        @content;
    }
    @-o-keyframes #{$animationName} {
        @content;
    }
    @keyframes #{$animationName} {
        @content;
    }
}
