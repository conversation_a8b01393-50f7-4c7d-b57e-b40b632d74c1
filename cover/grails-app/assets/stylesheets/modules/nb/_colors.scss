@function transformColor($color, $hue-change, $saturation-change, $lightness-change) {
  $hsl: hsl(hue($color), saturation($color), lightness($color));

  @return adjust_color($hsl, $hue: $hue-change, $saturation: $saturation-change, $lightness: $lightness-change);
}

$clr-primary: #25A4C1;
$clr-primary-darker: darken($clr-primary, 7);
$clr-primary-darkest: darken($clr-primary, 15);
$clr-primary-dirty: #2b8abd;
$clr-primary-intense: darken($clr-primary, 17);
$clr-primary-dull: #88a9bd;
$clr-primary-highlight: darken($clr-primary, 25);
$clr-primary-faded: rgba(128, 159, 189, 0.72);
$clr-primary-text-dark:  lighten($clr-primary, 10);
$clr-primary-text-light: #a0c8ec;

$clr-accent: #B20838;
$clr-accent-faded: rgb(191, 79, 101);
$clr-background-grey: #f6f2ef;
$clr-dark-grey-text: #555;

$clr-default: #fff;

$clr-mild: #FAFAFA;

$clr-distinct: #CCC;
$clr-distinct-slight-lighter: transformColor($clr-distinct, 0deg, 0%, 8.29412%);
$clr-distinct-light: transformColor($clr-distinct, 0deg, 0%, 15.29412%);
$clr-distinct-very-light: transformColor($clr-distinct, 0deg, 0%, 17.64706%);
$clr-distinct-dark: transformColor($clr-distinct, 0deg, 0%, -6.66667%);
$clr-distinct-very-dark: transformColor($clr-distinct, 0deg, 0%, -26.66667%);
$clr-distinct-off: transformColor($clr-distinct-dark, 51deg, 34.48276%, 8.62745%);

$clr-almost-black: #697078;
$clr-dark: #000;
$clr-dark-almost: transformColor($clr-dark, 0deg, 0%, 20%);
.ncdSpanPink{
     color:hotpink;
}

