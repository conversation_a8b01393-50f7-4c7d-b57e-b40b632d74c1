.carWarranty {
    color: #000;
    background: #f8f8f8;
    overflow: hidden;
    a {
        text-decoration: none;
    }
    p {
        font-size: 24px;

        @media (max-width: 768px) {
            font-size: 18px;
        }
    }
    header {
        height: auto;
        background: transparent;
    }
    #whatCovered {
        margin-bottom: 0;
    }
    @media (min-width: $screen-desktop) {
        .container-fluid {
            padding-right: 50px;
            padding-left: 50px;
        }
    }

    .is--relative {
        position: relative;
    }
    .is--borderless {
        border: none !important;
    }
    .btn {
        border-radius: 18px;

        &.btn-primary {
            background-color: #e2156b;
            border-color: transparent;
            font-weight: 700;
        }
        &.btn-default {
            background-color: #36397d;
            border-color: transparent;
            font-weight: 700;
            color: #fff;
        }
    }
    .text--large {
        font-size: 28px;

        @media (max-width: 700px) {
            font-size: 18px;
        }
    }
    .pageBg {
        background: linear-gradient(130deg, #ffc7f3 0%, #ffe9b0 100%);
        position: absolute;
        width: 100%;
        height: 70%;
        z-index: -1;

        &.pageBg--top {
            top: -110px;
            left: -290px;
            transform: rotate(45deg);
            border-bottom-right-radius: 500px;
            border-top-right-radius: 500px;
        }
    }
    .globalHeader__logo {
        img {
            // max-width: 100%;
            width: 400px;

            @media (max-width: 900px) {
                width: 250px;
            }
            @media (max-width: 320px) {
                width: 200px;
            }
        }
    }
    .hero {
        background: transparent;
        color: #000;
        margin-top: 50px;
        padding-bottom: 50px;
        @media (max-width: 800px) {
            padding-bottom: 30px;
        }
        .hero__text {
            flex: 0 0 50%;
            padding-right: 50px;
            h1 {
                font-weight: bold;
                font-size: 40px;
                @media (min-width: 1024px + 1) {
                    font-size: 60px;
                }
                span {
                    display: inline-block;
                }
            }
            p {
                font-size: 18px;
                text-align: justify;
            }
            .btn {
                padding: 15px;
                margin-top: 60px;
                font-size: 26px;
                border-radius: 30px;
            }
            @media (min-width: 1024px) {
                padding-inline-start: 50px;
            }
        }
        .hero__content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        @media (min-width: 1024px) {
            .container-fluid {
                padding: 0;
            }
        }

        .hero__img {
            height: 100%;
            flex: 0 0 50%;
            // background: url();
            padding-inline-start: 50px;
            background-repeat: no-repeat;
            background-size: cover;
            @media (width: 1024px) {
                img {
                    width: 650px !important;
                    max-width: none !important;
                }
                padding-inline-start: 20px;
            }
            img {
                border-top-left-radius: 30px;
                border-bottom-left-radius: 30px;
                max-width: 100%;
                float: right;
                overflow: hidden;
                width: 100%;
            }
        }
    }

    .globalHeader {
        padding: 40px 0;
        position: relative;
        overflow: visible !important;
        &.is--menuOpen {
            #headerMenu::after {
                transform: scaleX(1) !important;
            }
            .globalHeader__menuContent {
                top: 50px;
                opacity: 1;
                visibility: visible;
                z-index: 9999;

                .globalHeader__links {
                    flex-wrap: nowrap;
                    margin-bottom: 30px;
                    align-items: center;
                    justify-content: center;
                }
            }
        }
        .globalHeader__menuContent {
            position: absolute;
            top: 80px;
            right: 0;
            background-color: #fff;
            box-shadow: 0px 33px 33px -8px rgba(0, 0, 0, 0.12);
            border-radius: 16px;
            z-index: -1;
            padding: 40px;
            width: 100%;
            opacity: 0;
            visibility: hidden;
            transition: all 0.25s;
            .globalHeader__menuContent_title {
                font-size: 16px;
                font-weight: 700;
            }
            .globalHeader__menuContent_items {
                list-style: none;
                padding: 0;
                margin-top: 40px;

                li {
                    margin-bottom: 10px;
                    a {
                        color: rgba(0, 0, 0, 0.5);
                        font-size: 16px;
                    }
                }
            }
        }
        .globalHeader__links {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-size: 18px;
            flex-wrap: wrap;
            a {
                display: inline-block;
                margin-right: 20px;
                margin-left: 20px;
                color: #000;
                text-transform: capitalize;

                &.btn {
                    color: #fff;
                }
            }
            #headerMenu {
                display: flex;
                align-items: center;
                font-weight: 700;
                position: relative;

                &:after {
                    content: "";
                    width: 100%;
                    height: 4px;
                    background-color: #e2156b;
                    position: absolute;
                    bottom: -15px;
                    left: 0;
                    transform: scaleX(0);
                    transform-origin: left;
                    transition: all 0.25s;
                }
                span {
                    padding-right: 10px;
                }
            }
        }
    }

    .section {
        padding-top: 50px;
        padding-bottom: 50px;
        @media (max-width: 800px) {
            padding-top: 30px;
            padding-bottom: 30px;
        }
        .section__title {
            font-weight: 700;
            font-size: 28px;
            margin-top: 0;

            @media (max-width: 800px) {
                text-align: center;
            }
        }
    }
    .section__content {
        margin-top: 30px;
    }
    .section__card {
        background-color: #fff;
        padding: 30px;
        border-radius: 30px;
        &.card--hover {
            transition: all 0.25s;
            &:hover {
                box-shadow: 0px 20px 30px -7.68496px rgba(0, 0, 0, 0.1);
            }
        }
        .panel-body {
            border-top: none !important;
        }
        .panel-heading {
            &.is--in {
                border-bottom: none !important;
                i {
                    transform: rotate(0) !important;
                }
            }
            .panel-title {
                font-size: 22px;
                a {
                    position: relative;
                    display: block;
                    padding-inline-end: 30px;
                    i {
                        position: absolute;
                        right: 0;
                        font-size: 15px;
                        top: 5px;
                        transform: rotate(45deg);
                        transition: all 0.25s;
                    }
                }
            }
        }

        .panel-body {
            color: #999;
        }
    }
    .ulList {
        ul {
            margin-top: 30px;
            height: 280px;
            overflow: hidden;
            transition: all 0.25s;
        }
        li {
            margin-bottom: 20px;
            font-size: 16px;
        }
    }
    .notCovered {
        ul {
            // height: 660px;
            overflow: hidden;
            margin-bottom: 0;
        }
        &.showAll {
            ul {
                height: 100% !important;
            }
            .showNotCovered {
                display: none;
            }
        }
    }

    .section__list div:first-child .section__list_item::after {
        display: none;
    }
    .section__list_item {
        padding: 0 40px;
        position: relative;
        &::after {
            content: "";
            position: absolute;
            width: 1px;
            height: 150px;
            background: rgba(0, 0, 0, 0.12);
            left: 0;
            top: 0;
        }

        .section__list_item_text {
            margin-top: 15px;
        }
    }
    .rounded {
        width: 45px;
        height: 45px;
        border-radius: 100%;
        display: inline-block;
        color: #fff;
        font-size: 25px;
        line-height: 45px;
        text-align: center;

        &.rounded--purple {
            background-color: #36397d;
        }
    }
    .showNotCovered {
        .panel-heading {
            border-color: rgba(0, 0, 0, 0.05);
        }
    }
    .section__requirements {
        position: relative;

        // @media (max-width: 800px) {
        //     padding-top: 30px !important;
        // }
        .section__requirements_img {
            position: absolute;
            top: 70%;
            left: 30px;
            transform: translate(0, -50%);
            @media (max-width: 900px) {
                display: none;
            }
            img {
                width: 480px;
                overflow: hidden;
                height: 480px;
                border-radius: 100%;
                max-width: 100%;

                @media (width: 1024px) {
                    width: 320px;
                    height: 320px;
                }
            }
            &::before {
                content: "";
                position: absolute;
                top: -110px;
                left: -250px;
                border-radius: 100%;
                width: 100%;
                background-color: #36397d;
                height: 100%;
                z-index: -1;
            }
        }
    }
    #coverageSection .section__title {
        margin-bottom: 30px;
    }
    .otherProducts {
        text-align: center;
        svg {
            width: 120px;
            height: 120px;
        }
        a {
            display: block;
            font-size: 18px;
            color: #000;
            font-weight: 700;
        }
    }
    .contactus__section {
        .contantus__item {
            text-align: center;

            i {
                font-size: 80px;
            }
            p,
            .btn {
                font-size: 26px;
                font-weight: 700;
            }
            .btn {
                margin-top: 30px;
                padding: 10px 80px;
                border-radius: 30px;
            }
        }
        @media (max-width: 800px) {
            .contantus__item {
                margin-bottom: 30px;
            }
            .row div:last-child .contantus__item {
                margin-bottom: 0;
            }
        }
        @media (max-width: 1024px) {
            .btn {
                padding: 10px !important;
                font-size: 20px !important;
            }
        }
    }
    .whyYC {
        text-align: center;

        @media (max-width: 800px) {
            text-align: justify;
        }
    }
    .plan__box {
        h3 {
            margin-top: 0;
            text-align: center;
            font-size: 52px;
            color: #e2156b;
            margin-bottom: 30px;
            font-weight: 700;
        }
        .ulList {
            li:last-child {
                margin-bottom: 0;
            }
        }
    }
    .section--faq {
        .section__card {
            background-color: transparent;
            padding: 0;
            margin: 0;
        }
        .panel {
            background-color: transparent;
        }
    }
    .globalFooter {
        position: relative;
        background-repeat: no-repeat;
        background-image: url(/insurance/assets/footer_bg.png);
        background-position: left top;
        background-size: cover;
        .globalFooter__compare {
            h2 {
                text-align: center;
                font-weight: 700;
                font-size: 28px;
                margin-bottom: 30px;

                @media (max-width: 500px) {
                    font-size: 18px;
                }
            }
            ul {
                display: flex;
                align-items: center;
                justify-content: space-between;
                list-style: none;
                padding: 0;
                margin: 0;
                flex-wrap: wrap;
                @media (min-width: $screen-lg-desktop) {
                    width: 50%;
                    margin: auto;
                }
                li a {
                    color: #000000;
                    font-weight: 700;
                    font-size: 18px;
                    @media (max-width: 500px) {
                        font-size: 14px;
                    }
                }
            }
        }

        .globalFooter__countries {
            margin-top: 70px;
            @media (min-width: $screen-lg-desktop) {
                padding-right: 150px;
                padding-left: 150px;
            }
            ul {
                padding: 0;
                list-style: none;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                li {
                    a {
                        color: #000;
                        display: block;
                        text-align: center;
                    }
                    svg {
                        width: 50px;
                        height: 50px;
                    }
                }
            }
        }

        .globalFooter__bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 80px;
            color: rgba(0, 0, 0, 1);
            font-weight: 700;
            flex-wrap: wrap;
        }

        .globalFooter__links {
            margin-top: 80px;

            .row {
                @media (max-width: 640px) {
                    display: flex;
                    flex-wrap: wrap;
                }
            }
            h3 {
                margin-top: 0;
                font-size: 16px;
                margin-bottom: 20px;
            }
            ul {
                padding: 0;
                margin: 0;
                list-style: none;

                li a {
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.5);
                }
            }
            .socialMediaLinks {
                display: flex;
                align-items: center;
                li {
                    padding: 0 10px;

                    &:first-child {
                        padding-left: 0;
                    }
                    a {
                        color: #000;
                    }
                }
            }
        }
    }
    @media (max-width: 376px) {
        .section__card .panel-heading .panel-title {
            font-size: 18px;
        }
    }

    @media (min-width: 640px) and (max-width: 800px) {
        .hero + .section {
            padding-top: 100px;
        }
    }
    @media (max-width: 1024px - 1) {
        .pageBg--top {
            display: none;
        }
        .hero {
            margin-top: 0;
            .hero__content {
                flex-direction: column;
                margin-left: -50px;
                margin-right: -50px;
            }
            .hero__img {
                max-height: 300px;
                overflow: hidden;
                order: 1;
                padding: 0;
                width: 100%;
                img {
                    border-radius: 0;
                }
            }
            .hero__img_col,
            .hero__text_col {
                width: 100% !important;
            }

            .hero__text_col {
                order: 2;
                padding-top: 200px;
            }
            .hero__img_col {
                order: 1;
            }
            .hero__text {
                background: linear-gradient(0deg, #ffe9b0 0%, #ffc7f3 100%);
                padding: 30px;
                border-radius: 10px;
                flex: 0 0 100%;
                position: relative;
                margin-top: -50px;
                margin-right: 50px;
                margin-left: 50px;
                order: 2;
                h1 {
                    margin-top: 0;
                    font-size: 25px;
                    text-align: center;
                }
            }
        }
    }
    @media (max-width: 1024px) {
        .section__list div:last-child .section__list_item {
            margin-bottom: 0;
        }
    }

    @media (max-width: $screen-sm-max) {
        .globalHeader {
            padding: 30px 5px;
        }
        .section__requirements {
            .section__requirements_img {
                position: relative;
                top: 0;
                left: 0;
                transform: none;

                img {
                    width: auto;
                    height: auto;
                }
            }
        }
        .globalFooter {
            .globalFooter__links {
                h3 {
                    margin-top: 30px;
                }
            }
        }
    }

    @media (max-width: $screen-sm-min) {
        #headerMenu {
            span {
                display: none;
            }

            margin-left: 0;
        }
        .globalFooter {
            .globalFooter__countries {
                ul {
                    li {
                        flex: 0 0 30%;
                        text-align: center;
                        margin-bottom: 15px;
                    }
                }
            }
            .globalFooter__compare {
                li {
                    margin-bottom: 10px;
                }
            }
        }
        .globalHeader {
            .globalHeader__menuContent {
                .globalHeader__menuContent_title {
                    font-size: 14px;
                    height: 0;
                }
            }
        }
        .section__list_item {
            border-right: none;
            /* border-bottom: 1px solid rgba(0, 0, 0, 0.12); */
            padding-bottom: 20px;
            /* padding-top: 20px; */
            background-color: #fff;
            margin-bottom: 40px;
            border-radius: 10px;
            &::after {
                display: none;
            }
            .rounded.rounded--purple {
                margin-top: -20px;
            }
        }
        .section__card {
            margin-bottom: 60px;
        }
        #coverageSection .row div:last-child .section__card {
            margin-bottom: 0;
        }
        .globalFooter__bottom {
            justify-content: center;
        }
    }

    @media (min-width: 767px) and (max-width: 1024px) {
        .globalFooter__links .col-xs-6 {
            width: 20%;
        }
        .globalFooter__countries ul li {
            flex: 0 0 10% !important;
            text-align: center;
        }
    }
    @media (max-width: 414px) {
        .globalFooter__compare h2 {
            margin-top: 0;
        }
        .globalFooter__bottom_app {
            display: flex;

            svg {
                width: 130px;
                margin-left: 10px;
                margin-right: 10px;
            }
        }
    }
    @media (max-width: 811px) {
        #otherProductsSection .row div:last-child .section__card {
            margin-bottom: 0;
        }
        .globalFooter .globalFooter__links {
            margin-top: 20px;
        }
        .globalFooter__bottom {
            margin-top: 30px !important;
        }
    }

    @media (max-width: 568px) {
        .globalFooter .globalFooter__bottom_copyrights {
            margin-bottom: 10px;
        }
        .globalFooter .globalFooter__bottom {
            justify-content: center;
        }
        .globalFooter__compare ul {
            justify-content: center !important;
            text-align: center;
        }
        .globalFooter__compare ul li {
            flex: 0 0 70%;

            @media (max-width: 500px) {
                flex: 0 0 50%;
            }
        }
    }
    @media (max-width: $screen-phone) {
        #headerMenu {
            margin-right: 0;
            margin-left: 0;
        }

        .section__requirements {
            .section__requirements_img {
                position: absolute;
                top: -100px;
                left: 0;
                right: 0;
                margin: auto;
                width: 350px;

                &::before {
                    top: auto;
                    left: auto;
                    bottom: -30px;
                    right: -110px;
                    width: 80%;
                    height: 80%;
                }
                img {
                    width: 100%;
                    height: 100%;
                    margin-top: 50px;
                }
            }
        }
    }
}

/**
    RTL
 */

html[dir="rtl"] {
    .carWarranty {
        .section--faq {
            .col-md-push-3 {
                @media (min-width: 992px) {
                    left: 0;
                    right: 25%;
                }
            }
        }
        .pageBg.pageBg--top {
            right: -290px;
            left: auto;
            transform: rotate(-45deg);
            border-bottom-left-radius: 500px;
            border-top-left-radius: 500px;
        }
        @media (min-width: 992px) {
            .col-md-push-4 {
                right: 33.33333333%;
                left: auto;
            }
        }
        .hero {
            .hero__img img {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                border-top-right-radius: 30px;
                border-bottom-right-radius: 30px;
            }
            .hero__text p {
                font-size: 22px;
            }
        }
        .section__list_item {
            border-right: none;
            &::after {
                left: auto;
                right: 0;
            }
        }
        .section__card {
            .panel-heading {
                .panel-title {
                    a i {
                        left: 0;
                        right: auto;
                    }
                }
            }
        }
        .section__requirements {
            .section__requirements_img {
                right: 30px;
                left: auto;

                &:before {
                    right: -250px;
                    left: auto;
                }
            }
        }
    }
}
