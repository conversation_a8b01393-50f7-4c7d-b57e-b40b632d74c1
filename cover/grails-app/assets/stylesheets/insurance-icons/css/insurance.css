@font-face {
  font-family: 'insurance';
  src: url('../font/insurance.eot?57067243');
  src: url('../font/insurance.eot?57067243#iefix') format('embedded-opentype'),
       url('../font/insurance.woff2?57067243') format('woff2'),
       url('../font/insurance.woff?57067243') format('woff'),
       url('../font/insurance.ttf?57067243') format('truetype'),
       url('../font/insurance.svg?57067243#insurance') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'insurance';
    src: url('../font/insurance.svg?57067243#insurance') format('svg');
  }
}
*/
[class^="insurance-icon-"]:before, [class*=" insurance-icon-"]:before {
  font-family: "insurance";
  font-style: normal;
  font-weight: normal;
  speak: never;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.insurance-icon-help-circled:before { content: '\e800'; } /* '' */
.insurance-icon-sort-up:before { content: '\e801'; } /* '' */
.insurance-icon-sort-down:before { content: '\e802'; } /* '' */
.insurance-icon-circle-empty:before { content: '\e803'; } /* '' */
.insurance-icon-dot-circled:before { content: '\e804'; } /* '' */
.insurance-icon-down-open:before { content: '\e805'; } /* '' */
.insurance-icon-ok-circled:before { content: '\e806'; } /* '' */
.insurance-icon-check:before { content: '\e807'; } /* '' */
.insurance-icon-check-empty:before { content: '\e808'; } /* '' */
.insurance-icon-cancel-circled:before { content: '\e809'; } /* '' */
.insurance-icon-cog:before { content: '\e80a'; } /* '' */
.insurance-icon-airplane-flight-around-the-planet:before { content: '\e80b'; } /* '' */
.insurance-icon-airplane-travelling-around-earth:before { content: '\e80c'; } /* '' */
.insurance-icon-analytics:before { content: '\e80d'; } /* '' */
.insurance-icon-backpack:before { content: '\e80e'; } /* '' */
.insurance-icon-big-license:before { content: '\e80f'; } /* '' */
.insurance-icon-big-shopping-trolley:before { content: '\e810'; } /* '' */
.insurance-icon-briefcase-with-stickers:before { content: '\e811'; } /* '' */
.insurance-icon-credit-card:before { content: '\e812'; } /* '' */
.insurance-icon-family:before { content: '\e813'; } /* '' */
.insurance-icon-farm:before { content: '\e814'; } /* '' */
.insurance-icon-global-heart-beat:before { content: '\e815'; } /* '' */
.insurance-icon-medical-result:before { content: '\e816'; } /* '' */
.insurance-icon-percentage:before { content: '\e817'; } /* '' */
.insurance-icon-plane:before { content: '\e818'; } /* '' */
.insurance-icon-round-done-button:before { content: '\e819'; } /* '' */
.insurance-icon-tick-inside-circle:before { content: '\e81a'; } /* '' */
.insurance-icon-cross-out-mark:before { content: '\e81b'; } /* '' */
.insurance-icon-verification-mark:before { content: '\e81c'; } /* '' */
.insurance-icon-screen-guaranteed:before { content: '\e81d'; } /* '' */
.insurance-icon-allianz-logo:before { content: '\e81e'; } /* '' */
.insurance-icon-car:before { content: '\e81f'; } /* '' */
.insurance-icon-cardiogram:before { content: '\e820'; } /* '' */
.insurance-icon-homepage:before { content: '\e821'; } /* '' */
.insurance-icon-medical-kit:before { content: '\e822'; } /* '' */
.insurance-icon-plane-1:before { content: '\e823'; } /* '' */
.insurance-icon-speech-bubble:before { content: '\e824'; } /* '' */
.insurance-icon-left-open:before { content: '\e825'; } /* '' */
.insurance-icon-right-open:before { content: '\e826'; } /* '' */
.insurance-icon-up-open:before { content: '\e827'; } /* '' */
.insurance-icon-radar-detection:before { content: '\e828'; } /* '' */
.insurance-icon-plane-landing:before { content: '\e829'; } /* '' */
.insurance-icon-plane-liftoff:before { content: '\e82a'; } /* '' */
.insurance-icon-schengen:before { content: '\e82b'; } /* '' */
.insurance-icon-world:before { content: '\e82c'; } /* '' */
.insurance-icon-star:before { content: '\e82d'; } /* '' */
.insurance-icon-star-empty:before { content: '\e82e'; } /* '' */
.insurance-icon-star-half:before { content: '\e82f'; } /* '' */
.insurance-icon-download:before { content: '\e830'; } /* '' */
.insurance-icon-smile-thumbsup:before { content: '\e831'; } /* '' */
.insurance-icon-smile-wink:before { content: '\e832'; } /* '' */
.insurance-icon-chat:before { content: '\e833'; } /* '' */
.insurance-icon-female-support:before { content: '\e834'; } /* '' */
.insurance-icon-credit-card-filled:before { content: '\e835'; } /* '' */
.insurance-icon-percentage-filled:before { content: '\e836'; } /* '' */
.insurance-icon-support-headset:before { content: '\e837'; } /* '' */
.insurance-icon-support:before { content: '\e838'; } /* '' */
.insurance-icon-phone:before { content: '\e839'; } /* '' */
.insurance-icon-premium-badge:before { content: '\e841'; } /* '' */
.insurance-icon-twitter:before { content: '\f099'; } /* '' */
.insurance-icon-facebook:before { content: '\f09a'; } /* '' */
.insurance-icon-filter:before { content: '\f0b0'; } /* '' */
.insurance-icon-icon_expert-support:before { content: '\f0de'; } /* '' */
.insurance-icon-mail-alt:before { content: '\f0e0'; } /* '' */
.insurance-icon-linkedin:before { content: '\f0e1'; } /* '' */
.insurance-icon-angle-left:before { content: '\f104'; } /* '' */
.insurance-icon-angle-right:before { content: '\f105'; } /* '' */
.insurance-icon-angle-up:before { content: '\f106'; } /* '' */
.insurance-icon-angle-down:before { content: '\f107'; } /* '' */
.insurance-icon-star-half-alt:before { content: '\f123'; } /* '' */
.insurance-icon-instagram:before { content: '\f16d'; } /* '' */
.insurance-icon-file-pdf:before { content: '\f1c1'; } /* '' */
.insurance-icon-whatsapp:before { content: '\f232'; } /* '' */
