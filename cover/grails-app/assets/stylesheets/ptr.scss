/*
* This is a manifest file that'll be compiled into application.css, which will include all the files
* listed below.
*
* Any CSS file within this directory can be referenced here using a relative path.
*
* You're free to add application-wide styles to this file and they'll appear at the top of the
* compiled file, but it's generally better to create a new file per style scope.
*
*= require insurance-icons/css/insurance-embedded.css
*= require select2/select2.min.css
*= require unslider/css/unslider.css
*= require jquery-slick-carousel/slick.css
*= require jquery-slick-carousel/slick-theme.css
*= require dropzone/dropzone.css
*= require jquery-ui.css

*= require_self
*/


@import "/customBootstrap";

//modules -- This folder should contain only variable files that do not output any CSS.
@import "modules/ptr/colors";
@import "modules/ptr/font-families";
@import "modules/ptr/mixins";
@import "modules/ptr/paths";

@import "/assets/stylesheets/commonInsurance";

[data-brandCode='NB'] {
    /* Here will be all style for NB */


}
