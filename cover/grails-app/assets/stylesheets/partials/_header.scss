$mobile-app-link-height: 3rem;

header {
    background-color: $clr-default;
    z-index: 100;
    position: relative;
    top: 0;
    width: 100%;

    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);

    -webkit-backface-visibility: hidden;

    @media only screen and (max-width: $screen-sm-max) {
        height: 56px;
        overflow-y: hidden;
        //margin-top: $mobile-app-link-height;
    }

    @media only screen and (min-width: $screen-md-min) {
        height: 70px;
    }

    h1 {
        font-size: 0;
        margin: 0;
    }

    .main-menu {
        position: relative;
        &.isMySyara{
            display: flex;
            align-items: center;
            gap: 30px;
            justify-content: space-between;
            margin-top: 10px;
            .mySyara-logo{
                max-width: 100px;
                margin-inline-start: auto;
            }
            ul{
                li{
                    a {
                        display: inline-block;
                    }

                }
            }
            .desktop-nav{
                position: relative;
                top: 0;
            }
            .menu-bar{
                margin-top: 0;
                .navbar-toggle{
                    position: relative;
                    top: 0;
                    transform: none;
                }
            }
            @media (max-width: 992px){
                .menu-bar {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                    gap: 10px;
                }
                .mySyara-logo{
                    height: 20px;
                }
            }
        }
        .menu-bar {
            position: relative;
            margin-top: 10px;

            .logo {
                display: inline-block;
                text-decoration: none;
                color: $clr-primary;
                font-family: Inter-Loom;
                font-size:  26px;
                img {
                    @media only screen and (max-width: $screen-sm-max) {
                        height: 20px;
                    }
                    @media only screen and (min-width: $screen-md-min) {
                        height: 30px;
                        &.nb{
                            height: 45px;
                        }
                    }
                }
                .logo-caption{
                    text-align: center;
                    margin-top: 2px;
                    line-height: 0.7rem;
                    @media only screen and (max-width: $screen-sm-max) {
                        max-width: 88px;
                        font-size: 0.6rem;

                    }
                    @media only screen and (min-width: $screen-md-min) {
                        font-size: 0.7rem;
                        max-width:136px;
                    }
                }
            }
            .navbar-toggle {
                position: absolute;
                top: 50%;
                @include bidi-right(0);
                -webkit-transform: translateY(-50%);
                -moz-transform: translateY(-50%);
                -ms-transform: translateY(-50%);
                -o-transform: translateY(-50%);
                transform: translateY(-50%);

                @media only screen and (min-width: $screen-md-min) {
                    display: none;
                }
            }
        }

        nav {
            @media only screen and (max-width: $screen-sm-max) {
                height: 0;
                will-change: height;
                transition: height .5s;

                &.open {
                    height: 179px;
                }
            }

            @media only screen and (min-width: $screen-md-min) {
                display: inline-block;
                position: absolute;
                @include bidi-right(0);
                //padding: 5px;
                top: 10px;
            }

            h2 {
                font-size: 0;
                padding: 0;
                margin: 0;
            }

            ul {
                list-style: none;
                padding: 0;
                margin: 0;
                border-top: solid 1px $clr-distinct;

                @media only screen and (min-width: $screen-md-min) {
                    border: none;
                }

                li {
                    @media only screen and (min-width: $screen-md-min) {
                        display: inline-block;

                        &:not(:first-child) {
                            @include bidi-margin-left(11px);
                        }
                    }

                    a {
                        display: block;
                        text-align: center;
                        text-transform: uppercase;
                        font-size: 16px;
                        text-decoration: none;
                        color: darken($clr-distinct, 30%);
                        padding: 5px;

                        @media only screen and (min-width: $screen-md-min) {
                            font-size: 13px;
                            letter-spacing: 0.02em;
                            font-weight: 500;
                        }

                        &:hover {
                            text-decoration: none;
                            color: darken($clr-distinct, 75%);
                        }
                    }


                }
            }
        }
    }
}

.line-solid{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: #36397D;
    z-index: -1;
}
.mobile-nav {
    will-change: transform;
    transition: transform .5s;
    position: absolute;
    width: 100%;
    z-index: 99;
    background-color: $clr-default;
    border-bottom: 1px solid $clr-distinct;
    @include bidi-translate3d(0, -100%, 0);

    &.open {
        @include bidi-translate3d(0, 0, 0);
    }

    h2 {
        font-size: 0;
        padding: 0;
        margin: 0;
    }

    > ul {
        list-style: none;
        padding: 10px;
        margin: 0;

        @media only screen and (min-width: $screen-md-min) {
            border: none;
        }

        li {
            margin: 0;

            a {
                display: block;
                text-align: center;
                text-transform: uppercase;
                font-size: 16px;
                text-decoration: none;
                color: darken($clr-distinct, 30%);
                padding: 5px;

                @media only screen and (min-width: $screen-md-min) {
                    font-size: 14px;
                }

                &:hover {
                    text-decoration: none;
                    color: darken($clr-distinct, 60%);
                }
            }
        }
    }
}

body:not(.funnel) .visible-funnel {
    display: none !important;
}

.funnel {
    nav li:not(.visible-funnel) {
        display: none !important;
    }
}

.header-li {
    position: relative;
    top: -7px;
}
.z-ind-1{z-index:1}

nav .table {
    margin: 0;
}

nav .image-container {
    height: 40px;
    display: inline-block;
    position: relative;

    @media only screen and (min-width: $screen-md-min) {
        top: -6px;
    }

    img {
        max-width: 100%;
        max-height: 100%;
    }
}

nav .image-container__bg-text {
    height: 40px;
    display: inline-block;
    top: 5px;
    position: relative;
}

.mobile-nav > ul li .image-text, header .main-menu nav ul li .image-text {
    @include bidi-padding-left(10px);

    a {
        display: inline-block;
        padding: 0;
        color: $clr-accent;
        font-weight: 900;
    }
}

.youMayKnowUs {
    @include bidi-left(3.8rem);

    position: absolute;
    font-family: GothamBlackRegular;
    font-size: 1rem;
    color: $clr-primary;
    text-decoration: none;
    display: none;
    top: 2.2rem;

    &:hover {
        text-decoration: underline;
    }

    @media screen and (min-width: 992px) {
        display: inline;
    }
}

.youMayKnowUs--mobile {
    color: $clr-accent;
    display: block;
    position: static;
    padding: .5rem 0;
    text-align: center;

    @media screen and (min-width: 992px) {
        display: none;
    }
}

.app-header {
    display: none;
    z-index: 300;
    background-color: $clr-accent;
    color: #fff;
    position: fixed;
    height: 3rem;
    text-align: center;
    width: 100%;
    font-size: 1.1rem;
    line-height: $mobile-app-link-height;
    @media screen and (min-width: $screen-sm-max) {
        display: none;
    }
    a {
        color: #fff;
        font-weight: 600;
        text-decoration: underline;
        &:hover {
            cursor: pointer;
        }
    }
}
