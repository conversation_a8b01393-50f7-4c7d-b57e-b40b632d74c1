
%message {
    @include bidi-right(3.2rem);

    position: absolute;
    font-size: 10px;
    font-family: $font-fam-default;
    white-space: nowrap;
    visibility: hidden;

    bottom: 62px;
    color: white;
    padding: 0;
    width: 50px;
    text-align: center;
    opacity: .7;

    @media screen and (min-width: $screen-lg) {
        @include bidi-right(1rem);

        bottom: 77px;
    }
}

.validate-under {
    &.show-error [class*="message-error"], &.show-success [class="message-success"] {
        @include bidi-left(0);
        bottom: -16px;
        margin: 0;
    }
}

.message-error {
    @extend %message;
    background-color: red;
}

.message-error__bg {
    @extend .message-error;
    font-size: 13px;
    @at-root [dir='ltr'] & {
        width: 190px;
    }
    @at-root [dir='rtl'] & {
        width: 160px;
    }
}

.message-error--kwtLanding {
    @include bidi-right(0)
}


.message-success {
    @extend %message;
    background-color: green;
}

.message-success--kwtLanding {
    @include bidi-right(0)
}

.message-warning {
    color: red;
}



.show-error .message-error, .show-success .message-success {
    visibility: visible;
}

//.show-error {
//    >select, >input, >.input-group, .select2 {
//        border: solid 1px red;
//    }
//}

.show-error .select2-selection,
.show-error input,
.show-error select {
    border: solid 1px red;
}

.show-success .select2-selection,
.show-success input,
.show-success select {
    border: solid 1px green;
}

.declaration-form.newForm.newForm--v2 {
    div {
        .form-group {
            .message-error, .message-success {
                top: 0;
                right: 0;
                bottom: auto;
                width: 140px;
                font-size: 12px;
            }
        }
    }
}
