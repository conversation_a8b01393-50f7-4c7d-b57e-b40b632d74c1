.healthMember {
    position: relative;
    overflow: hidden;
    padding: 0 2rem;
    background-color: rgba(36,95,169,.05);
    //margin: 0 -2rem 2rem;
}

.healthMember__remove {
    @include bidi-right(3rem);

    color: $clr-distinct;
    position: absolute;
    top:22px;
    font-size:1.4rem;

    &:hover {
        color: red;
        cursor: pointer;
    }
}

.healthQuestion {
    overflow: auto;
    padding: 0 15px;
}

.healthQuestion:nth-of-type(2n - 1) {
    //background-color: $clr-distinct-off;
}

.healthQuestion:nth-of-type(2n) {
    background-color: #fafafa;
}

form.newForm .healthMember .form-group:first-of-type {
    margin-top: 1rem;
}
