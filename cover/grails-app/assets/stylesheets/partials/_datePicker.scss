.datePicker {
    $GUTTER_SIZE: 7.5px;
    $DAY_WIDTH: 65px;
    $YEAR_WIDTH: 85px;
    $MONTH_WIDTH_2: calc(100% - (#{$GUTTER_SIZE} * 4) - #{$YEAR_WIDTH});
    $MONTH_WIDTH_3: calc(100% - (#{$GUTTER_SIZE} * 6) - #{$DAY_WIDTH} - #{$YEAR_WIDTH});

    font-size: 0;
    margin: 0 -#{$GUTTER_SIZE};

    select {
        @extend .dropdown;
        margin: 0 $GUTTER_SIZE;
    }

    select:nth-last-child(3) {
        width: $DAY_WIDTH !important;
    }

    select {
        //when there are 3 selects
        &:nth-of-type(2):nth-last-of-type(2) {
            width: $MONTH_WIDTH_3 !important;
        }

        //when there are 2 selects
        &:nth-of-type(1):nth-last-of-type(2) {
            width: $MONTH_WIDTH_2 !important;
        }
    }

    select:nth-last-child(1) {
        width: $YEAR_WIDTH !important;
    }
}
