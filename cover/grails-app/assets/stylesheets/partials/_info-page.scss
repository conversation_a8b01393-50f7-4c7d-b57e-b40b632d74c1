.info-page {
    .header-bar {
        //margin: 0;
    }

    .header-bar_heading {
        font-size: 30px;
        font-weight: 500;
        font-family: $font-fam-default;
        color: #000;
    }

    .container {
        max-width: 700px;
    }

    section {
        margin: 0 0 25px;
    }

    .heading {
        font-size: 16px;
        font-weight: 900;
        margin: 0 0 20px;
    }

    .heading-big {
        font-size: 24px;
        font-weight: 900;
        margin: 0 0 20px;
    }

    p, ul, li {
        font-weight: 500;
        margin: 0 0 15px;
    }

    strong {
        font-size: 16px;
        font-weight: 900;
    }

    [class*="button-"] {
        margin: 0 0 15px;
    }

    .page-heading {
        font-size: 24px;
        font-weight: 900;
        text-align: center;
        line-height: 1.2em;
        margin: 0 0 10px;
    }

    .subheading {
        text-align: center;
    }

    .underline {
        text-decoration: underline;
    }

    .insurance-partner {
        .image-container {
            padding: 10px;
            height: 110px;
        }

        p {
            text-align: justify;
        }
    }

    #accordion {
        .panel-heading {
            .accordion-toggle {
                text-decoration: none;
                display: block;

                &:hover, &:active, &:focus, &:visited {
                    text-decoration: none;
                }
            }
        }
    }
}
