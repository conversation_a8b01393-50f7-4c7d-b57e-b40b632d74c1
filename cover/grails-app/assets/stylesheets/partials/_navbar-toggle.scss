.navbar-toggle {
  border-radius: 5px;
  border: 1px solid $clr-distinct;
  padding: 8px;
  float: none;
  margin: 0;

  &.open {
  background-color: $clr-primary;

    .toggle-bar {
      background-color: $clr-default;
    }

    .toggle-bar:first-child {
      transform: rotate(40deg) translate(4px, 3px);
    }

    .toggle-bar:nth-child(2) {
      transform: rotate(-40deg) translate(1px, 1px);
    }

    .toggle-bar:last-child {
      transform: rotate(-40deg) translate(4px, -3px);
    }
  }

  .toggle-bar {
    display: block;
    background-color: darken($clr-distinct, 50%);
    height: 2px;
    width: 20px;
    margin: 3px;
    -webkit-transition: transform .3s;
    -moz-transition: transform .3s;
    -o-transition: transform .3s;
    transition: transform .3s;
  }
}
