.yc-gradient {
    /////* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#83b9e2+21,e266a4+100 */
    //background: rgb(131,185,226); /* Old browsers */
    //background: -moz-linear-gradient(-45deg, rgba(131,185,226,1) 40%, rgba(226,102,164,1) 80%); /* FF3.6-15 */
    //background: -webkit-linear-gradient(-45deg, rgba(131,185,226,1) 40%,rgba(226,102,164,1) 80%); /* Chrome10-25,Safari5.1-6 */
    //background: linear-gradient(135deg, rgba(131,185,226,1) 40%,rgba(226,102,164,1) 80%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    ////filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#83b9e2', endColorstr='#e266a4',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */

    background-color:#F5F9FF;
    //background-color: #a5d7ff;
    min-height: calc(100vh - 315px);
}
