.reviewsContainer {
    margin: 2rem 0;
    padding: 0 2rem;

    @media screen and (min-width: 992px) {
        @include bidi-left(50%);
        position: absolute;
        height: 132px;
        width: 50%;
    }
}

.reviews {
    color: $clr-almost-black;
    margin: 0 auto 2rem;
    padding: 0 1rem;

    @media screen and (min-width: 992px) {
        //position: absolute;
        //top: 50%;
        //transform: translateY(-50%);
        margin: 0 auto;
        //height: 132px;
    }
}

.reviews__item {
    display: none;
    text-align: left;
    opacity: .2;
    -webkit-transition: opacity .3s;
    -moz-transition: opacity .3s;
    -ms-transition: opacity .3s;
    -o-transition: opacity .3s;
    transition: opacity .3s;
    text-align: center;

    @media screen and (min-width: 992px) {
        padding: 0 .5rem;
        font-size: 1.2rem;
        //height: 132px;
    }
}

.reviews__item.slick-active {
    opacity: 1;
}

[dir="ltr"] .reviews__item:first-of-type:not(.slick-slide) {
    display: block;
}

[dir="rtl"] .reviews__item:last-of-type:not(.slick-slide) {
    display: block;
}

.reviews__item.slick-active {
    display: block;
}

.reviews button:before {
    color: $clr-primary-dull;
}
