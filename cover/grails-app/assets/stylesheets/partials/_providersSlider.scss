//.partnersContainer {
//    padding: 0 2rem;
//}
//
//.partners {
//    margin: 1rem auto;
//    display: block;
//    height: 80px;
//    //padding: 0 1rem;
//}
//
//.partners__partner {
//    display: none;
//    opacity: .2;
//    -webkit-transition: opacity .3s;
//    -moz-transition: opacity .3s;
//    -ms-transition: opacity .3s;
//    -o-transition: opacity .3s;
//    transition: opacity .3s;
//}
//
//.partners__partner.slick-active {
//    opacity: 1;
//}
//
//[dir="ltr"] .partners__partner:first-of-type:not(.slick-slide) {
//    display: block;
//}
//
//[dir="rtl"] .partners__partner:last-of-type:not(.slick-slide) {
//    display: block;
//}
//
//.partners__partner.slick-active {
//    display: block;
//}
//
.providersSlider {
    margin: -1rem auto;

    button {
        line-height: 2.5rem;
        width: 2.5rem;
        height: 2.5rem;
        z-index: 9999;

        &:before {
            color: $clr-primary-dull;
            font-size: 2.5rem;
            opacity: 1;
        }
    }

    .slick-next {
        right: -4rem;
    }

    .slick-prev {
        left: -4rem;
    }
}

.providersSlider {
    height: 20vw;

    @media screen and (min-width: $screen-sm) {
        height: 66px;
    }

    @media screen and (min-width: $screen-md) {
        height: 87px;
    }

    @media screen and (min-width: $screen-lg) {
        height: 72px;
    }
}

.providersSlider__provider {
    display: none;
    //padding: .5rem;

    @media screen and (min-width: $screen-sm) {
        padding: 0 .5rem;
    }
    //

    //height: 72px;
    //text-align: center;
    //width: 15%;
}

.providersSlider__provider__img {
    max-height: 100%;
    max-width: 100%;
    margin: 0 auto;
}

.providersSlider__provider.slick-slide {
    opacity: .2;
    -webkit-transition: opacity .3s;
    -moz-transition: opacity .3s;
    -ms-transition: opacity .3s;
    -o-transition: opacity .3s;
    transition: opacity .3s;
    will-change: opacity;
}

.providersSlider__provider.slick-slide.slick-active {
    opacity: 1;
}

.providersSlider .slick-list {
    height: 100%;
    width: 100%;

}

.providersSlider .slick-track {
    margin: 0 auto;
    //height: 100%;
    //display: flex;
    //@media screen and (max-width: $screen-sm) {
    //    display: block;
    //}
}

header {
    position: relative;
}

.line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#2166b1+33,e84c9b+66 */
    background: $clr-primary; /* Old browsers */
    background: -moz-linear-gradient(left, $clr-primary 33%, $clr-accent 66%); /* FF3.6-15 */
    background: -webkit-linear-gradient(left, $clr-primary 33%,$clr-accent 66%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to right, $clr-primary 33%,$clr-accent 66%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$clr-primary, endColorstr=$clr-accent,GradientType=1 ); /* IE6-9 */
    z-index: -1;
}
