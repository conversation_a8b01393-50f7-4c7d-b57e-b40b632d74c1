
.unbulletted-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.horizontal-list {
    padding: 0;
    margin: 0;

    > li {
        display: inline-block;
    }
}

.company-list {
    @extend .unbulletted-list;
    @extend .horizontal-list;

    overflow: auto;

    > li {
        @include bidi-float(left);
        padding: 5px 0;

        @media only screen and (max-width: $screen-sm-max) {
            height: 50px;
            width: 50%;
            padding: 3px;
        }
        @media only screen and (min-width: $screen-md-min) {
            width: 25%;
        }

        @media only screen and (min-width: $screen-md-min) and (max-width: $screen-md-max) {
            height: 60px;
        }
        @media only screen and (min-width: $screen-lg-min) {
            height: 73px;
        }

        a {
            display: block;
            height: 100%;

            img {
                max-height: 100%;
                max-width: 100%;

                &:hover {
                    -webkit-filter: grayscale(0);
                    filter: grayscale(0);
                    opacity: 1;
                }
            }

        }
    }
}

.icon-list {
    padding: 0;
    margin: 0;
    list-style: none;

    li {
        position: relative;
        @include bidi-padding-left(30px);

        i {
            position: absolute;
            @include bidi-left(0);
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.good-things-list {
    padding: 0;
    margin: 0;
    list-style: none;
    font-size: 4vw;
    font-family: $font-fam-default;
    font-weight: 100;
    color: #444;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.2rem;
    }

    li {
        @include bidi-padding(0,0,0,2rem);
        margin: 0 auto 1rem;
        position: relative;

        &:before {
            @extend .insurance-icon-round-done-button:before;
            color: $clr-primary;
            position: absolute;
            @include bidi-left(0);
            top: .1rem;
        }

        //&:not(:last-child) {
        //    border-bottom: 1px $clr-distinct solid;
        //}
    }
}

.labelled-horizontal-list {
    position: relative;
    background-color: inherit;
    padding: 19px 0 0 0;
    margin: 0 0 37px;

    .section-heading-small {
        display: inline-block;
        margin: 0 auto;
        padding: 0 10px;
        font-family: $font-fam-default;
        letter-spacing: -0.0005em;
        vertical-align: bottom;
        position: relative;
        -webkit-transform: translateY(50%);
        -moz-transform: translateY(50%);
        -ms-transform: translateY(50%);
        -o-transform: translateY(50%);
        transform: translateY(50%);
        background-color: $clr-primary;
    }

    ul {
        @extend .unbulletted-list;
        padding: 30px 0;
        border-top: 1px solid $clr-primary-highlight;
        //border-bottom: 1px solid $clr-primary-highlight;
        text-align: center;
    }
}

ul.money-products-list {
    font-size: 15px;
    font-family: $font-fam-default;
    font-weight: 400;
    text-transform: uppercase;
    padding: 43px 0 35px;
    margin: 0 auto;

    .table-cell {
        width: 25%;
    }

    a, a:hover {
        color: $clr-default;

        @media only screen and (max-width: $screen-sm-max) {
            padding: 0;
        }

        @media only screen and (min-width: $screen-md-min) {
            padding: 20px;
        }
    }
}

.footer-menu-list {
    @extend .horizontal-list;
    font-size: 14px;
    font-weight: 400;
    margin: 0 0 21px;

    li {
        margin: 6px;
    }

    a,
    a:hover,
    a:active,
    a:focus,
    a:visited {
        color: $clr-default;
        padding: 10px;
        text-decoration: none;
    }

    .underlined {
        border-bottom: 2px solid transparentize($clr-default, .7);
        padding: 0 0 2px;

        &:hover {
            border-bottom: 2px solid $clr-default;
        }
    }
}

%card {
    background-image: url("#{$path-png-payment-methods}.png");
    height: 50px;
    width: 78px;
    background-size: cover;
    background-repeat: no-repeat;
}

.footer-cards-list {
    @extend .horizontal-list;
    margin: 0 0 14px;

    li {
        margin: 0 5px;
    }

    .mastercard {
        @extend %card;
        background-position: left -156px center;
    }

    .cash-payment {
        @extend %card;
        background-position: left 0 center;
    }

    .visa {
        @extend %card;
        background-position: left -78px center;
    }

    .fawry {
        @extend %card;
        background-image: url("../assets/png/fawry.png");
        height: 45px;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        position: relative;
        top: -3px;
    }

    .payfort {
        @extend %card;
        background-image: url("../assets/png/payfort.png");
        background-position: center;
        background-size: cover;
        height: 45px;
        position: relative;
        top: -3px;
    }
}
