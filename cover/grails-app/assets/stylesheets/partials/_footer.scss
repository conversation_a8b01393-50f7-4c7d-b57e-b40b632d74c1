.sub-footer {
    background-color: $clr-primary-darker;
    color: $clr-primary-text-light;
    padding: 22px 0 25px 0;
    .container {
        will-change: unset;
    }
}

.main-footer {
    background-color: $clr-primary-darkest;
    color: $clr-primary-text-dark;
    padding: 20px 0 15px 0;
    letter-spacing: 0.014em;
    font-family: $font-fam-default;
    .container {
        will-change: unset;
    }
}

.address {
    font-weight: 400;
    font-size: 12px;
    margin: 0 0 5px 0;
}

.copyright {
    font-weight: 400;
    font-size: 13px;
}
.text-inherit-imp {
    color: inherit !important;
}
.disclaimer {
    font-size: 12px;
    max-width: 500px;
    margin: 0 auto;
}

%footer-images-lg {
    @media (min-width: $screen-sm) {
        height: 3.5rem;
    }

    @media (max-width: $screen-sm - 1) {
        display: block;
        margin: 0 auto;
    }
}

.footer__long-image {
    @extend %footer-images-lg;

    @media (max-width: $screen-sm - 1) {
        width: 10rem;
    }
}

.footer__short-image {
    @extend %footer-images-lg;

    @media (min-width: $screen-sm) {
        left: 0.6rem;
        //position: relative;
    }

    @media (max-width: $screen-sm - 1) {
        padding-top: 0.87rem;
        width: 11.2rem;
    }
}

.footer__link {
    color: inherit;
    text-decoration: underline;
    &:hover,
    &:active,
    &:visited,
    &:focus {
        color: inherit;
    }
}

.insuranceProductLinks {
    color: white;
    margin: 30px;
    margin-bottom: 0;
    h2 {
        font-size: 1.2rem;
        color: white;
    }
    .flexContainer {
        margin-top: 15px;
        flex-wrap: wrap;

        & > div {
            padding: 0 15px;
            @media screen and (max-width: 320px) {
                flex-grow: 1;
            }
            &:last-child {
                @media screen and (max-width: 445px) {
                    flex-grow: 1;
                }
            }
            h4 {
                font-size: 14px;
                a {
                    color: white;
                    text-decoration: none;
                    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
                    padding: 0 0 2px;
                    &:hover {
                        border-color: white;
                    }
                }
            }
        }
    }
}

.cta-whatsapp {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    //background: #25D366;
    font-size: 40px;
    border-radius: 100%;
    padding: 5px;
    a {
        color: #fff;
        img{
            width: 65px;
            height: auto;
        }
    }
}
