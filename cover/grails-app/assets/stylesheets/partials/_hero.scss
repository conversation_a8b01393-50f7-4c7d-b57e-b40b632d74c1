.hero {
    background-color: $clr-primary;
    color: white;
    position: relative;

    .hero__textArea {
        text-align: center;
    }

    h1.hero__heading, h2.hero__heading {
        font-family: $font-fam-attention;
        font-size: 22px;
        margin: 1em auto .65em;
    }

    h2.hero__heading--new {
        font-size: 2.2rem;
    }

    p.hero__subheading {
        font-size: 14px;
        line-height: 1.2em;
        margin: 1em auto;
    }

    .hero__button {
        display: block;
        font-size: 14px;
        margin: 1.5em auto;
        font-weight: bold;

        &:hover {
            background-color: white;
            color: $clr-primary;
        }
    }

    //large smartphones
    @media screen and (min-width: 360px) {
        h1.hero__heading {
            font-size: 26px;
        }

        p.hero__subheading {
            font-size: 16px;
        }

        .hero__button {
            font-size: 16px;
        }
    }

    //phablets
    @media screen and (min-width: 414px) {
        h1.hero__heading {
            font-size: 30px;
        }

        p.hero__subheading {
            font-size: 18px;
        }

        .hero__button {
            font-size: 18px;
        }
    }

    //small desktops
    @media screen and (min-width: 992px) {
        .hero__textArea {
            @include bidi-text-align(left);
        }

        h1.hero__heading {
            @include bidi-padding-right(2em);

            font-size: 28px;
        }

        p.hero__subheading {
            @include bidi-padding-right(9em);

            font-size: 12px;
        }

        .hero__button {
            display: inline-block;
            margin: .5em auto 1em;
            min-width: 14em;
            font-size: 16px;
        }

        .hero__triangle {
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 0 317px 245px;
            border-color: transparent transparent #184b8a transparent;
            -webkit-transform: rotate(360deg);
            -moz-transform: rotate(360deg);
            -ms-transform: rotate(360deg);
            -o-transform: rotate(360deg);
            transform: rotate(360deg);
            position: absolute;
            bottom: 0;
            left: -45.4%;
        }
    }

    //large desktops
    @media screen and (min-width: 1200px) {
        h1.hero__heading {
            font-size: 35px;
        }

        p.hero__subheading {
            font-size: 15px;
        }

        .hero__triangle {
            left: -29.1%;
        }
    }
}

//Home-insurance overrides
.hero.home-insurance .main-heading .heading {
    @media only screen and (max-width: $screen-sm-max) {
        font-size: 26px;
    }

    @media only screen and (min-width: $screen-md-min) and (max-width: $screen-md-max) {
        font-size: 35px;
    }

    @media only screen and (min-width: $screen-lg-min) {
        font-size: 42px;
    }
}
