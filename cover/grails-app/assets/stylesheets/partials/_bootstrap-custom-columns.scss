// Fix to make bootstrap columns float correctly in arabic
html[dir="rtl"] div[class*="col-xs"] {
    float: right;
}

html[dir="rtl"] div[class*="col-sm"] {
    @media only screen and (min-width: $screen-sm-min) {
        float: right;
    }
}

html[dir="rtl"] div[class*="col-md"] {
    @media only screen and (min-width: $screen-md-min) {
        float: right;
    }
}

html[dir="rtl"] div[class*="col-lg"] {
    @media only screen and (min-width: $screen-lg-min) {
        float: right;
    }
}

html[dir="rtl"] div[class*="col-md-offset-3"] {
    @media only screen and (min-width: $screen-md-min) {
        margin: 0 25% 0 0;
    }
}

html[dir="rtl"] div[class*="col-sm-push-6"] {
    @media only screen and (min-width: $screen-sm-min) {
        right: 50%;
    }
}

html[dir="rtl"] div[class*="col-sm-pull-6"] {
    @media only screen and (min-width: $screen-sm-min) {
        right: -50%;
    }
}

//New bootstrap layouts
div[class*="col-"] {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
}

@for $i from 1 through 20 {
    $width: $i * 5;

    .col-xs-#{$width}-percent {
        width: $width#{'%'};
        position: relative;
        min-height: 1px;
        padding-right: 15px;
        padding-left: 15px;
        @include bidi-float(left);
    }

    .col-sm-#{$width}-percent {
        @media only screen and (min-width: $screen-sm-min) {
            width: $width#{'%'};
            position: relative;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px;
            @include bidi-float(left);
        }
    }

    .col-md-#{$width}-percent {
        @media only screen and (min-width: $screen-md-min) {
            width: $width#{'%'};
            position: relative;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px;
            @include bidi-float(left);
        }
    }

    .col-lg-#{$width}-percent {
        @media only screen and (min-width: $screen-lg-min) {
            width: $width#{'%'};
            position: relative;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px;
            @include bidi-float(left);
        }
    }
}

html[dir="rtl"] div[class*="col-md-offset-1"] {
    @media only screen and (min-width: $screen-md-min) {
        margin: 0 8.33% 0 0;
    }
}
