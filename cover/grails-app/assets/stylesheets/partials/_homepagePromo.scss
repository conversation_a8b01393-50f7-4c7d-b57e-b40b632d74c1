.homepagePromo {
    //@include bidi-padding(15px, 88px, 15px, 0);
    //padding: 15px 88px 15px 0;
    padding: 20px;
    position: relative;
    text-align: center;

    @media only screen and (min-width: $screen-sm-min) {
        padding: 60px 20px;
    }

    //&:after {
    //    position: absolute;
    //    font-family: $font-fam-insurance-icons;
    //    top: 50%;
    //    transform: translateY(-50%);
    //    right: 0;
    //    content: '\e813';
    //    line-height: 0;
    //    font-size: 88px;
    //    color: $clr-primary-intense;
    //}
}

.homepagePromo__title {
    font-family: $font-fam-attention;
    font-size: 26px;
    line-height: 1.2em;
    letter-spacing: 0.02em;
    color: $clr-primary-dirty;
}

.homepagePromo__divider {
    display: inline-block;
    height: 3px;
    background-color: $clr-primary-dirty;
    width: 80px;
    margin: 0 0 5px 0;
}

.homepagePromo__tagline {
    font-family: $font-fam-attention;
    color: $clr-accent;
    font-size: 14px;
}
