.carQuote {
    background-color: white;
    max-width: none !important;
    color: #444;
}

.carQuote__header {
    background-color: $clr-primary;
    color: white;
    padding: .3rem 1rem;
    font-size: .9rem;
}

.carQuote__header--thirdParty {
    background-color: #777;
}

.carQuote__body::after {
    display: block;
    content: "";
    clear: both;
}

.carQuote__expandoToggle {
    text-align: center;
    color: $clr-accent;
    font-size: .8rem;
    letter-spacing: 0.04em;
    padding: .5rem 0;
    //background-color: #ccc;

    &:hover {
        cursor: pointer;
        color: lighten($clr-accent, 20%);
    }

    @media screen and (min-width: $screen-sm-min) {
        padding: .25rem 0;
    }
}

.carQuote__expandoToggle--main {
    @media screen and (min-width: $screen-sm-min) {
        border-top: $clr-distinct 1px solid;

        &:hover {
            background-color: $clr-accent;
            color: white;
        }
    }
}

@include keyframes(pulse) {
    0%   {
        background-color: white;
        color: $clr-accent;
    }
    90% {
        background-color: $clr-accent;
        color: white;
    }
    100% {
        background-color: $clr-accent;
        color: white;
    }
}

.carQuote__expandoToggle--pulse {
    will-change: background-color, color;
    @include animation('pulse .6s ease-in-out 1.2s 4 alternate');
}

.carQuote__expandoToggle--right {
    @include bidi-right(.5rem);

    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);

    @media screen and (min-width: $screen-sm-min) {
        display: none;
    }
}

.expandoBox {
    display: none;
    overflow: hidden;
    position: relative;
}

.expandoBox--main {
    @media screen and (min-width: $screen-sm-min) {
        display: block;
        will-change: height;
        -webkit-transition: height .300s;
        -moz-transition: height .300s;
        -ms-transition: height .300s;
        -o-transition: height .300s;
        transition: height .300s;
    }
}

.expandoBox--sides {
    @media screen and (min-width: $screen-sm-min) {
        display:block;
    }
}

.carQuote .insurance-icon-down-open {
    display: inline-block;
}

.carQuote .insurance-icon-down-open.insurance-icon-down-open--rotated {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
}

.carQuote__imgContainer {
    @include bidi-float(left);

    .carQuote__imgContainer_tmImage{
    background: url(https://www.tokiomarine.com.eg/images/tm_egy.JPG);
        background-size: cover;
        background-position: left center;
        overflow: hidden;
        width: 100px;
        height: 100%;
        margin: auto;
    }

    width: 50%;
    height: 6rem;
    position: relative;
    border-bottom: 1px solid $clr-distinct;

    @media screen and (min-width: $screen-sm-min) {
        width: 20%;
        height: 90px;
    }
}


.carQuote__img {
    max-width: calc(100% - 3rem);
    max-height: calc(100% - 1rem);
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.carQuote__buttonContainer {
    @include bidi-float(left);

    width: 50%;
    position: relative;
    height: 6rem;
    border-bottom: 1px solid $clr-distinct;

    @media screen and (min-width: $screen-sm-min) {
        @include bidi-float(right);
        width: 20%;
        border-bottom: 1px solid $clr-distinct;
        height: 90px;
    }
}

.carQuote__button {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: calc(100% - 1rem);
    padding: 0 1em;

    button {
        padding: 0;
        line-height: 2.2em;
        font-size: 1rem;
    }
}

.carQuote__annualPremiumContainer {
    @include bidi-float(left);
    @include bidi-border-right(1px dashed $clr-distinct);

    padding: .5em 0;
    width: 50%;
    text-align: center;
    height: 114px;
    position: relative;
    white-space: nowrap;
    border-bottom: 1px solid $clr-distinct;

    @media screen and (min-width: $screen-sm-min) {
        @include bidi-border-right(none);
        width: 20%;
        height: 90px;
        padding: 1em 0;
    }
}

.carQuote__annualPremiumLabel {
    font-size: 14px;
}

.carQuote__annualPremiumValue {
    color: $clr-primary;
    //font-size: 1.4rem;
    font-size: 1.4rem;
    line-height:1em;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.carQuote__strikeout {
    position: relative;
    color: #ccc;

    &:after {
        content: '';
        width: 100%;
        height: 1px;
        background-color: #aaa;
        position: absolute;
        top: 50%;
        left: 0;
        transform-origin: center;
        transform: translateY(-50%) rotate(-5deg);
    }
}

.carQuote__promoPrice {
    color: $clr-accent;
}

.carQuote__discountMsg {
    font-size: .8rem;
    color: $clr-accent;
}

.carQuote__coverContainer {
    @include bidi-float(left);

    width: 50%;
    text-align: center;
    line-height: 1em;
    height: 114px;
    border-bottom: 1px solid $clr-distinct;
    padding: .5em 0;

    @media screen and (min-width: $screen-sm-min) {
        width: 40%;
        height: 90px;
        padding: 1.5em 0;
    }
}

.carQuote__price {
    -webkit-transition: color 1.5s;
    -moz-transition: color 1.5s;
    -ms-transition: color 1.5s;
    -o-transition: color 1.5s;
    transition: color 1.5s;
}

.carQuote__coverLabel {
    font-size: 14px;
    line-height: 1.4em;

    @media screen and (min-width: $screen-sm-min) {
        font-size: 12px;
    }

    @media screen and (min-width: $screen-md-min) {
        font-size: 14px;
    }

    @media screen and (min-width: $screen-lg-min) {
        font-size: 15px;
    }
}

.carQuote__cover {
    font-size: 12px;
    margin-bottom: .5rem;

    @media screen and (min-width: $screen-sm-min) {
        @include bidi-float(left);

        width: 50%;
        //height: 90px;
    }

    @media screen and (min-width: $screen-md-min) {
        font-size: 14px;
    }
}

.carQuote__seeMoreItem {
    border-bottom: solid 1px $clr-distinct;

    @media screen and (min-width: $screen-sm-min) {
        @include bidi-float(left);
    }
}

.carQuote__seeMoreItem--optionalExtras {
    display: none;

    @media screen and (min-width: $screen-sm-min) {
        display: block;
    }
}

@media screen and (min-width: $screen-sm-min) {
    .carQuote__seeMoreItem--outerBegin {
        @include bidi-border-right(1px $clr-distinct dashed);
    }

    .carQuote__seeMoreItem--innerTop {
        height: 90px;

        &:nth-child(2),
        &:nth-child(3) {
            @include bidi-border-right(1px $clr-distinct dashed);
        }
    }

    .carQuote__seeMoreItem--outerBegin,
    .carQuote__seeMoreItem--outerEnd {
        height: 390px;
        border-bottom: none;

        //@media screen and (min-width: $screen-sm-min) {
        //    pointer-events: none;
        //}
    }

    .carQuote__seeMoreItem--outerEnd {
        @include bidi-border-left(1px $clr-distinct dashed);
    }

    .carQuote__seeMoreItem--outerBegin,
    .carQuote__seeMoreItem--outerEnd,
    .carQuote__seeMoreItem--innerTop {
        width: 20%;
    }

    .carQuote__seeMoreItem--twoBoxes {
        width: 40%;
    }

    .carQuote__seeMoreItem--innerBottom {
        @include bidi-left(20%);

        width: 30%;
        position: absolute;
        top: 90px;
        border-bottom: none;
    }

    .carQuote__seeMoreItem--innerBottom.carQuote__seeMoreItem--optionalExtras {
        @include bidi-left(50%);
    }
}

@media screen and (min-width: $screen-md-min) {
    .carQuote__seeMoreItem--outerBegin,
    .carQuote__seeMoreItem--outerEnd {
        height: 410px;
        border-bottom: none;
    }
}

@media screen and (min-width: $screen-lg-min) {
    .carQuote__seeMoreItem--outerBegin,
    .carQuote__seeMoreItem--outerEnd {
        height: 380px;
    }
}

.carQuote__seeMoreHeader {
    position: relative;
    padding: .75rem 1rem;
    overflow: auto;

    @media screen and (min-width: $screen-sm-min) {
        text-align: center;
        font-size: 12px;
        height: 90px;
    }

    @media screen and (min-width: $screen-md-min) {
        font-size: 14px;
    }

    //@media screen and (min-width: $screen-lg-min) {
    //    padding: .75rem 3rem;
    //}
}

.carQuote__seeMoreHeader--outer {
    //@media screen and (min-width: $screen-sm-min) {
    //    pointer-events: none;
    //}
}

.carQuote__seeMoreHeader--noCenter {
    text-align: initial;

    padding: .75em 1rem;
}

.carQuote__seeMoreHeader--specialFeatures,
.carQuote__seeMoreHeader--optionalExtras {
    height: auto;
}

em {
    font-family: $font-fam-attention;
    font-style: normal;
    font-weight: 900;
}

[data-expando-state="collapsed"] .expando__seeLess {
    display: none;
}

.carQuote__bigIcon {
    @include bidi-right(2rem);

    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    color: $clr-primary-intense;
    font-size: 1.8rem;
    display: inline-block;
    line-height:1;

    @media screen and (min-width: $screen-sm-min) {
        display: block;
        position: static;
        text-align: center;
        -webkit-transform: none;
        -moz-transform: none;
        -ms-transform: none;
        -o-transform: none;
        transform: none;
        top: auto;
        margin: 0 auto 3px;
    }

    @media screen and (min-width: $screen-lg-min) {
        margin: 0 auto;
    }
}



.carQuote__bigIcon .insurance-icon-cancel-circled:before,
.carQuote__bigIcon .insurance-icon-ok-circled:before {
    margin: 0;
}

.carQuote__bigIcon .insurance-icon-cancel-circled:before {
    color: #ccc;
}

.carQuote__bigIcon .insurance-icon-ok-circled:before {
    margin-right: 0.05em !important;
}

.carQuote__fullyComprehensiveItems {
    display: table;
    width: 100%;

    @media screen and (min-width: $screen-sm-min) {
        display: block;
        margin: 1rem auto;
    }
}

.carQuote__optionalExtras {
    display: table;
    table-layout: fixed;
    width: 100%;
}

.carQuote__fullyComprehensiveItemsRow {
    display: table-row;

    @media screen and (min-width: $screen-sm-min) {
        display: block;
    }
}

.carQuote__optionalExtrasRow {
    display: table-row;
}

.carQuote__fullyComprehensiveItemsCell {
    display: table-cell;
    width: 50%;
    padding: .5rem 1rem;

    @media screen and (min-width: $screen-sm-min) {
        display: block;
        width: 100%;
        padding: 0 1rem;
    }
}

.carQuote__optionalExtrasCell {
    display: table-cell;
    width: 50%;
    padding: .5rem 1rem;
    vertical-align: middle;
}

[dir="rtl"] .carQuote__fullyComprehensiveItem {
    @media screen and (min-width: $screen-md-min) {
        font-size: 13px;
    }
}

.carQuote__fullyComprehensiveItem {
    font-size: 13px;
    display: block;
    line-height: 1em;

    @media screen and (min-width: $screen-sm-min) {
        line-height: 1em;
        font-size: 12px;
        margin: 8px auto;
    }

    @media screen and (min-width: $screen-md-min) {
        font-size: 14px;
    }
}

.carQuote__optionalExtrasItem {
    font-size: 13px;
    display: block;
    line-height: 1em;

    @media screen and (min-width: $screen-sm-min) {
        line-height: 1em;
        font-size: 12px;
    }

    @media screen and (min-width: $screen-md-min) {
        font-size: 14px;
    }
}

.carQuote__fullyComprehensiveItem--exclude {
    text-decoration: line-through;
    color: #bbb
}

.carQuote__addon {
    @include bidi-right(1.55rem);

    position: absolute;
    top: 50%;
    font-size: .7rem !important;
    transform: translateY(-50%);
    text-align: center;
    margin: 0;
    pointer-events: auto;
    cursor: pointer;

    @media screen and (min-width: $screen-sm-min) {
        display: block;
        position: static;
        text-align: center;
        -webkit-transform: none;
        -moz-transform: none;
        -ms-transform: none;
        -o-transform: none;
        transform: none;
        top: auto;
        margin: 0 auto 3px;
    }

    @media screen and (min-width: $screen-lg-min) {
        margin: 0 auto;
    }

    .insurance-label {
        display: inline !important;
    }
}

.carQuote__addon--optionalExtra {
    @include bidi-text-align(right);
}

.carQuote__addonPrice {
    @media screen and (min-width: $screen-sm-min) {
        display: inline-block;
        line-height: 27px;
    }
}

.expando__seeLess {
    display: none;
}

.expandoBox--main[data-expando-state="open"] {
    @media screen and (min-width: $screen-sm-min) {
        height: 390px;
    }

    @media screen and (min-width: $screen-md-min) {
        height: 410px;
    }

    @media screen and (min-width: $screen-lg-min) {
        height: 380px;
    }
}

.expandoBox--main[data-expando-state="collapsed"] {
    @media screen and (min-width: $screen-sm-min) {
        height: 89px;
    }
}

.v3SrpHead{
    font-size: 1.5rem;
    @include bidi-text-align(left);
    color: $clr-primary;
    padding: 5px 10px;

    .v3SrpHead__inner{
        display: flex;
        color: #000;
        @media ( max-width : $screen-xs){
            flex-wrap: wrap;

        }
        h3{
            @include bidi-margin(0, 38px, 0, 0);
            font-size: 32px;
            text-transform: capitalize;
            @media ( max-width : $screen-xs){
                flex: 0 0 100%;
                margin-right: 0;
                flex-wrap: wrap;

            }
        }
        p{
            font-size: 24px;
            font-weight: 400;
            font-family: "proxima-nova", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            @media ( max-width : $screen-xs){
                font-size: 16px;
                margin-top: 10px;

            }
        }
    }
}

.srpHeading {
    font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
    font-size: 1.4rem;
    margin: 28px 0 13px 0;
    color: $clr-primary;
    text-align: center;

    @media screen and (min-width: $screen-sm-min) {
        font-size: 1.8rem;
    }

    @media screen and (min-width: $screen-lg-min) {
        font-size: 2.2rem;
    }

    &.srpHeading--v2 {
        font-size: 4vw;
        margin: 1rem auto 0;

        @media screen and (min-width: $screen-sm) {
            font-size: 3vw;
        }

        @media screen and (min-width: $screen-lg) {
            font-size: 2rem;
        }
    }
}

.carQuote .select.dropdown,
.carQuote .datePicker select,
.carQuote .select2-container .select2-selection--single,
.carQuote .textbox,
.carQuote .datebox,
.carQuote input.textbox-with-badge {
    padding: 0 10px 0 0;
}

.car-quote-srp .select2-results__option {
    padding: 7px 10px;
    line-height: 1.2em;
}

.carQuote__termsLink {
    padding: 0 1rem;
    display: block;
    margin: 1rem auto 1.5rem;
    font-size: .9rem;
    text-align: center;

    @media screen and (min-width: $screen-sm-min) {
        margin: 2rem auto 1.5rem;
    }
}

.carQuote__addon .insurance-icon-verification-mark:before,
.carQuote__addon .insurance-icon-cross-out-mark:before,
.carQuote__addon .insurance-label:before {
    margin: 0 !important;
    font-size: 1.2rem;
}

.carQuote__addon .insurance-icon-verification-mark:before {
    color: $clr-primary-intense;
}

.carQuote__addon .insurance-icon-cross-out-mark:before {
    color: $clr-distinct;
}
.cheapestQuotes {
    margin-bottom: 20px;
}
.cheapestQuotes_item {
    //background: #2773b6;
    border-radius: 20px;
    border: solid 1px #36336c;
    padding: 32px 64px;
    box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
    a{
        text-decoration: none;
        color: blue;
    }
    .cheapestQuotes_item__title{
         //font-family: "GothamBlackRegular";
        font-size: 21px;
        line-height: 27px;
        margin-bottom: 32px;
        margin-top: 0;
        text-align: center;
        color: #000;
        @media ( max-width : $screen-md){
            font-size: 22px;
        }
    }
    // .cheapestQuotes_item__title.small-text{
    //     min-height: 54px;
    //     @media ( max-width : $screen-md){
    //         min-height: unset;
    //     }
    // }

    .cheapestQuotes_item__details{
        padding: 0;
        margin: 0;
        list-style: none;
        color: #000;
        li{
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 8px;
            position: relative;
            &:before{
                content: "";
                height: 1px;
                width: 100%;
                position: absolute;
                right: 0;
                bottom: 5px;
                border-bottom: 1px dashed #C8C8C8;
            }
            span{
                background-color: #fff;
                position: relative;
                padding: 0 2px;
                display: inline-block;
            }
            span:last-child{
                font-weight: 700;
            }
            .cheapestQuotes_item__details--price{
                font-family: "GothamBlackRegular";
                font-size: 24px;
                color: #F19135;
            }
        }
    }

    .cheapestQuotes_item--buy{
        display: block;
        background: #E2156B;
        border-radius: 20px;
        font-size: 24px;
        font-weight: bold;
        padding: 5px 0px;
        color: #fff;
        margin-top: 24px;
        text-align: center;
        border: none;
        width: 100%;
        @media ( max-width : $screen-xs){
            padding: 5px 40px;
        }
    }

    .viewOption{
        display: block;
        background: transparent;
        font-size: 14px;
        text-align: center;
        //color: #fff;
        margin-top: 20px;
        margin-right: 0;
        border: none;
        font-weight: 300;
        text-decoration: underline;
        width: 100%;
        padding: 0;
        text-transform: capitalize;
    }

    @media ( max-width : $screen-md){
        margin-bottom: 30px;
    }
}
.cheapestQuotes_item__noQuotes{
    // .cheapestQuotes_item{
    //     height: 400px;
    // }
}
[dir="rtl"] {
    .cheapestQuotes_item{
        min-height: 375px;
        @media ( max-width : $screen-md){
            min-height: unset;
        }
    }
    .cheapestQuotes_item__title{
        font-family: inherit;
        font-weight: 700;
        font-size: 18px;
    }
    .cheapestQuotes_item__title.small-text{
        font-size: 20px;
        min-height: unset;
    }
    .cheapestQuotes_item__noQuotes{
        .cheapestQuotes_item{
            height: 350px;
        }
    }
    .carQuoteV2 .carQuoteV2__detail.carQuoteV2__detail--discount{
        border-radius: 0 20px 20px 0 !important;
    }
}

/**
    Safari Hack
    https://browserstrangeness.bitbucket.io/css_hacks.html#safari
 */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) and (display:flow-root) {
        [dir="rtl"] {
            .cheapestQuotes_item__title, .cheapestQuotes_item__title.small-text {
                font-size: 18px;
            }

            .cheapestQuotes_item .cheapestQuotes_item__details li span{
                font-size: 14px;
            }
        }
    }
}
//avoid empty quotes on failed
.mixitup-container-failed .mix {
    display: block !important;
}
