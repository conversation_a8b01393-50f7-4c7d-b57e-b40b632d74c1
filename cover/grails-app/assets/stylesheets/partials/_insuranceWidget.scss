.insuranceWidget {
    //All Devices
    padding: 10px 20px 0 20px;
    background-color: white;

    //Desktop and bigger
    @media only screen and (min-width: $screen-md-min) {
        width: 520px;
        -webkit-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
        -moz-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
        box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.3);
        position: absolute;
        left: 50%;
        will-change: transform, opacity;
        -webkit-transform: translate(-50%, -190px);
        -moz-transform: translate(-50%, -190px);
        -ms-transform: translate(-50%, -190px);
        -o-transform: translate(-50%, -190px);
        transform: translate(-50%, -190px);
        z-index: 50;
        border: solid 1px $clr-distinct;
        opacity: 0;

        &.quickInsureWidget--ready {
            -webkit-animation: fadeInFromBottom .5s ease-out .2s 1 forwards;
            -o-animation: fadeInFromBottom .5s ease-out .2s 1 forwards;
            animation: fadeInFromBottom .5s ease-out .2s 1 forwards;
        }
    }
}

@keyframes fadeInFromBottom {
    100% {
        opacity: 1;
        -webkit-transform: translate(-50%, -255px);
        -moz-transform: translate(-50%, -255px);
        -ms-transform: translate(-50%, -255px);
        -o-transform: translate(-50%, -255px);
        transform: translate(-50%, -255px);
    }
}
