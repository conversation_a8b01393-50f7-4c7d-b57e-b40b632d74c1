.competition-wrapper{
    background-color: #fff;
    display: block;
    .banner{
        display: block;
        height: 500px;
        transform: skewY(-10deg);
        -webkit-transform-origin: 0;
        transform-origin: 0;
        background: linear-gradient(70deg,$clr-primary 50%,#fff 50%);
        padding: 40px 40px 0 40px;
        position: relative;
        margin-bottom: 40px;
        .banner-text{
            transform: skewY(10deg);
            color: #fff;
            margin-top: 145px;
            box-sizing: border-box;
            padding: 0 40px 0 0;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            p{
                font-size: 30px;
            }
            h2{
                font-size: 50px;
            }
        }
        .banner-img{
            transform: skewY(10deg);
            margin-top: 145px;
        }
    }
    .cash-compitition-wrapper{
        background-color: $clr-primary;
        background-image: url("https://assets.yallacompare.com/insurance/landing-page-images/Insurance-Home-Page-bg-en.png");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: bottom right;
        display: block;
        margin-bottom: 30px;
        .text-wrapper{
            width: 50%;
            color: #fff;
            padding: 30px 0;
            h1,h2{
                margin: 0;
                font-size: 40px;
                line-height: 55px;
                font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                font-weight: normal;
            }
            p{
                color: $clr-default;
                font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                font-size: 35px;
                margin-top: 30px;
                line-height: 50px;
                border-radius: 3px;
            }
        }
    }
    .row{
        display: flex;
        margin-bottom: 40px;
    }
    .winning-content{
        h2{
            color: $clr-accent;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            font-size: 36px;
            margin-top: 0;
            line-height: 58px;
        }
        p{
            font-size: 16px;
            line-height: 22px;
        }
    }
    .get-quote{
        background-color: #f5f9ff;
        padding: 20px 40px 0 40px;
        box-sizing: border-box;
        margin-left: 40px;
        text-align: center;
        @media only screen and (max-width: $screen-sm-max){
            margin-left: 0;
        }
        h3{
            font-family: "proxima-nova", "Tahoma", sans-serif;
            font-size: 24px;
            margin-top: 0;
            margin-bottom: 20px;
        }
        a{
            background-color: $clr-accent;
            color: #fff;
            padding: 10px 50px;
        }
        p{
            margin-top: 20px;
            span{
                a{
                    background-color: unset;
                    color: #000;
                    text-decoration: none;
                    padding: 0;
                }
            }
        }
        span{
            letter-spacing: 1px;
            font-weight: bold;
            a{
                background-color: unset;
                color: #000;
                text-decoration: none;
                padding: 0;
            }
        }
    }
    .how-works{
        h2{
            color: $clr-primary;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            font-size: 36px;
            margin-top: 0;
            line-height: 58px;
        }
        p{
            line-height: 24px;
        }
    }
    .row-logos{
        display: flex;
        align-items: flex-start;
    }
    .how-works-logos{
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100px;
        img{
            float: right;
            display: inline-block;
            vertical-align: middle;
            max-height: 100%;
            max-width: 100%;
            width: auto;
            height: auto;
            margin: 0 20px;
            @media only screen and (max-width: $screen-sm-max){
                position: unset;
                float: none;
            }
            &:last-child{
                width: 100px;
            }
            &:first-child{
                height: 50px;
            }
        }
    }
    .winners{
        h2{
            color: $clr-accent;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            font-size: 36px;
            margin-top: 0;
            line-height: 58px;
        }
        p{
            //margin-bottom: 40px;
        }
        .row{
            .winners-wrapper{
                height: 417px;
            }
            img{
                object-fit: cover !important;
                object-position: 0 0;
                height: 100%;
                width: 100%;
            }
            .img-description{
                position: absolute;
                display: flex;
                bottom: 0;
                height: auto;
                width: calc(100% - 30px);
                padding: 20px;
                background: rgba(33, 102, 177, 0.5);
                color: #fff;
                h3{
                    font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                    font-size: 20px;
                    margin: 0 0 10px 0;
                    color: #fff;
                }
                p{
                    font-size: 12px;
                    font-weight: bold;
                }
                .year{
                    //font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                    font-size: 12px;
                    border-radius: 50%;
                    width: 80px;
                    height: 80px;
                    background-color: $clr-accent;
                    color: #fff;
                    text-align: center;
                    display: table;
                    span{
                        display: table-cell;
                        vertical-align: middle;
                    }
                }
            }
            .img-description-cash{
                width: 100% !important;
                h3{
                    margin: 30px 0 !important;
                }
            }
        }
        .slideshow-container {
            max-width: 1000px;
            position: relative;
            margin: auto;
            .mySlides {
                display: none;
                .text {
                    font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                    color: #fff;
                    font-size: 20px;
                    padding: 10px 35px;
                    position: absolute;
                    bottom: 65px;
                    width: 100%;
                    z-index: 1;
                }
                .fit-contain {
                    height: 417px;
                    display: flex;
                    align-items: center;
                }
                &.augustSlides{
                        img {
                            object-fit: cover !important;
                            object-position: top;
                            height: 100%;
                            width: 100%;
                        }
                }
            }
            .prev, .next {
                cursor: pointer;
                position: absolute;
                top: 50%;
                width: auto;
                padding: 16px;
                margin-top: -22px;
                color: white;
                font-weight: bold;
                font-size: 18px;
                transition: 0.6s ease;
                border-radius: 0 3px 3px 0;
                user-select: none;
                background-color: $clr-accent;
                opacity: 0.6;
            }
            .next {
                right: 0;
                border-radius: 3px 0 0 3px;
            }
            .prev:hover, .next:hover {
                opacity: 1;
            }

        }

    }
}
@media only screen and (max-width: $screen-sm-max){
    .competition-wrapper{
        .banner{
            transform: skewY(0deg);
            background: transparent;
            padding: 0;
            height: auto;
            overflow: hidden;
            .banner-text{
                transform: skewY(0deg);
                padding: 25px 40px;
                text-align: center;
                background-color: $clr-primary;
                margin-top: 0px;
                p{
                    font-size: 30px;
                    line-height: 45px;
                }
                h2{
                    font-size: 40px;
                    line-height: 60px;
                }
            }
            .banner-img{
                transform: skewY(0deg);
                margin: 25px 40px;
                img{
                    width: 100%;
                }
            }
        }
        .wrapper{
            margin-top: -50px;
        }
        .row{
            display: block;
        }
        .winning-content{
            margin: 0 ;
            padding: 30px 20px 20px 20px;
            //border-top: solid 1px #ddd;
            h2{
                font-size: 30px;
            }
        }
        .row-logos{
            align-items: unset;
        }
        .how-works{
            padding-left: 40px;
            padding-right: 40px;
            h2{
                font-size: 30px;
            }
        }
        .how-works-logos{
            display: block;
            justify-content: unset;
            text-align: center;
            background-color: #f1f1f1;
            padding: 40px;
            margin-top: 40px;
            img{
                position: unset;
                float: none;
                &:last-child{
                    width: 80px;
                    margin-left: 20px;
                }
                &:first-child{
                    height: 30px;
                    right: 0;
                }
            }
        }
        .winners{
            padding-left: 25px;
            padding-right: 25px;
            h2{
                font-size: 30px;
            }
            p{
                margin-bottom: 20px;
            }
            .row{
                margin-bottom: 0;
                img {
                    object-fit: cover !important;
                    object-position: top;
                }
                .winners-wrapper{
                    height: unset;
                }
                .col-md-6{
                    margin-bottom: 40px;
                }
                .img-description{
                    display: block;
                    text-align: center;
                    padding: 10px;
                    height: 90px;
                    h3{
                        font-size: 16px;
                    }
                    p{
                        font-size: 14px;
                    }
                    .year{
                        font-family: "proxima-nova", "Tahoma", sans-serif;
                        border-radius: 0;
                        padding:0;
                        left: 0;
                        bottom:0;
                        position: absolute;
                        width: 100%;
                        height: 20px;
                    }
                }
                .img-description-cash{
                    h3{
                        height: 17px;
                        margin: 0 0 10px 0!important;
                    }
                }
            }
            .slideshow-container {
                .mySlides {
                    .text {
                        font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                        font-size: 16px;
                        text-align: center;
                        width: 100%;
                        bottom: 50px;
                    }
                    &.augustSlides{
                        .fit-contain{
                            img {
                                object-fit: cover !important;
                                object-position: center !important;
                            }
                        }
                    }
                }
                .prev, .next {
                    cursor: pointer;
                    position: absolute;
                    top: 50%;
                    width: auto;
                    padding: 16px;
                    margin-top: -22px;
                    color: white;
                    font-weight: bold;
                    font-size: 18px;
                    transition: 0.6s ease;
                    border-radius: 0 3px 3px 0;
                    user-select: none;
                    background-color: $clr-accent;
                    opacity: 0.6;
                }
                .next {
                    right: 0;
                    border-radius: 3px 0 0 3px;
                }
                .prev:hover, .next:hover {
                    opacity: 1;
                }

            }
        }
    }
}
@media only screen and (max-width: $screen-xs){
    .competition-wrapper{
        .row{
            margin-bottom: 20px;
        }
        .text-wrapper{
            h3{
                margin-top: 10px;
            }
        }
        .banner{
            .banner-text{
                padding: 20px;
                p{
                    font-size: 20px;
                    line-height: 30px;
                }
                h2{
                    font-size: 30px;
                    line-height: 45px;
                }
            }
            .banner-img{
                margin: 20px;
            }
        }
        .cash-compitition-wrapper{
            position: relative;
            height: 350px;
            margin-bottom: 30px;
            .text-wrapper{
                width: 100%;
                padding: 10px 0;
                margin-top: 0;
                h1,h2{
                    margin: 0;
                    font-size: 20px;
                    line-height: 30px;
                }
                h3{
                    font-size: 16px;
                    line-height: 20px;
                }
                p{
                    font-size: 18px;
                    //padding: 10px;
                    margin-top: 10px;
                    line-height: 24px;
                    border-radius: 3px;
                }
            }
        }
        [dir=rt] .landingPageHero__button{
            margin-left: 10px;
            margin-right: 0;
        }
        .winning-content{
            margin: 0;
            h2{
                font-size: 20px;
                line-height: 30px;
            }
            p{
                font-size: 14px;
            }
        }
        .get-quote{
            padding: 20px;
            h3{
                font-size: 18px;
            }
        }
        .winners{
            padding-left: 5px;
            padding-right: 5px;
            h2{
                font-size: 20px;
                line-height: 30px;
            }
        }
        .how-works{
            padding: 20px;
            h2{
                font-size: 20px;
                line-height: 30px;
            }
            p{
                font-size: 14px;
            }
            iframe{
                width: 100% !important;
            }
        }
        .how-works-logos{
            padding: 20px;
            margin-top: 0;
        }
        .winners{
            .row{
                .img-description{
                    padding: 10px 0;
                    p{
                        font-size: 12px;
                    }
                }
                .col-md-6{
                    margin-bottom: 20px;
                }
            }
        }
    }
}

[dir="rtl"]{
    .competition-wrapper{
        .banner{
            background: linear-gradient(-70deg,$clr-primary 50%,#fff 50%);
            top: 140px;
            margin-bottom: 80px;
            .banner-text{
                padding: 0 40px;
            }
            .banner-img{
                margin-top: -115px;
                text-align: left;
            }
        }
        .get-quote{
            margin-left: 0;
            margin-right:40px;
        }
        .winners{
            .row{
                .year{
                    display: table;
                }
                .img-description-cash{
                    h3{
                        //margin: 10px 0 !important;
                    }
                }
            }
            .slideshow-container {
                .mySlides{
                    .text{
                        font-size: 16px;
                    }
                }
                .next {
                    left: 0!important;
                    right:unset;
                }
            }
        }
    }
    @media only screen and (max-width: $screen-sm-max){
        .competition-wrapper {
            .banner {
                background: none;
                top: 0;
                margin-bottom: 40px;
                .banner-text{
                    padding: 40px;
                    margin-bottom: 40px;
                }
                .banner-img{
                    margin-top:0;
                }
            }
            .get-quote{
                margin: 0;
            }
        }
    }
}
[dir=rtl] .competition-wrapper{
    .cash-compitition-wrapper{
        background-image: url("https://assets.yallacompare.com/insurance/landing-page-images/Insurance-Home-Page-bg-ar.png") !important;
        background-position: bottom left !important;
        .text-wrapper{
            .landingPageHero__button{
                margin-left: 30px !important;
                margin-right: 0 !important;
            }
        }
    }
}


