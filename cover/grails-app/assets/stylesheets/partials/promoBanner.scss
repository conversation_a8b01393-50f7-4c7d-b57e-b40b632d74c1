.promoBanner {
    text-align: center;
    font-size: 1.2rem;
    background-color: #E2156B;
    color: white;
    line-height: 1;
    padding: 0.5rem 1rem;

    @media screen and (min-width: 768px) {
        font-size: 1.4rem;
    }
}
.promo-icon-container {
    display: flex;
    justify-content: space-between;
    @media only screen and (max-width: 680px) {
        flex-wrap: wrap;
    }
}
.promo-icon {
    @media only screen and (max-width: 680px) {
        max-width: 50% !important;
        flex: 0 0 50%;
        margin-bottom: 10px !important;
    }
}
.promo-icon,
.longer-promo-icon {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    height: 70px;

    @media screen and (max-width: 768px) {
        height: 50px;
        margin: 10px 0;
    }

    &.promo-icon__150 {
        background-size: 150px;

        @media (max-width: $screen-sm) {
            background-size: 75px;
        }
    }
}

.longer-promo-icon {
    max-width: 33.33%;
}

.checkout-promo {
    color: $clr-accent;
    text-transform: uppercase;
    font-size: 20px;
    font-weight: 900;
    line-height: 1em;
    margin-bottom: 5px;
}

.checkout-promo-text {
    color: $clr-accent;
    text-transform: uppercase;
    font-size: 15px;
    font-weight: 900;
    line-height: 1em;
    margin-top: 10px;
    margin-bottom: 10px;
}

.promo-text {
    background-color: #E2156B;
    color: white;
    text-decoration: none;
    &:hover {
        color: white;
        text-decoration: underline;
    }
}

.specialPromotion {
    max-width: 100%;
    margin: 1rem auto;
    width: 700px;
    display: block;

    @media screen and (min-width: $screen-sm) {
        margin: 2rem auto 1rem;
    }
}
.voucher-logo-img{
    margin: 0 20px;
    img{
        width: 200px;
    }
    @media screen and (max-width: 768px) {
        margin: 0 5px;
        img{
            width: 100px;
        }
    }
}
