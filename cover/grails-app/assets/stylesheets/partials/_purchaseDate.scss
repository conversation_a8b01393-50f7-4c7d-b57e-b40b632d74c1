.purchaseDate {
    @media only screen and (min-width: $screen-sm-min) {
        .purchaseDate__date {
            display: inline-block;
            width: 260px;
        }

        .purchaseDate__or {
            display: inline-block;
            padding: 0 10px;
        }

        .purchaseDate__checkbox {
            display: inline-block;
            width: 166px;
            position: relative;
            top: -2px;

            #brandNew.insurance-checkbox[checked="checked"] + .insurance-label {
                border: 1px solid $clr-accent !important;
                background-color: $clr-accent !important;
            }

            label {
                margin-bottom: 0;
            }
        }
    }
}
