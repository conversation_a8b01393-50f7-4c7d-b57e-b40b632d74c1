.portrait-notification {
    position: absolute;
    background-color: $clr-accent;
    color: $clr-default;
    width: 97%;
    margin: 5px auto;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    text-align: center;
    font-size: 13px;
    border-radius: 10px;
    opacity: 0;
    z-index:1000;

    &.fixed {
        position: fixed;
        top: 0;
    }
}
