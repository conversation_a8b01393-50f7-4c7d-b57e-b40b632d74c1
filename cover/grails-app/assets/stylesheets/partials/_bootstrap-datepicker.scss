.datepicker table {
    border-radius: 0;
    border-collapse: separate;

    thead tr:nth-child(-n+2) {
        background-color: $clr-primary;
        color: $clr-distinct-very-light;

        th {
            border-radius: 0;
            font-size: 16px;

            &:hover {
                background-color: $clr-primary-highlight;
                color: $clr-default;
            }
        }
    }

    tbody {
        tr td.day, tr td span {
            font-size: 16px;
            font-weight: 700;
            border-radius: 0;
            color: $clr-primary;
            min-width: 42px;

            &:hover {
                background-color: $clr-primary-highlight;
                color: $clr-default;
            }

            &.active, &.focused {
                background-color: $clr-default;
                color: $clr-accent;
                border: 1px solid $clr-distinct;
            }

            &.old, &.new {
                font-size: 11px;
                font-weight: 500;
            }

            &.disabled {
                color: $clr-distinct;
                background-color: $clr-default;
            }
        }
    }

    tfoot .today {
        font-size: 16px;
        font-weight: 700;
        border-radius: 0;
        color: $clr-primary;
        padding: 2px 10px;

        &:hover {
            background-color: $clr-primary-highlight;
            color: $clr-default;
        }
    }
}
