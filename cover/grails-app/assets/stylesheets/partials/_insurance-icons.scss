%insurance-icon {
    font-family: "insurance";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: .2em;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    margin-left: .2em;
}

.insurance-icon-help-circled:before { @extend %insurance-icon; content: '\e800'; } /* '' */
.insurance-icon-sort-up:before { @extend %insurance-icon; content: '\e801'; } /* '' */
.insurance-icon-sort-down:before { @extend %insurance-icon; content: '\e802'; } /* '' */
.insurance-icon-circle-empty:before { @extend %insurance-icon; content: '\e803'; } /* '' */
.insurance-icon-dot-circled:before { @extend %insurance-icon; content: '\e804'; } /* '' */
.insurance-icon-down-open:before { @extend %insurance-icon; content: '\e805'; } /* '' */
.insurance-icon-ok-circled:before { @extend %insurance-icon; content: '\e806'; } /* '' */
.insurance-icon-check:before { @extend %insurance-icon; content: '\e807'; } /* '' */
.insurance-icon-check-empty:before { @extend %insurance-icon; content: '\e808'; } /* '' */
.insurance-icon-cancel-circled:before { @extend %insurance-icon; content: '\e809'; } /* '' */
.insurance-icon-cog:before { @extend %insurance-icon; content: '\e80a'; } /* '' */
.insurance-icon-airplane-flight-around-the-planet:before { @extend %insurance-icon; content: '\e80b'; } /* '' */
.insurance-icon-airplane-travelling-around-earth:before { @extend %insurance-icon; content: '\e80c'; } /* '' */
.insurance-icon-analytics:before { @extend %insurance-icon; content: '\e80d'; } /* '' */
.insurance-icon-backpack:before { @extend %insurance-icon; content: '\e80e'; } /* '' */
.insurance-icon-big-license:before { @extend %insurance-icon; content: '\e80f'; } /* '' */
.insurance-icon-big-shopping-trolley:before { @extend %insurance-icon; content: '\e810'; } /* '' */
.insurance-icon-briefcase-with-stickers:before { @extend %insurance-icon; content: '\e811'; } /* '' */
.insurance-icon-credit-card:before { @extend %insurance-icon; content: '\e812'; } /* '' */
.insurance-icon-family:before { @extend %insurance-icon; content: '\e813'; } /* '' */
.insurance-icon-farm:before { @extend %insurance-icon; content: '\e814'; } /* '' */
.insurance-icon-global-heart-beat:before { @extend %insurance-icon; content: '\e815'; } /* '' */
.insurance-icon-medical-result:before { @extend %insurance-icon; content: '\e816'; } /* '' */
.insurance-icon-percentage:before { @extend %insurance-icon; content: '\e817'; } /* '' */
.insurance-icon-plane:before { @extend %insurance-icon; content: '\e818'; } /* '' */
.insurance-icon-round-done-button:before { @extend %insurance-icon; content: '\e819'; } /* '' */
.insurance-icon-tick-inside-circle:before { @extend %insurance-icon; content: '\e81a'; } /* '' */
.insurance-icon-cross-out-mark:before { @extend %insurance-icon; content: '\e81b'; } /* '' */
.insurance-icon-verification-mark:before { @extend %insurance-icon; content: '\e81c'; } /* '' */
.insurance-icon-screen-guaranteed:before { @extend %insurance-icon; content: '\e81d'; } /* '' */
.insurance-icon-allianz-logo:before { @extend %insurance-icon; content: '\e81e'; } /* '' */
.insurance-icon-radar-detection:before { @extend %insurance-icon; content: '\e828'; } /* '' */
.insurance-icon-whatsapp:before { @extend %insurance-icon; content: '\f232'; } /* '' */

.insurance-icon-ok:before {
    @include bidi-margin-left(3px);
}

.insurance-icon-cancel-circled {
    @include bidi-margin(0, -2px, 0, -1px);
    position: relative;
}

.insurance-icon-flight-around1:before {
    width: 1.2em
}

.insurance-icon-plane-front:before {
    width: 2.2em
}

.insurance-icon-briefcase:before {
    width: 1.2em
}

.insurance-icon-flight-around2:before {
    width: 1.2em
}

.insurance-icon-backpack:before {
    width: 1.2em
}

.insurance-icon-radar-detection {
    padding-top: 0.05em;
}
