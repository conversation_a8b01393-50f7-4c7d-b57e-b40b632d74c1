//hide from visual but still accessible (e.g. screen readers)
.offscreen
{
  position: absolute;
  clip: rect(1px 1px 1px 1px); /* for Internet Explorer */
  clip: rect(1px, 1px, 1px, 1px);
  padding: 0;
  border: 0;
  height: 1px;
  width: 1px;
  overflow: hidden;
}
//Sets children to fill up parent at equal widths - DONT NEED RIGHT NOW
//.equal-widths {
//  @for $cols from 1 through 12 {
//    @for $i from 1 through $cols {
//      &>:nth-child(#{$i}):nth-last-child(#{$cols - ($i - 1)}) {
//        width: (100%/$cols);
//      }
//    }
//  }
//}

.grab-attention {
    //color: $clr-accent !important;
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -ms-transition: none !important;
    -o-transition: none !important;
    transition: none !important;
}

.more-quotes {
    text-align: center;
    margin-bottom: 30px;
    cursor: pointer;
}

%grayscale {
    filter: grayscale(100%);
    //filter: url("data:image/svg+xml;utf8,<svg version='1.1' xmlns='http://www.w3.org/2000/svg' height='0'><filter id='greyscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0' /></filter></svg>#greyscale");

    //filter: gray;
    -webkit-filter: grayscale(100%);

}

.grayscale-dark {
    @extend %grayscale;
    opacity: .7;
}

.grayscale-light {
    @extend %grayscale;
    opacity: .4;
}

.inline-block {
    display: inline-block;
}

.container {
    //-webkit-transition: all .5s;
    //-moz-transition: all .5s;
    //-ms-transition: all .5s;
    //-o-transition: all .5s;
    //transition: all .5s;
    //will-change: -webkit-filter, filter;
    //-webkit-filter: none;
    //filter: none;
}

.container.blur {
    //@media only screen and (min-width: $screen-md-min) {
        -webkit-filter: blur(8px);
        filter: blur(8px);
        -webkit-transition: none;
        -moz-transition: none;
        -ms-transition: none;
        -o-transition: none;
        transition: none;
    //}
}

.height-auto {
    height: auto;
}

.block {
    display: block;
}

[dir='rtl'] .flip-ar:before {
    -webkit-transform: scaleX(-1);
    -moz-transform: scaleX(-1);
    -ms-transform: scaleX(-1);
    -o-transform: scaleX(-1);
    transform: scaleX(-1);
}

.cursor-pointer {
    cursor: pointer;
}

.padding-end-30{
    padding-inline-end: 30px;
}
.mt-20{
    margin-top: 20px;
}
.text-transform-none{
    text-transform: none;
}
