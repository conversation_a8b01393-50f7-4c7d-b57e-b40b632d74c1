.modal-dialog {
    .modal-header {
        .close {
            @include bidi-right(0.5rem);

            background-color: transparent;
            border: 0;
            color: $clr-primary-dull;
            font-size: 2.8rem;
            position: absolute;
            top: 0;
            font-weight: bold;
            text-shadow: 0px -1px 0px rgba(0,0,0,0.7);
        }
    }

    .modal-footer {
        .btn-primary {
            border: 0;
            box-shadow: none;
        }
        .btn-alignment{
            display: flex;
            flex-direction: row;
            /* iphone 6/7/8 plus */
            @media only screen and (max-width: 414px) {
                flex-direction: column-reverse;
                .button-accent{
                    margin-bottom: 10px;
                }
            }
        }
    }

    .btn-primary {
        border: 0;
        box-shadow: none;
    }
    .btn-alignment{
        display: flex;
        flex-direction: row;
        padding-bottom: 10px;
        /* iphone 6/7/8 plus */
        @media only screen and (max-width: 414px) {
            flex-direction: column-reverse;
            .button-accent{
                margin-bottom: 10px;
            }
        }
    }
}
