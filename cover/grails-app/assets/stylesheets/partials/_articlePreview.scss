.articlePreview {
    margin: 0 0 35px;
    position: relative;
}

.articlePreview__image {
    //height: 128px;
    height: 0;
    padding: 0 0 32% 0;
    width: 100%;
    overflow: hidden;
    background-color: $clr-distinct-light;
    position: relative;
    margin: 0 0 8px 0;

    img {
        position: absolute;
        height: 100%;
        left: 50%;
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
        transform: translateX(-50%);
    }
}

.articlePreview__title {
    $lineHeight: 1.2em;

    color: $clr-primary-faded;
    font-size: 16px;
    text-align: left;
    line-height: $lineHeight;
    letter-spacing: 0.03em;
    font-weight: 900;

    //tablets and larger
    //@media only screen and (min-width: $screen-sm-min) {
    //    height: #{2 * $lineHeight};
    //    overflow: hidden;
    //}
}

.articlePreview__summary {
    $lineHeight: 1.3em;

    font-size: 14px;
    text-align: justify;
    margin: 0 0 0 0;
    letter-spacing: 0.03em;
    line-height: $lineHeight;

    //tablets and larger
    //@media only screen and (min-width: $screen-sm-min) {
    //    height: #{6 * $lineHeight};
    //    overflow: hidden;
    //}
}

.articlePreview__link {
    color: $clr-accent;
    font-weight: 900;
    padding: 5px 0;
    display: block;
    text-align: center;
}
