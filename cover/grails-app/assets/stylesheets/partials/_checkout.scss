[dir="ltr"] .checkout__summaryAmount {
    float: right;
    margin: 0px 15px;
}
[dir="rtl"] .checkout__summaryAmount {
    float: left;
    margin: 0px 15px;
}

.checkoutVoucher {
    margin-top: 30px;
}

.checkoutVoucher--travel br + div {
    flex-wrap: wrap;
}

.checkoutVoucher--travel .voucher-logo-img {
    flex: 0 0 20%;
}

.checkoutVoucher--travel .voucher-logo-img:last-child img {
    height: 60px !important;
}

body.car-checkout-index .secure-checkout-section .peace-of-mind .table-cell.value {
    text-align: start;
}

//#personalAccident247:checked + .insurance-label, #personalAccident247:checked + .insurance-label:before{
//    color: $clr-distinct-very-dark;
//}
.ramadan_iftar_logos {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}


.featureBalloon__htmlContent {
    margin: 2%;
}

.featureBalloon__tableContent_cell {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    justify-content: space-between;
}

.payment-form--generalInsurance{
    .order-summary{
        .grid-cell--half{
            width: 100%;
            display: block;
        }
    }
}

@media(max-width: 700px){
    .thankyou--generalInsurance{
        .order-summary{
            .grid-cell--half{
                width: 100%;
                display: block;
            }
        }
    }
}

.checkoutVoucher.checkoutVoucher--travel{
    .voucher-logo-img{
        flex: 0 0 40%;
    }
}


.tabby {
    &__paymentDetails {
        background-color: #f3f3f3;
        padding: 15px;
        padding-top: 0;

        h5 {
            font-weight: bold;
            margin: 0;
            margin-bottom: 10px;
        }

        p {
            font-size: 13px;
        }
    }
}

.tabbyContact {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 55px;

    h1 {
        font-size: 20px;
        font-weight: 500;
        margin-bottom: 55px;
    }

    &__list {
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 48px;

        &-item {
            border-radius: 8px;
            background: #fff;
            box-shadow: 0px 0px 12px 0px rgba(165, 165, 165, 0.25);
            padding: 11px 13px;
            display: flex;
            align-items: center;
            gap: 16px;
            width: 390px;

            i {
            }
            span {
                color: #585858;
                font-weight: 500;
                font-size: 14px;
            }
        }
    }

    button {
        border-radius: 7px;
        background: #da1884;
        width: 159px;
        height: 48px;
        color: #fff;
    }
}
