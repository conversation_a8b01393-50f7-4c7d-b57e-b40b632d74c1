.header-bar {
    //background-color: $clr-primary-intense;
    font-family: "GothamBlackRegular", bahi<PERSON>-j<PERSON>, Helvetica, Tahoma, Verdana, Arial, sans-serif;
    color: $clr-default;
    margin: 0;
    position: relative;
    overflow: hidden;

    &.with-pointer {
        padding: 25px 0 40px;
        margin: 0 0 20px 0;

        &:after {
            position: absolute;
            content: '';
            width: 30px;
            bottom: 0;
            left: 50%;
            transform: translate(-50%, 50%) rotate(45deg);
            background-color: $clr-default;
            padding-bottom: 30px;
        }
    }
}

.header-bar--new {
    padding: 0;
}

.header-bar_heading {
    font-family: $font-fam-attention;
    margin: 20px 0 8px 0;
    text-align: center;

    @media only screen and (max-width: $screen-sm-max) {
        font-size: 28px;
    }

    @media only screen and (min-width: $screen-md-min) {
        font-size: 42px;
    }
}

.header-bar_subheading {
    font-size: 17px;
    font-family: $font-fam-default;
    text-align: center;
    font-weight: 400;
    margin: 0 0 10px;
}

.header-banner{
    margin: 20px 0;
    .header-banner-img{
        width: 728px;
        @media only screen and (max-width: $screen-sm-max) {
            display: none;
        }
    }
    .header-banner-img-mobile{
        width: 300px;
        @media only screen and (min-width: $screen-md-min) {
            display: none;
        }
    }
}
//---

.formHeader {
    text-align: center;
    font-family: $font-fam-attention;
    color: $clr-accent;
    margin: 2rem auto;
    font-size: 1.3rem;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.8rem;

        &.formHeader__generalInsurance{
            span{
                display: block;
            }
        }
    }
    @media screen and (max-width: $screen-sm) {
        &.formHeader__generalInsurance{
            span{
                display: block;
            }
        }
    }
}
