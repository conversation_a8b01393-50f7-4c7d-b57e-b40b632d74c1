$checked-color: $clr-accent;
$unchecked-color: $clr-primary;
$checkbox-height: 50px;
$background-color: white;
$font-color: #white;
$duration: .8s;

.crossSell {
    display: block;
    font-size: 1rem;
    margin: 2rem auto;

    .crossSell__checkbox {
        vertical-align: middle;
        height: $checkbox-height;
        width: $checkbox-height;
        background-color: transparent;
        border: $checkbox-height * .1 solid $unchecked-color;
        border-radius: 5px;
        position: relative;
        display: inline-block;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        -moz-transition: border-color ease $duration/2;
        -o-transition: border-color ease $duration/2;
        -webkit-transition: border-color ease $duration/2;
        transition: border-color ease $duration/2;
        cursor: pointer;

        &::before, &::after {
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            position: absolute;
            height: 0;
            width: $checkbox-height * .2;
            background-color: $checked-color;
            display: inline-block;
            -moz-transform-origin: left top;
            -ms-transform-origin: left top;
            -o-transform-origin: left top;
            -webkit-transform-origin: left top;
            transform-origin: left top;
            border-radius: 5px;
            content: ' ';
            -webkit-transition: opacity ease .5s;
            -moz-transition: opacity ease .5s;
            transition: opacity ease .5s;
        }

        &::before {
            top: $checkbox-height * .72;
            left: $checkbox-height * .41;
            box-shadow: 0 0 0 $checkbox-height * .05 $background-color;
            -moz-transform: rotate(-135deg);
            -ms-transform: rotate(-135deg);
            -o-transform: rotate(-135deg);
            -webkit-transform: rotate(-135deg);
            transform: rotate(-135deg);
        }

        &::after {
            top: $checkbox-height * .37;
            left: $checkbox-height * .05;
            -moz-transform: rotate(-45deg);
            -ms-transform: rotate(-45deg);
            -o-transform: rotate(-45deg);
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
        }
    }

    .crossSell__loader {
        @include bidi-translate(-50%, 0);
        height: $checkbox-height;
        width: $checkbox-height;
        margin: 0;
        vertical-align: middle;
    }

    .crossSell__textbox {
        @include bidi-text-align(left);
        @include bidi-margin(0, 0, 0, 1rem);
        width: 190px;
        font-size: inherit;
        font-weight: 900;
        color: $clr-primary;
        display: inline-block;
        vertical-align: middle;
    }

    &.crossSell--unchecked {
        .crossSell__checkbox {
            display: inline-block;
        }
        .crossSell__loader {
            display: none;
        }

        .crossSell__uncheckedMessage {
            display: inline-block;
        }

        .crossSell__checkedMessage {
            display: none;
        }

        .crossSell__errorMessage {
            display: none;
        }
    }

    &.crossSell--loading {
        .crossSell__checkbox {
            display: none;
        }

        .crossSell__loader {
            display: inline-block;
        }

        .crossSell__uncheckedMessage {
            display: inline-block;
        }

        .crossSell__checkedMessage {
            display: none;
        }

        .crossSell__errorMessage {
            display: none;
        }
    }

    &.crossSell--checked {
        .crossSell__checkbox {
            border-color: $checked-color;
            display: inline-block;

            &::after {
                height: $checkbox-height * .5;
                -moz-animation: dothabottomcheck $duration/2 ease 0s forwards;
                -o-animation: dothabottomcheck $duration/2 ease 0s forwards;
                -webkit-animation: dothabottomcheck $duration/2 ease 0s forwards;
                animation: dothabottomcheck $duration/2 ease 0s forwards;
            }

            &::before {
                height: $checkbox-height * 1.2;
                -moz-animation: dothatopcheck $duration ease 0s forwards;
                -o-animation: dothatopcheck $duration ease 0s forwards;
                -webkit-animation: dothatopcheck $duration ease 0s forwards;
                animation: dothatopcheck $duration ease 0s forwards;
            }
        }

        .crossSell__loader {
            display: none;
        }

        .crossSell__uncheckedMessage {
            display: none;
        }

        .crossSell__checkedMessage {
            display: inline-block;
        }

        .crossSell__errorMessage {
            display: none;
        }
    }

    &.crossSell--error {
        .crossSell__checkbox {
            display: inline-block;
        }

        .crossSell__loader {
            display: none;
        }

        .crossSell__uncheckedMessage {
            display: none;
        }

        .crossSell__checkedMessage {
            display: none;
        }

        .crossSell__errorMessage {
            display: inline-block;
        }
    }
}

@-moz-keyframes dothabottomcheck {
    0% {
        height: 0;
    }
    100% {
        height: $checkbox-height/2;
    }
}

@-webkit-keyframes dothabottomcheck {
    0% {
        height: 0;
    }
    100% {
        height: $checkbox-height/2;
    }
}

@keyframes dothabottomcheck {
    0% {
        height: 0;
    }
    100% {
        height: $checkbox-height/2;
    }
}

@keyframes dothatopcheck {
    0% {
        height: 0;
    }
    50% {
        height: 0;
    }
    100% {
        height: $checkbox-height;
    }
}

@-webkit-keyframes dothatopcheck {
    0% {
        height: 0;
    }
    50% {
        height: 0;
    }
    100% {
        height: $checkbox-height;
    }
}

@-moz-keyframes dothatopcheck {
    0% {
        height: 0;
    }
    50% {
        height: 0;
    }
    100% {
        height: $checkbox-height;
    }
}
