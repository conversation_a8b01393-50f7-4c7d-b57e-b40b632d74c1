.quickInsureWidget {
    //All Devices
    font-size: 10px;
    color: $clr-distinct-very-dark;
    height: 315px;
}

.quickInsureWidget__title {
    //All devices
    text-align: center;
    font-family: $font-fam-attention;
    font-size: 1.8em;
    margin: 0 0 10px 0;
    color: $clr-accent;
}

.quickInsureWidget__menu {
    position: relative;

    @include clearfix;
}

.quickInsureWidget__menuItem {
    display: inline-block;
    width: 20%;
    @include bidi-float(left);
    text-align: center;
    color: #999;
    cursor: pointer;
    -webkit-transition: color .3s;
    -moz-transition: color .3s;
    -ms-transition: color .3s;
    -o-transition: color .3s;
    transition: color .3s;
    border-bottom: 2px solid $clr-distinct-slight-lighter;
    padding: 0 0 10px 0;

    &.quickInsureWidget__menuItem--active {
        color: $clr-accent;
        -webkit-transition: none;
        -moz-transition: none;
        -ms-transition: none;
        -o-transition: none;
        transition: none;
    }

    &:hover:not(.quickInsureWidget__menuItem--active) {
        color: $clr-primary-highlight;
    }
}

.quickInsureWidget__menuItem__icon {
    font-size: 3em;
    line-height: 0;
}

.quickInsureWidget__menuItem__text {
    font-size: 1.6em;
}

.quickInsureWidget__form {
    margin: 0 auto;
    display: none;
    max-width: 520px;
}
