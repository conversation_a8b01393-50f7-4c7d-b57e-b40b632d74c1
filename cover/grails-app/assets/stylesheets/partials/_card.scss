.card {
    -webkit-box-shadow: 3px 3px 8px rgba(0,0,0,0.4);
    -moz-box-shadow: 3px 3px 8px rgba(0,0,0,0.4);
    box-shadow: 3px 3px 8px rgba(0,0,0,0.4);
    margin: 2rem auto;
}

.card__title {
    color: $clr-primary;
    font-weight: bold;
    text-align: center;
}

.card__form {
    background-color: white;
    max-width: 700px;
    overflow: auto;
    margin: auto;

    @media (min-width: $screen-md) {
        box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.4);
    }

    & > div {
        margin-left: auto;
        margin-right: auto;

        @media (min-width: $screen-md) {
            width: 70%;
        }
        @media (max-width: $screen-sm) {
            width: 90%;
        }
    }
}

.card__form_input {
    @extend %form-control;
}

.card__message {
    margin: 4rem auto;
    text-align: center;
    font-size: 1.3rem;
}

.gradient_background {
    @media screen and (min-width: $screen-md) {
        padding: 3rem 0;
        min-height: 55vh;
        /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#83b9e2+21,e266a4+100 */
        background: rgb(131,185,226); /* Old browsers */
        background: -moz-linear-gradient(-45deg, rgba(131,185,226,1) 21%, rgba(226,102,164,1) 100%); /* FF3.6-15 */
        background: -webkit-linear-gradient(-45deg, rgba(131,185,226,1) 21%,rgba(226,102,164,1) 100%); /* Chrome10-25,Safari5.1-6 */
        background: linear-gradient(135deg, rgba(131,185,226,1) 21%,rgba(226,102,164,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#83b9e2', endColorstr='#e266a4',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
    }
}
