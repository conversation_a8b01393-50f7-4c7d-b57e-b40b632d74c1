.carQuoteV2 {
    font-size: 2.8vw;
    position: relative;
    color: #000;

    //.etisalatEarn{
    //    display: none;
    //}

    @media screen and (min-width: $screen-sm) {
        font-size: 2.4vw;
    }

    @media screen and (min-width: $screen-lg) {
        width: 1140px;
        font-size: 1rem;
    }
    &.hasDiscount {
        .carQuoteV2__innerMobile .carQuoteV2__header {
            padding: 0px 15px 15px 15px;
        }
    }
    .carQuoteV2__discount {
        background-color: #fdea14;
        color: #000;
        padding: 0.2em 2em;
        font-weight: 900;
        font-style: italic;
        text-align: right;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
        border-top-right-radius: 20px;
    }
    .carQuoteV2__price {
        color: #f19135;
        font-family: "gothamBlackRegular";
        margin-left: auto;
        font-size: 30px;
        text-align: end;
        s {
            color: silver;
            font-size: 15px;
        }
        @media screen and (max-width: $screen-md-min) {
            text-align: center;
        }
    }
    &.hasDiscountImg {
        margin-top: 30px !important;
    }
    .carQuoteV2__discount--img {
        position: absolute;
        top: -25px;
        left: 0;
        right: 0;
        margin: auto;
        width: 100px;
        img {
            width: 112px;
            height: 50px;
            @media (max-width: $screen-md) {
                width: 80px;
                height: 40px;
            }
        }
    }

    form:not(.sg-survey-form) {
        max-width: none;
    }

    .insurance-icon-star,
    .insurance-icon-star-half-alt,
    .insurance-icon-star-empty {
        margin: 0 -0.2em;
        color: orange;
    }

    .insurance-icon-star.desktop,
    .insurance-icon-star-half-alt.desktop,
    .insurance-icon-star-empty.desktop {
        margin: 0 -0.3em;
        color: orange;
    }

    .carQuoteV2__moreDetailsMessage--clickForMore {
        display: inline-block;
    }

    .carQuoteV2__moreDetailsMessage--clickToClose {
        display: none;
    }

    .carQuoteV2__type {
        color: white;
        padding: 0.2em 0.5rem;
        font-weight: 900;

        &.carQuoteV2__type--tpl {
            background-color: #aaa;
        }

        &.carQuoteV2__type--comprehensive {
            background-color: $clr-primary;
        }

        .carQuoteV2__typeName {
            @include bidi-float(left);
            font-family: $font-fam-attention;
        }

        .carQuoteV2__stars {
            @include bidi-float(right);
            @include bidi-padding-right(1em);
        }

        .carQuoteV2__stars--tpl {
            .insurance-icon-star,
            .insurance-icon-star-half-alt,
            .insurance-icon-star-empty {
                color: orange;
            }
        }
    }

    &.card.carQuoteV2--discount {
        margin: 3em auto 1rem;

        @media screen and (min-width: $screen-lg) {
            margin: 1rem auto;
        }
    }
    .carQuoteV2--discount {
        @media screen and (min-width: $screen-sm) {
            margin-top: 30px !important;
        }
    }

    &.carQuoteV2--showMoreDetails {
        .carQuoteV2__moreDetailsMessage--clickForMore {
            display: none;
        }

        .carQuoteV2__moreDetailsMessage--clickToClose {
            display: inline-block;
        }
    }

    &.card {
        margin: 1rem auto;
        border-radius: 20px;
    }
    .v3 {
        @media screen and (min-width: $screen-sm) {
            margin-bottom: 90px;
        }
    }
    &.v3.card:first-child {
        //margin-top: 0;
    }

    &.carQuoteV2--tpl .carQuoteV2__moreDetailsToggle {
        //display: none;
    }

    .carQuoteV2__header {
        display: flex;
        align-items: center;
        padding-left: 15px;
        padding: 15px;
        flex-wrap: wrap;

        &.recommended {
            padding-top: 54px;
        }
    }
    .tags {
        align-items: center;
    }
    .carQuoteV2__provider {
        margin-top: 0;
        max-width: 200px;
        @media screen and (max-width: $screen-md-min) {
            @include bidi-padding-left(5vw);
            width: 150px;
        }

        @media screen and (min-width: $screen-md-min) {
            @include bidi-padding-right(15px);
        }

        img {
            max-width: 100%;
            max-height: 100%;
            display: block;
        }
    }

    .carQuoteV2__product {
        margin-top: 0;
        width: 30%;
        @include bidi-padding-left(1.5em);
        //padding: 0 0 0 1.5em;
        font-weight: 400;
        color: $clr-primary;
        font-weight: 700;
    }
    .carQuoteV2__price_text {
        color: #36336c;
        font-size: 0.9rem;
        font-weight: 700;
    }
    .carQuoteV2__cta {
        width: 30%;
        padding: 0.2rem 0.5rem 0;

        button {
            font-size: 1.5em;
            padding: 0.5em;
            line-height: 1.2;
            margin-top: 7px;
            border-radius: 20px;

            .price {
                font-size: 1.1em;
            }
        }

        &.carQuoteV2__cta--desktop {
            width: auto;
            padding: 0;

            button {
                padding: 0.4em;
            }
        }
        @media screen and (max-width: $screen-md-min) {
            flex: 0 0 60%;
            width: 60%;
            margin-top: 10px;
            margin-left: auto;
            margin-right: auto;
        }
    }

    .carQuoteV2__divider {
        margin: 0.3em auto 0;
        width: 90%;

        &.carQuoteV2__divider--desktop {
            width: calc(100% - 2rem);
            &.customMargin {
                margin: 0.5rem;
                @include bidi-margin-left(1.5rem);
            }
        }
    }

    .carQuoteV2__moreDetails {
        display: none;
    }

    .carQuoteV2__footer {
        .carQuoteV2__moreDetailsToggle {
            text-align: center;
            cursor: pointer;

            .carQuoteV2__moreDetailsMessage {
                color: $clr-primary;
                padding: 0.4em 2em;
                font-weight: 900;
                width: 100%;
            }
        }
    }

    .carQuoteV2__alwaysVisible {
        display: grid;
        grid-template-columns: 25% 25% 25% 25%;
        grid-template-rows: auto 5.5em auto;
        margin: 1em auto 0.5em;
        //max-width: calc(92vw);
        position: relative;

        &.carQuoteV2__alwaysVisible--desktop {
            grid-template-columns: 15% 15% 15% 15% 15% 25%;
            grid-template-rows: none;
            margin: 0;
            padding: 1rem 0;
        }
    }

    .insurance-checkbox:checked + .insurance-label,
    .insurance-checkbox:checked + .insurance-label:before {
        color: $clr-primary;
    }

    .carQuoteV2__addon {
        font-size: inherit;
        margin: 0;

        .insurance-label {
            line-height: 1;
            font-size: inherit;
            transform-origin: center;
            transform: scale(2);

            &::before {
                line-height: 1;
                font-size: inherit;
                position: relative;
            }
        }
    }

    .price--changed {
        position: relative;
        animation: 0.5s ease pulseV2;
        display: inline-block;
    }

    .insurance-icon-verification-mark {
        display: inline-block;
        -webkit-transform: scale(1.3);
        -moz-transform: scale(1.3);
        -ms-transform: scale(1.3);
        -o-transform: scale(1.3);
        transform: scale(1.3);
    }

    .insurance-icon-cross-out-mark {
        display: inline-block;
        color: #ccc;
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -ms-transform: scale(1.1);
        -o-transform: scale(1.1);
        transform: scale(1.1);
    }

    .carQuoteV2__detail {
        justify-self: stretch;
        align-self: center;
        text-align: center;

        &.carQuoteV2__detail--header {
            font-weight: 400;
            line-height: 1;
            padding: 0 0.2rem 0;
            cursor: help;

            &:not(.carQuoteV2__detail--noBorder) {
                border-right: 1px solid #ccc;
            }
        }

        &.carQuoteV2__detail--body {
            color: $clr-primary;
            line-height: 1;
            padding: 1.6em 0 0;
            font-weight: 900;

            &:not(.carQuoteV2__detail--noBorder) {
                border-right: 1px solid #ccc;
            }

            &.carQuoteV2__detail--bodyDesktop {
                align-self: center;
                padding: 0.5rem 1rem;
            }

            &.carQuoteV2__detail--bodyDesktopExtra {
                align-self: center;
                padding: 1.5rem 1rem;
            }
        }

        &.carQuoteV2__detail--make {
            //grid-row: span 2;
            line-height: 1.3;

            img {
                max-width: calc(100% - 0.5rem);
                max-height: 100%;
                margin: 0 auto;
                display: block;
            }

            &.carQuoteV2__detail--makeDesktop {
                grid-row: auto;
                line-height: inherit;

                img {
                    max-width: 90%;
                    max-height: 3rem;
                    margin: 0 auto;
                    display: block;
                }
            }
        }

        &.carQuoteV2__detail--discount {
            position: relative;
            background-color: #fdea14;
            padding: 0.5em 0;
            color: #000;
            box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.4);
            margin-top: -17px;
            border-radius: 20px 0 0 20px;
            &:before {
                content: "";
                background-color: #fdea14;
                height: 100%;
                width: 1rem;
                display: block;
                position: absolute;
                top: 0;
            }

            &:after {
                content: "";
                display: block;
                position: absolute;
                bottom: -0.5rem;
                width: 0;
                height: 0;
                border-bottom: 0.5rem solid transparent;
            }
        }

        &.carQuoteV2__detail--agency {
            padding: 0.4em 0;
        }

        &.carQuoteV2__detail--pdf {
            padding: 1em 0;
        }

        &.carQuoteV2__detail--tpl {
            line-height: 1.3;
        }

        &.carQuoteV2__detail--tplLimit {
            line-height: 1.3;
        }

        &.carQuoteV2__detail--footer {
            padding: 0 0.2rem 0;
            line-height: 1;

            &:not(.carQuoteV2__detail--noBorder) {
                border-right: 1px solid #ccc;
            }
        }

        .offline-quotes-btn-text {
            font-size: 17px;
        }
    }

    .carQuoteV2__detailsPanel:not(:first-of-type) {
        display: none;

        @media screen and (min-width: $screen-lg) {
            display: block;
        }
    }

    .carQuoteV2__detailsPanel {
        @media screen and (min-width: $screen-lg) {
            display: block;
        }
    }

    .carQuoteV2__detailsPanel {
        will-change: transform;
    }

    .carQuoteV2__promo {
        background-color: fade-out($clr-accent, 0.9);
        color: darken($clr-accent, 15%);
        margin: 0 1rem 0.5rem;

        padding: 0.5rem;
        border-radius: 3px;
        font-weight: 900;
        font-size: 1.1rem;
        text-align: center;

        @media (max-width: $screen-sm) {
            font-size: 0.9rem;
        }
    }

    .carQuoteV2__thingsToConsider {
        padding: 0.5rem;
        border-radius: 3px;
        font-weight: 900;
    }

    .carQuoteV2__thingsToConsider--fromYc {
        background-color: fade-out($clr-primary, 0.9);
        color: darken($clr-primary, 15%);
    }

    .carQuoteV2__thingsToConsider--fromInsurer {
        background-color: fade-out($clr-accent, 0.8);
        color: darken($clr-accent, 15%);
    }

    .carQuoteV2__list {
        padding: 0 1.5em;
    }

    .carQuoteV2__termsLink {
        display: block;
        text-align: center;
        color: $clr-primary;
        line-height: 1;
        font-size: 2em;
        text-decoration: underline;
        i {
            display: block;
            color: $clr-primary;
        }
    }

    .carQuoteV2__innerMobile {
        display: block;
        .carQuoteV2__cta {
            flex: 0 0 45%;
            width: 45%;
            button {
                font-size: 14px;
                padding: 0.5em;
            }
        }
        .carQuoteV2__price_text {
            font-size: 12px;
        }
        .carQuoteV2__price {
            font-size: 20px;
            line-height: 1;
        }
        .carQuoteV2__header {
            flex-wrap: nowrap;
            padding: 0 15px;

            &.recommended {
                padding-top: 43px;
            }
        }
        .carQuoteV2__provider {
            padding: 0;
            width: 80px;
        }
        .carQuoteV2__discount {
            width: 100%;
            border-radius: 20px 20px 0 0;
            text-align: center;
            position: relative;
        }
        .carQuoteV2__product {
            font-size: 13px;
        }
        @media screen and (min-width: $screen-lg) {
            display: none;
        }
    }

    .carQuoteV2__innerDesktop {
        display: none;
        position: relative;
        z-index: 999;

        @media screen and (min-width: $screen-lg) {
            display: block;
        }
    }

    .carQuoteV2__swipeForMore {
        height: 100%;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        pointer-events: none;
    }

    .slick-track .carQuoteV2__swipeForMore {
        background-color: rgba(0, 0, 0, 0.5);
        animation: 3s ease 0.75s carQuoteV2__reveal;
        pointer-events: none;
    }

    .slick-track .carQuoteV2__detailsPanel {
        display: block;
    }

    &__recommended {
        position: absolute;
        top: 0;
        width: 220px;
        height: 41px;
        background-color: $clr-primary;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        padding: 9px 13px;

        @include bidi-left(-1px);
        @include bidi-border-radius(20px, 0, 0, 0);

        @media screen and (min-width: $screen-lg) {
            width: 249px;
            height: 47px;
            font-size: 16px;
            padding: 12px 19px;
        }

        &-icon {
            font-size: 18px;
            @include bidi-margin-right(8px);
        }

        &--text {
            color: $clr-default;
            font-weight: 700;

            .highlighted {
                color: #fdea14;
            }
        }
    }
}

[dir="ltr"] .carQuoteV2__detail--discount {
    &:before {
        right: -1rem;
    }

    &:after {
        right: -1rem;
        border-left: 1rem solid rgba(111, 103, 10, 0.4);
    }
}

[dir="rtl"] .carQuoteV2__price {
    margin-left: 0;
    margin-right: auto;
}
[dir="rtl"] .carQuoteV2__detail--discount {
    &:before {
        left: -1rem;
    }

    &:after {
        left: -1rem;
        border-right: 1rem solid #1f4f90;
    }
}

[dir="ltr"] .slick-track .carQuoteV2__detailsPanel {
    animation: 1.5s ease 1.5s teaseMore;
}
[dir="rtl"] .slick-track .carQuoteV2__detailsPanel {
    animation: 1.5s ease 1.5s teaseMore_opposite;
}

@keyframes pulseV2 {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.3);
    }

    100% {
        transform: scale(1);
    }
}

@include keyframes(teaseMore) {
    0% {
        position: relative;
        -webkit-transform: translate3d(0, 0, 0);
        -moz-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        -o-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    50% {
        position: relative;
        -webkit-transform: translate3d(-20vw, 0, 0);
        -moz-transform: translate3d(-20vw, 0, 0);
        -ms-transform: translate3d(-20vw, 0, 0);
        -o-transform: translate3d(-20vw, 0, 0);
        transform: translate3d(-20vw, 0, 0);
    }
    100% {
        position: relative;
        -webkit-transform: translate3d(0, 0, 0);
        -moz-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        -o-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}
@include keyframes(teaseMore_opposite) {
    0% {
        position: relative;
        -webkit-transform: translate3d(0, 0, 0);
        -moz-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        -o-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    50% {
        position: relative;
        -webkit-transform: translate3d(20vw, 0, 0);
        -moz-transform: translate3d(20vw, 0, 0);
        -ms-transform: translate3d(20vw, 0, 0);
        -o-transform: translate3d(20vw, 0, 0);
        transform: translate3d(20vw, 0, 0);
    }
    100% {
        position: relative;
        -webkit-transform: translate3d(0, 0, 0);
        -moz-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        -o-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

@include keyframes(carQuoteV2__reveal) {
    0% {
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        display: none;
    }
}

.bigbluebox {
    margin: 0 0.25rem;
}
.px-0-imp {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.px-0 {
    padding-left: 0;
    padding-right: 0;
}
.px-3 {
    padding-left: 3px;
    padding-right: 3px;
}
.px-2-imp {
    padding-left: 2px !important;
    padding-right: 2px !important;
}
.px-5 {
    padding-left: 5px;
    padding-right: 5px;
}
.agency_swiper {
    .low_opacity {
        opacity: 0.2;
    }
    .low_opacity:hover {
        opacity: 0.7;
        color: $clr-primary;
    }
    > div:not(.low_opacity, .noPopoverAnimation) {
        color: $clr-accent;
        -webkit-transform: scale(1.2, 1.2);
        -moz-transform: scale(1.2, 1.2);
        -ms-transform: scale(1.2, 1.2);
        -o-transform: scale(1.2, 1.2);
        transform: scale(1.2, 1.2);
        -webkit-animation: bounceUpAnimation 0.5s ease-in-out; /* Safari 4.0 - 8.0 */
        animation: bounceUpAnimation 0.5s ease-in-out;
    }
    .agency_option {
        color: $clr-dark-grey-text;
    }
    .noPopoverAnimation img {
        max-width: calc(100% - 1rem) !important;
    }
    .disabled_loadingCursorImp {
        cursor: wait !important;
        pointer-events: none;
    }

    /* Safari 4.0 - 8.0 */
    @-webkit-keyframes bounceUpAnimation {
        0% {
            transform: scale(1, 1);
        }
        75% {
            transform: scale(1.3, 1.3);
        }
        100% {
            transform: scale(1.2, 1.2);
        }
    }

    /* Standard syntax */
    @keyframes bounceUpAnimation {
        0% {
            transform: scale(1, 1);
        }
        75% {
            transform: scale(1.3, 1.3);
        }
        100% {
            transform: scale(1.2, 1.2);
        }
    }
}
.premiumGarageLogo .badge {
    position: absolute;
    left: -50px;
    right: 0;
    display: inline-block;
    color: $clr-accent;
    top: -4px;
    font-size: 2em;
    &.l4t4rUnset {
        right: unset;
        left: 4px;
        top: 4px;
    }
}
.garageIcon.premiumGarageLogo .badge {
    @media screen and (max-width: $screen-lg) {
        left: -22px;
    }
}
.shorterImg.premiumGarageLogo img {
    @media screen and (max-width: $screen-lg) {
        width: calc(100% - 1.6rem);
    }
}
.garageIcon .badge {
    display: none;
}
.garageIcon.premiumGarageLogo .badge {
    display: inline-block;
}

.border-primary-1 {
    border: 1px solid $clr-primary;
}
.border-none-imp {
    border: none !important;
}
.opacity1 {
    opacity: 1 !important;
}

.srptag {
    padding: 4px 12px;
    font-weight: bold;
    font-size: 0.9rem;
    border-radius: 10px;
    background: $clr-primary;
    margin: 3px 5px;
    color: white;
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.6);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.6);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.6);
    &.impTag {
        background: linear-gradient(
            to right,
            $clr-primary 10%,
            $clr-accent 100%
        );
    }
    &.defaultTag {
        background: transparent;
        color: inherit;
    }
    @media screen and (max-width: $screen-md-min) {
        padding: 3px 8px;
        font-size: 0.55rem;
    }
    @media screen and (max-width: 320px) {
        font-size: 0.5rem;
    }
}

#header-message {
    position: fixed;
    top: 0;
    font-size: 1.2rem;
    z-index: 1000;
    width: 100%;
    text-align: center;
    box-shadow: 3px 3px 8px;
    background-color: $clr-primary;
    padding-top: 10px;
    font-weight: 900;

    p {
        color: white;
    }
}

.important-notice {
    @media screen and (max-width: $screen-phone) {
        padding: 1.5rem;
        font-size: 1.2rem;
    }
    color: white;
    background-color: $clr-primary;
    font-size: 1.5rem;
    padding: 1rem 5rem;
    text-align: center;
    font-weight: 600;
}
