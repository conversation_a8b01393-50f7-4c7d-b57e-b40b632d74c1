
.docUpload {

}

.docUpload__heading {
    color: $clr-primary;
    font-family: $font-fam-attention;
    text-align: center;
}

form.dropzone {
    @include clearfix;

    margin: 3rem auto 2rem;
    text-align: center;
    border-style: dashed;
    border-color: $clr-accent;
}

form.dropzone .docLabel {
    @include bidi-left(.5rem);

    font-size: 1rem;
    position: absolute;
    background-color: white;
    padding: 0 0.5rem;
    color: $clr-primary;
    margin: 0;
    -webkit-transform: translateY(-2.1rem);
    -moz-transform: translateY(-2.1rem);
    -ms-transform: translateY(-2.1rem);
    -o-transform: translateY(-2.1rem);
    transform: translateY(-2.1rem);

    @media screen and (min-width: $screen-md) {
        -webkit-transform: translateY(-2.2rem);
        -moz-transform: translateY(-2.2rem);
        -ms-transform: translateY(-2.2rem);
        -o-transform: translateY(-2.2rem);
        transform: translateY(-2.2rem);
        font-size: 1.3rem;
    }
}

form.dropzone p.downloadLinkLabel{
    position: absolute;
    top: -12px;
    background: white;
    padding: 0 8px;
    a.button-accent.icon-button{
        border-radius: 50%;
        height: 33px;
        padding: 6px;
        cursor: pointer;
        &:hover{
            cursor: pointer;
        }
        i{
            cursor: pointer;
        }
    }


}
[dir="ltr"] form.dropzone p.downloadLinkLabel{
    right: 0;
}
[dir="rtl"] form.dropzone p.downloadLinkLabel{
    left: 0;
}

.dropzone .dz-preview .dz-progress .dz-upload {
    background: linear-gradient(to bottom, $clr-accent, $clr-accent-faded);
}

.dropzone .dz-preview .dz-success-mark svg {
    background-color: greenyellow;
    border-radius: 100%;
}

.dropzone .dz-preview .dz-error-mark svg {
    background-color: orangered;
    border-radius: 100%;
}

.dropzone .dz-preview .dz-image>img {
    min-width: 100%;
    min-height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.dropzone .dz-preview.dz-file-preview .dz-image {
    background-image: url("/insurance/assets/pdf.png");

    background-color: $clr-primary;
    background-repeat: no-repeat;
    background-size: 75%;
    background-position: center;
}

.dropzone .dz-preview.dz-image-preview .dz-image {
    border: 1px solid #eee;
}

.dropzone .dz-preview:hover .dz-image img {
    -webkit-transform: translate(-50%, -50%) scale(1.05, 1.05);
    -moz-transform: translate(-50%, -50%) scale(1.05, 1.05);
    -ms-transform: translate(-50%, -50%) scale(1.05, 1.05);
    -o-transform: translate(-50%, -50%) scale(1.05, 1.05);
    transform: translate(-50%, -50%) scale(1.05, 1.05);
    -webkit-filter: blur(8px);
    filter: blur(8px);
}

.yc-message {
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    cursor: pointer;
}

.dropzone .dz-message {
    margin: 0;

}

.yc-loader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    background-color: #fff;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
    background-image: url("/insurance/assets/trash.gif");
    background-size: 40%;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #eee;
}
