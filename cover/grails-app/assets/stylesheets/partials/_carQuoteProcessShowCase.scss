.carQuoteProcessShowCase{
    .customFlexWithSliderWrapper{
        display: flex;
        @media screen and (max-width: $screen-sm-max) {
            flex-direction: column;
        }
    }
    .pointsFlexWrapper{
        display: flex;
        flex-direction: column;
        padding: 10px 15px;
        min-height: 100px;
        @media screen and (max-width: $screen-sm-max) {
            position: relative;
            overflow: hidden;
            display: block;
        }
    }
    .pointsFlexWrapper > div.customBox-primary{
        margin: 10px auto;
        flex-grow: 1;
        width: 100%;
        padding: 16px 10px;
        display: flex;
        -webkit-transition: all 500ms;
        -moz-transition: all 500ms;
        -ms-transition: all 500ms;
        -o-transition: all 500ms;
        transition: all 500ms;
        @media screen and (max-width: $screen-sm-max) {
            height: 0px;
            opacity: 0;
            visibility: hidden;
            padding: 0;
            margin: 0;
        }

        div > h3{
            color: $clr-primary;
        }

        & > div.icon{
            color: $clr-primary;
            margin-right: 3px;
            font-size: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
            i{
                display: flex;

            }
        }

        &.active{
            @media screen and (max-width: $screen-sm-max) {
                height: auto;
                opacity: 1;
                visibility: visible;
                padding: 16px 10px;
                margin: 10px auto;
            }
            background: $clr-primary;
            color: white;
            h3{
                color: white;
            }
            .icon{
                color: white;
            }
        }
    }
    .car-spare-parts-background{
        min-height: 245px;
        background: url("//assets.yallacompare.com/insurance/landing-page-images/yallacompare-car-parts-bottom-min.jpg");
        background-position: bottom;
        background-size: cover;
        opacity: 0.2;
        width: 100%;
    }
    .laptopScreenWrapper{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }
    .macbook-frame-background{
        min-height: 255px;
        width: 364px;
        /*width: 80%;*/
        position: relative;
        background: url("//assets.yallacompare.com/insurance/landing-page-images/frames/macbook-frame.png");
        background-position: center;
        background-size: cover;
        .screenWrapper{
            position: absolute;
            display: flex;
            left: 46px;
            right: 46px;
            bottom: 50px;
            top: 36px;
            img {
                display: none;
            }
            img:nth-child(1) {
                display: inline-block;
            }
        }

    }

    .mblScreenWrapper{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        .mbl-frame-background{
            margin: 0 auto;
            min-height: 502px;
            width: 244px;
            position: relative;
            background: url("//assets.yallacompare.com/insurance/landing-page-images/frames/mobile-frame.png");
            background-position: center;
            background-size: cover;
            .screenWrapper{
                position: absolute;
                top: 58px;
                right: 14px;
                bottom: 60px;
                left: 14px;
                overflow: hidden;
            }
        }

    }


}

.box-shadow-normal{
    -webkit-box-shadow: 0 0 12px 0 rgba(0,0,0,.08);
    -moz-box-shadow: 0 0 12px 0 rgba(0,0,0,.08);
    box-shadow: 0 0 12px 0 rgba(0,0,0,.08);
}
.box-shadow-2{
    -webkit-box-shadow: 0 0 12px 0 rgba(0,0,0,.25);
    -moz-box-shadow: 0 0 12px 0 rgba(0,0,0,.25);
    box-shadow: 0 0 12px 0 rgba(0,0,0,.25);
}
.box-shadow-hard{
    -webkit-box-shadow: 0 1px 3px 0 rgba(0,0,0,0.6);
    -moz-box-shadow: 0 1px 3px 0 rgba(0,0,0,0.6);
    box-shadow: 0 1px 3px 0 rgba(0,0,0,0.6);
}
