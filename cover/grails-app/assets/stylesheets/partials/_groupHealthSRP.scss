$logo-max-height: 5rem;

.groupHealthQuote {

    .groupHealthQuote-price {
        font-size: 1.25rem;
        text-align: center;
        color: $clr-accent;
    }

    .groupHealthQuote-link {
        font-size: 1rem;
        font-weight: 600;

        @media (max-width: $screen-lg - 1) {
            font-size: 0.65rem;
        }
    }

    .groupHealthQuote__details {
        display: grid;
        grid-template-rows: auto 5.5em auto;
        margin: 1em auto .5em;
        //max-width: calc(92vw);
        position: relative;

        &.groupHealthQuote__details--1 {
            grid-template-columns: 33% 33% 33%;
        }

        &.groupHealthQuote__details--2 {
            grid-template-columns: 50% 50%;
        }

        /*grid-template-rows: none;
        margin: 0;
        padding: 1rem 0;*/

        /*&.carQuoteV2__alwaysVisible--desktop {
            grid-template-columns: 15% 15% 15% 15% 15% 25%;
            grid-template-rows: none;
            margin: 0;
            padding: 1rem 0;
        }*/
    }

    .groupHealthQuote__premium {
        @media (max-width: $screen-lg - 1) {
            font-size: 1rem;
            color: $clr-accent;
            font-weight: bold;
            text-align: center
        }
    }
}

.quoteInfo {
    border: 1px solid #bbbbbb;
    padding: 1rem;
    margin-top: 1rem;
    border-radius: 5px;
}

.noQuotesForActivatedFilters {
    font-size: 1.2rem;
}

.quotesHeaderMsg {

    margin-top: 4rem;
    @media (max-width: $screen-lg - 1) {
        margin-top: 1rem;
    }

}
