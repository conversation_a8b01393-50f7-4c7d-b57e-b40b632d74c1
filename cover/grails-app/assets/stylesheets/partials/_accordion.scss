

.accordion-button {
  position: relative;

  &:after {
    position: absolute;
    content: '';
    background: $clr-default $path-png-dropdown-arrow-black no-repeat;
    @include bidi-background-position-with-offset(right, 5px, center);
    background-size: 14px;
    height: 1.3em;
    width: 25px;
    @include bidi-margin(0, 0, 0, 10px);
    -webkit-transform: scaleY(1);
    -moz-transform: scaleY(1);
    -ms-transform: scaleY(1);
    -o-transform: scaleY(1);
    transform: scaleY(1);

    transform-origin: center;
    will-change: transform;
    -webkit-transition: transform .3s;
    -moz-transition: transform .3s;
    -o-transition: transform .3s;
    transition: transform .3s;
  }
}

.mobile-nav.open .accordion.open {
    $item-height: 31px;
    $items: 10;
    $items-default: 5;

    .accordion-button {
        color: $clr-primary-faded;

        &:after {
            -webkit-transform: scaleY(-1);
            -moz-transform: rotate(-1);
            -ms-transform: rotate(-1);
            -o-transform: rotate(-1);
            transform: scaleY(-1);
        }
    }

    .accordion-items-lg {
        @media only screen and (max-width: $screen-sm-max) {
            height: $item-height * $items;
        }
    }

    .accordion-items-default {
        @media only screen and (max-width: $screen-sm-max) {
            height: $item-height * $items-default;
        }
    }
}

.accordion:hover {
    $item-height: 30px;
    $items: 10;
    $items-default: 5;


  .accordion-button {
    color: $clr-primary-faded;

    &:after {
      @media only screen and (min-width: $screen-md-min) {
        -webkit-transform: scaleY(-1);
        -moz-transform: rotate(-1);
        -ms-transform: rotate(-1);
        -o-transform: rotate(-1);
        transform: scaleY(-1);
      }
    }
  }

    .accordion-items-lg {
        @media only screen and (min-width: $screen-md-min) {
            height: $item-height * $items;
        }
    }

    .accordion-items-default {
        @media only screen and (min-width: $screen-md-min) {
            height: $item-height * $items-default;
        }
    }
    .accordion-items-kwt{
        @media only screen and (min-width: $screen-md-min) {
            height: $item-height * 6;
        }
    }
}

.accordion-items {
  height: 0;
  overflow: hidden;
  will-change: height;
  -webkit-transition: height .3s;
  -moz-transition: height .3s;
  -o-transition: height .3s;
  transition: height .3s;
  width: 100%;
  padding: 0;

  @media only screen and (max-width: $screen-sm-max) {
    -webkit-transform: translateY(10px);
    -moz-transform: translateY(10px);
    -ms-transform: translateY(10px);
    -o-transform: translateY(10px);
    transform: translateY(10px);
    background-color: #eee;
  }

  @media only screen and (min-width: $screen-md-min) {
    background-color: $clr-default;

    -webkit-box-shadow: 0 3px 5px 0 rgba(0,0,0,0.28);
    -moz-box-shadow: 0 3px 5px 0 rgba(0,0,0,0.28);
    box-shadow: 0 3px 5px 0 rgba(0,0,0,0.28);
  }

  li {
    @media only screen and (min-width: $screen-md-min) {
      display: block !important;
      margin: 0 !important;
    }
  }
  a {
    font-size: 14px !important;

    @media only screen and (min-width: $screen-md-min) {
      @include bidi-text-align(left);
    }

    &:hover {
      color: $clr-primary-faded !important;
      background-color: lighten($clr-distinct, 10%);
    }
  }
}

.accordion {
  position: relative;
}

.accordion-items {
  position: absolute;
}
