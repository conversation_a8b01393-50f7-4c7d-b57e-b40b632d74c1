.landing-wrapper{
    width: 100%;
    .landing-banner{
        width: 100%;
        padding: 0;
        height: 500px;
        background-image: url(/insurance/assets/axa-banner.png);
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center right;

        h1{
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            font-size: 75px;
            width: 40%;
            color: $clr-primary;
            span{
                color: $clr-accent;
            }
        }
    }
    .section-wrapper{
        margin-bottom: 40px;
        .section-txt{
            .titl{
                font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                margin-top: 0;
                padding-top: 0;
                font-size: 35px;
            }
            .sub-titl{
                margin-top: 20px;
                font-size: 24px;
            }
            .btn-container{
                display: flex;
                justify-content: left;
                a{
                    padding: 15px 60px;
                    margin-top: 20px;
                    background-color: #fff;
                    font-family: "GothamBlackRegular", bahi<PERSON><PERSON>j<PERSON>, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                    font-size: 20px;
                    font-weight: bold;
                    border-radius: 2px;
                    color: $clr-primary;
                }
            }
        }
        .section-img{
            .img-container{
                display: flex;
                justify-content: center;
            }
            .inline-photo {
                opacity: 0;
                transform: translateY(4em) rotateZ(-5deg);
                transition: transform 4s .25s cubic-bezier(0,1,.3,1),
                opacity .3s .25s ease-out;
                width: auto;
                max-height: 250px;
                will-change: transform, opacity;
            }

            .inline-photo.is-visible {
                opacity: 1;
                transform: rotateZ(0deg);
            }
        }
        .simple-life{
            background-color: $clr-primary;
            padding: 40px 0;
            color: #fff;
        }
        .sponsor{
            background-color: #676bd0;
            padding: 40px 0;
            color: #fff;
        }
        .hospital{
            background-color: $clr-primary;
            padding: 40px 0;
            color: #fff;
        }
        .union{
            background-color: #676bd0;
            padding: 40px 0;
            color: #fff;
        }
        .section-tabs{
            margin: 40px 0;
            box-shadow: 0 2px 6px 0 hsla(0, 0%, 0%, 0.2);
            padding-top: 15px;
            .tabs-nav-wrap {padding: 0;}
            .tabs-nav-wrap ul {display: inline-block; list-style: none; width: 100%;}
            .tabs-nav-wrap ul li {
                list-style: none;
                display: inline-block;
            }
            .tabs-nav {
                padding: 0;
                margin: 0;
                background-color: #fff;
                li {
                    list-style: none;
                    display: block;
                    padding: 20px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                    text-align: left;
                    text-decoration: none;
                    border-radius: 2px;
                }
            }
            .tabs-nav .tab-nav-link.current,
            .tabs-nav .tab-nav-link:hover {
                border: solid 1px $clr-accent;
                color: $clr-primary;
            }
            .tab-content {
                padding: 40px 0;
                //background: #f1f1f1;
                display:none;
                border-radius: 2px;
                h3 {
                    font-size: 16px;
                    font-weight: bold;
                    line-height: 24px;
                }
                h5{
                    color: #666;
                }
                ul{
                    padding: 0;
                }
            }
            }
        }
}
@media (max-width: 1024px){
    .landing-wrapper {
        .landing-banner {
            width: 100%;
            padding: 0;
            height: 500px;
            background-image: url('/insurance/assets/axa-banner.png');
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center center;

            h1 {
                font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                font-size: 75px;
                width: 40%;
                color: $clr-primary;

                span {
                    color: $clr-accent;
                }
            }
        }

        .section-wrapper {
            .section-txt {
                width: 100%;
                text-align: center;

                .btn-container {
                    justify-content: center;
                }
            }

            .section-img {
                .img-container {
                    display: block;
                    text-align: center;
                }

                .inline-photo {
                    opacity: 0;
                    transform: translateY(4em) rotateZ(-5deg);
                    transition: transform 4s .25s cubic-bezier(0, 1, .3, 1),
                    opacity .3s .25s ease-out;
                    width: auto;
                    max-width: 100%;
                    max-height: 250px;
                    will-change: transform, opacity;
                }

                .inline-photo.is-visible {
                    opacity: 1;
                    transform: rotateZ(0);
                }
            }

            .simple-life {
                padding: 20px 0;
            }

            .sponsor {
                padding: 20px 0;
                position: relative;
                height: 510px;
                .container{
                    display: flex;
                    flex-direction: column-reverse;
                    height: 100%;
                }

                .section-img {
                    position: unset;
                }

                .section-txt {
                    position: absolute;
                    top: 20px;
                    left: 0;
                    right: 0;
                }
            }

            .hospital {
                padding: 20px 0;
            }
        }
    }
}
@media (max-width: 768px){
    .landing-wrapper{
        .landing-banner{
            height: 400px;
            h1{
                font-size: 50px;
            }
        }
    }
}
@media (max-width: 568px){
    .landing-wrapper {
        .landing-banner {
            height: 300px;
            background-size: contain;
            background-position: bottom center;
            h1 {
                font-size: 35px;
                width: 100%;
                text-align: center;
            }
        }
        .section-wrapper{
            .section-txt {
                .titl {
                    font-size: 20px;
                }
                .sub-titl {
                    font-size: 16px;
                }
            }
            .section-tabs{
                margin: 20px 0;
            .tabs-nav {
                    li {
                        padding: 10px;
                        font-size: 14px;
                    }
                }
                .tab-content {
                    padding: 20px 0;
                    font-size: 12px;
                    h3{
                        font-size: 18px;
                    }
                }
            }
        }
    }
}
@media (max-width: 375px){
    .landing-wrapper {
        .landing-banner {
            background-position: bottom center;
            height: 250px;
            background-size: contain;

            h1 {
                font-size: 30px;
            }
        }
        .section-wrapper{
            .section-txt{
                width: 100%;
                text-align: center;
                .titl{
                    font-size: 18px;
                }
                .sub-titl{
                    font-size: 16px;
                }
                .btn-container {
                    justify-content: center;
                }
            }
            .section-img{
                width: calc(100% + 30px);
                margin-left: -15px;
                margin-right: -15px;
                float: left;
            }
            .section-tabs{
                margin: 20px -10px;
                width: calc(100% + 20px);
                .tabs-nav {
                    li {
                        padding: 10px;
                        font-size: 12px;
                        font-weight: bold;
                    }
                }
                .tab-content {
                    padding: 10px 0;
                    font-size: 12px;
                    h3{
                        font-size: 16px;
                    }
                }
            }
        }
    }
}
@media (max-width: 320px){}
[dir="rtl"] .landing-banner{
    transform: scaleX(-1);
    .container{
        transform: scaleX(-1);
    }
}

