.discountControl {
    $balloonWidth: 24px;
    $cellHeight: 50px;

    width: 100%;
    white-space: nowrap;
    @include bidi-text-align(right);

    &__question {
        line-height: $cellHeight;
        display: inline-block;
        padding: 0 3px;
        cursor: pointer;
    }

    &__form {
        line-height: $cellHeight;
        display: inline-block;

        &__error {
            padding: 0 3px;
            color: red;
            display: none;
            line-height: 1 !important;
            white-space: normal;
            margin-top: 5px;
            margin-bottom: 0px;
        }

        &__input {
            padding: 0 3px;

            input {
                position: relative;
                line-height: $cellHeight - 2px !important;
                padding: 0 10px !important;
                text-transform: uppercase;
                width: 150px;
            }
        }

        &__button {
            padding: 0 3px;
            cursor: pointer;
            line-height: $cellHeight !important;

        }
    }

    &__loader {
        line-height: $cellHeight;
        display: none;
        padding: 0 3px;
    }

    &__success {
        line-height: $cellHeight;
        display: none;
        padding: 0 3px;
        color: green;
    }

    &__tooltip {
        line-height: $cellHeight;
        display: inline-block !important;
        padding: 0 3px;

        &__balloon {
            text-align: center;
            line-height: 1.2em;
            width: 180px;
            @include bidi-left(auto !important);
            @include bidi-right(0 !important);
            white-space: normal;

            -webkit-transform: translate(0, -100%) !important;
            -moz-transform: translate(0, -100%) !important;
            -ms-transform: translate(0, -100%) !important;
            -o-transform: translate(0, -100%) !important;
            transform: translate(0, -100%) !important;

            &:after {
                @include bidi-left(auto !important);
                @include bidi-right(0 !important);
            }
        }
    }
}
