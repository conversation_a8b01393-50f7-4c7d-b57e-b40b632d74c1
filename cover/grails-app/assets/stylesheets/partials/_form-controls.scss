%form-control {
    font-family: $font-fam-default;
    font-weight: 400;
    width: 100%;
    font-size: 15px;
    padding: 10px 10px 7px;
    letter-spacing: 0.02em;
    border-radius: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: $clr-default;

    border-width: 1px;
    border-style: solid;
    border-color: $clr-distinct-dark;
}

%focus-effect {
    -webkit-appearance: none !important;
    outline: none !important;
    box-shadow: 0 0 2px 1px $clr-primary !important;
    border-color: transparent !important;
}

select, .dropdown, .select2-container .select2-selection--single {
    background: $clr-default $path-png-dropdown-arrow-black no-repeat;
    @include bidi-background-position-with-offset(right, 5px, center);
    //@include bidi-padding-right(27px);
    background-size: 14px;
    height: auto;
    width: 100%;

    &:hover, &:focus, &:active {
        @extend %focus-effect;
    }
}

.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__rendered {
    color: #ccc;
}

select {
    height: 47px !important;
    @include bidi-padding(10px, 0, 8px, 18px);
}

select.dropdown, .select2-container .select2-selection--single {
    @extend %form-control;
    @extend .dropdown;
    box-shadow: none !important;
    width: 100%;

    &::-ms-expand {
        display: none;
    }
}

.select2-container {
    width: 100% !important;
    max-width: 100% !important;
    //overflow-x: hidden !important;
}
#autodataVehicleTrim + .select2-container{
    width: 460px !important;
}
.select2-selection__arrow {
    display: none;
}

.select2-dropdown, .select2-container {
    max-width: calc(100vw - 30px) !important;
}

.select2-results__option {
    line-height: 22px;
}

.select2-results__options li:nth-child(2n) {
    //background-color: #fcfcff;
    background-color: lighten($clr-primary, 20%);
    color: white;
}

.select2-container--open .select2-dropdown--below,
.select2-container--open .select2-dropdown--above {
    background-color: lighten($clr-primary, 20%);
    //padding: 1px;
}

.select2-results__options li:nth-child(2n - 1) {
    //background-color: white;
    background-color: lighten($clr-primary, 20%);
    color: white;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: none;
    background-color: lighten($clr-primary, 57%);
    border-radius: 3px;
}

.select2-search--dropdown {
    margin-bottom: 1px;
    padding: 2px;
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: $clr-primary-faded;
    color: white;
}

.important-note {
    color: #666;
    //background-color: $clr-primary;
    padding: 1rem 0;
    font-size: 1.2rem;
    //border: 1px solid $clr-primary;
}

.textbox, .datebox {
    @extend %form-control;

    &:hover, &:focus, &:active {
        @extend %focus-effect;
    }
}

.textbox[disabled="disabled"] {
    background-color: #ccc;
}

.textbox:not(textarea), .datebox {
    height: 47px;
    font-size: 16px !important;
}

.select2-search__field {
    height: 34px;
}

.datebox, .dropdown {
    cursor: pointer;
}

.datebox::-webkit-calendar-picker-indicator {
    display: none;
}

.datebox[type="date"]::-webkit-input-placeholder {
    visibility: hidden !important;
}

input.textbox-with-badge {
    @extend %form-control;
    height: 47px;

    &:hover, &:focus, &:active {
        @extend %focus-effect;
    }
}

.direction-ltr{
    direction: ltr;
}

.input-group-addon:first-child {
    //border-right: -1px !important;
    //@include bidi-border-left(1px solid #bbb !important);
    //@include bidi-border-right(0 !important);
}

.radio-group {
    display: table;
    width: 100%;
}

.radio-group div {
    display: table-cell;
    vertical-align: middle;
}

.radio-btn {
    @include bidi-text-align(right);
    //@include bidi-padding-left(10px);
    text-transform: capitalize;
    width: 65px;
}

.radio-btn.radio-btn--newRadio {
    display: inline-block;
    margin: .2rem 0 0;
}
.radio-btn--newRadio.dynamic-width-radio{
    width: auto;
    label.insurance-label{
        -webkit-border-radius: 7px;
        -moz-border-radius: 7px;
        border-radius: 7px;
        width: auto;
        font-size: 0.9rem;
        padding:0 10px !important;
    }
}

.radio-btn--bg {
    margin: 10px 0;
}

label.insurance-label-bg {
    width: auto;
    font-size: 0.9rem;
    padding:10px 20px !important;
    border: solid 2px;
    @include bidi-text-align(left);
    border-radius: 15px;
    cursor: pointer;
    position: relative;
    font-weight: 500;
    .selected-check {
        display: none;
        position: absolute;
        @include bidi-right(12px);
    }
}

.insurance-radio--newRadio:checked + .insurance-label-bg {
    background-color: $clr-primary;
    border: solid 3px $clr-primary;
    color: white !important;
    .selected-check {
       display: inline-block;
    }
}

.radio-btn.radio-btn--newRadio:not(:nth-child(2)) {
    display: inline-block;
    margin: 0 1rem;
}

.radio-group-label {
    display: table-cell;
}

.insurance-radio--newRadio + .insurance-label:before {
    content: none !important;
}

.insurance-radio.insurance-radio--newRadio + .insurance-label {
    text-align: center !important;
    display: inline-block;
    width: 60px;
    height: 60px;
    border: 1px solid #666;
    border-radius: 100%;
    line-height: 60px;
    color: #666 !important;
    background-color: #F3F3F3;
    padding: 0 !important;
}

.insurance-radio.insurance-radio--newRadio:checked + .insurance-label {
    background-color: $clr-primary;
    color: white !important;
    border: 1px solid $clr-primary;
}

.insurance-checkbox,
.insurance-radio {
    position: absolute;
    z-index: -3000;
    @include bidi-left(-3000px);
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
}

.insurance-checkbox + .insurance-label,
.insurance-radio + .insurance-label {
    display: inline-block;
    line-height: 16px;
    font-size: 16px;
    vertical-align: middle;
    cursor: pointer;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.insurance-radio + .insurance-label:before {
    @extend .insurance-icon-circle-empty:before;
    font-size: 1.15em;
    color: $clr-distinct;
}

.insurance-radio:checked + .insurance-label:before {
    @extend .insurance-icon-dot-circled:before;
    color: $clr-accent;
}

.insurance-checkbox + .insurance-label:before {
    @extend .insurance-icon-check-empty:before;
    font-size: 1.15em;
    color: $clr-distinct;
    top: 0.1em;
    position: relative;
}

.insurance-checkbox:checked + .insurance-label,
.insurance-checkbox:checked + .insurance-label:before {
    //color: $clr-accent;
}

.insurance-checkbox:checked + .insurance-label:before {
    @extend .insurance-icon-check:before;
}

.checkbox {
    padding: 10px 0;
    font-size: 17px;
    font-family: $font-fam-default;
    font-weight: 600;
    color: $clr-distinct-very-dark;
    cursor: pointer;
    margin: 0;
    display: inline-block;
}

input[type='checkbox'] {
    display: inline-block;
    cursor: pointer;

    &:disabled + .insurance-label {
        color: $clr-distinct;
    }
}

input[type='number'] {
    padding-left: 1.2rem;
}

textarea {
    overflow-y: scroll;
    resize: none;
}

#brandNew.insurance-checkbox[checked="checked"] + .insurance-label,
#brandNew.insurance-checkbox[checked="checked"] + .insurance-label:before {
    color: white;
    background-color: $clr-accent;
}

.nameInput {
    text-transform: capitalize;
}
.nameInput::placeholder {
    text-transform: none;
}
#model + span, #select2-model-results li {
    //direction: ltr;
}

.dropdown--hidden,
.dropdown--hidden + .select2 {
    visibility: hidden;
}
