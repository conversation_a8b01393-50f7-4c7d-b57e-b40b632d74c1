.section-heading {
    font-family: $font-fam-default;
    font-size: 5.4vw;
    margin: 2rem auto 1.5rem;
    max-width: 72vw;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.8rem;
        margin: 3rem auto 0rem;
        max-width: 100%;
    }

    @media screen and (min-width: $screen-md) {
        //max-width: 43%;
    }

    @media screen and (min-width: $screen-lg) {
        //max-width: 36%;
    }
}

.section-heading--blue {
    color: $clr-primary !important;
}

.section-heading-centered {
    text-align: center;
}

.section-subheading {
    margin: 0 0 36px 0;
}

.section-heading-small {
    font-family: $font-fam-default;
    margin: 0 0 5px 0;
    font-size: 4vw;
    font-weight: bold;
    letter-spacing: 0.01em;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.2rem;
    }
}

.section-heading-small--grey {
    color: #555;
}

.section-heading-small-blue {
    color: $clr-primary;
}

.fancy-section {
    background-color: $clr-primary;
    position: relative;
    overflow: hidden;
    color: $clr-default;

    &:before {
        @media only screen and (min-width: $screen-md-min) {
            position: absolute;
            display: block;
            height: 210%;
            width: 100%;
            background-color: $clr-primary-intense;
            content: '';
            @include bidi-left(53.3%);
            @include bidi-border-radius(999999px, 0, 0, 999999px);
            top: 50%;
            transform: translateY(-52%);
        }
    }
}

.default-section {
    background-color: $clr-default;
    padding: 3rem 0;

    //@media screen and (min-width: $screen-sm) {
    //    padding: 3rem 0;
    //}
    //
    //@media screen and (min-width: $screen-md) {
    //    padding: 4rem 0;
    //}

    h2, h1 {
        color: $clr-primary;
        font-family: $font-fam-default;
        font-weight: 900;
        margin: 0 auto 3rem;

        //@media screen and (min-width: $screen-sm) {
        //    margin: 0 0 3rem;
        //}
        //
        //@media screen and (min-width: $screen-md) {
        //    margin: 0 0 4rem;
        //}
    }
}

.accent-section {
    background-color: $clr-accent;
    padding: 3rem 0;
    color: white;

    //@media screen and (min-width: $screen-sm) {
    //    padding: 3rem 0;
    //}
    //
    //@media screen and (min-width: $screen-md) {
    //    padding: 4rem 0;
    //}

    h2, h1 {
        color: white;
        font-family: $font-fam-default;
        font-weight: 900;
        margin: 0 auto 3rem;

        //@media screen and (min-width: $screen-sm) {
        //    margin: 0 0 3rem;
        //}
        //
        //@media screen and (min-width: $screen-md) {
        //    margin: 0 0 4rem;
        //}
    }
}

.mild-section {
    background-color: #F5F9FF;
    padding: 3rem 0;

    //@media screen and (min-width: $screen-sm) {
    //    padding: 3rem 0;
    //}
    //
    //@media screen and (min-width: $screen-md) {
    //    padding: 4rem 0;
    //}

    h2, h1 {
        color: $clr-primary;
        font-family: $font-fam-default;
        font-weight: 900;
        margin: 0 auto 3rem;

        //@media screen and (min-width: $screen-sm) {
        //    margin: 0 0 3rem;
        //}
        //
        //@media screen and (min-width: $screen-md) {
        //    margin: 0 0 4rem;
        //}
    }
}

.primary-section {
    background-color: $clr-primary;
    color: $clr-default;
    position: relative;
}

.primary-section--city {
    //height: 347px;
    background-image: url("data:image/jpg;base64,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");
    background-position: bottom;

    @media only screen and (min-width: $screen-md-min) {
        height: 445px;
    }
}
@mixin inner-banner($bg, $size, $position){
    display: block;
    position: relative;
    height: 90px;

    background-image: $bg;
    background-size: $size;
    background-position: $position;
    background-repeat: no-repeat;
    a{
        cursor: pointer;
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 10;
        top:0;
        left:0;
    }
}
.quotes-section {
    //  BANNER IN QUOTES RESULTS PAGE
    //.inner-banner{
    //    @include inner-banner(url(''), contain, center);
    //    margin-top: 28px;
    //    @media screen and (min-width: $screen-sm) {
    //        @include inner-banner(url(''), contain, center);
    //        margin-top: 28px;
    //    }
    //}
    .section-heading {
        color: $clr-primary;
        font-family: $font-fam-default;
        font-weight: 600;

        margin: 28px auto 35px;

        @media only screen and (max-width: $screen-sm-max) {
            font-size: 20px;
        }

        @media only screen and (min-width: $screen-md-min) {
            font-size: 26px;
        }
    }
    .section-sub-heading {
        color: #36397D;
        font-family: $font-fam-default;
        font-weight: 600;
        text-decoration: underline;

        margin: -15px 0 30px;

        @media only screen and (max-width: $screen-sm-max) {
            font-size: 18px;
        }

        @media only screen and (min-width: $screen-md-min) {
            font-size: 22px;
        }
    }

    .quotes-table {
        border-collapse: collapse;
        margin: 40px 0;
        width: 100%;
        max-width: 100%;
        box-shadow: 1px 5px 5px rgba(0, 0, 0, 0.5);
        border: solid 1px #ddd;
        background-color: #fff;

        [data-sort-value="true"]:before {
            @extend .insurance-icon-verification-mark:before;
            font-size: 26px;
            color: $clr-primary;
            line-height: 0;
        }

        [data-sort-value="false"]:before {
            @extend .insurance-icon-cross-out-mark:before;
            font-size: 22px;
            line-height: 0;
        }

        &--renewal {
            background-color: #F1F4F7;
            border: 1px solid grey;
        }
        .discount-message{
            background-color: #36397D;
            color: $clr-default;
            box-shadow: 1px 5px 5px rgba(0, 0, 0, 0.5);
            width: calc( 100% + 30px);
            margin-left: -10px;
            font-weight: normal;
            font-size: 13px;
            padding: 5px 10px;
            &:after{
                content: ' ';
                background-color: #22518e;
                width: 20px;
                height: 15px;
                top: 22px;
                right: -14px;
                position: absolute;
                z-index: -1;
                transform: translateY(-10%) rotate(-10deg);
            }
            @media screen and (max-width: $screen-sm-min) {
                padding: 5px;
                width: calc( 100% + 40px);
                margin-left: -20px;
                font-size: 14px;
                &:after{
                    top: 24px;
                }
            }

        }
    }

    .insurer-image {
        display: block;
        height: 60px;
        margin: 0 auto 5px;

        @media only screen and (max-width: $screen-sm-max) {
            height: 41px;
        }

        img {
            height: 100%;
        }
    }

    .buttons {
        margin: 20px 0 0 0;
    }

    .table-row.header-row {
        background-color: $clr-distinct-light;
        border-bottom: solid 1px $clr-distinct-dark;
        border-top: solid 1px $clr-distinct-dark;
        font-size: 13px;
        font-weight: 500;
        height: 57px;
        text-transform: uppercase;
    }

    .data-row--health .table-cell {
        padding-bottom: 20px;

        @media screen and (min-width: 992px) {
            padding-bottom: 30px;
        }

        @media screen and (min-width: 1200px) {
            padding-bottom: 17px;
        }
    }

    .table-row.header-row.header-row-white {
        background-color: #fff;
    }

    .table-row.data-row {
        font-size: 17px;
        font-weight: 600;
        color: $clr-distinct-dark;
        position: relative;
        //white-space: nowrap;

        &:not(:first-child) {
            border-bottom: solid 1px $clr-distinct-dark;
        }
    }

    .discountMessage {
        display: table-row;
        white-space: nowrap;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 5px);
        color: $clr-primary;
    }

    .mobileDeductible {
        display: table-row;
        white-space: nowrap;
        position: absolute;
        margin-top: 10px;
        left: 50%;
        transform: translate(-50%, 120px);
        color: #555;
        background-color: #F0F3F6;
        width: calc(100% - 30px);
        text-align: center;
        font-size: .8rem;
        padding: 4px 0;

        @media screen and (min-width: 992px) {
            transform: translate(-50%, 133px);
        }

        @media screen and (min-width: 1200px) {
            display: none;
        }
    }

    .table-cell {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
        padding: 5px 5px 0;
        position: relative;

        span {
            display: block;
            padding: 0 10px;
        }

        .insurer-package {
            @media screen and (max-width: 1199px) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
        }

        .excess {
            color: $clr-distinct-very-dark;
        }

        [class*='insurer-logo-'] {
            height: 45px;
            margin: 0 0 10px;
        }

        .package-label {
            font-family: $font-fam-default;
            font-size: 14px;
            color: $clr-dark;
        }

        .package-label--health {
            @media screen and (max-width: 1199px) {
                //min-height: 4rem;
            }
        }

        .price {
            font-size: 13px;
            font-family: $font-fam-default;
            font-weight: 600;
            color: $clr-primary;
            -webkit-transition: color 1.3s;
            -moz-transition: color 1.3s;
            -ms-transition: color 1.3s;
            -o-transition: color 1.3s;
            transition: color 1.3s;

            @media only screen and (min-width: $screen-md-min) {
                font-size: 18px;
            }
        }

        button {
            display: block;
            margin: 0 0 5px;
            text-transform: capitalize;

            @media only screen and (max-width: $screen-sm-max) {
                min-width: 102px;
            }

            @media only screen and (min-width: $screen-md-min) {
                min-width: 200px;
            }

            &.link {
                @extend %button;
                color: $clr-primary;
                background-color: inherit;
                text-decoration: none;
                font-weight: 700;
                white-space: normal;
            }

            &.link:hover {
                text-decoration: underline;
            }
        }
    }

    .table-cell--deductible {
        font-size: .8rem;
    }

    .table-cell--networks {
        width: 120px;

        @media screen and (min-width: 992px) {
            width: 130px;
        }
    }

    .table-cell__discount {
        color: #245FA9;
    }

    .table-cell--buyNow {
        width: 125px;
    }

    .table-cell--promotion {
        font-size: 3vw;
        vertical-align: top;
        padding: 24px 23px 0 0;

        @media screen and (min-width: $screen-sm) {
            font-size: .8rem;
        }
    }

    .insurance-icon-help-circled {
        @include bidi-right(0);

        color: $clr-primary;
        position: absolute;
        bottom: 10px;
    }

    .insurance-icon-sort-up, .insurance-icon-sort-down {
        position: absolute;
        @include bidi-right(0);
        top: 50%;
        transform: translateY(-100%);
    }

    [data-current-sort-order="asc"] {
        .insurance-icon-sort-up {
            opacity: 1;
        }

        .insurance-icon-sort-down {
            opacity: .4;
        }
    }

    [data-current-sort-order="desc"] {
        .insurance-icon-sort-up {
            opacity: .4;
        }

        .insurance-icon-sort-down {
            opacity: 1;
        }
    }

    .sort-column:hover {
        text-decoration: underline;
        text-decoration-color: $clr-primary;
        cursor: pointer;
    }

    .link {
        -webkit-appearance: none;
        background-color: transparent;
        border: none;
        text-decoration: none;
        color: $clr-primary;
        margin: 0;
        padding: 10px;
        white-space: normal;

        &:hover, &:focus, &:active {
            text-decoration: underline;
        }
    }
}

.quote-details-section {
    padding: 30px 0;

    form {
        max-width: none;
        width: auto;
    }

    .insurance-logo-container {
        display: block;
        margin: 0 auto 10px;
        height: 107px;

        @media only screen and (max-width: $screen-sm-max) {
            height: 80px;
        }

        img {
            height: 100%;
        }
    }

    #floating-price {
        font-family: $font-fam-default;
        font-size: 35px;
        line-height: 1em;
        color: $clr-distinct;
        font-weight: 700;
        text-align: center;
        -webkit-transition: color .9s;
        -moz-transition: color .9s;
        -ms-transition: color .9s;
        -o-transition: color .9s;
        transition: color .9s;
        will-change: background-color, color, font-size;

        &.fixed {
            @media only screen and (max-width: $screen-sm-max) {
                position: fixed;
                top: 0;
                left: 0;
                display: block;
                margin: 0 auto;
                width: 100%;
                z-index: 9999;
                color: $clr-default;
                -webkit-animation: becomeFixed .5s forwards;
                -o-animation: becomeFixed .5s forwards;
                animation: becomeFixed .5s forwards;

                &.grab-attention {
                    font-size: 35px;
                }
            }
        }
    }

    .package {
        color: $clr-distinct-dark;
        margin: 0 0 10px;
        font-size: 20px;
        text-transform: uppercase;
        line-height: 1em;
        font-family: $font-fam-default;
        font-weight: 700;
        text-align: center;
    }

    .quote-details-table {
        margin: 0 0 20px;

        .table-cell {
            @include bidi-padding(10px, 25px, 10px, 0);

            @media only screen and (min-width: $screen-md-min) {
                width: 50%;
            }
        }

        .table-cell--healthProp {
            width: 90px;
        }

        .table-cell--healthTitle {
            text-align: center;
            width: 100%;
        }

        .table-cell--healthVal {
            width: auto;
        }

        .checkbox {
            padding: 0;

            input {
                zoom: 1.5;
                top: 3px;
            }
        }

        .table-cell, .checkbox {
            font-size: 14px;
            font-weight: bold;
            font-family: $font-fam-default;
            color: $clr-dark-almost;
            text-transform: capitalize;
        }

        .table-cell.value, .checkbox {
            color: $clr-primary;
        }

        .table-cell.value {
            //@media only screen and (max-width: $screen-sm-max) {
            @include bidi-text-align(left);
            vertical-align: middle;
            //padding: 0;
            width: 45%;

            .checkbox .insurance-label {
                @include bidi-margin-left(-31px);
            }
            //}
        }
    }

    .terms-link {
        text-align: center;
        margin: 0 0 10px;

        a {
            color: $clr-accent;
            text-transform: uppercase;
            text-decoration: none;
            font-family: $font-fam-default;
            font-size: 14px;

            &:hover {
                text-decoration: underline;
                color: $clr-primary;
            }
        }
    }

    [class*="button-"] {
        @media only screen and (max-width: $screen-sm-max) {
            margin: 0 0 10px;
        }
    }

    .insurance-icon-cross-out-mark {
        font-size: 20px;
        color: $clr-distinct;
    }

    .insurance-icon-verification-mark {
        font-size: 26px;
    }
}

.policy-features-section {
    background-color: $clr-distinct-light;
    color: $clr-dark-almost;
    padding: 20px;
    margin: 0 0 32px;

    .title {
        font-family: $font-fam-default;
        font-weight: 700;
        font-size: 18px;
        text-align: center;
        margin: 0 0 20px 0;
        background-color: #ddd;
        padding: 10px 0;
    }

    .policy-features-table {
        border-collapse: collapse;

        .table-row:not(:last-child) {
            border-bottom: 1px solid $clr-distinct-off;
        }

        .table-cell {
            @include bidi-padding(10px, 25px, 10px, 0);

            &.table-cell--healthProp {
                @include bidi-padding(5px, 25px, 5px, 0);
            }

            @media only screen and (min-width: $screen-md-min) {
                width: 50%;
            }
        }

        .checkbox {
            padding: 0;
            //@include bidi-padding(0, 0, 0, 25px);

            input {
                zoom: 1.5;
                top: 3px;

                //position: absolute;
                //top: 50%;
                //@include bidi-left(50%);
                //@include bidi-calc-translate(-50%, -20px, -50%);
            }
        }

        .table-cell, .checkbox {
            font-size: 14px;
            font-weight: bold;
            font-family: $font-fam-default;
            color: $clr-dark-almost;
            text-transform: capitalize;
        }

        .table-cell.value, .checkbox {
            color: $clr-primary;
        }

        .insurance-icon-cross-out-mark {
            color: $clr-distinct;
        }

        .table-cell.value {
            //@media only screen and (max-width: $screen-sm-max) {
            @include bidi-text-align(left);
            vertical-align: middle;
            padding: 0;
            width: 45%;
            //}
        }
    }

    .policy-features-table--health {
        margin: 0 !important;

        .table-row:not(:last-child) {
            border: none;
        }
    }

    //.policy-features-table--health:not(:last-of-type) {
    //    border-bottom: 2px solid #ccc;
    //}

    .table-cell--healthProp {
        width: 70px;

    }
}

.policy-features-section--health {
    padding: 0 20px 20px;

    @media screen and (max-width: $screen-sm-max) {
        margin: 0 -15px 32px;
    }
}

.policy-features-section__title {
    font-family: $font-fam-default;
    font-weight: 700;
    font-size: 18px;
    text-align: center;
    background-color: #ddd;
    padding: 1em 0;
    position: relative;
    margin: 0 -20px;
}

@-webkit-keyframes becomeFixed {
    0% {
        background-color: $clr-default;
        font-size: 35px;
    }
    65% {
        background-color: $clr-primary;
        font-size: 35px;
    }
    100% {
        background-color: $clr-primary;
        font-size: 25px;
    }
}

@keyframes becomeFixed {
    0% {
        background-color: $clr-default;
        font-size: 35px;
    }
    65% {
        background-color: $clr-primary;
        font-size: 35px;
    }
    100% {
        background-color: $clr-primary;
        font-size: 25px;
    }
}

.breadcrumbs-section {
    //background-color: $clr-primary;
}

.secure-checkout-section {

    .page-heading {
        color: $clr-primary;
        font-family: $font-fam-default;
        border-bottom: 2px solid $clr-distinct;
        padding: 15px 0;
        margin: 0;
        font-size: 24px;
        font-weight: 800;
        text-transform: capitalize;

        @media only screen and (max-width: $screen-sm-max) {
            text-align: center;
        }
    }

    .secure-checkout-heading {
        display: flex;
        align-items: center;
        justify-content: space-between;

        @media only screen and (max-width: $screen-sm-max) {
            flex-direction: column;
        }

        button{
            max-width: 200px;
            border-radius: 20px;

            @media only screen and (max-width: $screen-sm-max) {
                margin-top: 10px;
            }
        }
    }

    .overview {
        color: $clr-dark-almost;

        @media only screen and (max-width: $screen-sm-max) {
            text-align: center;
        }

        .row {
            padding: 5px 0;
        }

        .col-md-height {
            @media only screen and (max-width: $screen-sm-max) {
                padding: 10px 15px;
            }

            @media only screen and (min-width: $screen-md-min) {
                height: 80px;
            }
        }

        .image {
            @media only screen and (max-width: $screen-sm-max) {
                max-width: 290px;
                max-height: 100px;
            }
            @media only screen and (min-width: $screen-md-min) {
                max-width: 100%;
                max-height: 100%;
            }
        }

        .label, .value {
            padding: 0;
            margin: 0;
        }

        .label {
            font-size: 15px;
            font-weight: 600;
            line-height: 26px;
        }

        .value {
            font-size: 12px;
            line-height: 16px;
        }

        .package {
            @media only screen and (min-width: $screen-md-min) {
                display: inline-block;
            }
            .price {
                font-size: 20px;
                font-weight: 900;
                font-family: $font-fam-attention;
                line-height: 26px;
                margin: 0;
                -webkit-transition: color 1.3s;
                -moz-transition: color 1.3s;
                -ms-transition: color 1.3s;
                -o-transition: color 1.3s;
                transition: color 1.3s;
            }

            .name {
                font-size: 16px;
                font-weight: 900;
                line-height: 16px;
                margin: 0;
                color: $clr-distinct-very-dark
            }

            .price, .name {
                @media only screen and (min-width: $screen-md-min) {
                    @include bidi-text-align(left);
                }
            }
        }

    }

    .features-table {
        border-top: 2px solid $clr-distinct;
        border-bottom: 2px solid $clr-distinct;
        background-color: $clr-distinct-light;

        .highlight {
            @media only screen and (min-width: $screen-md-min) {
                background-color: $clr-distinct-very-light;
                border-left: 1px solid $clr-distinct;
                border-right: 1px solid $clr-distinct;
            }
            .inside-full-height {
                @media only screen and (max-width: $screen-sm-max) {
                    background-color: $clr-distinct-very-light;
                    border-top: 1px solid $clr-distinct;
                    border-bottom: 1px solid $clr-distinct;
                }
            }
        }

        .inside-full-height {
            padding: 0 20px;

            @media only screen and (max-width: $screen-sm-max) {
                text-align: center;
            }
        }

        .insurance-icon-verification-mark {
            font-size: 30px;
            color: $clr-primary;
        }

        .insurance-icon-cross-out-mark {
            font-size: 24px;
            color: $clr-accent;
        }

        .no-icon, .insurance-icon-verification-mark, .insurance-icon-cross-out-mark {
            display: block;
            line-height: 45px;
            min-height: 20px;

            @media only screen and (min-width: $screen-md-min) {
                height: 45px;
            }
        }

        .heading {
            margin: 0 0 15px;
            padding: 0;
            font-size: 16px;
            text-transform: capitalize;
            font-weight: 900;
        }

        .plain-list {
            list-style-type: none;
            padding: 0;
            margin: 0 0 20px;
            font-size: 14px;
            font-weight: 500;
            color: $clr-distinct-very-dark;
            line-height: 1.2;
            text-transform: capitalize;

            li {
                margin: 15px 0;
            }
        }
    }

    .whatHappensNext {
        font-size: 12px;

        .heading {
            font-size: 16px;
            color: $clr-primary;
            padding: 0;
            margin: 0 0 10px;

            &:first-letter {
                text-transform: capitalize;
            }

            @media only screen and (max-width: $screen-sm-max) {
                text-align: center;
            }
        }

        p, li {
            line-height: 15px;

            &:first-letter {
                text-transform: capitalize;
            }
        }

        ol {
            list-style-type: none;
            margin: 0 0 10px;
            padding: 0;
        }

        ol > li {
            counter-increment: customlistcounter;
        }

        ol > li:before {
            content: counter(customlistcounter) ")";
            @include bidi-float(left);
            width: 20px;
        }

        ol:first-child {
            counter-reset: customlistcounter;
        }

        .tel-number {
            color: $clr-accent;
        }
    }

    .shop-with-confidence {
        .heading {
            color: $clr-primary;
            font-size: 18px;

            &:first-letter {
                text-transform: capitalize;
            }
        }

        .attention {
            color: $clr-accent;
            text-transform: uppercase;
            font-size: 20px;
            font-weight: 900;
            line-height: 1em;
        }

        .image {
            vertical-align: middle;

            .insurance-icon-screen-guaranteed {
                margin: 0 auto;
                display: block;
                width: 1.35em;
                font-size: 80px;
                color: $clr-distinct;
            }
        }

        .info {
            font-size: 13px;
            line-height: 1.2em;
        }
    }

    .peace-of-mind {
        font-size: 12px;
        padding: 20px 0 0;

        .heading {
            font-size: 16px;
            color: $clr-primary;
            padding: 0;
            margin: 0 0 10px;

            &:first-letter {
                text-transform: capitalize;
            }

            @media only screen and (max-width: $screen-sm-max) {
                text-align: center;
            }
        }

        .table-cell.property {
            text-transform: capitalize;
            font-weight: 400;
            color: $clr-distinct-very-dark;

            @media only screen and (max-width: $screen-sm-max) {
                font-size: 12px;
            }

            @media only screen and (min-width: $screen-md-min) {
                font-size: 14px;
            }
        }
        .vertical-aligin-middle{vertical-align: middle}

        .table-cell.value {
            @media screen and (min-width: $screen-sm-min){
                width: 218px;
                max-width: 218px;
            }
            padding: 5px 0;
            @include bidi-text-align(right);
            width: 190px;
            max-width: 190px;

            .checkbox {
                padding: 0;
                display: inline-block;
                @include bidi-text-align(left);
                min-width: 90px;
            }
        }

    }

    .round-arrow-below {
        height: 34px;
        margin-left: 3px;
        @at-root [dir='rtl'] & {
            -webkit-transform: rotateY(180deg);
            -moz-transform: rotateY(180deg);
            transform: rotateY(180deg);
        }
    }

    form {
        max-width: none;
    }

    .total-amount {
        @include bidi-text-align(right);
        line-height: 1.8em;

        .label {
            font-size: 24px;
            font-weight: 400;
            color: $clr-distinct-very-dark;
            margin: 0;

            &:first-letter {
                text-transform: capitalize;
            }
        }

        .price {
            font-size: 24px;
            font-weight: 900;
            color: $clr-dark-almost;
            margin: 0 0 20px;
            -webkit-transition: color 1.3s;
            -moz-transition: color 1.3s;
            -ms-transition: color 1.3s;
            -o-transition: color 1.3s;
            transition: color 1.3s;
        }
    }

    .terms-and-conditions, .self-declaration, .pregnancy-terms {
        margin: 0 0 10px;

        @media only screen and (max-width: $screen-sm-max) {
            text-align: center;
        }

        @media only screen and (min-width: $screen-md-min) {
            @include bidi-text-align(right);
        }

        .insurance-label {
            position: relative;
            @include bidi-left(-8px);

            &:first-letter {
                text-transform: capitalize;
            }
        }
    }

    .checkout-buttons {
        margin: 0 0 50px;

        button{
            border-radius: 20px;
        }

        @media only screen and (max-width: $screen-sm-max) {
            .button-container {
                padding: 5px 0;
            }

        }

        @media only screen and (min-width: $screen-md-min) {
            .button-container:first-of-type {
                @include bidi-padding-right(5px);
            }

            .button-container:last-of-type {
                @include bidi-padding-left(5px);
            }
        }
    }

    .empty-col {
        border-top: 2px solid $clr-distinct;

        @media only screen and (max-width: $screen-sm-max) {
            height: 30px;
        }
    }
}

h2.insurers {
    margin: 0;
    padding: 0;
    font-size: 15px;
    line-height: 28px;
    font-weight: 400;
    color: $clr-dark-almost;
}

.travel-hero {
    .lead {
        text-align: center;

        .icon {
            font-size: 134px;
        }

        .header {
            text-align: center;
            font-size: 36px;
            margin: 0 0 20px;
        }

        .info {
            font-size: 15px;
            font-weight: 400;
        }
    }

    .cta {
        //@media only screen and (max-width: $screen-sm-max) {
        //    padding: 0 0 40px;
        //}

        //@media only screen and (min-width: $screen-sm-max) {
        //    padding: 60px 0 0 50px;
        //}

        @media only screen and (min-width: $screen-md-min) {
            padding: 40px 0 0 90px;
        }

        .insurance-icon-plane:before {
            font-size: 130px;
            line-height: 1;
            margin: 0 0 -20px;
        }

        &.life {
            padding: 0 0 40px;
        }

        &.travel {
            @media only screen and (max-width: $screen-sm-max) {
                padding: 0 0 40px;
            }

            @media only screen and (min-width: $screen-md-min) {
                padding: 60px 0 40px;
            }
        }

        .insurance-icon-global-heart-beat,
        .insurance-icon-medical-kit {
            font-size: 111px;
            line-height: 1.2;
        }

        .heading {
            text-align: center;
            font-size: 24px;
            margin: 0 0 10px;
        }

        .subheading {
            margin: 0 0 10px;
        }

        button {
            width: 250px;
        }
    }

    .callCompare {
        a {
            color: white;
        }

        img {
            width: 65px;
            float: left;
            padding-right: 10px;
            padding-left: 10px;
            @media only screen and (max-width: $screen-sm-max) {
                display: none;
            }
        }

    }

}

.travel-company-list {
    display: table;
    margin: 0 auto;
    max-width: 100%;
    padding: 0 0 10px;

    li {
        display: table-cell !important;
        vertical-align: middle;
        padding: 0 10px;
        height: 55px;
    }

    img {
        height: 100%;
    }
}

.promo-box-icon {
    color: $clr-primary;

    &:not(:last-child) {
        margin: 0 auto 2rem;
    }

    h3 {
        color: #444;
    }

    p {
        color: #444444;
        font-size: 4vw;

        @media screen and (min-width: $screen-sm) {
            font-size: 1.2rem;
        }
    }

    @media screen and (min-width: 992px) {
        margin: 1rem auto;
    }

    .icon {
        font-size: 5rem;
        line-height: 1;
        margin-bottom: .5rem;

        @media screen and (min-width: 992px) {
            font-size: 100px;
        }
    }

    &.travel {
        p {
            @media only screen and (min-width: $screen-md-min) {
                height: 110px;
            }
        }

        a {
            margin: 0 0 15px;
        }
    }
}

.thankyou-section {
    .heading {
        font-size: 22px;
        font-weight: 600;
        color: $clr-primary;
    }

    ul {
        font-weight: 900;
    }
}

.invoice-section {
    background-color: $clr-distinct-light;
    border-top: 3px solid $clr-distinct;

    .image-container {
        margin: 0 auto;
        width: 100%;
        text-align: center;
        padding: 10px 30px;
        position: relative;
        vertical-align: middle;

        @media only screen and (min-width: $screen-md-min) {
            height: 120px;
        }

        .logo {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .info-block {
        text-align: center;
        font-weight: 900;
        margin: 0 0 15px;
        color: $clr-dark-almost;
    }

    .price-breakdown {
        border-top: 1px solid $clr-distinct;
        margin: 0 25px;
        padding: 11px 0 25px;

        .heading {
            padding: 0;
            font-size: 18px;
            color: $clr-dark-almost;
            margin: 0 0 5px;
        }

        .table-cell {
            line-height: 2em;
            font-size: 14px;
            font-weight: 500;

            &.value {
                color: $clr-primary;
                @include bidi-text-align(right);
                font-family: $font-fam-attention;
            }
        }

        .total {
            @include bidi-text-align(right);
            font-family: $font-fam-attention;
        }
    }

    .barcode {
        //hard coded color to match png background
        background-color: #dbdbdb;
        text-align: center;

        img {
            max-height: 100%;
            max-width: 100%;
        }
    }
}

.payment-gateway {
    position: relative;

    iframe {
        width: 100%;
        height: 350px;
        border: none;

        &.loading {
            display: none;
        }
    }

    .veil {
        display: none;
    }

    .error {
        margin: 0 0 0 1%;
        color: red;
        font-weight: 600;
    }

    .heading {
        margin: 0 0 0 1%;
        color: $clr-primary;
        font-weight: 600;
        font-size: 24px;
    }

    .bg-green {
        background-color: #00974b;
        padding: 5px 20px;
    }

    .table {
        width: auto;
        margin: 0 auto;

        p {
            @include bidi-margin(0, 15px, 0, 0);
        }
    }

    .payfort-white {
        display: inline-block;
        height: 2em;
        width: 95px;
        background-image: url("https://assets.yallacompare.com/insurance/logos/payfort-white.png");
        background-size: contain;
        background-repeat: no-repeat;
    }

    .secure-logos {
        padding: 15px 0;

        @media only screen and (max-width: $screen-sm-max) {
            padding-left: 8px;
            padding-right: 8px;
        }

        img {
            max-width: 100%;
            max-height: 25.19px;
        }
    }

    .insurance-label {
        height: 60px;

        img {
            max-width: 100%;
            max-height: 100%;
        }
    }

}

.payment-gateway.disabled .veil {
    position: absolute;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    background-color: rgba(240, 240, 240, .8);
    display: block;
    z-index: 9999980;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

form.contact-us-form {
    max-width: none;

    .heading {
        font-size: 30px;
        color: $clr-accent;
    }

    .col-md-6:first-of-type {
        @include bidi-border-right(1px solid $clr-distinct-light);
    }

    &:after {
        content: "";
        display: table;
        clear: both;
    }
}

.no-quotes-section {
    width: 450px;
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;

    .image-container {
        text-align: center;
        img {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .info-box {
        background-color: $clr-distinct-light;
        padding: 20px;

        p {
            font-size: 15px;
        }

        ul {
            font-size: 14px;
        }
    }

    .link {
        text-align: center;
        text-decoration: none;

        a {
            padding: 10px;
            color: $clr-accent;
            display: inline-block;
            font-weight: 600;
        }
    }
}

.error-section {
    margin: 30px auto;
    max-width: 580px;
    text-align: center;

    .heading {
        font-size: 30px;
        font-weight: 600;
    }

    .subheading {
        font-size: 24px;
        font-weight: 600;
    }

    .description {
        font-size: 12px;
    }

    .button-accent {
        margin: 20px auto;
        display: inline-block;
        padding: 15px;
        width: auto;
        text-decoration: none;
    }
}

#dummy-form {
    width: 100%;
    height: 350px;
    background-image: url("https://assets.yallacompare.com/insurance/images/dummy-form.png");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.show-balloon {
    cursor: pointer;
    position: relative;
    display: inline-block;
}

.pharmaceuticals:hover i,
.coverage:hover i,
.deductible:hover i{
    background-color: $clr-primary !important;
}

.view-balloon {
    opacity: 1 !important;
}

.balloon {
    will-change: opacity;
    transition: opacity .3s;
    pointer-events: none;
    opacity: 0;
    font-family: $font-fam-default;
    font-size: 12px;
    font-weight: 600;
    max-width: 270px;
    position: absolute;
    width: calc(100vw - 30px);
    text-transform: none;
    background-color: $clr-accent;
    color: $clr-default;
    z-index: 10;
    border-radius: 5px;
    top: -5px;
    padding: 8px;
    -webkit-box-shadow: 0px 0px 5px 0px rgba(180, 29, 100, 1);
    -moz-box-shadow: 0px 0px 5px 0px rgba(180, 29, 100, 1);
    box-shadow: 0px 0px 5px 0px rgba(180, 29, 100, 1);
    letter-spacing: 0.05em;

    @media only screen and (max-width: $screen-sm-max) {
        @include bidi-translate(0, -100%);
        @include bidi-left(0);

    }

    @media only screen and (min-width: $screen-md-min) {
        @include bidi-translate(-50%, -100%);
        @include bidi-left(50%);
    }

    &.featureBalloon {
        @media only screen and (max-width: $screen-sm-max) {
            @include bidi-translate(0, -100%);
            @include bidi-left(initial);

        }

        @media only screen and (min-width: $screen-md-min) {
            @include bidi-translate(0, -100%);
            @include bidi-left(initial);
        }
    }

    &:after {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        border-top-color: #E2156B;
        bottom: -5px;
        border-width: 5px 5px 0;
        @include bidi-translate(-50%, 0);

        @media only screen and (max-width: $screen-sm-max) {
            @include bidi-left(50px);
        }

        @media only screen and (min-width: $screen-md-min) {
            @include bidi-left(50%);
        }
    }
}

.travel-funnel-header {
    padding: 10px 0;
    //margin-bottom: 40px;

    .huge-text, .huge-icon {
        @media only screen and (min-width: $screen-md-min) {
            line-height: 114px;
        }
    }

    .huge-text {
        font-size: 28px;
        font-weight: 900;
    }

    .huge-icon {

        @media only screen and (max-width: $screen-sm-max) {
            font-size: 50px;
        }

        @media only screen and (min-width: $screen-md-min) {
            font-size: 80px;
        }
    }
}

.travel-iframe {
    @media only screen and (max-width: $screen-xs-max) {
        height: 1400px;
    }

    @media only screen and (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {
        height: 1350px;
    }

    @media only screen and (min-width: $screen-md-min) and (max-width: $screen-md-max) {
        height: 850px;
    }

    @media only screen and (min-width: $screen-lg-min) {
        height: 850px;
    }
}

.header1 {
    font-family: $font-fam-attention;
    font-size: 22px;
    line-height: 1.2em;
    letter-spacing: 0.02em;
}

.header1--clrPrimaryDirty {
    color: $clr-primary-dirty !important;
}

.main--grey {
    //@media screen and (min-width: $screen-sm-min) {
        background-color: $clr-background-grey;
    //}
}

.applyNowForm__input--mgn-bottom {
    margin-bottom: 1em !important;
}

.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #B20838;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.offline-quotes{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    margin: 56px 0;



    @media only screen and (max-width: $screen-sm-max) {
        margin-bottom: 56px;
    }

    i {
        font-size:20px
    }

    &__title {
        font-size: 28px;
        font-weight: 600;
        letter-spacing: -0.14px;
    }

    &__text {
        color: #424242;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        letter-spacing: -0.1px;
        max-width: 929px;
        margin: auto;


        @media only screen and (max-width: $screen-sm-max) {
            font-size: 16px;
        }
    }

    &__button {
        outline: none;
        border: none;
        background-color: #da1884;
        border-radius: 7px;
        width: 160px;
        height: 56px;
        color: white;
        font-size: 19px;
        font-weight: 500;
        letter-spacing: -0.095px;
        margin-top: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 17px;
        padding: 16px 15px;
    }

    &__button img {
        align-self: flex-start;
    }
}
.car-quote-srp .vat-section{
    margin: 30px 0;
}
.payment__banner {
    border-radius: 7px;
    background: #e5f4fa;
    padding: 12px 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    max-width: 600px;
    margin-left: auto;

    @media only screen and (max-width: $screen-sm-max) {
        flex-direction: column-reverse;
        margin-right: auto;
    }

    &__logos {
        display: flex;
        align-items: center;
        gap: 9px;

        &-logo {
            border-radius: 2px;
            height: 30px;
            width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                object-fit: contain;
            }
        }
    }
    &-text {
        color: #36336c;
        margin: 0;
        font-weight: 600;

        @media only screen and (max-width: $screen-sm-max) {
            font-size: 14px;
        }
    }
}

.healthQuote {
    &__recommended {
        position: relative;
        top: -5px;
        width: 220px;
        height: 41px;
        background-color: $clr-primary;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        padding: 9px 13px;
        @include bidi-left(-5px);
        @include bidi-border-radius(0, 10px, 10px, 10px);

        @media screen and (min-width: $screen-lg) {
            width: 249px;
            height: 47px;
            font-size: 16px;
            padding: 12px 19px;
        }

        &-icon {
            font-size: 18px;
            @include bidi-margin-right(8px);
        }

        &--text {
            color: $clr-default;
            font-weight: 700;
            line-height: 120%;

            .highlighted {
                color: #fdea14;
            }
        }
    }
}
