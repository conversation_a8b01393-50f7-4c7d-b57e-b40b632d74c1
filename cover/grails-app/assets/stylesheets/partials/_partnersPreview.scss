.partnersPreview {
    background-color: $clr-distinct-very-light;
    overflow: auto;

    &__row {
        margin: 2rem auto;
        text-align: center;
    }

    &__partner {
        display: inline-block;
        width: 135px;
        height: 55px;
        text-align: center;
        position: relative;
        margin: 0 2.2em;
    }

    &__partner img {
        max-width: 100%;
        max-height: 100%;
        display: block;
        margin: 0 auto;
        position: absolute;
        bottom: 0;
        left: 50%;
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
        transform: translateX(-50%);
    }

    &__button {
        display: inline-block;

        &:hover {
            color: white;
            background-color: $clr-primary;
            border: 1px solid $clr-primary;
        }
    }

    &__partner.grayscale-light:hover,
    &__partner.grayscale-dark:hover {
        filter: none;
        -webkit-filter: none;
        opacity: 1;
    }

    @media screen and (min-width: 1200px) {
        &__partner {
            margin: 0 3.9em;
        }
    }
}
