.js_sticky{top: 0;width: 100%;z-index: 1}
.carRatingsFilterWrapper{
    position: relative;
    border-radius: 5px;
    //position: -webkit-sticky;
    //position: -moz-sticky;
    //position: -ms-sticky;
    //position: -o-sticky;
    //position: sticky;
    -webkit-box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.75);
    -moz-box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.75);
    box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.75);
    top: 0;
    z-index: 2;
    background-color: white;
    padding: 0;
}
.v3.is_sticky_wrapper .carRatingsFilterWrapper{
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.text-primary.insurance-checkbox:checked + .insurance-label:before{
    color: $clr-primary;
}
.h-100{height:100%}
.max-width-250px{max-width: 250px}
.js_sticky.sticked .carRatingsFilter{
    max-height: 98vh;
    overflow: auto;
}
.carRatingsFilter{
    .dropdown-toggle{font-family: GothamBlackRegular, bahij-janna, sans-serif;}

    .filter_heading{
        @include bidi-padding-right(10px);
        @include bidi-border-right( 1px solid $clr-distinct-dark);

    }
    &.v3 .filter_heading{
        font-size: 1.4rem;
        border: 0;
        padding-right: 0;
        .carSrpFilter_clearFilterButton{
            font-size: 0.8rem;
        }
    }
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    border: 1px solid $clr-distinct-dark;
    border-radius: 20px;
    .disabled {
        color: $pager-disabled-color !important;
        cursor: $cursor-disabled;
    }
    .insurance-label{font-weight: bold; &:first-child{max-width: 260px}}

    &.v3{
        display: block;
        font-size: 1rem;
        .custom-collapsible-container{
            margin: 0 -15px;
            .collapsible-content{
                padding: 0 12px;
                background: white;
            }
            .bg-grey{
                background: transparent;
            }
            ul li label{ display: block;}
            .min-width-320px{min-width:100%}
            .carSrpFilters_onlyLink.d-none{
                opacity: 0;
                display: inline-block;
            }
            .insurance-label:hover .carSrpFilters_onlyLink{
                opacity: 1;
            }

            label {
                input[type=checkbox]{
                    ~ .insurance-label{
                        position: relative;
                    }
                    ~ .insurance-label:before{
                        content: "";
                        min-width: 33px;
                        max-width: 33px;
                        height: 22px;
                        top: 0px;
                        @include bidi-right(3px);
                        background: #ccc;
                        -webkit-border-radius: 34px;
                        -moz-border-radius: 34px;
                        border-radius: 34px;
                        -webkit-transition: .3s;
                        transition: .3s;
                    }
                    ~ .insurance-label:after{
                        position: absolute;
                        content: "";
                        height: 18px;
                        width: 18px;
                        @include bidi-left(3px);
                        -webkit-border-radius: 50%;
                        -moz-border-radius: 50%;
                        border-radius: 50%;
                        background-color: white;
                        -webkit-transition: .3s;
                        transition: .3s;
                    }
                }
                input[type=checkbox]:checked{
                    ~ .insurance-label:before{
                        background: $clr-primary;
                    }
                    ~ .insurance-label:after{
                        -webkit-transform: translateX(10px);
                        -ms-transform: translateX(10px);
                        transform: translateX(10px);
                    }
                }

            }




        }
    }

}
[dir=rtl] .custom-collapsible-container label .insurance-label:after{
    right: 13px !important;
}
[dir=rtl] .carRatingsFilter ul{
    padding-right: 0px;}
.hover-text-primary-child-span{
     span:hover{color: $clr-primary;}
}
.carSrpFilters_insurer_wrapper{
    > h4{
        font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
    }
    font-size: 1rem;
    -webkit-box-shadow: rgba(0, 0, 0, 0.2) 2px 2px 5px 5px;
    -moz-box-shadow: rgba(0, 0, 0, 0.2) 2px 2px 5px 5px;
    box-shadow: rgba(0, 0, 0, 0.2) 2px 2px 5px 5px;
}
.min-width-250px{min-width:250px}
.min-width-320px{min-width:320px}
.box-shadow-none{box-shadow: none !important;}
.box-shadow-none:hover{box-shadow: none !important;}
.bg-none{background:none !important;}
.w-auto{width:auto}; .d-flex-imp{display:flex !important;}.w-auto-imp{width:auto !important;};
.flex-spacer {flex-grow: 1;}
.d-none{display: none}
.in-height-170px{min-height: 170px}
.insurance-label:hover .carSrpFilters_onlyLink,.insurance-label:hover .carSrpFilters_type_onlyLink{display: inline-block; opacity: 1;}
.carSrpFilters_onlyLink{min-width: 34px}
.carSrpFilters_onlyLink:hover{color: $clr-primary-highlight !important;}
.hover-text-primary:hover{color:$clr-primary}
.mr-10{margin-right:10px}
.mr-5{margin-right:5px}
.ml-0{ margin-left:0px }
.ml-5{ margin-left:5px }
.ml-10{ margin-left:10px }
.ml-10-imp{ margin-left:10px !important;}
.ml-0-imp{ margin-left:0px !important;}
.px-15{
    padding-left: 15px;
    padding-right: 15px;
}
.mx--15{
    margin-left: -15px;
    margin-right: -15px;
}
.pr-25{padding-right:25px !important;}
.pr-10{padding-right:10px;}
.py-5{padding-top:5px;padding-bottom:5px}

.d-inline-block{display:inline-block};
.d-block{display:block}
.d-inline-block-imp{display:inline-block !important;}
.position-fixed{position: fixed}
.border-radius-50{border-radius: 50%};
.hover-bg-primary:hover{background-color: $clr-primary}
.overflow-x-unset{overflow-x: unset}
.filter_trigger_fixed{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 52px;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    .filterTriggerIcon{
        padding: 20px 22px;
        padding-top: 12px;
        .iconWrapper{
            font-size: 1.6rem;
        }
    }
}
.filterTriggerIcon{
    color: $clr-dark-grey-text;
    &:hover{
        color: white;
    }

    padding: 6px 18px 9px;
    .iconWrapper{
        font-size: 1.5rem;
    }
    small{
        font-size: 0.9rem;
        font-weight: bold;
        margin-top: -10px;
    }
}
.height-90p{height: 90%}
.height-35px{height: 35px}
.flex-column{flex-direction: column}
.font-size-0p7rem{font-size: 0.7rem}
.max-width-124px{max-width: 124px;}
.bg-gradient {
    background: -moz-linear-gradient(left, rgba($clr-accent,0) 0%, rgba($clr-accent, 0.01) 50%, rgba($clr-accent, 0.1) 95%, rgba($clr-accent, 0.15) 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(left, rgba($clr-accent,0) 0%,rgba($clr-accent, 0.01) 50%, rgba($clr-accent, 0.1) 95%, rgba($clr-accent, 0.15) 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to right, rgba($clr-accent ,0) 0%,rgba($clr-accent, 0.01) 50%, rgba($clr-accent, 0.1) 95%, rgba($clr-accent, 0.15) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='white', endColorstr=$clr-accent,GradientType=1 ); /* IE6-9 */
}
[dir="ltr"] .flex-direction-bidi-row{ flex-direction: row;};
[dir="rtl"] .flex-direction-bidi-row{ flex-direction: row-reverse;}
.d-md-down-none{
    @media screen and (max-width:$screen-md-max) {
        display: none;
    }
}
.mobileFiltersSheetBottomLay .carSrpFilters_onlyLink{display: inline-block !important;
    opacity: 1 !important;}
.bg-grey{background-color: $clr-distinct}
.d-md-up-none{
    @media screen and (min-width:1200px) {
        display: none;
    }
}
.line-height-2em{line-height: 2em}
.flex-grow-1-basis-0{flex-grow: 1; flex-basis: 0}
.driverExitIntentSheetBottomLay{
    position: fixed;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    right: 0;
    bottom: 0;
    z-index: 2;
    display: none;
    .relativeWrapper{
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
        .ImgWrapper{
            height: auto;
            border: 2px solid $clr-accent;
            display: flex;
            justify-content: center;
            align-items: center;
            max-width: 550px;
        }
        .overlay{
            width: auto;
            background: white;
            //overflow: auto;
            .header{
                -webkit-box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.75);
                -moz-box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.75);
                box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.75);
                font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                padding: 8px 5px;
                font-size: 1.4rem;
            }
            .canScroll{overflow: auto};
            label{width:100%}
        }
    }
}
.mobileFiltersSheetBottomLay{
    position: fixed;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    right: 0;
    bottom: 0;
    z-index: 2;
    display: none;
    .relativeWrapper{
        width: 100%;
        height: 100%;
        position: relative;
        bottom: -200vh;
        .overlay{
            position: absolute;
            bottom: 0;
            right: 0;
            left: 0;
            height: 60vh;
            width: 100%;
            background: white;
            overflow: auto;
            .header{
                -webkit-box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.75);
                -moz-box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.75);
                box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.75);
                font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
                padding: 8px 5px;
                font-size: 1.4rem;
            }
            .canScroll{overflow: auto};
            label{width:100%}
        }
    }
}

.quickFilterWrapper{
    .container{
        position: relative;
        z-index: 3;
    }
    .quickFilter{
        align-items: center;
        @media screen and (max-width:$screen-sm-min) {
            flex-direction: column;
            align-items: initial;
            .rightSelection{
                margin-left: 0px;
            }
        }
    }
     .leftHeading{
         font-size: 1.5rem;
         @media screen and (max-width:$screen-xs-max) {
             font-size: 1rem;
         }
     }
    .rightSelection{
        font-size: 1.6rem;
        @media screen and (max-width:$screen-xs-max) {
            font-size: 1.2rem;
        }
        margin-left: 10px;
        font-weight: bold;
        .selector{
            border-bottom: 1px solid white;
            &:hover{
                color: rgba(255,255,255,0.8);
            };

        }
        .dropdown.open .selector{
            color: rgba(255,255,255,0.8);
        }
        .dropdown-menu{
            z-index: 2;
            width: 100%;
            max-height: 305px;
            overflow: auto;
            li div{
                color: $clr-primary;
                font-size: 1.05rem;
                margin:12px auto;
                border-bottom: 1px solid transparent;
                padding-bottom: 2px;
                &:hover{
                    border-bottom: 1px solid $clr-primary;
                }
            }
        }
    }
}

[dir=rtl] .dropdown
{
    .dropdown-menu{
        right: 0;
        left: unset;

        li > label{
            text-align: right;
        }

    }
}

.custom-switch-checkbox {
    position: relative;
    display: inline-block;

    height: 24px;

    input {
        opacity: 0;
        width: 0;
        height: 0;
        &:checked + .slider {
            background-color: $clr-primary;
        }
        &:checked + .slider {
            background-color: $clr-primary;
        }

        &:focus + .slider {
            box-shadow: 0 0 2px $clr-primary;
        }

        &:checked + .slider:before {
            -webkit-transform: translateX(15px);
            -ms-transform: translateX(15px);
            transform: translateX(15px);
        }
    }
    &.checkbox-right{
        display: flex;
        .slider{
            @include bidi-left(unset);
        }
    }

    .slider {
        width: 40px;
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
        &.round{
            border-radius: 34px;
        }
        &.round:before {
            border-radius: 50%;
        }
    }
    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 4px;
        bottom: 3px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }
}

/* Ripple effect */
.accent-ripple {
    background-position: center;
    transition: background 0.8s;
}
.accent-ripple:hover {
    background: $clr-accent radial-gradient(circle, transparent 1%, $clr-accent 1%) center/15000%;
}
.accent-ripple:active {
    background-color: lighten($clr-accent, 20);
    background-size: 100%;
    transition: background 0s;
}
.btn-v3-outline{
    background: transparent;
    outline: none;
    //-webkit-box-shadow: 0 0 12px 0 rgba(0,0,0,.08);
    //-moz-box-shadow: 0 0 12px 0 rgba(0,0,0,.08);
    //box-shadow: 0 0 12px 0 rgba(0,0,0,.08);
    -webkit-transition: all 200ms;
    -moz-transition: all 200ms;
    -ms-transition: all 200ms;
    -o-transition: all 200ms;
    transition: all 200ms;
    font-weight: 600;
    &.btn-v3-outline-accent{
        border: 1px solid  rgba(#ccc, 0.9)  ;
        border-bottom-width: 3px;
        &.grey-border-inactive{
            border-color: #ccc;
        }

        color: $clr-dark-grey-text;
        &:hover{
            border-color: $clr-accent;
            color: $clr-accent;

        }
        &.active{
            background-color: $clr-accent;
            color: white;
            &[data-repairBy="maker"]{
                background: linear-gradient(to right, $clr-primary 10%, $clr-accent 100%);
            }
        }
    }
    &:hover{
        -webkit-box-shadow: 0 1px 3px 0 rgba(0,0,0,0.3);
        -moz-box-shadow: 0 1px 3px 0 rgba(0,0,0,0.3);
        box-shadow: 0 1px 3px 0 rgba(0,0,0,0.3);
    }
}
.bg-primary-light{
    background: rgba($clr-primary,0.04);
}
.small-text{
    font-size: 0.8rem;
    line-height: 1.1rem;
}
.button-image-width{

    max-width: 60px;
}
.centered-flex-box{
    display: flex;
    justify-content: center;
    align-items: center;
}


