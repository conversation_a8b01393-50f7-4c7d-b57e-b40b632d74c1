.homepageBanner {
    // All screen sizes
    font-size: 10px;
    line-height: 1.2;
    text-align: center;
    letter-spacing: 0.05em;
    padding: 20px 0;

    // Laptops and bigger
    @media only screen and (min-width: $screen-md-min) {
        @include bidi-text-align(left);
        @include bidi-padding(20px, 0, 37%, 0);

        padding: 20px 0 155px 0;
        font-size: 12px;
        height: 445px;
        max-width: 850px;
        margin: 0 auto;
    }
}

.homepageBanner__title {
    font-family: $font-fam-attention;
    font-size: 3.4em;
    line-height: 1.2;
    margin: 0 0 10px;
    padding: 0;
    text-align: center;

    @media only screen and (min-width: $screen-md-min) {
        font-size: 4.2em;
    }
}

.homepageBanner__text {
    font-size: 1.6em;
    line-height: 1.2;
    margin: 0;
    padding: 0;
    text-align: center;
}

//.best_price__banner {
//    background: url('png/best-price-guaranteed.png');
//    background-repeat: no-repeat;
//    background-size: contain;
//    background-position: center;
//    margin: 0rem 0 1rem 0;
//    @media screen and (max-width: 768px) {
//        height: 50px;
//    }
//
//    @media only screen and (max-width: $screen-sm-max) {
//        height: 80px;
//        margin-top: 1rem;
//        margin-left: 10px;
//        margin-right: 10px;
//    }
//
//    @media only screen and (min-width: $screen-md-min) {
//        height: 140px;
//    }
//
//    @media only screen and (min-width: $screen-lg-min) {
//        height:150px;
//    }
//}

.bestPriceBanner {
    background-color: $clr-primary;
    color: white;
    padding: 0 1rem;
    text-align: center;
    overflow: auto;
}

.bestPriceBanner__header {
    margin: 1rem 0;
    font-family: $font-fam-attention;
    font-size: 4vw;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.2rem;
    }
}

.bestPriceBanner__body {
    margin: 1rem 0;
    font-size: 4vw;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.2rem;
    }
}

.bestPriceBanner__hashtag {
    margin: 1rem 0;
    font-family: $font-fam-attention;
    font-size: 4vw;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.2rem;
    }
}

.promo__text {
    color: #444;
    padding: 0 1rem;
}

.promo-list {
    padding: 0;
    margin: 0 auto 3rem;
    list-style: none;
    font-size: 4.2vw;

    @media screen and (min-width: $screen-sm) {
        font-size: 1.2rem;
    }

    li {
        @include bidi-padding(0, 0, 0, 2rem);
        margin: 0 auto 1rem;
        position: relative;

        &:before {
            @include bidi-left(0);
            @extend .insurance-icon-round-done-button:before;
            color: $clr-primary;
            position: absolute;
            top: .1rem;
        }
    }
}

.giveaway-row {

    @media (min-width: $screen-sm + 1) {
        height: 20rem;
        .giveaway-info {
            padding: 2rem 0;
        }

        .giveaway-img {
            height: 20rem;
        }
    }

    .giveaway-img {
        @media (max-width: $screen-sm) {
            height: 15rem;
        }
        box-shadow: 0 0 5px -1px;
        display: block;
        margin: 0 auto;

        &:hover {

        }
    }
}
