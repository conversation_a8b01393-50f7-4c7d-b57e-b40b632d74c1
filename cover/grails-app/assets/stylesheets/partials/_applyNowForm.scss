.applyNowForm {
    background-color: #f9f9f9;
    text-align: center;
    overflow: auto;

    h2.applyNowForm__heading {
        font-family: $font-fam-attention;
        background-color: $clr-distinct-light;
        color: $clr-primary;
        font-size: 12px;
        padding: 0 15px;
        line-height: 3em;
        margin: 0;
    }

    form.applyNowForm__form {
        background-color: #fff;
        padding: 0 15px;
    }

    .applyNowForm__input, .applyNowForm__input + .select2 {
        display: block;
        margin: .75em auto;
    }

    .applyNowForm__link, .applyNowForm__link:hover {
        font-size: 12px;
        margin: 1em auto;
        display: block;
        text-decoration: none;
    }

    //large phones
    @media screen and (min-width: 360px) {
        h2.applyNowForm__heading {
            font-size: 14px;
        }
    }

    //phablets
    @media screen and (min-width: 414px) {
        h2.applyNowForm__heading {
            font-size: 16px;
        }
    }

    @media screen and (min-width: 768px) {
        margin: 0 auto;
        min-height: 317px;
    }

    @media screen and (min-width: 992px) {
        max-width: 29rem;
    }
}
