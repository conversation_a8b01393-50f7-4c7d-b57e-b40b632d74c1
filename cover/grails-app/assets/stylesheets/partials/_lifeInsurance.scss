.form-group input[disabled]{
    cursor: not-allowed;
    color: #333333;
}

.bg-primary{
    background-color: $clr-primary !important;
}
.text-primary{
    color: $clr-primary !important;
}
.text-primary-darken{
    color: darken($clr-primary, 15);
}

.bg-accent{
    background-color: $clr-accent !important;
}
.text-accent{
    color: $clr-accent !important;
}
.text-dark{color: $clr-dark}
.bg-white{
     background-color: white !important;
 }
.text-white{
    color: white !important;
}
.text-grey{color:$clr-dark-grey-text}
[hidden]{display:none}

//utils
.pointer:hover{
    cursor: pointer;
}
.font-weight-bold{font-weight:bold}
.font-weight-normal{font-weight:normal}


// spacing
.mt-0{margin-top:0}
.mt-0-imp{margin-top:0!important;}
.mt-5{margin-top:5px}
.mt-10{margin-top:10px}
.mt-20{margin-top:20px}
.mt-30{margin-top:30px}

.mb-0{margin-bottom:0}
.mb-0-imp{margin-bottom:0 !important}
.mb-3{margin-bottom:3px}
.mb-5{margin-bottom:5px}
.mb-10{margin-bottom:10px}
.mb-15{margin-bottom:15px}
.mb-20{margin-bottom:20px}
.mb-30{margin-bottom:30px}
.mb-70{margin-bottom:70px}
.mb-sm-0-imp{
    @media screen  and (max-width:$screen-md-min) {
        margin-bottom: 0 !important;
    }
}

.my-5{margin-top: 5px !important;margin-bottom: 5px !important;}
.my-10{margin-top: 10px !important;margin-bottom: 10px !important;}
.my-20{margin-top: 20px !important;margin-bottom: 20px !important;}
.my-30{margin-top: 30px !important;margin-bottom: 30px !important;}

.mx-15{margin-left: 15px;margin-right: 15px}

//padding
.pt-0{padding-top:0}
.pt-2{padding-top:2px}
.pt-3{padding-top:3px}
.pt-5{padding-top:5px}
.pt-10{padding-top:10px}
.pt-20{padding-top:20px}
.pt-30{padding-top:30px}
.pt-30-imp{padding-top:30px !important;}

.pb-0{padding-bottom:0 !important;}
.pb-5{padding-bottom:5px}
.pb-10{padding-bottom:10px}
.pb-20{padding-bottom:20px}
.pb-30{padding-bottom:30px}

.pl-0{padding-left: 0 !important;}
.pl-10{padding-left: 10px !important;}

.pr-20{padding-right:20px}

.p-0{padding:0 !important;}
.p-5{padding:5px} .p-10{padding:10px} .p-8{padding:8px}

.py-15{padding-top: 15px;padding-bottom: 15px;}
.py-20{padding-top: 20px !important;padding-bottom: 20px !important;}
.py-30{padding-top: 30px !important;padding-bottom: 30px !important;}
.py-40{padding-top: 40px !important;padding-bottom: 40px !important;}
.py-10{padding-top: 10px !important;padding-bottom: 10px !important;}
.px-10{padding-left: 10px !important;padding-right: 10px !important;}


//flex utils
.d-flex{display:flex}
.justify-content-center{justify-content: center}  .align-items-center{align-items: center}
.justify-content-between{justify-content: space-between}  .align-items-end{align-items: end}
.justify-content-stretch{justify-content: stretch}  .align-items-stretch{align-items: stretch}
.flex-sm-column{
    @media screen and (max-width: $screen-sm-max) {
        flex-direction: column;
    }
}
.shareValueIncorrect{
    width:auto;
    position: absolute;
    font-size: 10px;
    font-family: "proxima-nova", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
    white-space: nowrap;
    visibility: hidden;
    bottom: 62px;
    color: white;
    padding: 0 5px;
    text-align: center;
    background-color: red;
    opacity: .7;}

//fonts
.headerText__heading{
    @media only screen and (min-width: $screen-lg-min){
        font-size:1.5rem;
        text-align: center;
    }
}
.addBeneficiaryButton{
    display:inline-block;
    line-height: 1;
    transform: rotate(45deg);
    color: $clr-primary;
    font-size: 4rem;

    &:hover {
        color: $clr-primary-intense;
        cursor: pointer;
    }
}
.text-accent-hover:hover{color: $clr-accent;}
.font-size-1p1rem{font-size:1.1rem}
.font-size-1p3rem{font-size:1.3rem}
.overflow-auto{
    overflow: auto;}

.font-size-1p5rem{font-size:1.5rem}
.font-size-2p1rem{font-size:2.1rem}
.font-family-gotham{font-family: GothamBlackRegular;}
.font-familty-proxima{font-family: proxima-nova, bahij-janna, arial, sans-serif}

//border-bottom
.border-bottom-1{border-bottom:1px solid $clr-distinct}
.border-top-1{border-top:1px solid $clr-distinct}

.overflow-hidden{overflow:hidden}

.h-35p{height:35px}
.width-25P{width:25%} .width-45P{width:45%} .width-30P{width:30%} .w-100{width:100%}
.mw-170p{max-width: 170px;} .minw-170p{min-width:170px} .maxw-200px{max-width:200px}

.benefitBorder-md-left-sm-top{
    @media only screen and (min-width: $screen-md-min){
        border-left: 1px solid $clr-distinct;
    }
    @media only screen and (max-width: $screen-sm-max){
        border-top: 1px solid $clr-distinct;
    }
}


.borderless td, .borderless th {
    border: none !important;
}

.slidersForOptions{
    width:100%
}
.hide{
    display: none;
}

//slick slider style override
.slickContainer {
    &.slick-dotted.slick-slider {
        margin-bottom: 0;
        //padding-bottom: 40px;
    }

    //.slick-dots {
    //    bottom: 10px;
    //}

    .slick-prev {
        @include bidi-left(5px);
        z-index: 100;
    }

    .slick-next {
        @include bidi-right(5px);
        z-index: 100;
    }

    .slick-dots li button:before {
        color: grey;
        font-size: 10px;
    }
    .slick-prev:before{
        color:$clr-accent
    }
    .slick-next:before{
        color:$clr-accent
    }

}
.noOutlineNoBorder{
    outline: none !important;
    border: none !important;
}
.position-relative{position:relative};.position-absolute{position:absolute}
.t0r0{top:0;right: 0}
.r0b0l0{right:0;left:0;bottom: 0}
.r0b0t0{right:0;bottom: 0;top:0}

.insudeBoxShadow {
    position:relative;
    overflow:hidden;
    border-top:1px solid #ddd;
    border-bottom:1px solid #ddd;
}
.insudeBoxShadow:before {
    content: "";
    position:absolute;
    z-index: 1;
    width:96%;
    top: -10px;
    height: 10px;
    left: 2%;
    border-radius: 100px / 5px;
    box-shadow:0 0 18px rgba(0,0,0,0.6);
}
.insudeBoxShadow:after {
    content: "";
    position:absolute;
    z-index: 1;
    width:96%;
    bottom: -10px;
    height: 10px;
    left: 2%;
    border-radius: 100px / 5px;
    box-shadow:0 0 18px rgba(0,0,0,0.6);
}


.custom-collapsible-container{

    .custom-collapsible {
        cursor: pointer;

    }

    .active, .custom-collapsible:hover {
    //    add here something to show collapsable is active
    }

    .collapsible-content {
        padding: 0 18px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
        background-color: #f1f1f1;
    }
}

.customLifeForm{
    .dropzone{
        margin: 2rem auto 1.5rem;
        text-align: center;
        max-width: 560px;
        position: relative;
        min-height: 150px;
        border: 2px dashed #E2156B;

        .docLabel{
            position: absolute;
            left: 0.2rem;
            top: -12px;
            background-color: white;
            padding: 0 0.6rem;
            margin: 0;
        }
        #passport{
            width: 100%;
            height: 100%;
        }
    }
}
[dir="ltr"] .b47pr0-bidi{
    bottom: 47px;
    right: 0;
}
[dir="rtl"] .b47pr0-bidi{
     bottom: 47px;
     left: 0;
 }
.oneBeneficiary{
    background-color: rgba($clr-primary, 0.1);
    padding: 15px
}

.brace {
    width: auto;
    min-width: 35px;
    padding-bottom: 20px;
    font-size: 2em;
    line-height: 2em;
    position: relative;
    text-align: center;
    vertical-align: middle;
    margin: 0 15px 15px;
    border: none;
    background-color: transparent;
    background-image: radial-gradient(circle at 0 0, rgba(255,255,255,0) 14.5px, $clr-accent 15.5px, $clr-accent 19.5px, rgba(255,255,255,0) 20.5px), radial-gradient(circle at 35px 0, rgba(255,255,255,0) 14.5px, $clr-accent 15.5px, $clr-accent 19.5px, rgba(255,255,255,0) 20.5px);
    background-size: 35px 20px;
    background-position: center bottom;
    background-repeat: no-repeat;
    text-transform: lowercase;
    font-style: italic;
    color: #fff;
    -webkit-filter: drop-shadow(0 1px 1px rgba(0,0,0,0.15));
    filter: drop-shadow(0 1px 1px rgba(0,0,0,0.15));
    overflow: visible;
}
.brace:before {
    width: 50%;
    border-top: 5px solid $clr-accent;
    border-left: 1px solid transparent; /* play with this until you like the look of it */
    border-top-left-radius: 20% 30px;
    height: 100%;
    content: "";
    position: absolute;
    top: 100%;
    left: -15px;
    box-sizing: border-box;
    margin-top: -5px;
}
.brace:after {
    width: 50%;
    border-top: 5px solid $clr-accent;
    border-right: 1px solid transparent; /* play with this until you like the look of it */
    border-top-right-radius: 20% 30px;
    height: 100%;
    content: "";
    position: absolute;
    top: 100%;
    right: -15px;
    box-sizing: border-box;
    margin-top: -5px;
}

.line-height-1em{
    line-height: 1em;
}

.min-height-160px{min-height: 160px}

.gradianText-accent-primary{
    background:linear-gradient(to right, $clr-accent 10%, $clr-primary 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}


@keyframes bobbingAnim {
    0% {
        transform: translate(0px, 0px);
        animation-timing-function:ease-in-out
    }

    50% {
        transform: translate(0px, 8px);
        animation-timing-function:ease-in-out
    }

    100% {
        transform: translate(0px, 0px);
        animation-timing-function:ease-in-out
    }
}

@-webkit-keyframes bobbingAnim {
    0% {
        -webkit-transform: translate(0px, 0px);
        -webkit-animation-timing-function:ease-in-out
    }

    50% {
        -webkit-transform: translate(0px, 8px);
        -webkit-animation-timing-function:ease-in-out
    }

    100% {
        -webkit-transform: translate(0px, 0px);
        -webkit-animation-timing-function:ease-in-out
    }
}
