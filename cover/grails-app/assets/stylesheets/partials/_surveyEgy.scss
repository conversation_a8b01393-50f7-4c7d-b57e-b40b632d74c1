.survey-header{
    //font-family: GothamBlackRegular, sans-serif;
    //font-size: 22px;
}
.survey-egy{
    padding: 0;
    margin-right: -15px;
    select{
        margin-top: 5px;
        margin-bottom: 10px;
        cursor: pointer;
    }
    .datePicker {
        @media screen and (max-width: 1000px) {
            margin: 0 -15px 0 -7px;
        }
        @media screen and (max-width: 600px) {
            margin: 0 0 0 -7px;
        }
        select:nth-last-child(3){
            width: calc(23.4% - 7px) !important;
            @media screen and (max-width: 1200px) {
                width: calc(23.4% - 15px) !important;
            }
            @media screen and (max-width: 600px) {
                width: calc(23.4% - 23px) !important;
            }
        }
        select:nth-last-child(2){
            width: 46.8% !important;
        }
        select:nth-last-child(1) {
            width: calc(23.4% - 8px) !important;
        }
    }
    button{
        margin-top: 10px;
        @media screen and (max-width: 1000px) {
            width: calc(100% - 15px) !important;
        }
    }
}
.thankyou-survey-section{
    text-align: center;
    .heading {
        font-size: 22px;
        font-weight: 600;
        color: #36397D;
    }
}
[dir="rtl"] {

    .survey-header {
        margin-right: -30px;
    }
    @media screen and (max-width: 1000px) {
        .survey-header {
            margin-right: 0;
        }
        .survey-egy {
            margin-right: 15px;

            button{
                width: calc( 100% + 15px) !important;
                margin-right: -15px;
            }
        }
    }

}
