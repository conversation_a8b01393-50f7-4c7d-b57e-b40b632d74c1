.carFormAd {
    border: 1px solid $clr-primary;
    @include bidi-margin(1rem, 32px, 1rem, 0);
    -webkit-box-shadow: 3px 3px 3px 0 rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 3px 3px 3px 0 rgba(0, 0, 0, 0.5);
    box-shadow: 3px 3px 3px 0 rgba(0, 0, 0, 0.5);
    display: none;

    @media screen and (min-width: $screen-md) {
        width: 270px;
        position: absolute;
        right: calc(-20rem);
        @include bidi-margin(0, 0, 0, 0);
    }

    .carFormAd__header {
        padding: .5rem 1rem;
        font-family: $font-fam-attention;
        color: $clr-primary;
        text-align: center;

        strong {
            color: $clr-accent;
        }
    }

    .carFormAd__body img {
        max-width: 100%;
    }

    .carFormAd__footer {
        display: flex;
        align-items: center;
        padding: .5rem 1rem;
        background-color: $clr-primary;
        color: white;

        .carFormAd__footer__text {
            width: 80%;
        }

        .carFormAd__footer__cta {
            width: 20%;
            @include bidi-text-align(right);

            .carQuote__addon {
                position: static;
                top: auto;
                font-size: inherit !important;
                transform: none;
                right: auto !important;
                left: auto !important;
                display: inline-block;

                .insurance-label:before {
                    -webkit-transform: scale(2);
                    -moz-transform: scale(2);
                    -ms-transform: scale(2);
                    -o-transform: scale(2);
                    transform: scale(2);
                    color: white !important;
                }
            }
        }
    }
}
