// BLOCK
.noticeBox {
    border: 0 solid white;
    padding: 0;
    height: 0;
    overflow: hidden;
    color: $clr-accent;
    font-weight: 900;
    line-height: 1.4em;
    letter-spacing: 0.03em;
}

// --modifiers
.noticeBox--visible {
    padding: 10px;
    border: 1px solid $clr-accent;
    height: auto;
}

// __ELEMENT
.noticeBox__title {
    font-family: $font-fam-attention;
    font-size: 12px;
    margin: 0 0 10px;
}

// __ELEMENT
.noticeBox__message {
    line-height: 1.4em;
    font-size: 12px;
}
