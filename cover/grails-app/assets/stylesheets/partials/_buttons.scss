%button {
    width: 100%;
    font-family: $font-fam-default;
    font-size: 16px;
    border: none;
    color: inherit;
    background: none;
    cursor: pointer;
    display: inline-block;
    letter-spacing: 1px;
    outline: none;
    position: relative;
    -webkit-transition: none;
    -moz-transition: none;
    transition: none;
    margin: 0;
    padding: 9px 0;
    border-radius: 0;
    text-transform: capitalize;
    font-weight: 600;
    top: -5px;
    text-align: center;
}

@mixin create-button($background-color, $text-color) {
    @extend %button;

    $shadow-color: transformColor($background-color, 0.15172deg, -3.78806%, -16.47059%);

    background-color: $background-color;
    box-shadow: 0 3px $shadow-color;
    color: $text-color;
    border: 1px solid $background-color;

    &:active {
        box-shadow: 0 0 $shadow-color;
        top: -2px;
        color: $text-color;
        text-decoration: none;
    }

    &:hover:not(:active), &:focus:not(:active) {
        box-shadow: 0 2px $shadow-color;
        top: -4px;
        color: $text-color;
        text-decoration: none;
    }

    &.inverted {
        color: $background-color;
        background-color: $text-color;

        &:active,
        &:hover {
            color: $background-color;
        }
    }
}

.button-accent {
    @include create-button($clr-accent, $clr-default);
}

.button-whatsapp {
    @include create-button(#25D366, white);
    display: block;
    @media screen and (min-width: $screen-sm) {
        margin: 5rem auto 1rem;
        display: none;
    }
    @media screen and (min-width: $screen-xs) {
        margin: 5rem auto 1rem;
        display: none;
    }
}

.button--healthQuote {
    font-size: 12px;

    @media screen and (min-width: 992px) {
        font-size: 16px;
    }
}

.button-primary {
    @include create-button($clr-primary, $clr-default);
}

.button-accent[disabled],
.button-primary[disabled] {
    @include create-button($clr-distinct, $clr-default);
}

.button {
    line-height: 3rem;
    text-align: center;
    cursor: pointer;
    padding: 0 2em;

    //modifier
    &--ghost {
        background-color: transparent;
        border-style: solid;
        border-width: 1px;
    }

    //modifier
    &--whiteGhost,
    &--whiteGhost:hover,
    &--whiteGhost:focus,
    &--whiteGhost:active {
        border-color: #fff;
        color: #fff;
        text-decoration: none;
    }

    //modifier
    &--greyGhost,
    &--greyGhost:hover,
    &--greyGhost:focus,
    &--greyGhost:active {
        border-color: $clr-distinct-dark;
        color: $clr-distinct-dark;
        text-decoration: none;
    }

    //modifier
    &--hidden {
        display: none !important;
    }
}
