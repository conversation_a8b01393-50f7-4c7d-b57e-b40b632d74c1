.promo-box,
.promo-box:hover,
.promo-box:visited,
.promo-box:focus {
  border-style: solid;
  border-width: 1px;
  position: relative;
  font-size: 16px;
  padding: 36px 28px 0;
  margin: 38px 0 13px;
  display: block;
  color: inherit;
  text-decoration: none;

  .section-heading-small {
    text-transform: none;
    line-height: 24px;
    letter-spacing: 0;
    font-family: $font-fam-default;
    margin: 0 0 12px;

    @media only screen and (max-width: $screen-sm-max) {
      text-align: center;
    }

    @media only screen and (min-width: $screen-md-min) {
      @include bidi-text-align(left);
      height: 48px;
    }
  }

  p {
    font-size: 15px;
    line-height: 22px;

    @media only screen and (max-width: $screen-sm-max) {
      text-align: center;
    }

    @media only screen and (min-width: $screen-md-min) and (max-width: $screen-md-max) {
      height: 180px
    }

    @media only screen and (min-width: $screen-md-min) {
      @include bidi-text-align(left);
    }

    @media only screen and (min-width: $screen-lg-min) {
      height: 154px;
    }
  }

  .link, .link:hover {
    color: inherit;
    text-decoration: underline;
    font-size: 15px;
    margin: 0 0 19px 0;

    @media only screen and (max-width: $screen-sm-max) {
      display: block;
      text-align: center;
    }

    @media only screen and (min-width: $screen-md-min) {
      display: inline-block;
    }
  }
}

.promo-box {
  border-color: $clr-primary-highlight;
  will-change: box-shadow, border-color;
  -webkit-transition: all .25s;
  -moz-transition: all .25s;
  -ms-transition: all .25s;
  -o-transition: all .25s;
  transition: all .25s;
}

.promo-box:hover {
  border-color: $clr-default;
}

.pink-tick-promo {
  @extend .promo-box;

  i {
    position: absolute;
    top: 0;
    @include bidi-left(50%);
    @include bidi-translate(-50%, -50%);
    //background-image: url(https://assets.yallacompare.com/insurance/images/pink-tick.png);
    height: 47px;
    width: 47px;
    background-size: cover;
    background-position: center;
    background-color: $clr-primary;

      &:before {
          @extend .insurance-icon-round-done-button:before;
          font-size: 2.2rem;
          color: $clr-primary-intense;
          position: absolute;
          @include bidi-left(0);
          top: 50%;
          transform: translateY(-50%);
      }
  }
}

.blue-icons {
    $num-items: 3;
    $background-width: $num-items * 100%;
    $item-width: 100% / ($num-items - 1);

    .blue-icon {
        width: 72px;
        margin: 0 auto 20px;

        &:after {
            background-image: $path-spritesheet-blue-icons;
            background-repeat: no-repeat;
            background-size: $background-width 100%;
            content: "";
            display: block;
            padding: 0 0 100%;
        }

        &.cog:after {
            background-position: left ($item-width * 0) center;
        }

        &.cart:after {
            background-position: left ($item-width * 1) center;
        }

        &.diploma:after {
            background-position: left ($item-width * 2) center;
        }
    }
}
