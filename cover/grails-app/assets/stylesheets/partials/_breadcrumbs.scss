.breadcrumbs {
    list-style: none;
    padding: 0;
    margin: 1rem auto 0;
    text-align: center;
    font-size: 3.6vw;

    @media screen and (min-width: $screen-sm) {
        font-size: 1rem;
    }

    @media screen and (min-width: $screen-md) {
        text-align: initial;
    }

    .breadcrumbs__crumb {
        display: inline-block;
        color: #999999;
        font-weight: 900;
        pointer-events: none;

        &:not(:first-child) {
            @include bidi-margin(0, 0, 0, .5rem);
        }

        &.breadcrumbs__crumb--visited {
            color: $clr-primary;
            text-decoration: underline;
            pointer-events: initial;
        }

        &.breadcrumbs__crumb--active {
            //color: $clr-accent;
        }

        a {
            color: inherit;
            text-decoration: inherit;
        }
    }
}




//.breadcrumbs {
//    $crumb-width: 175px;
//
//    @media only screen and (max-width: $screen-sm-max) {
//        margin: 10px auto;
//        padding: 0;
//        list-style-type: none;
//        width: 100%;
//        display: table;
//
//        .crumb {
//            counter-increment: step-counter;
//            display: table-cell;
//            font-size: 12px;
//            text-align: center;
//            width: 25%;
//            position: relative;
//
//            a {
//                display: block;
//                color: $clr-default;
//                text-transform: capitalize;
//                text-decoration: underline;
//            }
//
//            &:before {
//                background-color: $clr-primary-intense;
//                border: none;
//                height: 30px;
//                width: 31px;
//                padding: .2em .3em;
//                border-radius: 50%;
//                font-size: 18px;
//                content: '\e81c';
//                font-family: "insurance";
//                color: $clr-default;
//            }
//
//            &:after {
//                background-color: $clr-primary-intense;
//            }
//
//            &:not(:last-child):after {
//                @include bidi-left(78%);
//
//                content: '';
//                height: 3px;
//                width: 45%;
//                background-color: $clr-primary-intense;
//                display: inline-block;
//                position: absolute;
//                top: 30%;
//                //left: 78%;
//            }
//        }
//
//        .crumb.active {
//            a {
//                display: block;
//                color: $clr-default;
//                pointer-events: none;
//                text-decoration: none;
//                font-weight: 900;
//            }
//        }
//
//        .crumb.active:before {
//            background: $clr-primary-intense;
//        }
//
//        .crumb.active ~ .crumb:before {
//            background: none;
//        }
//
//        .crumb.active:before,
//        .crumb.active ~ .crumb:before {
//            font-family: $font-fam-default;
//            border: 2px solid $clr-default;
//            font-weight: bold;
//            padding: 3px 8px;
//            content: counter(step-counter);
//            color: $clr-default;
//            font-size: 16px;
//            display: inline-block;
//        }
//
//        .crumb.active ~ .crumb a {
//            pointer-events: none;
//            text-decoration: none;
//        }
//
//        .crumb.active:after,
//        .crumb.active ~ .crumb:after {
//            background-color: $clr-default;
//        }
//    }
//
//    @media only screen and (min-width: $screen-md-min) {
//        list-style: none;
//        overflow: hidden;
//        margin: 0;
//        padding: 0;
//
//        .crumb {
//            @include bidi-float(left);
//            line-height: 43px;
//            width: $crumb-width;
//            @include bidi-border-right(0);
//            background-color: $clr-accent-faded;
//
//            a {
//                @include bidi-padding(0, 41px, 0, 50px);
//                display: block;
//                @include bidi-float(left);
//                min-width: $crumb-width;
//                text-align: center;
//                color: $clr-default;
//                text-transform: capitalize;
//                text-decoration: underline;
//
//                &:before {
//                    @include bidi-float(right);
//                    display: block;
//                    @include bidi-margin(0, -53px, 0, auto);
//                    height: 21px;
//                    width: 20px;
//                    content: " ";
//                    background: $clr-accent-faded;
//                    @include bidi-border-right(3px solid $clr-primary-dull);
//                    @include bidi-skew(30deg);
//                }
//
//                &:after {
//                    position: relative;
//                    display: block;
//                    @include bidi-margin(-22px, -53px, 0, auto);
//                    height: 21px;
//                    width: 20px;
//                    content: " ";
//                    background: $clr-accent-faded;
//                    @include bidi-border-right(3px solid $clr-primary-dull);
//                    @include bidi-skew(-30deg);
//                }
//            }
//        }
//
//        .crumb.active,
//        .crumb.active a,
//        .crumb.active a:before,
//        .crumb.active a:after {
//            background-color: $clr-accent;
//            pointer-events: none;
//            text-decoration: none;
//            font-weight: 900;
//        }
//
//        .crumb.active ~ .crumb,
//        .crumb.active ~ .crumb a,
//        .crumb.active ~ .crumb a:before,
//        .crumb.active ~ .crumb a:after {
//            pointer-events: none;
//            cursor: default;
//            background-color: $clr-primary;
//            text-decoration: none;
//        }
//    }
//}
//
//[dir="rtl"] .breadcrumbs .crumb:nth-of-type(3) a {
//               padding: 0px 37px 0px 36px;
//           }
//
//[dir="rtl"] .breadcrumbs .crumb:nth-of-type(3) a:after{
//    margin: -22px auto 0 -50px;
//}
//
//[dir="rtl"] .breadcrumbs .crumb:nth-of-type(3) a:before{
//    margin: 0 auto 0 -50px;
//}
