$margin: 15px;
$padding: 15px;

%margin {
    margin: $margin 0;
}

%padding-horizontal {
    padding: 0 $padding;
}

%padding-vertical {
    padding: $padding 0;
}

.order-summary {
    background-color: $clr-distinct-light;
    border-top: 4px solid $clr-distinct;
    border-bottom: 6px solid $clr-distinct;

    .title{
        font-size: 20px;

        & > div{
            margin-top: 5px;
        }
    }
}
.order-summary__row{
    margin-top: 15px;
    margin-bottom: 15px;
}
.responsive-image {
    max-width: 100%;
    max-height: 100%;

    &--lg {
        max-height: 120px;
    }

    &--centered {
        display: block;
        margin: 0 auto;
    }
}

.grid {
    display: table;
    width: 100%;

    &--margin {
        @extend %margin;
    }

    &--padding-horizontal {
        @extend %padding-horizontal;
    }
}

.grid-row {
    display: table-row;
}

.grid-cell {
    display: table-cell;

    &--middle {
        vertical-align: middle;
    }

    &--half {
        width: 50%;
    }

    &--padding-horizontal {
        @extend %padding-horizontal;
    }
}

.content-block {
    &--margin {
        @extend %margin;
    }

    &--margin-xl {
        margin: 50px;
    }

    &--padding-horizontal {
        @extend %padding-horizontal;
    }
}

.text {
    &--regular {
        font-weight: 400;
    }

    &--bold {
        font-weight: 800;
    }

    &--clr-primary {
        color: $clr-primary;
    }

    &--clr-accent {
        color: $clr-accent;
    }

    &--right {
        @include bidi-text-align(right);
    }

    &--margin {
        @extend %margin;
    }

    &--padding-horizontal {
        @extend %padding-horizontal;
    }

    &--padding-vertical {
        @extend %padding-vertical;
    }
}
