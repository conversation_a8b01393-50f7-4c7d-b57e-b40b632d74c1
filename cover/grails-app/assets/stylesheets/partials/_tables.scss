.full-width {
    width: 100%;
}

.table {
    display: table;
}

.table--collapse-borders {
    border-collapse: collapse;
}

.table-row {
    display: table-row;
}

.table-cell {
    display: table-cell;
}

@media only screen and (min-width: $screen-md-min) {
    .table-md {
        display: table;
    }

    .table-row-md {
        display: table-row;
    }

    .table-cell-md {
        display: table-cell;
    }
}

//To hide columns on mobile
.table-cell {
    display: table-cell;

    &.hidden-mobile-portrait {
        @media only screen and (max-width: 460px) {
            display: none !important;
        }
    }

    &.hidden-mobile-landscape {
        @media only screen and (max-width: 668px) {
            display: none !important;
        }
    }

    &.hidden-tablet-portait {
        @media only screen and (max-width: $screen-sm-max) {
            display: none !important;
        }
    }

    &.hidden-tablet-landscape {
        @media only screen and (max-width: $screen-md-max) {
            display: none !important;
        }
    }
}

.middle {
    vertical-align: middle;
}

.top {
    vertical-align: top;
}

.thirty-seventy {
    display: table;
    width: 100%;

    >* {
        display: table-cell;

        &:first-child {
            width: 30%;
        }
        &:last-child {
            width: 70%;
        }
    }
}

@media only screen and (min-width: $screen-md-min) {
    .twenty-eighty-md {
        display: table;
        width: 100%;

        >* {
            display: table-cell;

            &:first-child {
                width: 20%;
            }
            &:last-child {
                width: 80%;
            }
        }
    }

    .fifty-fifty-md {
        display: table;
        width: 100%;

        >* {
            display: table-cell;

            &:first-child {
                width: 50%;
            }
            &:last-child {
                width: 50%;
            }
        }
    }
}
