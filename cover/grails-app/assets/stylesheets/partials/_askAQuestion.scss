.askAQuestion {
    //background-color: white;
    margin: 0;
    position: relative;
    //padding: 85px 15px 425px 15px;
    padding: 85px 15px 15px 15px;
    text-align: center;
    //background-image: url("phone_small.png");
    //background-position: bottom center;
    //background-size: 275px auto;
    //background-repeat: no-repeat;

    @media only screen and (min-width: $screen-sm-min) {
        background-color: white;
        margin: 55px 0 0 0;
    }

    &:after {
        position: absolute;
        font-family: $font-fam-insurance-icons;
        content: '\e824';
        top: 15px;
        left: 50%;
        line-height: 1;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%);
        transform: translateX(-50%);
        font-size: 60px;
        color: $clr-primary-faded;
    }
}

.askAQuestion__title {
    font-family: $font-fam-attention;
    color: $clr-primary-dirty;
    font-size: 20px;
    margin: 0 0 10px 0;
}

.askAQuestion__content {
    font-size: 14px;
    margin: 0 0 20px 0;
    text-align: justify;
}
