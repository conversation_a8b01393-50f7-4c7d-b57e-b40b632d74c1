.hostedInstallmentContainer {
    border: 1px solid lightgray;
    margin: 10px 0px;
}

.hostedInstallment {
    margin: 1rem 28px;
    display: block;
    height: 110px;
}

.hostedInstallment__plan {
    //display: none;
    opacity: .7;
    -webkit-transition: opacity .3s;
    -moz-transition: opacity .3s;
    -ms-transition: opacity .3s;
    -o-transition: opacity .3s;
    transition: opacity .3s;
    background: 0 0;
    border: 2px solid #e7e7e7;
    border-radius: 3px;
    cursor: pointer;
    width: 130px;
    float:left;
    margin: 5px 5px;
    padding: 5px;
}

.hostedInstallment__plan.active {
    opacity: 1;
    border-color:#36397D;
}

.hostedInstallment__plan.fullPayment {
    min-height: 96px;
}

.hostedInstallment__plan.fullPayment .full-payment {
    margin: 8px;
}

[dir="ltr"] .hostedInstallment__plan:first-of-type:not(.slick-slide) {
    display: block;
}

[dir="rtl"] .hostedInstallment__plan:last-of-type:not(.slick-slide) {
    display: block;
}

.hostedInstallment__plan.slick-active {
    display: block;
}

.hostedInstallment button:before {
    color: $clr-primary-dull;
}

.processingFeesMessage, .hostedInstallmentMessage {
    margin: 5px 10px;
}

.hostedInstallment {
    min-height: 110px;
}
.hostedInstallment__plan.fullPayment {
    min-height: 110px;
}

.hostedInstallment__plan {
    min-height: 110px;
}

@media only screen and (max-width: 480px) {
    .hostedInstallment {
        min-height: 140px;
    }
    .hostedInstallment__plan.fullPayment {
        min-height: 140px;
    }

    .hostedInstallment__plan {
        min-height: 140px;
    }
}

[dir="rtl"].hostedInstallmentContainer .balloon {
    width: 300px;
    right: -300px;

}

[dir="ltr"].hostedInstallmentContainer .balloon {
    width: 300px;
    left: -300px;
}

[dir="rtl"].hostedInstallmentContainer .hostedInstallmentMessage__info {
    float: right;
}

[dir="ltr"].hostedInstallmentContainer .hostedInstallmentMessage__info {
    float: left;
}

[dir="rtl"].hostedInstallmentContainer .show-balloon {
    float: left;
}

[dir="ltr"].hostedInstallmentContainer .show-balloon {
    float: right;
}
