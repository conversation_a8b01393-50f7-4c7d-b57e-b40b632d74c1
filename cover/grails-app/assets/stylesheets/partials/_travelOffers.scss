#travelFunnel {
    h1 {
        text-align: center;
        color: #444;
        font-size: 1.8rem;
        margin: 2.5rem auto;
        text-shadow: 1px 1px white;
        font-family: $font-fam-default;
        font-weight: 900;
        letter-spacing: 0.05em;
    }
    //
    //form.cardForm {
    //    background: transparent;
    //    -webkit-box-shadow: none;
    //    -moz-box-shadow: none;
    //    box-shadow: none;
    //}
    //
    form.newForm label {
        color: $clr-primary;
        //text-shadow: 1px 1px 0 rgba(0, 0, 0, .2);
    }

    form.newForm .radio-btn input+label {
        color: #555;
    }

    form.newForm .radio-btn input:checked+label {
        color: white;
    }


    .form-group {
        margin: 3rem auto;
    }
    //
    //.card__form_input, select.dropdown, .datePicker select, .select2-container .select2-selection--single, .textbox, .datebox, input.textbox-with-badge {
    //    border: none;
    //}
    //
    //.input-group-addon {
    //
    //    border: none !important;
    //}
}

.travelOffers {

}

.travelOffer {
    margin: 2rem auto;
    box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.4);
    background-color: white;
}

.travelOffer__message {
    text-align: center;
    color: red;
    font-weight: bold;
}

.travelOffer__summary {
    height: 5rem;

    @media screen and (min-width: $screen-md) {
        height: 8rem;
    }
}

.travelOffer__name {
    @include bidi-float(left);
    @include bidi-padding(.5rem, 1rem, .5rem, .5rem);

    width: calc(100% - 12rem);
    background-color: #173D6C;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    height: 100%;
    line-height: 1.2;
    letter-spacing: 0.04em;

    @media screen and (min-width: $screen-md) {
        @include bidi-padding(1rem, 7.5rem, 1rem, 1rem);
        width: calc(100% - 18rem);
        font-size: 1.2rem;
    }
}

.travelOffer__total {
    @include bidi-float(left);
    @include bidi-padding(.9rem, .7rem, 0, 0);

    width: 6rem;
    color: #555;
    font-weight: 900;
    text-align: right;
    height: 100%;
    line-height: 1;

    @media screen and (min-width: $screen-md) {
        width: 8rem;
    }
}

.travelOffer__price {
    font-size: 1.4rem;
    color: #555;
    //font-weight: 900;
    letter-spacing: 0.02em;
    font-family: $font-fam-attention;

    @media screen and (min-width: $screen-md) {
        font-size: 1.5rem;
    }
}

.travelOffer__currency {
    font-size: 0.8rem;
}

.travelOffer__cta {
    @include bidi-float(left);

    width: 6rem;
    font-size: 0.8rem;
    height: 100%;
    padding: 0.8rem .5rem;
    overflow: visible;

    @media screen and (min-width: $screen-md) {
        width: 10rem;
        font-size: 1rem;
    }

    .travelOffer__ctaDetails {
        @include bidi-padding-left(.8rem);
        @include bidi-left(-.5rem);

        margin-top: 1rem;
        text-align: center;
        color: #444;
        position: relative;
        background-color: #f3f3f3;
        width: 12rem;
        top: -0.3rem;
        padding: 2px;
        cursor: pointer;
        @media screen and (min-width: $screen-md) {
            width: 18rem;
            top: 2.14rem;
        }
    }

    .button {
        line-height: 1;
        cursor: pointer;
        padding: .5rem;
        font-size: 1rem;
        font-family: $font-fam-attention;

        @media screen and (min-width: $screen-md) {
            font-size: 1.3rem;
        }
    }
}

.travelOfferCover {
    height: 3.5rem;
    line-height: 1;
    font-size: 0.8rem;

    @media screen and (min-width: $screen-md) {
        font-size: 1rem;
    }

    &.jqueryBalloon:hover {
        cursor: help;
        .travelOfferCover__title {
            background-color: #245FA9;
            color: white;
        }

        .travelOfferCover__value {
            background-color: #245FA9;
            color: white;
        }
    }
}

.travelOfferCover__title {
    @include bidi-float(left);

    width: calc(100% - 12rem);
    background-color: #1F4F90;
    border-bottom: lighten(#1F4F90, 10%);
    color: white;
    padding: 0.5rem;
    height: 100%;

    @media screen and (min-width: $screen-md) {
        width: calc(100% - 18rem);
    }
}

.travelOfferCover__value {
    @include bidi-float(left);

    width: 12rem;
    background-color: #fafafa;
    color: #444;
    padding: 0.5rem;
    height: 100%;

    @media screen and (min-width: $screen-md) {
        width: 18rem;
    }
}

#travelParameters, #travelQuoteSummary {
    background-color: white;
    box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.4);
    padding: 0 1rem;
    color: #444;
    margin: 0 auto 2rem;
    text-align: center;

    @media screen and (min-width: $screen-sm) {
        margin: 2rem auto;
        padding: 0 2rem;
    }

    h3 {
        margin: 2rem auto 1rem;
        font-family: $font-fam-default;
        font-weight: 900;
        background-color: $clr-primary;
        color: white;
        padding: .5rem 1rem;
    }
}

.travelRoute {
    margin: 2rem auto 2rem;

    .travelRoute__item {
        float: left;
        width: calc(100% / 3);
        line-height: 1.2;

        i {
            font-size: 3rem;
        }
    }
}

.travelRoute__buttons {
    margin: 2rem auto;

    .travelRoute__buttonsColumn {
        @include bidi-float(left);
        padding: 0 1rem;
        width: 50%;
    }

    button {
        width: 100%;
        max-width: 345px;
        line-height: 1;
        height: 3rem;
        background-color: white;
        border: 1px solid #173D6C;
        color: $clr-primary !important;
        font-size: 0.8rem;
        padding: 0 1rem;
        font-family: $font-fam-attention;

        &:hover {
            background-color: #173D6C;
            color: white;
        }

        @media screen and (min-width: $screen-md) {
            font-size: 1rem;
        }

        &:active, &:focus, &:hover {
            color: white !important;
        }
    }
}

.travelerDetail h3 {
    font-family: $font-fam-default;
    font-weight: 900;
    color: #444;
}

#offersAllianzLogoContainer {
    margin: 0 auto 2rem;

    @media screen and (min-width: $screen-sm) {
        margin: 3rem auto 0;
    }
}

#travelOffer__insuranceBy {
    text-align: center;
    margin: 0 auto 0.5rem;

    @media screen and (min-width: $screen-md) {
        @include bidi-margin(0.8rem, auto, 0, 1.5rem);
        @include bidi-float(left);
    }

    @media screen and (min-width: $screen-lg) {
        @include bidi-margin(1rem, auto, 0, 1.5rem);
    }
}

#travelOffer__allianzLogo {
    width: 80%;
    margin: 0 auto;
    display: block;

    @media screen and (min-width: $screen-sm) {
        width: 70%;
    }

    @media screen and (min-width: $screen-md) {
        @include bidi-float(left);
        @include bidi-margin(0, auto, 0, 1rem);

        width: 55%;
    }

    @media screen and (min-width: $screen-lg) {
        @include bidi-margin(0, auto, 0, 1.5rem);
    }
}

[dir='ltr']{
    .travelRoute__coverage{
        div{
            p{
                text-align: left !important;
            }
        }
    }
}
