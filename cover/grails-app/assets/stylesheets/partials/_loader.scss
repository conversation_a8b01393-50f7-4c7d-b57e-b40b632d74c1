@-webkit-keyframes -webkit-ripple {
    33% {
        -webkit-transform: scale(.1);
        -moz-transform: scale(.1);
        -ms-transform: scale(.1);
        -o-transform: scale(.1);
        transform: scale(.1);
        opacity: 0;
    }
}
@-moz-keyframes -moz-ripple {
    33% {
        -webkit-transform: scale(.1);
        -moz-transform: scale(.1);
        -ms-transform: scale(.1);
        -o-transform: scale(.1);
        transform: scale(.1);
        opacity: 0;
    }
}
@-ms-keyframes -ms-ripple {
    33% {
        -webkit-transform: scale(.1);
        -moz-transform: scale(.1);
        -ms-transform: scale(.1);
        -o-transform: scale(.1);
        transform: scale(.1);
        opacity: 0;
    }
}
@-o-keyframes -o-ripple {
    33% {
        -webkit-transform: scale(.1);
        -moz-transform: scale(.1);
        -ms-transform: scale(.1);
        -o-transform: scale(.1);
        transform: scale(.1);
        opacity: 0;
    }
}
@keyframes ripple {
    33% {
        -webkit-transform: scale(.1);
        -moz-transform: scale(.1);
        -ms-transform: scale(.1);
        -o-transform: scale(.1);
        transform: scale(.1);
        opacity: 0;
    }
}

.loader-ripple {
    z-index: 100;
    margin: 0 auto;
    display: block;
    position: relative;
}

.loader-ripple:before, .loader-ripple:after {
    content: '';
    position: absolute;
    margin: 0;
    width: 100%;
    height: 100%;
    left:0;
    border-radius: 50%;
    border-width: 6px;
    border-style: solid;
    will-change: transform, opacity;
    -webkit-animation: -webkit-ripple 1.5s ease-out infinite;
    -moz-animation: -moz-ripple 1.5s ease-out infinite;
    -ms-animation: -ms-ripple 1.5s ease-out infinite;
    -o-animation: -o-ripple 1.5s ease-out infinite;
    animation: ripple 1.5s ease-out infinite;
}

.loader-ripple:before {
    border-color: $clr-accent;
    -webkit-animation-delay: 1s;
    -moz-animation-delay: 1s;
    -ms-animation-delay: 1s;
    -o-animation-delay: 1s;
    animation-delay: 1s;
}

.loader-ripple:after {
    border-color: $clr-primary;
    -webkit-animation-delay: .25s;
    -moz-animation-delay: .25s;
    -ms-animation-delay: .25s;
    -o-animation-delay: .25s;
    animation-delay: .25s;
}
