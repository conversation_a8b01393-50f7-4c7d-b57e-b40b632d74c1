//FONTS
body, html {
    font-family: $font-fam-default;
    font-size: 15px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-weight: 400;
    max-width: 100vw;
    overflow-x: hidden;
}
body.disable-scroll, html.disable-scroll  {
    overflow-x: unset;
    overflow: hidden;

}
.link {
    font-size: 15px;
    margin: 0 0 19px 0;
    text-decoration: underline;
}

.link--healthQuote {
    font-size: 10px;

    @media screen and (min-width: 1200px) {
        font-size: 14px;
    }
}

.insurance-icon-verification-mark:before {
    @include bidi-margin-left(3px);
}

.insurance-icon-ok-circled:before {
    //margin: 0;
    font-size: 0.9em;
}

.insurance-icon-cancel-circled:before {
    @include bidi-margin-right(.3em);
}

//.insurance-icon-flight-around1:before {
//    width: 1.2em
//}

//.insurance-icon-plane-front:before {
//    width: 2.2em
//}

//.insurance-icon-briefcase:before {
//    width: 1.2em
//}

//.insurance-icon-flight-around2:before {
//    width: 1.2em
//}

//.insurance-icon-backpack:before {
//    width: 1.2em
//}

[disabled] {
    pointer-events: none;
    opacity: .45;
    -webkit-transition: opacity .2s;
    -moz-transition: opacity .2s;
    -ms-transition: opacity .2s;
    -o-transition: opacity .2s;
    transition: opacity .2s;
}

@media only screen and (max-width: $screen-sm-max) {
    .add-spacing .insurance-logo-container {
        margin-bottom: 45px !important;
    }
}

.image-container {
    text-align: center;

    img {
        max-width: 100%;
        max-height: 100%;
    }
}

main {
    $headerHeightSm: 56px;
    $footerHeightSm: 295px;

    $headerHeightMd: 70px;
    $footerHeightMd: 278px;

    position: relative;

    @media only screen and (max-width: $screen-sm-max) {
        min-height: calc(100vh - #{$headerHeightSm} - #{$footerHeightSm});
    }

    @media only screen and (min-width: $screen-md-min) {
        min-height: calc(100vh - #{$headerHeightMd} - #{$footerHeightMd});
    }
}

.page-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 999;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);

    .loader-ripple {
        width: 80px;
        height: 80px;
    }
}

.clr-accent {
    color: $clr-accent;
}

.clr-primary {
    color: $clr-primary;
}

.clr-primary-intense {
    color: $clr-primary-intense;
}

.datepicker table {
    border-radius: 0;
    border-collapse: separate;

    thead tr:nth-child(-n+2) {
        background-color: $clr-primary;
        color: $clr-distinct-very-light;

        th {
            border-radius: 0;
            font-size: 16px;

            &:hover {
                background-color: $clr-primary-highlight;
                color: $clr-default;
            }
        }
    }

    tbody {
        tr td.day, tr td span {
            font-size: 16px;
            font-weight: 700;
            border-radius: 0;
            color: $clr-primary;
            min-width: 42px;

            &:hover {
                background-color: $clr-primary-highlight;
                color: $clr-default;
            }

            &.active, &.focused {
                background-color: $clr-default;
                color: $clr-accent;
                border: 1px solid $clr-distinct;
            }

            &.old, &.new {
                font-size: 11px;
                font-weight: 500;
            }

            &.disabled {
                color: $clr-distinct;
                background-color: $clr-default;
            }
        }
    }

    tfoot .today {
        font-size: 16px;
        font-weight: 700;
        border-radius: 0;
        color: $clr-primary;
        padding: 2px 10px;

        &:hover {
            background-color: $clr-primary-highlight;
            color: $clr-default;
        }
    }
}

.portrait-notification {
    position: absolute;
    background-color: $clr-accent;
    color: $clr-default;
    width: 97%;
    margin: 5px auto;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    text-align: center;
    font-size: 13px;
    border-radius: 10px;
    opacity: 0;
    z-index:1000;

    &.fixed {
        position: fixed;
        top: 0;
    }
}

hr {
    border-top: 1px solid $clr-distinct;
}

.padding {
    padding-top: 10px;
    padding-bottom: 10px;
}
.loading-gradient-line {
    animation-duration: 1.25s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: placeHolderShimmer;
    animation-timing-function: linear;
    background: linear-gradient(to right, #36397D 33%, #E2156B 66%);
    background-size: 1100px 104px;
    width: 100%;
    position: relative;
}
.border-radius-x-4{
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.balloon{
    will-change: opacity;
    transition: opacity .3s;
    pointer-events: none;
    opacity: 0;
    font-family: $font-fam-default;
    font-size: 12px;
    font-weight: 600;
    max-width: 350px;
    position: absolute;
    width: calc(100vw - 30px);
    text-transform: none;
    background-color: $clr-primary !important;
    color: #FFFFFF;
    z-index: 10;
    border-radius: 5px;
    top: -5px;
    padding: 8px;
    -webkit-box-shadow: 0px 0px 5px 0px rgba(54, 57, 125, 1);
    -moz-box-shadow: 0px 0px 5px 0px rgba(54, 57, 125, 1);
    box-shadow: 0px 0px 5px 0px rgba(54, 57, 125, 1) !important;
    letter-spacing: 0.05em;
    &:after{
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        border-top-color: $clr-primary !important;
        bottom: -5px;
        border-width: 5px 5px 0;
        @include bidi-translate(-50%, 0);
    }
}
@keyframes placeHolderShimmer{
    0%{
        background-position: -568px 0
    }
    100%{
        background-position: 568px 0
    }
}

