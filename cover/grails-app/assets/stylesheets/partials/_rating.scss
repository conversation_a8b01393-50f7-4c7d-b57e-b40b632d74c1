.rating {
    margin: 1rem 0;

    @media screen and (min-width: 992px) {
        @include bidi-float(left);
        width: 50%;
    }
}

.rating__text {
    color: $clr-almost-black;
    text-align: center;
    font-weight: 900;
    margin: 1rem 0;
    font-size: 1.2rem;
    line-height: 1;
}

.rating__stars {
    width: 200px;
    margin: 0 auto;
}

.googleRating {
    text-align: center;
    padding: 0 16vw;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    //width: 100%;

    &:not(:last-child) {
        margin: 0 auto 3rem;
    }

    @media screen and (min-width: $screen-sm) {
        @include bidi-float(left);
        width: calc(100% / 3);
        padding: 0 2rem;

        &:not(:last-child) {
            margin: 0;
        }
        //margin: 3rem auto 0;

        //&:last-child {
        //    margin: 3rem auto 0;
        //}
    }

    @media screen and (min-width: $screen-lg) {
        padding: 0 4rem;
    }

    .googleRating__score {
        font-family: $font-fam-attention;
        font-size: 10vw;
        line-height: 1;

        @media screen and (min-width: $screen-sm) {
            font-size: 2.8rem
        }

        @media screen and (min-width: $screen-md) {
            font-size: 3.8rem;
        }

        @media screen and (min-width: $screen-lg) {
            font-size: 3.8rem;
        }
    }

    .googleRating__outOf {
        font-family: $font-fam-attention;
        font-size: 5.4vw;
        line-height: 1;

        @media screen and (min-width: $screen-sm) {
            font-size: 2rem
        }

        @media screen and (min-width: $screen-lg) {
            font-size: 1.6rem;
        }
    }

    .googleRating__text {
        font-size: 1.1rem;

        @media screen and (min-width: $screen-sm) {
            font-size: 1.1rem;
        }

        @media screen and (min-width: $screen-md) {
            font-size: 1.2rem;
        }

    }
    
    i{
        font-size: 2.6rem;
    }

    .googleRating__stars {
        margin: 0 auto;

        .star {
            fill: white;
            width: 9vw;
            height: 9vw;
            margin: 0 .2rem;

            @media screen and (min-width: $screen-sm) {
                width: 2rem;
                height: 2rem;
            }

            @media screen and (min-width: $screen-md) {
                width: 2rem;
                height: 2rem;
            }

            @media screen and (min-width: $screen-lg) {
                width: 3rem;
                height: 3rem;
                margin: 0 .3rem;
            }
        }
    }
}
