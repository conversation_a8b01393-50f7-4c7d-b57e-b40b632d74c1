.landingPageHero {
    overflow-x: hidden;
    .landingPageHero__title {
        font-family: $font-fam-attention;
        color: $clr-primary;
        font-size: 6vw;
        margin: 2rem auto 0;

        .landingPageHero__title--highlight {
            color: $clr-accent;
        }
        @media screen and (min-width: $screen-sm) {
            @include bidi-padding(0, 2rem, 0, 0);
            font-size: 2.1rem;
            margin: 3rem auto 0;
        }

        @media screen and (min-width: $screen-lg) {
            font-size: 4.2rem;
            margin: 6rem auto 0;
        }
    }

    .landingPageHero__title--groupHealth{
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 35px;
        border-radius: 100%;
        width: 300px;
        margin: 20px;
        position: relative;
        text-align: center;
        background-color: $clr-accent;
        font-size: 16px;
        font-weight: bold;
        &:before {
            content:"";
            background-color: $clr-accent;
            border-radius:50%;
            display:block;
            position:absolute;
            z-index:-1;
            width:44px;
            height:44px;
            top:-12px;
            left:28px;
            box-shadow:-50px 30px 0 -12px $clr-accent;}
        &:after {
            content:"";
            background-color: $clr-accent;
            border-radius:50%;
            display:block;
            position:absolute;
            z-index:-1;
            bottom:-10px;
            right:26px;
            width:30px;
            height:30px;
            box-shadow:40px -34px 0 0 $clr-accent,
            -28px -6px 0 -2px $clr-accent,
            -24px 17px 0 -6px $clr-accent,
            -5px 25px 0 -10px $clr-accent;
        }
        span{
            color: $clr-default;
            font-size: 30px;
            text-transform: capitalize;
            font-family: GothamBlackRegular;
            display: block;
        }
        p{
            line-height: 30px;
            margin-bottom: 0;
        }

        a {
            text-align: right;
            font-weight: normal;
            display: block;
            text-align: center;
            line-height: 30px;
            cursor: pointer;
            color: $clr-default;
        }

        @media screen and (max-width: $screen-sm) {
            width: 60%;
            padding: 20px;
            font-size: 14px;
            margin: 30px auto;
            height: 170px;
            span {
                font-size: 20px;
            }
            p {
                line-height: 20px;
            }
        }
    }

    .landingPageHero__image {
        position: relative;
        height: calc((calc(100vw - 30px)) * 0.5526);
        padding: 0 3rem 0 0;
        margin: 2rem auto;

        @media screen and (min-width: $screen-sm) {
            padding: 0 8rem 0 0;
            margin: 3rem auto 2rem;
            height: 156.97px;
        }

        @media screen and (min-width: $screen-md) {
            height: 136.7px;
        }

        @media screen and (min-width: $screen-lg) {
            @include bidi-padding(0, 0, 0, 0);

            position: relative;
            margin: 5rem auto 2rem;
            height: 223.69px;
        }

        &.landingPageHero__image--carInsurance {
            @media screen and (min-width: $screen-lg) {
                left: -2.5rem;
            }
        }

        &.landingPageHero__image--travelInsurance {
            left: 2rem;
            @media screen and (min-width: $screen-lg) {
                left: -1rem;
            }
        }

        &.landingPageHero__image--homeInsurance {
            left: 2rem;
            @media screen and (min-width: $screen-lg) {
                left: -1rem;
            }
        }

        &.landingPageHero__image--healthInsurance {
            left: 2rem;
            @media screen and (min-width: $screen-lg) {
                left: 0;
            }
        }

        img {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .landingPageHero__button {
        font-family: $font-fam-attention;
        font-size: 1.5rem;
        padding: .2em 0;
        max-width: 250px;
        margin: 0 auto;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;


        @media screen and (min-width: $screen-sm) {
            font-size: 1.5rem;
        }

        &.button--kwtLanding {
            //@media screen and (min-width: $screen-sm) {
            //    margin: 5rem auto 1rem;
            //}
            margin: 1.3rem auto 1rem;
        }

        //&.button--whatsapp:hover {
        //    text-decoration: none;
        //    top: 1px;
        //}
    }

    .landingPageHero__link {
        font-family: $font-fam-default;
        text-decoration: underline;
        text-align: center;
        display: block;
        margin: 1.3rem auto 0;
        color: $clr-primary;
        font-size: 5vw;


        @media screen and (min-width: $screen-sm) {
            font-size: 1.4rem;
            margin: 1.3rem auto 0;
        }
    }

    .landingPageHero__col1, .landingPageHero__col2 {
        @media screen and (min-width: $screen-sm) {
            @include bidi-float(left);
        }
    }

    .landingPageHero__col1 {

        @media screen and (min-width: $screen-sm) {
            width: 50%;
        }

        @media screen and (min-width: $screen-md) {
            width: 65%
        }

        @media screen and (min-width: $screen-lg) {
            width: 70%;
        }
    }

    .landingPageHero__col2 {
        text-align: center;
        margin: 0 0 2rem;

        @media screen and (min-width: $screen-sm) {
            width: 50%;
            margin: 0 0 5rem;
        }

        @media screen and (min-width: $screen-md) {
            width: 35%
        }

        @media screen and (min-width: $screen-lg) {
            width: 30%;
        }
    }

    .uae-banner-ad {
        display: block;
        height: 92px;
        text-align: center;
        position: relative;
        background-image: url("https://d16053qvinakro.cloudfront.net/insurance/images/four-lucky-winners.png");
        background-size: auto;
        background-repeat: no-repeat;
        background-position: top center;

        a {
            height: 90px;
            cursor: pointer;
            display: block;
            cursor: pointer;
        }
    }
}
.landingPage-promotion{
    text-align: center;
    margin-top: 10px;
    .bannerimg{
        @media screen and (max-width: $screen-md) {
            display: none;
        }
    }
    .bannerimg-mobile{
        width: 100%;
        @media screen and (min-width: $screen-md) {
            display: none;
        }
    }
}
[dir=rtl] .landingPageHero__title--groupHealth span {
    font-family: unset;
}

[dir=rtl] .landingPageHero.v2 .landingPageHero__col1 a:not(.button-whatsapp) i.insurance-icon-right-open {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
}
[dir=rtl] .landingPageHero.v2 .landingPageHero__col1 a{
    font-size: 1rem;
}
[dir=rtl] .landingPageHero.v2 .discount-container {
    margin: 0 100px 0 0;
    @media screen and (max-width: $screen-sm) {
        margin: 0;
    }
}

.landingPageHero.v2 {
    &.kwt {
        background-image: url(https://assets.yallacompare.com/insurance/kuwait/landingPageImage4.jpg);
    }

    &.uae {
        //background-image: url(https://assets.yallacompare.com/insurance/landing-page-images/landingPage-uae-3.jpg);

        //background: linear-gradient(90deg, #00aaff, #149ce0);
    }

    background-position: center;
    background-size: cover;
    flex-direction: column;
    @media screen and (min-width: $screen-md) {
        min-height: 475px;
        .innerContentWrapper {
            min-height: 280px;
        }
    }

    .container {
        @media screen and (max-width: $screen-md) {
            display: block !important;
        }

    }

    .landingPageHero__col1 {
        border-bottom: 8px solid $clr-primary;
        background: rgba(255, 255, 255, 0.9);
        width: auto;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        padding: 12px 18px;
        margin: 30px 5px 0 5px;
        -webkit-box-shadow: 0px 2px 12px -2px rgba(0, 0, 0, 0.75);
        -moz-box-shadow: 0px 2px 12px -2px rgba(0, 0, 0, 0.75);
        box-shadow: 0px 2px 12px -2px rgba(0, 0, 0, 0.75);

        .middleSectionOnMobile {
            @media screen and (max-width: $screen-xs) {
                padding: 0 39px;
            }
        }

        .landingPageHero__head-title {
            font-size: 2rem;
            margin-top: 15px;
            margin-bottom: 0;
        }

        .landingPageHero__title {
            font-family: unset;
            margin: 15px auto 25px;
            max-width: 460px;
            padding-right: 0;
            @media screen and (min-width: $screen-md) {
                font-size: 1.4rem;
            }
            @media screen and (max-width: $screen-md) {
                letter-spacing: 0.6px;
            }

        }

        .cancel-btn {
            border: solid 1px #36397D;
            border-radius: 3px;
            padding: 6px 10px 0 10px;
            background-color: #fff;
            box-shadow: 0 2px #0f3d75;
            color: #36397D;
            margin-top: -5px !important;
            line-height: 2.6rem;
            text-align: center;
            max-height: 48px;

            &:hover {
                margin-top: -4px !important;
                box-shadow: 0 1px #0f3d75;
            }
        }

        .cancelPolicyButton-small {
            font-size: 1.2rem;
            @media screen and (max-width: $screen-md){
                margin-top: 15px !important;
            }
            .icon-width-12px {
                &:before {
                    width: 12px;
                }
            }
        }

        a {
            margin: 0 auto 20px;
            font-size: 1.4rem;
            max-width: 220px;
            padding: 0.2em 0;
            line-height: 2.5rem;

            @media screen and (max-width: $screen-md) {
                display: block;
                margin: 15px 0;
            }

            .whatsappButton {
                font-size: inherit;
            }
        }
    }

    .discount-container {
        display: block;
        text-align: center;
        justify-content: center;
        margin: 0 0 0 100px;

        @media screen and (max-width: $screen-md) {
            margin: 20px 0;
        }

        h2 {
            text-align: left;
            color: #36397D;
            display: inline-block;
            font-size: 30px;
            //margin-top: -30px;
            margin-top: 0;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
        }

        h1 {
            margin: 5px 0 0 0;
            border-radius: 50%;
            padding: 25px 20px;
            font-size: 50px;
            color: #fff;
            background-color: #E2156B;
            display: inline-block;
            position: relative;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;

            span {
                font-size: 30px;
                color: #36397D;
                position: absolute;
                bottom: 15px;
                right: 5px;
                -webkit-text-stroke-width: 1px;
                -webkit-text-stroke-color: #fff;
                font-weight: bold;
                @media screen and (max-width: $screen-md) {
                    bottom: 15px;
                    right: 10px;
                }
            }

            @media screen and (max-width: $screen-md) {
                font-size: 50px;
            }
        }

        h3 {
            display: inline-block;
            color: #676bd0;
            font-size: 30px;
            @media screen and (max-width: $screen-md) {
                font-size: 25px;
            }
        }

        h4 {
            color: #E2156B;
            margin: 15px 0;
            font-size: 16px;
            @media screen and (max-width: $screen-md) {
                font-size: 14px;
            }
        }

        .days-container, .hours-container, .mints-container, .seconds-container {
            width: 110px;
            height: 110px;
            display: inline-block;
            position: relative;
            padding: 0 20px;
            border-radius: 3px;
            box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.18);
            margin: 0 2px;
            background: linear-gradient(45deg, #676bd0, #36397D);
            @media screen and (max-width: $screen-md) {
                width: 70px;
                padding: 10px;
                height: 80px;
            }
        }

        .days-txt, .hours-txt, .mints-txt, .seconds-txt {
            position: absolute;
            bottom: -25px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #fff;
            background-color: #36397D;
            display: block;
            width: 100%;
            left: 0;
            padding: 5px 0;
            box-shadow: inset 0px 2px 5px rgba(0, 0, 0, 0.3);
            border-bottom-left-radius: 3px;
            border-bottom-right-radius: 3px;
            @media screen and (max-width: $screen-md) {
                font-size: 12px;
                letter-spacing: unset;
            }
        }

        .hours-txt {
            background-color: #e2156b;
        }

        .days-num, .hours-num, .mints-num, .seconds-num {
            color: #fff;
            line-height: 100px;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            font-size: 60px;
            font-weight: bold;
            @media screen and (max-width: $screen-md) {
                font-size: 40px;
                line-height: 60px;
            }
        }

        .hours-container {
            background: linear-gradient(45deg, #E2156B , #921147) !important;
        }

        .expired {
            font-size: 45px;
            color: $clr-primary;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            display: block;
            text-align: center;

            img {
                display: inline-block;
                height: 230px;
            }

            span {
                color: $clr-accent;
                display: block;
            }

            @media screen and (max-width: $screen-md) {
                font-size: 30px;
                margin-top: 10px;
                img {
                    display: inline-block;
                    height: 190px;
                }
            }
        }
    }

    .innerContentWrapper {
        display: flex;
        align-items: center;

        &.innerContentWrapper--TL {
            align-items: flex-start;
        }

        &.innerContentWrapper--TC {
            align-items: flex-start;
            justify-content: center;
        }

        &.innerContentWrapper--TR {
            align-items: flex-start;
            justify-content: flex-end;
        }

        &.innerContentWrapper--CL {
            align-items: center;
            justify-content: flex-start;
        }

        &.innerContentWrapper--CC {
            align-items: center;
            justify-content: center;
        }

        &.innerContentWrapper--CR {
            align-items: center;
            justify-content: flex-end;
        }

        &.innerContentWrapper--BL {
            align-items: flex-end;
            justify-content: flex-start;
        }

        &.innerContentWrapper--BC {
            align-items: flex-end;
            justify-content: center;
        }

        &.innerContentWrapper--BR {
            align-items: flex-end;
            justify-content: flex-end;
        }
    }

    .innerContentWrapper-Featured {
        background: #36397D;
        //background: rgba(black, 0.2);
        color: white;
        padding: 0 0 20px;
        margin-top: 30px;

        .award-winning {
            text-align: center;
            padding-top: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 18px;
        }

        .featuredMagPicWrapper {
            height: 35px;
            margin: 10px auto 0;

            img {
                display: block;
                max-width: 100%;
                max-height: 100%;
                margin: 0 auto;
            }

            @media screen and (max-width: $screen-md) {
                height: 22px;
            }
        }
    }

    &.uae {
        @media screen and (max-width: $screen-md) {
            .innerContentWrapper-Featured {
                //margin-top: 175px;
                .award-winning {
                    font-size: 14px;
                    padding: 20px 40px 10px 40px;
                }
            }
        }
    }

    .countdown-container {
        display: none !important;
    }

    .car-cash-compitition-wrapper {
        background-color: $clr-primary;
        background-image: url("https://assets.yallacompare.com/insurance/landing-page-images/Insurance-Home-Page-bg-en.png");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: bottom right;
        .text-wrapper{
            text-align: unset;
            width: 50%;
        }

    }
    .campaign-without-whitelabel{
        background-color: #000;
        background-image: url("https://assets.yallacompare.com/insurance/landing-page-images/tomJerry.png");
        background-size: 50%;
        background-repeat: no-repeat;
        background-position: center right;
        display: block;
    }
    .text-wrapper {
        text-align: center;
        width: 36%;
        color: #fff;
        padding: 30px 2%;

        .buttons-container {
            width: 100%;
            display: flex;
            align-items: flex-start;
            margin-top: 30px;
            justify-content: space-between;

            a {
                height: 50px;
                line-height: 50px;
                font-size: 16px;
                padding: 0;
                width: calc(50% - 15px);
                max-width: unset;
            }
        }

        .more-details__button {
            border: solid 1px #0f3d75;
            border-radius: 3px;
            background-color: $clr-primary;
            box-shadow: 0 2px #0f3d75;
            color: $clr-default;
            margin-top: -5px !important;
            text-align: center;

            &:hover {
                margin-top: -2px !important;
                line-height: 53px;
                box-shadow: 0 1px #0f3d75;
            }
        }

        .landingPageHero__button {
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            margin: 0 30px 0 0;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
            line-height: 48px !important;
            border-color: #c21e71;
        }

        .canel-btn-wrapper {
            display: block;
            text-align: center !important;
            margin-top: 15px !important;
        }

        .cancel-btn {
            font-family: "proxima-nova", "Tahoma", sans-serif;
            border: solid 1px #36397D;
            border-radius: 3px;
            padding: 0;
            background-color: #fff;
            box-shadow: 0 2px #0f3d75;
            color: #36397D;
            width: calc(50% - 15px);
            height: 50px;
            line-height: 50px;
            display: inline-block;
            padding: 0;
            text-align: center;
        }

        .cancelPolicyButton-small {
            font-size: 16px;

            .icon-width-12px {
                &:before {
                    width: 12px;
                }
            }
        }

        h1, h2 {
            margin: 0;
            font-size: 40px;
            line-height: 55px;
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            font-weight: normal;
        }

        p {
            font-family: "GothamBlackRegular", bahij-janna, Helvetica, Tahoma, Verdana, Arial, sans-serif;
            color: $clr-default;
            font-size: 25px;
            margin-top: 30px;
            line-height: 40px;
        }

        .rating {
            width: 100%;
            text-align: center;
        }
    }
    @media screen and (max-width: $screen-md) {
        .car-cash-compitition-wrapper {
            position: relative;
            height: 450px;
            .text-wrapper {
                width: 100%;
                padding: 10px 0;
                height: 100%;
                h1,h2{
                    text-align: center;
                }
            }
        }
        .innerContentWrapper {
            height: 100%;
        }
        .campaign-without-whitelabel{
            height: 420px;
            background-size: 100%;
            background-position: bottom center !important;
        }
        .text-wrapper {
            width: 100%;
            padding: 10px 0;
            height: 100%;
            h1{
                font-size: 20px;
            }
            h3{
                font-size: 16px;
                line-height: 20px;
                margin-top: 10px;
            }
            .buttons-container {
                width: 100%;
                display: flex;
                align-items: flex-start;
                margin-top: 15px;
                justify-content: center;
                a {
                    height: 35px;
                    line-height: 30px;
                    font-size: 14px;
                    padding: 0 5px;
                    width: auto;
                    min-width: 150px;
                    max-width: unset;
                    -webkit-border-radius: 3px;
                    -moz-border-radius: 3px;
                    border-radius: 3px;
                }
            }

            .more-details__button {
                &:hover {
                    margin-top: unset;
                    line-height: unset;
                    box-shadow: 0 1px #0f3d75;
                }
            }

            .landingPageHero__button {
                line-height: 28px !important;
                margin: 0 15px 0 0;
            }

            .canel-btn-wrapper {
                display: block;
                text-align: left;
                margin-top: 15px !important;
            }

            .cancel-btn {
                border: solid 1px #36397D;
                border-radius: 3px;
                padding: 0 5px;
                background-color: #fff;
                box-shadow: 0 2px #0f3d75;
                color: #36397D;
                width: auto;
                min-width: 150px;
                height: 35px;
                line-height: 30px;
                text-align: center;
                font-size: 14px;
            }

            .cancelPolicyButton-small {
                .icon-width-12px {
                    &:before {
                        width: 12px;
                    }
                }
            }
            h1, h2 {
                margin: 0;
                font-size: 25px;
                line-height: 30px;
            }

            p {
                font-size: 16px;
                //padding: 10px;
                margin-top: 10px;
                line-height: 24px;
                border-radius: 3px;
            }

            .rating {
                position: absolute;
                bottom: 0;
                font-size: 16px !important;
            }
        }
        [dir=rt] .landingPageHero__button {
            margin-left: 5px !important;
            margin-right: 0;
        }
    }
}

[dir=rtl] .car-cash-compitition-wrapper {
    background-color: $clr-primary;
    background-image: url("https://assets.yallacompare.com/insurance/landing-page-images/Insurance-Home-Page-bg-ar.png") !important;
    background-position: bottom left !important;
    .text-wrapper {
        .landingPageHero__button {
            margin-left: 5px !important;
            margin-right: 0 !important;
        }

        .canel-btn-wrapper {
            text-align: right !important;
        }
    }
}
[dir=rtl]  .campaign-without-whitelabel{
    background-position: left center !important;
}
.landingPage-formHeader {
    font-size: 3rem;
    @media (max-width: $screen-sm) {
        font-size: 2rem;
    }
}

.landingPage-link {
    text-align: center;
    font-size: 1.25rem;

    &.mb-1 {
        margin-bottom: 1rem;
    }

    &.mt-1 {
        margin-top: 1rem;
    }
}

.bottomPageFoldShadow:after, .bottomPageFoldShadow:before {
    content: "";
    position: absolute;
    bottom: 0;
    display: block;
    width: 70px;
    height: 1px;
    box-shadow: 0 11px 24px 0 rgba(0, 0, 0, .3), 0 2px 14px 4px rgba(0, 0, 0, .2);
}

.bobbingAnimActive {
    animation-name: bobbingAnim;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-play-state: running;
    -webkit-animation-name: bobbingAnim;
    -webkit-animation-duration: 2s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-play-state: running;
}

.gradianBg-primary-accent {
    background: $clr-primary;
    background: linear-gradient(to right, $clr-primary 33%, $clr-accent 66%);

    &.fordPoromotion {
        background: linear-gradient(to right, #36397d 33%, $clr-accent 66%);
    }
}

@-webkit-keyframes flash {
    0% {
        opacity: .5;
    }
    100% {
        opacity: 1;
    }
}

@keyframes flash {
    0% {
        opacity: .5;
    }
    100% {
        opacity: 1;
    }
}

.flash:hover {
    opacity: 1;
    -webkit-animation: flash 1.5s;
    animation: flash 1.5s;
}

.w-md-100pcent {
    @media screen and (max-width: 760px) {
        width: 100%;
    }
}


.event-banner-img{
    @media screen and (min-width: 760px) {
        width: auto;
        max-height: 90px;
        margin: 10px;
    }
    @media screen and (max-width:760px) {
        //width: calc(100% - 20px);
        height: auto;
        max-height: 60px;
        margin: 10px;
    }
    @media screen and (max-width:($screen-xs - 66px)) {
        max-height: 47px !important;
        height: 47px !important;
    }
    @media screen and (max-width:($screen-xs - 105px)) {
        max-height: 40px !important;
        height: 40px !important;
    }
    @media screen and (max-width:($screen-xs - 160px)) {
        max-height: 36px !important;
        height: 36px !important;
    }
}
