# override values for a specific machine by creating/updating the following file:
# /etc/environment.yml

---
hibernate:
    cache:
        queries: false
        use_second_level_cache: true
        use_query_cache: false
        region.factory_class: 'org.hibernate.cache.ehcache.SingletonEhCacheRegionFactory'

dataSource:
    pooled: true
    jmxExport: true
    driverClassName: org.mariadb.jdbc.Driver
    username: root
    password: root

dataSources:
    dataSource:
        pooled: true
        jmxExport: true
        driverClassName: org.mariadb.jdbc.Driver
        username: root
        password: root
    reports:
        url: jdbc:mysql://${MARIADB_HOST:localhost}/insurance_db?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true
        pooled: true
        jmxExport: true
        driverClassName: org.mariadb.jdbc.Driver
        username: root
        password: root
        properties:
            # Connection pool configuration to prevent stale connections
            maxActive: 20
            maxIdle: 10
            minIdle: 2
            initialSize: 2
            maxWait: 10000
            # Connection validation settings
            testOnBorrow: true
            testOnReturn: false
            testWhileIdle: true
            validationQuery: "SELECT 1"
            validationQueryTimeout: 3
            validationInterval: 30000
            # Connection lifetime settings (8 hours = 28800 seconds)
            maxAge: 28800000
            # Remove abandoned connections
            removeAbandoned: true
            removeAbandonedTimeout: 300
            logAbandoned: true
            # Connection timeout settings
            timeBetweenEvictionRunsMillis: 30000
            minEvictableIdleTimeMillis: 600000

environments:
    development:
        dataSources:
            dataSource:
                dbCreate: none
                url: jdbc:mysql://${MARIADB_HOST:localhost}/insurance_db?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true
                logSql: false
                properties:
                    # Connection pool configuration to prevent stale connections
                    maxActive: 50
                    maxIdle: 25
                    minIdle: 5
                    initialSize: 5
                    maxWait: 10000
                    # Connection validation settings
                    testOnBorrow: true
                    testOnReturn: false
                    testWhileIdle: true
                    validationQuery: "SELECT 1"
                    validationQueryTimeout: 3
                    validationInterval: 30000
                    # Connection lifetime settings (8 hours = 28800 seconds)
                    maxAge: 28800000
                    # Remove abandoned connections
                    removeAbandoned: true
                    removeAbandonedTimeout: 300
                    logAbandoned: true
                    # Connection timeout settings
                    timeBetweenEvictionRunsMillis: 30000
                    minEvictableIdleTimeMillis: 600000
    test:
        dataSources:
            dataSource:
                url: jdbc:mysql://${MARIADB_HOST:localhost}/insurance_db?useUnicode=true&characterEncoding=UTF-8
                logSql: true
    staging:
        dataSources:
            dataSource:
                jndiName: java:comp/env/jdbc/insuranceDS
            reports:
                jndiName: java:comp/env/jdbc/insuranceReportDS
    stageCover1:
        dataSources:
            dataSource:
                jndiName: java:comp/env/jdbc/insuranceDS
            reports:
                jndiName: java:comp/env/jdbc/insuranceReportDS
    stageCover2:
        dataSources:
            dataSource:
                jndiName: java:comp/env/jdbc/insuranceDS
            reports:
                jndiName: java:comp/env/jdbc/insuranceReportDS
    preprodCover:
        dataSources:
            dataSource:
                jndiName: java:comp/env/jdbc/insuranceDS
            reports:
                jndiName: java:comp/env/jdbc/insuranceReportDS
    production:
        dataSources:
            dataSource:
                jndiName: java:comp/env/jdbc/insuranceDS
            reports:
                jndiName: java:comp/env/jdbc/insuranceReportDS
---
grails:
    profile: web
    codegen:
        defaultPackage: cover
    spring:
        transactionManagement:
            proxies: false
    gorm:
        # Whether to autowire entities.
        # Disabled by default for performance reasons.
        autowire: false
        reactor:
            # Whether to translate GORM events into Reactor events
            # Disabled by default for performance reasons
            events: false
info:
    app:
        name: '@info.app.name@'
        version: '@info.app.version@'
        grailsVersion: '@info.app.grailsVersion@'
spring:

    groovy:
        template:
            check-template-location: false

---
grails:
    mime:
        disable:
            accept:
                header:
                    userAgents:
                        - Gecko
                        - WebKit
                        - Presto
                        - Trident
        types:
            all: '*/*'
            atom: application/atom+xml
            css: text/css
            csv: text/csv
            form: application/x-www-form-urlencoded
            html:
              - text/html
              - application/xhtml+xml
            js: text/javascript
            json:
              - application/json
              - text/json
            multipartForm: multipart/form-data
            pdf: application/pdf
            rss: application/rss+xml
            text: text/plain
            hal:
              - application/hal+json
              - application/hal+xml
            xml:
              - text/xml
              - application/xml
    urlmapping:
        cache:
            maxsize: 1000
    controllers:
        defaultScope: singleton
    converters:
        encoding: UTF-8
    views:
        default:
            codec: html
        gsp:
            encoding: UTF-8
            htmlcodec: xml
            codecs:
                expression: html
                scriptlets: html
                taglib: none
                staticparts: none
endpoints:
    jmx:
        unique-names: true

---
server:
    contextPath: '/insurance'
    session:
        timeout: 172800  #seconds
    cover: 'http://localhost:8080'

---

environments: # CAUTION!!!!! IF YOU UPDATE THE URLS HERE, DO NOT FORGET TO UPDATE THE CORRESPONDING ONES IN SAFEGUARD plugin.yml FILE
    development:
        grails:
            serverURL: 'http://localhost:8080/insurance'
    staging:
        grails:
            serverURL: 'https://app.testingyalla.xyz/insurance'
    stageCover1:
        grails:
            serverURL: 'https://stage-cover-1.testingyalla.xyz/insurance'
    stageCover2:
        grails:
            serverURL: 'https://stage-cover-2.testingyalla.xyz/insurance'
    preprodCover:
        grails:
            serverURL: 'https://preprod-cover.testingyalla.xyz/insurance'
    production:
        grails:
            serverURL: 'https://app.yallacompare.com/insurance'

---

environments:
    development:
        yallacompare:
            baseURL: 'http://localhost:8080'
            apiBaseURL: 'http://localhost:8080'
    staging:
        yallacompare:
            baseURL: 'https://testingyalla.xyz'
            apiBaseURL: 'https://api.testingyalla.xyz'
    stageCover1:
        yallacompare:
            baseURL: 'https://stage-cover-1.testingyalla.xyz'
            apiBaseURL: 'http://api.testingyalla.xyz'
    stageCover2:
        yallacompare:
            baseURL: 'https://stage-cover-2.testingyalla.xyz'
            apiBaseURL: 'http://api.testingyalla.xyz'
    preprodCover:
        yallacompare:
            baseURL: 'https://preprod-cover.testingyalla.xyz'
            apiBaseURL: 'http://api.testingyalla.xyz'
    production:
        yallacompare:
            baseURL: 'https://app.yallacompare.com'
            apiBaseURL: 'https://api-int.yallacompare.com'
---
springsession:
    maxInactiveIntervalInSeconds: 172800
    sessionStore: SessionStore.REDIS
    defaultSerializer: Serializer.JSON
    strategy:
        defaultStrategy: SessionStrategy.COOKIE
        cookie:
            name: "SESSION"
        httpHeader:
            headerName: "x-auth-token"
    redis:
        connectionFactory:
            hostName: ${REDIS_HOST:localhost}
            port: 6379
            timeout: 2000
            usePool: true
            dbIndex: 2
    allow:
        persist:
            mutable: true

environments:
    staging:
        springsession:
            redis:
                connectionFactory:
                    hostName: stg-redis.ipyetd.ng.0001.mec1.cache.amazonaws.com
                    port: 6379
                    dbIndex: 2
                poolConfig:
                    maxTotal: 15
                    maxIdle: 15
                    minIdle: 5
    stageCover1:
        springsession:
            redis:
                connectionFactory:
                    hostName: stg-redis.ipyetd.ng.0001.mec1.cache.amazonaws.com
                    port: 6379
                    dbIndex: 2
                poolConfig:
                    maxTotal: 15
                    maxIdle: 15
                    minIdle: 5
    stageCover2:
        springsession:
            redis:
                connectionFactory:
                    hostName: stg-redis.ipyetd.ng.0001.mec1.cache.amazonaws.com
                    port: 6379
                    dbIndex: 2
                poolConfig:
                    maxTotal: 15
                    maxIdle: 15
                    minIdle: 5
    preprodCover:
        springsession:
            redis:
                connectionFactory:
                    hostName: stg-redis.ipyetd.ng.0001.mec1.cache.amazonaws.com
                    port: 6379
                    dbIndex: 2
                poolConfig:
                    maxTotal: 15
                    maxIdle: 15
                    minIdle: 5
    production:
        springsession:
            redis:
                connectionFactory:
                    hostName: prod-redis.ipyetd.ng.0001.mec1.cache.amazonaws.com
                    port: 6379
                    dbIndex: 2
                poolConfig:
                    maxTotal: 15
                    maxIdle: 15
                    minIdle: 5
---
cover:
    fallback.currency: "AED"
    fallback.language: "en"
    noquote.policy.prefix: "C4M"
    hq.office.timings: "(Sunday to Thursday, 9am – 6pm)"
    homepageUrl: https://www.yallacompare.com/
    ksaHomepageUrl: https://www.yallacompare.com/

environments:
    development:
        cover:
            homepageUrl: http://localhost:8080/insurance/
            ksaHomepageUrl: http://localhost:8080/insurance/
    staging:
        cover:
            homepageUrl: https://testingyalla.xyz/
            ksaHomepageUrl: https://testingyalla.xyz/
    stageCover1:
        cover:
            homepageUrl: https://stage-cover-1.testingyalla.xyz/
            ksaHomepageUrl: https://stage-cover-1.testingyalla.xyz/
    stageCover2:
        cover:
            homepageUrl: https://stage-cover-2.testingyalla.xyz/
            ksaHomepageUrl: https://stage-cover-2.testingyalla.xyz/
    preprodCover:
        cover:
            homepageUrl: https://preprod-cover.testingyalla.xyz/
            ksaHomepageUrl: https://preprod-cover.testingyalla.xyz/
    production:
        cover:
            homepageUrl: https://www.yallacompare.com/
---
javamelody:
    authorized-users: compare:C@mp@re2011
---
grails:
    plugin:
        auditLog:
            auditDomainClassName: com.safeguard.audit.AuditTrail
---
clements:
    iframe:
        default: https://forms.clements.com/life_health/term_life/Default.asp?brokercode=AAA_CI4M
        union: https://www.unionlifeinsurance.ae/expat/step1.asp?brokercode=AAA_CI4M&name=AAA_CI4M
---
mixpanel:
    token: c6b27ebdb28bf17fb701e81d7a5bb46b
    secret: 662acd476416a45961888d785de3a62c
environments:
    production:
        mixpanel:
            token: 899a6fc961dabb2f8972509512b75960
            secret: 66833f725a971c4a22d67fd3ec44aa82
---
ajax:
    saveLead: https://testingyalla.xyz/ajax/saveLead/
environments:
    development:
        ajax:
            saveLead: https://testingyalla.xyz/ajax/saveLead/
    staging:
        ajax:
            saveLead: https://testingyalla.xyz/ajax/saveLead/
    stageCover1:
        ajax:
            saveLead: https://stage-cover-1.testingyalla.xyz/ajax/saveLead/
    stageCover2:
        ajax:
            saveLead: https://stage-cover-2.testingyalla.xyz/ajax/saveLead/
    preprodCover:
        ajax:
            saveLead: https://preprod-cover.testingyalla.xyz/ajax/saveLead/
    production:
        ajax:
            saveLead: https://yallacompare.com/ajax/saveLead/

---
grails:
    cors:
        enabled: true

---

ajax:
    blacklist:
        userAgents:
            - Java/
---
cover:
    vouchers:
        promo:  false
---
grails:
    controllers:
        upload:
            maxFileSize: ********
            maxRequestSize: ********
            extensions: ['jpg','pdf','jpeg','png','bmp','tiff']

extensions:
    image: ['jpg','jpeg','png','bmp','tiff']
---
environments:
    development:
        banking: "http://localhost:8081/fuse"
    staging:
        banking: "http://api.testingyalla.xyz"
    stageCover1:
        banking: "https://api.yallacompare.net"
    stageCover2:
        banking: "https://api.yallacompare.net"
    preprod-cover:
        banking: "https://api.yallacompare.net"
    production:
        banking: "https://api-int.yallacompare.com"

---
cover:
    social:
        oauth:
            retry:
                attempts: 2
            facebook:
                key: "133a2756cf30bdb3a272849ee0813201"
                secret: "9fef1b35925eeb1cf4b70ffcd0f65850"
                callbackUrl: "www.yallacompare.com"
---
checkout:
    payment:
        installment:
            redirection:
                enabled: false
            hosted:
                enabled: true
---
app:
    links:
        android: https://play.google.com/store/apps/details?id=com.yallacompare.yallacompareapp
        ios: https://itunes.apple.com/ae/app/yallacompare/id1392871524?mt=8

---
environments:
    development:
        smartDubai:
            secretKey: "F1BFE1BADCC17D67427B9716C1A0B7F0309B7AF1D27A2C32A9A9938C97456EDDA9CBAEC1C910CAD5C50545DF75170BB674E9E84E9A5B78DF31F3D5BD48FC6C5E"
            ycEmailForSmartDubaiPaymentNotification: "<EMAIL>"
    staging:
        smartDubai:
            secretKey: "F1BFE1BADCC17D67427B9716C1A0B7F0309B7AF1D27A2C32A9A9938C97456EDDA9CBAEC1C910CAD5C50545DF75170BB674E9E84E9A5B78DF31F3D5BD48FC6C5E"
            ycEmailForSmartDubaiPaymentNotification: "<EMAIL>"
    stageCover1:
        smartDubai:
            secretKey: "F1BFE1BADCC17D67427B9716C1A0B7F0309B7AF1D27A2C32A9A9938C97456EDDA9CBAEC1C910CAD5C50545DF75170BB674E9E84E9A5B78DF31F3D5BD48FC6C5E"
            ycEmailForSmartDubaiPaymentNotification: "<EMAIL>"
    stageCover2:
        smartDubai:
            secretKey: "F1BFE1BADCC17D67427B9716C1A0B7F0309B7AF1D27A2C32A9A9938C97456EDDA9CBAEC1C910CAD5C50545DF75170BB674E9E84E9A5B78DF31F3D5BD48FC6C5E"
            ycEmailForSmartDubaiPaymentNotification: "<EMAIL>"
    preprodCover:
        smartDubai:
            secretKey: "F1BFE1BADCC17D67427B9716C1A0B7F0309B7AF1D27A2C32A9A9938C97456EDDA9CBAEC1C910CAD5C50545DF75170BB674E9E84E9A5B78DF31F3D5BD48FC6C5E"
            ycEmailForSmartDubaiPaymentNotification: "<EMAIL>"
    production:
        smartDubai:
            secretKey: "05D515237777F79F6304945C5DBCA15BCB2E120D62C074F79B5194A9D8CE4B8A64D3E50A91BBB19EC6178E719C8C0FC88D10E4858A512A7BA51C272B964AD4DF"
            ycEmailForSmartDubaiPaymentNotification: "<EMAIL>"

---
environments:
    development:
        instantIssue:
            union:
                enabled: true
    staging:
        instantIssue:
            union:
                enabled: true
    stageCover1:
        instantIssue:
            union:
                enabled: true
    stageCover2:
        instantIssue:
            union:
                enabled: true
    preprodCover:
        instantIssue:
            union:
                enabled: true
---
environments:
    production:
        instantIssue:
            union:
                enabled: false

---
environments:
    development:
        lifeInsuranceAddon:
            car:  false
#            home: true

    staging:
        lifeInsuranceAddon:
            car:  false
#            home: true

    stageCover1:
        lifeInsuranceAddon:
            car:  false
    #            home: true

    stageCover2:
        lifeInsuranceAddon:
            car:  false
        #            home: true
    preprodCover:
        lifeInsuranceAddon:
            car:  false
        #            home: true

    production:
        lifeInsuranceAddon:
            car:  false
#            home: true
---
egypt:
    fra:
        en: 'http://www.fra.gov.eg/jtags/efsa_en/index_en.jsp'
        ar: 'http://www.fra.gov.eg/jtags/efsa_ar/index.jsp'

---
environments:
    development:
        axaBaseUrl: http://localhost:3000
        life:
            api:
                enabled: false
                username: yallacompare
                password: znczJtWv3W7cwmbgDs
                url: https://axalifeuat.democrance.com
                version: 3

    staging:
        axaBaseUrl: https://axa.testingyalla.xyz
        life:
            api:
                enabled: false
                username: yallacompare
                password: znczJtWv3W7cwmbgDs
                url: https://axalifeuat.democrance.com
                version: 3

    stageCover1:
        axaBaseUrl: https://axa.testingyalla.xyz
        life:
            api:
                enabled: false
                username: yallacompare
                password: znczJtWv3W7cwmbgDs
                url: https://axalifeuat.democrance.com
                version: 3

    stageCover2:
        axaBaseUrl: https://axa.testingyalla.xyz
        life:
            api:
                enabled: false
                username: yallacompare
                password: znczJtWv3W7cwmbgDs
                url: https://axalifeuat.democrance.com
                version: 3

    preprodCover:
        axaBaseUrl: https://axa.testingyalla.xyz
        life:
            api:
                enabled: false
                username: yallacompare
                password: znczJtWv3W7cwmbgDs
                url: https://axalifeuat.democrance.com
                version: 3

    production:
        axaBaseUrl: https://yallacompare.com
        life:
            api:
                enabled: false
                username: yallacompare
                password: fk_P;g,pX2^j:*At
                url: https://axalife.democrance.com
                version: 3

---
newrelic:
  enabled: true
---
recaptcha:
    publicKey: "6Lf96T4lAAAAALux09CLxBDKaklYx5PH1sUQK-RJ"
    privateKey: "6Lf96T4lAAAAANiWUyWl7PX0haqY2zIY42F08HFO"
    includeScript: true
    includeNoScript: true
environments:
    development:
        recaptcha:
            enabled: false
    staging:
        recaptcha:
            enabled: false
    production:
        recaptcha:
            enabled: false

---
# Tabby Payment Configuration
tabby:
    minimumAmounts:
        AED: 10    # UAE Dirham - minimum 10 AED
        SAR: 10    # Saudi Riyal - minimum 10 SAR
        KWD: 1     # Kuwaiti Dinar - minimum 1 KWD
