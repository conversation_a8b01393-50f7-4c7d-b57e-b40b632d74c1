import com.cover.apiV1.CustomRestAuthenticationFailureHandler
import grails.plugins.mail.MailMessageBuilderFactory
import grails.plugins.mail.MailService
import org.grails.web.i18n.ParamsAwareLocaleChangeInterceptor
import org.springframework.mail.javamail.JavaMailSenderImpl

import javax.servlet.http.HttpServletResponse

// Place your Spring DSL code here
beans = {
    localeResolver(org.springframework.web.servlet.i18n.SessionLocaleResolver) {
        defaultLocale = new Locale("en","AE")
        java.util.Locale.setDefault(defaultLocale)
    }

    personTitleEnumConverter com.safeguard.databinding.converters.PersonTitleEnumConverter

    restAuthenticationFailureHandler(CustomRestAuthenticationFailureHandler) {
        statusCode = HttpServletResponse.SC_UNAUTHORIZED
        messageSource = ref("messageSource")
    }

    mailSenderSecondary(JavaMailSenderImpl) {
        port = Integer.parseInt(grailsApplication.config.grails.sesMailConfig.port)
        host = grailsApplication.config.grails.sesMailConfig.host
        username = grailsApplication.config.grails.sesMailConfig.username
        password = grailsApplication.config.grails.sesMailConfig.password
        javaMailProperties = grailsApplication.config.grails.sesMailConfig.props
    }

    mailMessageBuilderFactorySecondary(MailMessageBuilderFactory) {
        mailSender = ref('mailSenderSecondary')
    }

    sesMailService(MailService) {
        it.autowire = true
        mailMessageBuilderFactory = ref('mailMessageBuilderFactorySecondary')
    }
}
