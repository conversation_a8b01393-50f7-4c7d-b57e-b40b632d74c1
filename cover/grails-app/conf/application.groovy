grails.gorm.default.mapping = {
    "user-type" type: org.jadira.usertype.dateandtime.joda.PersistentDateTime, class: org.joda.time.DateTime
    "user-type" type: org.jadira.usertype.dateandtime.joda.PersistentLocalDate, class: org.joda.time.LocalDate
    "user-type" type: org.jadira.usertype.dateandtime.joda.PersistentLocalDateTime, class: org.joda.time.LocalDateTime
}

// Added by the Spring Security Core plugin:
grails.plugin.springsecurity.userLookup.userDomainClassName = 'com.safeguard.User'
grails.plugin.springsecurity.userLookup.authorityJoinClassName = 'com.safeguard.security.UserRole'
grails.plugin.springsecurity.authority.className = 'com.safeguard.security.Role'
grails.plugin.springsecurity.securityConfigType = "InterceptUrlMap"
grails.plugin.springsecurity.useRoleGroups = true
grails.plugin.springsecurity.filterChain.chainMap = [
    //Stateless chain
    [pattern: '/v1/profile/login',                      filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/logout',                     filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    //[pattern: '/v1/profile/validateLogin',            filters: 'anonymousAuthenticationFilter,restTokenValidationFilter,restExceptionTranslationFilter,filterInvocationInterceptor'],
    [pattern: '/v1/policies/**',                        filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/view',                       filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/update',                     filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/offers',                     filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/request-verification-email', filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/request-otp',                filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/applications',               filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/notifications',              filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/unread',                     filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/profile/subscriptions',              filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/userPaymentCard/list',               filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/userPaymentCard/delete/**',          filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/userPaymentCard/switch',             filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required
    [pattern: '/v1/userPaymentCard/default',            filters: 'JOINED_FILTERS,-anonymousAuthenticationFilter,-exceptionTranslationFilter,-authenticationProcessingFilter,-securityContextPersistenceFilter,-rememberMeAuthenticationFilter'], //Anonymous access not allowed, token is required

    [pattern: '/v1/**',                                 filters: 'anonymousAuthenticationFilter,restTokenValidationFilter,restExceptionTranslationFilter,filterInvocationInterceptor'],//Allow anonymous access, but validate if token is sent

    //Statefull chain
    [pattern: '/assets/**',                 filters: 'none'],
    [pattern: '/**/js/**',                  filters: 'none'],
    [pattern: '/**/css/**',                 filters: 'none'],
    [pattern: '/**/images/**',              filters: 'none'],
    [pattern: '/**/favicon.ico',            filters: 'none'],
//  [pattern: '/**',                        filters: 'JOINED_FILTERS, -restTokenValidationFilter, -restExceptionTranslationFilter'] //No spring security REST filter applied
    [pattern: '/**',                        filters: 'none']
]

grails.plugin.springsecurity.interceptUrlMap = [
    [pattern: '/v1/user/**',                                   access: ['isAuthenticated()']],
    [pattern: '/v1/calls/handle-details/AEJ875GYHUGJH576',     access: ['permitAll']],
    [pattern: '/v1/calls/handle-details/**',                   access: ['denyAll']],
    [pattern: '/v1/**',                                        access: ['permitAll']],
    [pattern: '/**',                                           access: ['permitAll']],
    [pattern: '/',                                             access: ['permitAll']]
]

grails.plugin.springsecurity.logout.postOnly = true
grails.plugin.springsecurity.userLookup.usernamePropertyName = "email"


grails.plugin.springsecurity.rest.token.validation.useBearerToken = false
grails.plugin.springsecurity.rest.token.validation.enableAnonymousAccess = true

grails.plugin.springsecurity.rest.token.storage.useGorm=true
grails.plugin.springsecurity.rest.token.storage.gorm.tokenDomainClassName='com.safeguard.security.AuthenticationToken'
grails.plugin.springsecurity.rest.token.storage.gorm.tokenValuePropertyName='token'
grails.plugin.springsecurity.rest.token.storage.gorm.usernamePropertyName='username'
grails.plugin.springsecurity.rest.login.useJsonCredentials=true
//grails.plugin.springsecurity.rest.login.useRequestParamsCredentials=true

grails.plugin.springsecurity.rest.login.endpointUrl = '/v1/profile/login'
grails.plugin.springsecurity.rest.logout.endpointUrl = '/v1/profile/logout'
grails.plugin.springsecurity.rest.token.validation.endpointUrl='/v1/profile/validateLogin'

cors.headers = ['Access-Control-Allow-Methods': '*', 'Access-Control-Allow-Headers': '*', 'Access-Control-Allow-Origin':'*' ]
