import com.cover.life.InitializationService
import grails.core.GrailsApplication

/**
 * Any bootstraping on application startup goes here.
 */
class BootStrap {

    GrailsApplication grailsApplication
    InitializationService initializationService

    def init = { servletContext ->
        //TimeZone.setDefault(TimeZone.getTimeZone("GST"))

        if(grailsApplication.config.getProperty('life.api.enabled').toBoolean()) {
            initializationService.init()
        }
    }

    def destroy = {
    }
}
