package com.cover

import com.safeguard.AdditionalCharges
import com.safeguard.ProductType

class additionalChargesTagLib {
    static namespace = 'additionalCharges'

    def additionalChargesService
    def commonUtilService

    def showAdditionalChargesDetails = { attr ->
        if (attr.rateCommand && attr.productType) {
            List<AdditionalCharges> additionalCharges = additionalChargesService.getAdditionalCharges(attr.rateCommand,
                ProductType.findByName(attr.productType), attr.rateCommand.city?.id)

            out << g.render(
                template: '/components/additionalChargesDetails',
                model: [
                    additionalCharges: additionalCharges,
                    showVAT          : attr.showVAT,
                    currency         : attr.currency ?: 'AED',
                    lang             : attr.lang
                ]
            )
        } else if (attr.quote && !attr.rateCommand) {

        } else {
            return
        }

    }

    def getVATAmount = { attr ->
        if (attr.price) {
            out << "${g:formatNumber(number:commonUtilService.getVATAmount(attr.price), type:'number')}"
        } else if (attr.amount) {
            out << "${g:formatNumber(number:commonUtilService.getVATAmount(attr.amount), type:'number')}"
        }
    }
}
