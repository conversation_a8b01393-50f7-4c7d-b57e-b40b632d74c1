package com.cover.home

import com.cover.home.commands.HomeRateCommand
import com.cover.util.IConstant
import com.safeguard.CoverageTypeEnum
import com.safeguard.Product
import com.safeguard.car.translation.AddonTranslation
import com.safeguard.home.HomeContent
import com.safeguard.home.HomeInsuranceCategory
import com.safeguard.home.HomePersonalBelonging
import com.safeguard.home.HomeQuote

import java.math.RoundingMode

class HomeInsuranceTagLib {

    static namespace = 'homeInsurance'

    def commonUtilService
    def homeQuoteService
    def utilService

    /**
     * Renders a coverage-type dropdown list
     *
     * @attr id REQUIRED (text) becomes id and name of element
     * @attr label (text) label-text for form control
     * @attr value (value) selected value
     * @attr validationMessages (bool) show validation messages
     * @attr onchange (function)
     */
    def coverageTypeDDL = { attr ->

        List<HomeInsuranceCategory> homeInsuranceCategoryList = HomeInsuranceCategory.getActiveList().list()

        List options = homeInsuranceCategoryList.collect {
            [value:it.id, text:it.name]
        }

        out << formControl.dropdown([
            id                : attr.id,
            label             : attr.label,
            options           : options,
            value             : attr.value,
            validationMessages: attr.validationMessages,
            onchange          : attr.onchange
        ])
    }

    /**
     * Renders a homeContents dropdown list
     *
     * @attr id REQUIRED (text) becomes id and name of element
     * @attr label (text) label-text for form control
     * @attr value (value) selected value
     * @attr validationMessages (bool) show validation messages
     * @attr onchange (function)
     */
    def homeContentsDDL = { attr ->
        List<HomeContent> homeContentList = HomeContent.getActiveList().list()

        List options = homeContentList.collect {
            [value:it.id, text:it.name]
        }

        String tooltip = 'Contents include all household goods and items within your home, such as furniture, electronics, rugs, art, and valuables. It also includes basic items like linens, curtains, clothes, make-up, CDs and similar items.Please select the option that covers the replacement cost of your contents if damaged or stolen. Single items over AED 20,000 must be listed separately to be covered.'

        out << formControl.noSearchDropdown([
            id                : attr.id,
            label             : attr.label,
            options           : options,
            value             : attr.value,
            validationMessages: attr.validationMessages,
            onchange          : attr.onchange,
            cssClass          : attr.cssClass,
            tooltip           : tooltip
        ])
    }

    /**
     * Renders a personalBelongings dropdown list
     *
     * @attr id REQUIRED (text) becomes id and name of element
     * @attr label (text) label-text for form control
     * @attr value (value) selected value
     * @attr validationMessages (bool) show validation messages
     * @attr onchange (function)
     */
    def personalBelongingsDDL = { attr ->
        List<HomePersonalBelonging> homePersonalBelongingList = HomePersonalBelonging.getActiveList().list()

        List options = homePersonalBelongingList.collect {
            [value:it.id, text:it.name]
        }

        String tooltip = 'Personal belongings are items that you take out of the home, such as laptops, mobile phones, digital cameras, jewellery, sunglasses, MP3 players, clothing, luggage and handbags. Please select the option that covers the replacement costs of your items in case of damage or theft. There is a single article limit of AED 5,000. Higher valued items must be listed separately to be covered.'

        out << formControl.noSearchDropdown([
            id                : attr.id,
            label             : attr.label,
            options           : options,
            value             : attr.value,
            validationMessages: attr.validationMessages,
            onchange          : attr.onchange,
            tooltip           : tooltip
        ])
    }

    def quotesRows = { attr ->

        HomeQuote homeQuote = attr.homeQuote
        def currency = utilService.getCountry().currency
        def quotes = attr.quotes

        String _template =  '/homeInsurance/partials/quotesRow'

        quotes.eachWithIndex { el, i ->
            out << g.render(template: _template, model: [homeQuoteId: homeQuote.id, quote: el, currency: currency, index: i])
        }
    }

    def quotesHeadRow = { attr ->
        HomeQuote homeQuote = attr.homeQuote

        String _template =  '/homeInsurance/partials/srpHeadRowBuilding'

        if (homeQuote.homeInsuranceCategoryId == HomeInsuranceCategory.HOME_CONTENTS_ONLY) {
            _template =  '/homeInsurance/partials/srpHeadRowContents'
        }

        out << g.render(template: _template, model: [quote: homeQuote])
    }

    def featuresTable = { attr ->
        HomeRateCommand rateCommand = attr.rateCommand
        HomeQuote homeQuote = attr.homeQuote

        List includedList = []
        List notIncludedList = []
        List featuresSummaryList = []

        if (!homeQuote.homeInsuranceCategory) {
            featuresSummaryList = [
                "Content cover upto ${g:formatNumber(number:rateCommand.contentsValue, type:'number')}",
                "Belonging cover upto ${g:formatNumber(number:rateCommand.belongingsValue, type:'number')}",
                "Personal Liability cover upto ${g:formatNumber(number:rateCommand.personalLiability, type:'number')}"
            ]

            if (rateCommand.buildingValue) {
                featuresSummaryList +=  "Buildings cover upto ${g.formatNumber(number:rateCommand.buildingValue, type:'number')}"
                if (rateCommand.contentsValue && rateCommand.belongingsValue && rateCommand.personalLiability) {
                    includedList << "Home Contents And Building"
                } else {
                    includedList << "Building Only"
                }
            } else {
                includedList << "Home Contents Only"
            }
        } else if (homeQuote.homeInsuranceCategory.id == HomeInsuranceCategory.HOME_CONTENTS_ONLY) {
            featuresSummaryList = [
                "Content cover upto ${g:formatNumber(number:rateCommand.contentsValue, type:'number')}",
                "Belonging cover upto ${g:formatNumber(number:rateCommand.belongingsValue, type:'number')}",
                "Personal Liability cover upto ${g:formatNumber(number:rateCommand.personalLiability, type:'number')}"
            ]
            includedList << "Home Contents Only"
        } else if (homeQuote.homeInsuranceCategory.id == HomeInsuranceCategory.HOME_CONTENTS_AND_BUILDING) {
            featuresSummaryList = [
                "Content cover upto ${g:formatNumber(number:rateCommand.contentsValue, type:'number')}",
                "Belonging cover upto ${g:formatNumber(number:rateCommand.belongingsValue, type:'number')}",
                "Personal Liability cover upto ${g:formatNumber(number:rateCommand.personalLiability, type:'number')}"
            ]
            includedList << "Home Contents and Building"
        } else if (homeQuote.homeInsuranceCategory.id == HomeInsuranceCategory.BUILDING_ONLY) {
            featuresSummaryList = [
                "Buildings cover upto ${g:formatNumber(number:rateCommand.buildingValue, type:'number')}"
            ]
            includedList << "Building Only"
        }

        if (!utilService.toBoolean(rateCommand.secondMedicalOpinion)) {
            notIncludedList << "Second Medical Opinion"
        }

        if (rateCommand.lossOfRent < 1) {
            notIncludedList << "Loss Of Rent / Alternative Accommodation"
        }

        if (rateCommand.spoilageOfFood < 1) {
            notIncludedList << "Spoilage Of Food In Freezer"
        }

        if (rateCommand.lossTheftMoney < 1) {
            notIncludedList << "Loss Or Theft Of Money"
        }

        if (rateCommand.fatalInjury < 1) {
            notIncludedList << "Fatal Injury Cover"
        }

        if (rateCommand.contentsInTheOpen < 1) {
            notIncludedList << "Contents In The Open"
        }

        def command = [
            coverageTypeId: CoverageTypeEnum.COMPREHENSIVE.value()
        ]

        out << g.render(template: '/homeInsurance/checkout/features',
            model: [includedList: includedList, notIncludedList: notIncludedList,
                    featuresSummaryList: featuresSummaryList, command: command])
    }

    def checkoutAddons = { attr ->
        HomeRateCommand rateCommand = attr.rateCommand

        def checkboxList = homeQuoteService.getAddons(rateCommand,
                                                session[IConstant.HOME_INSURANCE_DOMESTIC_HELPER_COVER] ? true : false)

        if (checkboxList) {
            out << g.render (template:'/homeInsurance/checkout/addons', model:[checkBoxlist:checkboxList])
        }
    }

}
