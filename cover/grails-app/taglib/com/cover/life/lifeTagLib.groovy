package com.cover.life

import com.cover.util.IConstant
import com.safeguard.LifeSalaryCategoryEnum
import com.safeguard.life.LifeBeneficiaryRelationEnum

class lifeTagLib {

    static namespace = 'life'

    def utilService



    /**
     * Renders model drop down list
     *
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def  salaryCatSelectOptions= { attr ->

        List modelList = LifeSalaryCategoryEnum.values()


        out << g.select (name:"salaryEnum", id:"salaryEnum", from:modelList, optionKey: "name", optionValue: "description", class:attr.class,
            value: session[IConstant.LIFE_INSURANCE_DETAILS_SALARY_CAT_ENUM]?: attr.value)
    }

    def  lifeBeneficiaryRelationOptions= { attr ->

        List modelList = LifeBeneficiaryRelationEnum.values()


        out << g.select (name:attr.name, id:attr.id, from:modelList, optionKey: "identifier", optionValue: "text", class:attr.class,
            value: attr.value ?: 'SPOUSE')
    }

    def staticConditionsAndBenefits = { attr ->
        out << g.render(
            template: '/life/partials/staticConditionsAndBenefits',
            model: [
                cover: attr.cover,
                currency: attr.currency,
                currentLang: attr.currentLang
            ])


    }

    def beneficiarySection = { attrs ->


            out << render(template: "/life/templates/beneficiarySection", model: [
                beneficiaries: attrs.beneficiaries
            ])
    }

    def beneficiary = { attrs ->
        def showRemovalButton = (attrs.i != 0)

        def beneficiaryLabel
        def number = attrs.i > -1 ? attrs.i + 1 : null

        beneficiaryLabel = "Beneficiary <span class='beneficiary__number'>${number}</span>"


        out << render(template: "/life/templates/beneficiary", model: [
            i: attrs.i,
            name: attrs.name,
            share: attrs.share,
            relation:attrs.relation,
            beneficiaryLabel: beneficiaryLabel,
            showRemovalButton: showRemovalButton,
        ])
    }



    def whatHappenNext = { attr ->
        out << g.render(
            template: '/life/checkout/whatHappenNext',
            model: [
            ])


    }



}
