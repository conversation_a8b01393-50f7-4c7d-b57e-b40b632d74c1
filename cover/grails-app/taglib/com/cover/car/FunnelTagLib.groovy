package com.cover.car

import com.cover.car.commands.RateCommand
import com.cover.util.IConstant
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.CoverPreferenceEnum
import com.safeguard.car.VehicleConditionEnum
import com.safeguard.car.vehicle.Model
import com.safeguard.health.HealthGender
import com.safeguard.health.HealthRelationship
import com.safeguard.health.ResidentType
import groovyjarjarantlr.collections.List
import org.codehaus.groovy.runtime.ArrayUtil
import org.springframework.context.i18n.LocaleContextHolder

class FunnelTagLib {

    static namespace = 'funnel'
    static returnObjectForTags = ['countryList', 'drivingExperienceList', 'ncdList']

    def quoteService
    def vehicleService
    def valuationService
    def utilService
    def commonUtilService

    /**
     * Year drop down list.
     * Populating 10 years from current+1
     */
    def yearDDL = { attr ->

        def yearsList = commonUtilService.getCarYears()

        out << g.select(name:'year', id: 'year', from: yearsList, class: attr.class,
                noSelection: ['-1':g.message(code: 'funnel.makeYear')], value: session[IConstant.STEP1_YEAR]?: attr.value)
    }

    /**
     * Renders make drop down list
     *
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def makeDDL = { attr ->
        String countryCode = attr.countryCode
        CountryEnum countryEnum = CountryEnum.findCountryByCode(countryCode)
        Country country = Country.read(countryEnum ? countryEnum.id : CountryEnum.UAE.id)

        def makeList = vehicleService.getMakeList(country).sort { it.name }

        out << g.select (name:"make", id:"make", from:makeList, optionKey:"id", optionValue: "name", class:attr.class,
                noSelection: ['-1':g.message(code: 'funnel.make')], value: session[IConstant.STEP1_MAKE]?: attr.value)
    }

    /**
     * Renders model drop down list
     *
     * @attr make REQUIRED make id of car
     * @attr year REQUIRED manufacturing year of car
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def modelMasterDDL = { attr ->
        def modelList = []

        if(session[IConstant.STEP1_YEAR] && session[IConstant.STEP1_MAKE]){
            attr.year =  session[IConstant.STEP1_YEAR];
            attr.make =  session[IConstant.STEP1_MAKE];
        }

        String countryCode = attr.countryCode
        CountryEnum countryEnum = CountryEnum.findCountryByCode(countryCode)
        Country country = Country.read(countryEnum ? countryEnum.id : CountryEnum.UAE.id)

        if (attr.year && attr.make) {
            modelList = vehicleService.getModelMasterList('en', attr.make, attr.year, country)
        }

        out << g.select (name:"modelMaster", id:"modelMaster", from:modelList, optionKey: "id", optionValue: "name", class:attr.class,
            noSelection: ['-1':g.message(code: 'funnel.model.master')], value: session[IConstant.STEP1_MODEL_MASTER]?: attr.value)
    }

    /**
     * Renders model drop down list
     *
     * @attr make REQUIRED make id of car
     * @attr year REQUIRED manufacturing year of car
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def modelDDL = { attr ->
        def modelList = []

        if(session[IConstant.STEP1_YEAR] && session[IConstant.STEP1_MAKE] && session[IConstant.STEP1_MODEL_MASTER]){
            attr.year =  session[IConstant.STEP1_YEAR];
            attr.make =  session[IConstant.STEP1_MAKE];
            attr.modelMaster =  session[IConstant.STEP1_MODEL_MASTER];
        }

        String countryCode = attr.countryCode
        CountryEnum countryEnum = CountryEnum.findCountryByCode(countryCode)
        Country country = Country.read(countryEnum ? countryEnum.id : CountryEnum.UAE.id)

        if (attr.year && attr.make && attr.modelMaster) {
            def modelDetail = vehicleService.getModelList('en', attr.year, attr.make, attr.modelMaster, null, country)
            modelList = modelDetail.modelList
        }

        out << g.select (name:"model", id:"model", from:modelList, optionKey: "modelId", optionValue: "trim", class:attr.class,
                noSelection: ['-1':g.message(code: 'funnel.model')], value: session[IConstant.STEP1_MODEL]?: attr.value)
    }

    /**
     * Renders valuation range label
     *
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def cityDDL = { attr ->
        boolean isHealth = (attr.health)? true : false

        def cityList = utilService.getCities(utilService.getCountry(), isHealth)

        out << g.select (name: attr.name ?: "city", id: attr.id ?: "city", from:cityList, optionKey:"id", optionValue:"name", class:attr.class,
            noSelection: ['-1': attr.placeholder ?: g.message(code: 'funnel.dropdown.default')], value: attr.value)
    }

    def vehicleCondition = { attr ->

        log.info("VehicleConditionEnum.findAll():${VehicleConditionEnum.values()}")

        out << g.select (name: attr.name ?: "vehicleCondition", id: attr.id ?: "vehicleCondition",
            from: VehicleConditionEnum.values(), optionValue:"value", class:attr.class,
            noSelection: ['-1': attr.placeholder ?: g.message(code: 'funnel.dropdown.default')], value: attr.value)
    }

    def genderDDL = { attr ->
        def genderList = HealthGender.findAll(sort:"sortOrder")

        out << g.select (name: attr.name, id: attr.name, from:genderList, optionKey:"id", optionValue:"name",
            class:attr.class, noSelection: ['-1': attr.placeholder ?: g.message(code: 'funnel.dropdown.default')],
            value: attr.value ?: -1)
    }

    def relationshipToSponsorDDL = { attr ->
        def sponsorList = HealthRelationship.findAllByIdInList([1,2,4], [sort:"sortOrder"])

        if (!attr.applicationType) {
            sponsorList.add(HealthRelationship.findById(5))
        }

        out << g.select(name: attr.name, id: attr.name, from: sponsorList, optionKey: "id", optionValue: "name",
            class: attr.class, noSelection: ['-1': attr.placeholder ?: g.message(code: 'funnel.dropdown.default')],
            value: attr.value ?: -1)
    }

    def residentTypeDDL = { attr ->
        def residentTypeList = ResidentType.findAllByActiveAndIdNotEqual(true, ResidentType.DOMESTIC_WORKER)

        out << g.select(name: attr.name, id: attr.name, from: residentTypeList, optionKey: "id", optionValue: "name",
            class: attr.class, noSelection: ['-1': attr.placeholder ?: g.message(code: 'funnel.dropdown.default')],
            value: attr.value ?: -1)
    }

    /**
     * Renders valuation range label
     *
     * @attr model REQUIRED model id of car
     * @attr year REQUIRED manufacturing year of car
     */
    def valuationLBL = { attr ->
        def valuation = [:]

        if (attr.model && attr.year) {
            def country = utilService.getCountry()
            if (attr.admeId && attr.quote) {
                valuation = quoteService.getValuation(admeId, quote)
            } else {
                valuation = valuationService.getValuation(attr.model, attr.year, attr.isBrandNew, country, null, !attr.isNonGcc)
            }
        }

        if (valuation) {
            String currency;
            if(valuation.currency?.equalsIgnoreCase("EGP"))
                currency = g.message(code:"currency.egy")
            else if(valuation.currency?.equalsIgnoreCase("AED"))
                currency = g.message(code:"currency.uae")
            else
                currency = valuation.currency
            out << g.message(code:"funnel.step1.valuationlbl", args: [currency, valuation.minimum, valuation.maximum ])
        }
    }

    /**
     * returns firstLicenseCountry list object.
     */
    def countryList = { attr ->
        String  sortBy= attr.sortBy ?: 'nameEn'
        utilService.getCountries(sortBy)
    }

    /**
     * returns list of driving experience drop down values
     */
    def drivingExperienceList = {
        utilService.getDrivingExperienceList()
    }

    /**
     * returns list of ncd drop down values
     */
    def ncdList = {
        utilService.getNoClaimDiscountList()
    }

    def quotes = { attr ->
        List quotes = attr.quotes

        //TODO: render no quote page incase if no rates returns

        def currency = utilService.getCountry().currency
        def renewal = attr.renewal ?: false

        quotes.each {
            out << g.render(template: '/car/funnel/quote_row', model:[quoteId:attr.quoteId, quote:it, currency:currency, renewal: renewal])
        }
    }

    def productImpressions = { attr ->
        attr.quotes.eachWithIndex { quote, index ->
            out << g.render(template: '/car/funnel/productImpression', model:[quote:quote, index: index])
        }
    }

    /**
     * Render checkbox, tick or text label for break down, personal accident and replacement car cover.
     *
     * Logic:
     *  if field is number? render checkbox
     *  if field is yes/no? render tick
     *  if field is something else render label.
     *
     * @attr quote RateCommand object
     */
    def srpCoverField = { attr ->
        RateCommand rates = attr.quote
        def currency = utilService.getCountry().currency

        //Agency repair Cover
        out << g.render(template:'/car/funnel/srp_tick', model:[value: rates.agencyRepair, cssClass:'hidden-mobile-portrait'])

        //Break down Cover
        if (rates.breakdownCover.isNumber()) {
            out << g.render(template:'/car/funnel/srp_checkbox', model:[field:'breakdownCover', cost: rates.breakdownCover, currency:currency, cssClass:'hidden-tablet-landscape'])
        } else if (rates.breakdownCover.equalsIgnoreCase("yes") || rates.breakdownCover.equalsIgnoreCase("no")) {
            out << g.render(template:'/car/funnel/srp_tick', model:[value: utilService.toTrueFalse(rates.breakdownCover), cssClass:'hidden-tablet-landscape'])
        } else {
            out << g.render(template:'/car/funnel/srp_label', model:[value:rates.breakdownCover, cssClass:'hidden-tablet-landscape'])
        }

        //Personal accident Cover
        if (rates.paCover.isNumber()) {
            out << g.render(template:'/car/funnel/srp_checkbox', model:[field:'paCover', cost: rates.paCover, currency:currency, cssClass:'hidden-tablet-landscape'])
        } else if (rates.paCover.equalsIgnoreCase("yes") || rates.paCover.equalsIgnoreCase("no")) {
            out << g.render(template:'/car/funnel/srp_tick', model:[value: utilService.toTrueFalse(rates.paCover), cssClass:'hidden-tablet-landscape'])
        } else {
            out << g.render(template:'/car/funnel/srp_label', model:[value:rates.paCover, cssClass:'hidden-tablet-landscape'])
        }

        //Replacement car Cover
        if (rates.replacementCar.isNumber()) {
            out << g.render(template:'/car/funnel/srp_checkbox', model:[field:'replacementCar', cost: rates.replacementCar, currency:currency, cssClass:'hidden-tablet-landscape'])
        } else if (rates.replacementCar.equalsIgnoreCase("yes") || rates.replacementCar.equalsIgnoreCase("no")) {
            out << g.render(template:'/car/funnel/srp_tick', model:[value: utilService.toTrueFalse(rates.replacementCar), cssClass:'hidden-tablet-landscape'])
        } else {
            out << g.render(template:'/car/funnel/srp_label', model:[value:rates.replacementCar, cssClass:'hidden-tablet-landscape'])
        }

    }

    /**
     * Renders heading label of SRP page
     *
     * @attr year year of the car
     * @attr model Id of model domain object
     * @attr total total number of records
     */
    def srpHeading = { attr ->
        def year = attr.year
        def modelName = 'Car'
        def totalRecords = attr.total
        def bestQuote = attr.bestQuote
        def insuredValue = attr.insuredValue
        def currency = attr.currency
        def country = attr.country
        Model modelObj = attr.model

        if (modelObj) {
            //TODO: modelObj.loadTransients(LocaleContextHolder.locale.language)
            modelObj.loadTransients('en')
            modelName = modelObj.make.name + " " + modelObj.getName("en")
        }

        if (bestQuote) {
            out << g.message(code: 'funnel.srp.heading.rsa', args: [totalRecords, modelName, year.toString()])
        } else {
            if (attr.v3) {
                if( country.toLowerCase() == CountryEnum.UAE.dfp.toLowerCase()){

                    out << g.message(code: 'funnel.srp.heading.simple', args: [totalRecords, modelName])

                } else {
                    out << g.message(code: 'funnel.srp.heading.simple.not.ae', args: [totalRecords, modelName, year.toString(), insuredValue, currency])

                }
            }else {
                out << g.message(code: 'funnel.srp.heading', args: [totalRecords, modelName, year.toString(), insuredValue, currency])
            }

        }
    }

    /**
     * Render field for quote details page.
     *
     * Logic:
     *  if field is number? render checkbox
     *  if field is yes/true? render tick
     *  if field is no/false? render cross
     *
     * @attr value REQUIRED value of field
     * @attr selected  true/false for checkbox selected
     */
    def quoteDetailsField = { attr ->
        String val = attr.value
        boolean isLabel = attr.label

        if (isLabel) {
            if (!val) {
                out << "<i class=\"insurance-icon-cross-out-mark\"></i>"
            } else if (val.isNumber() && Double.parseDouble(val)){
                def currency = utilService.getCountry().currency
                out << "${currency} ${g.formatNumber(number:val, type:'number')}"
            } else {
                out << "<i class=\"insurance-icon-cross-out-mark\"></i>"
            }
        } else {
            if (!val) {
                out << "<i class=\"insurance-icon-cross-out-mark\"></i>"
            } else  if (val.isNumber()) {
                if (Double.parseDouble(val)) {
                    def currency = utilService.getCountry().currency
                    out << g.render(template: '/car/funnel/quote_details_checkbox', model:[name: attr.name, value:val,
                                                                                           selected:attr.selected,
                                                                                           currency:currency])
                } else {
                    out << "<i class=\"insurance-icon-cross-out-mark\"></i>"
                }
            } else if (val.equalsIgnoreCase('yes') || val.equalsIgnoreCase('true')) {
                out << "<i class=\"insurance-icon-verification-mark\"></i>"
            } else if (val.equalsIgnoreCase('no') || val.equalsIgnoreCase('false')) {
                out << "<i class=\"insurance-icon-cross-out-mark\"></i>"
            } else {
                out << val
            }
        }
    }

    def cancelPolicyModal = {
        out << render(template: "/car/partials/cancelPolicyModal")
    }

    def coverPreferenceField = { attr ->
       EnumSet enumSet = EnumSet.of(CoverPreferenceEnum.COMPREHENSIVE, CoverPreferenceEnum.THIRD_PARTY)
        out << render(
            template: "/car/funnel/coverPreferenceField",
            model: [
                options: enumSet,
                lang: (String) attr.lang,
                value: session[IConstant.STEP2_COVER_PREFERENCE]?: attr.value
            ]


        )
    }
}
