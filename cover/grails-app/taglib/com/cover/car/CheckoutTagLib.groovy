package com.cover.car

import com.safeguard.InsuranceTypeEnum
import com.cover.car.commands.RateCommand
import com.cover.util.IConstant
import com.safeguard.CountryEnum
import com.safeguard.KnetPaymentResponse
import com.safeguard.PaLead
import com.safeguard.Product
import com.safeguard.ProductType
import com.safeguard.car.CarQuote
import com.safeguard.car.vehicle.Model
import com.safeguard.general.GeneralQuote
import com.safeguard.health.HealthQuote
import com.safeguard.home.HomeQuote
import com.safeguard.life.LifeQuote
import com.safeguard.pet.PetQuote
import com.safeguard.travel.TravelQuote

import java.math.RoundingMode

class CheckoutTagLib {

    static namespace = 'checkout'
    static returnObjectForTags = ['getInludedAndNotIncludedList']

    def commonUtilService
    def checkoutService
    def utilService
    def quoteService

    /**
     * Renders heading label of SRP page
     *
     * @attr year REQUIRED year of the car
     * @attr model REQUIRED of model domain object
     */
    def vehicleHeading = { attr ->
        def year = attr.year
        def modelName = 'Car'

        Model modelObj = attr.model

        if (modelObj) {
            //TODO: Fix language later.
            modelObj.loadTransients('en')
            modelName = modelObj.make.name + " " + modelObj.getName("en")
        }

        out << "${year} ${modelName}"
    }

    def features = { attr ->

        RateCommand rateCommand = attr.rateCommand
        CarQuote quote = attr.quote
        def list = getInludedAndNotIncludedList(rateCommand)

        List<String> featuresSummaryList = []

        if (rateCommand.thirdPartyDamageLimit) {
            String thirdPartyDamageLimit =
                "${g.message(code: "funnel.quote.thirdPartyDamageLimit")} : ${rateCommand.thirdPartyDamageLimit}"
            featuresSummaryList.add(thirdPartyDamageLimit)
        } else if (rateCommand.tplBodilyDamageLimit || rateCommand.tplMaterialDamageLimit) {
            String tplBodilyDamageLimit =
                "${g.message(code: "funnel.quote.tplBodilyInjuryLimit")} : ${rateCommand.tplBodilyDamageLimit}"
            featuresSummaryList.add(tplBodilyDamageLimit)

            if (rateCommand.tplMaterialDamageLimit) {
                String tplMaterialDamageLimit =
                    "${g.message(code: "funnel.quote.tplMaterialDamageLimit")} : ${rateCommand.tplMaterialDamageLimit}"
                featuresSummaryList.add(tplMaterialDamageLimit)
            }
        }

        if (rateCommand.thirdPartyLiability) {
            String thirdPartyLiability =
                "${g.message(code:"funnel.quote.thirdPartyLiability")} : ${rateCommand.thirdPartyLiability}"
            featuresSummaryList.add(thirdPartyLiability)
        }

        if (session[IConstant.SITE_COUNTRY] != CountryEnum.EGYPT) {
            String damageToYourVehicle =
                "${g.message(code:"funnel.quote.damageToYourVehicle")} : ${rateCommand.damageToYourVehicle}"
            featuresSummaryList.add(damageToYourVehicle)
        }

        if (rateCommand.excess) {
            String currency = "", excess = ""

            if (rateCommand.excess.isNumber()) {
                if (quote.currency?.equalsIgnoreCase("EGP"))
                    currency = g.message(code: "currency.egy");
                else if (quote.currency?.equalsIgnoreCase("AED"))
                    currency = g.message(code: "currency.uae");
                else
                    currency = quote.currency
                excess = "${g.message(code:"funnel.quote.excess")} : ${rateCommand.excess} ${currency}"
            } else {
                excess = "${g.message(code:"funnel.quote.excess")} : ${g.message(code: "subject.to.refer")}"
            }

            featuresSummaryList.add(excess)
        }

        String[] specialFeatures = rateCommand.specialFeatures?.split(";")

        if (rateCommand.otherInformation) {
            featuresSummaryList.add(g.message(code:"funnel.checkout.otherInformation"))
            featuresSummaryList.add(rateCommand.otherInformation)
        }

        out << g.render (template:'/car/checkout/features',
            model:[quote:quote, includedList:list.includedList, notIncludedList:list.notIncludedList,
            featuresSummaryList:featuresSummaryList, specialFeatures : specialFeatures, coverLink : rateCommand.coverLink])
    }

    def getInludedAndNotIncludedList(RateCommand rateCommand) {
        List<String> includedList = []
        List<String> notIncludedList = []

        if (rateCommand.agencyRepair) {
            includedList.add(g.message(code:'funnel.checkout.agencyRepairs'))
        } else {
            notIncludedList.add(g.message(code:'funnel.checkout.agencyRepairs'))
        }

        if (utilService.toBoolean(rateCommand.paCover) && session[IConstant.SITE_COUNTRY] != CountryEnum.LBN){
            includedList.add(g.message(code:'funnel.checkout.personalAccidentDriver'))
        } else if (!rateCommand.paCover.isNumber() && session[IConstant.SITE_COUNTRY] != CountryEnum.LBN){
            notIncludedList.add(g.message(code:'funnel.checkout.personalAccidentDriver'))
        }

        if (utilService.toBoolean(rateCommand.personalAccidentPax) && session[IConstant.SITE_COUNTRY] != CountryEnum.LBN){
            if (rateCommand.productId == DubaiNationalRateService.PREMIUM_PRODUCT_ID) {
                includedList.add(g.message(code: 'funnel.checkout.personalAccidentPassengerFamily'))
                notIncludedList.add(g.message(code:'funnel.checkout.personalAccidentPassengerEmployee'))
            } else {
                includedList.add(g.message(code: 'funnel.checkout.personalAccidentPassenger'))
            }
        } else if (!rateCommand.personalAccidentPax.isNumber() && session[IConstant.SITE_COUNTRY] != CountryEnum.LBN) {
            notIncludedList.add(g.message(code:'funnel.checkout.personalAccidentPassenger'))
        }

        if (rateCommand.breakdownCover in ['silver', 'gold', 'platinum', 'bronze']){
            //includedList.add(g.message(code:'funnel.checkout.breakdownCover') + ' ' + g.message(code:'common.' + rateCommand.breakdownCover))
        } else if (!rateCommand.breakdownCover.isNumber()){
            notIncludedList.add(g.message(code:'funnel.checkout.breakdownCover'))
        }

        if (utilService.toBoolean(rateCommand.replacementCar)){
            includedList.add(g.message(code:'funnel.checkout.replacementCar'))
        } else if (!rateCommand.replacementCar.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.replacementCar'))
        }

        if (utilService.toBoolean(rateCommand.carHireCashBenefit)){
            includedList.add(g.message(code:'funnel.checkout.carHireService'))
        } else if (!rateCommand.carHireCashBenefit?.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.carHireService'))
        }

        if (session[IConstant.SITE_COUNTRY] == CountryEnum.LBN &&
            utilService.toBoolean(rateCommand.personalAccidentPax) &&
            utilService.toBoolean(rateCommand.paCover)) {
            includedList.add(g.message(code:'funnel.checkout.medicalExpenses', args:[rateCommand.emergencyMedical]))
        }

        if (utilService.toBoolean(rateCommand.carRegService)){
            if (rateCommand.providerId == WathbaRateV2Service.PROVIDER_ID) {
                includedList.add(g.message(code: 'funnel.checkout.carRegistrationServiceWathba'))
            } else {
                includedList.add(g.message(code: 'funnel.checkout.carRegistrationService'))
            }
        } else if (!rateCommand.carRegService.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.carRegistrationService'))
        }

        if (utilService.toBoolean(rateCommand.offRoadDesertRecovery)){
            if (rateCommand.providerId != WathbaRateV2Service.PROVIDER_ID) {
                includedList.add(g.message(code: 'funnel.checkout.breakdownRecovery'))
            }
        } else if (!rateCommand.offRoadDesertRecovery?.isNumber()) {
            if (session[IConstant.SITE_COUNTRY] == CountryEnum.LBN) {
                notIncludedList.add(g.message(code: 'common.offroadRecovery'))
            } else if (session[IConstant.SITE_COUNTRY] == CountryEnum.UAE) {
                notIncludedList.add(g.message(code: 'funnel.checkout.breakdownRecovery'))
            }
        }

        if (rateCommand.naturalCalamity == 'yes'){
            includedList.add(g.message(code:'common.naturalCalamity'))
        } else {
            notIncludedList.add(g.message(code:'common.naturalCalamity'))
        }

        /*
         * Bellow three are used under features column so need to add in no/included list

        if (utilService.toBoolean(rateCommand.thirdPartyDamageLimit)){
            includedList.add(g.message(code:'funnel.checkout.thirdPartyDamageLimit'))
        } else if (!rateCommand.thirdPartyDamageLimit.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.thirdPartyDamageLimit'))
        }

        if (utilService.toBoolean(rateCommand.thirdPartyLiability)){
            includedList.add(g.message(code:'funnel.checkout.thirdPartyLiability'))
        } else if (!rateCommand.thirdPartyLiability.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.thirdPartyLiability'))
        }

        if (utilService.toBoolean(rateCommand.damageToYourVehicle)){
            includedList.add(g.message(code:'funnel.checkout.damageToYourVehicle'))
        } else if (!rateCommand.damageToYourVehicle.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.damageToYourVehicle'))
        }
        */

//        if (utilService.toBoolean(rateCommand.dentRepair)){
//            includedList.add(g.message(code:'funnel.checkout.dentRepair'))
//        } else if (!rateCommand.dentRepair.isNumber()) {
//            notIncludedList.add(g.message(code:'funnel.checkout.dentRepair'))
//        }

        if (utilService.toBoolean(rateCommand.lossOfEmployment)){
            includedList.add(g.message(code:'funnel.checkout.lossOfEmployment'))
        } else if (!rateCommand.lossOfEmployment?.isNumber() && session[IConstant.SITE_COUNTRY] == CountryEnum.UAE) {
            notIncludedList.add(g.message(code:'funnel.checkout.lossOfEmployment'))
        }

        if (rateCommand.lossOfPersonalBelongings && rateCommand.lossOfPersonalBelongings != 'no') {
            includedList.add(g.message(code:'funnel.checkout.lossOfPersonalBelongings') + ' (Upto ' + rateCommand.currency + ' ' + rateCommand.lossOfPersonalBelongings + ')')
        } else if (!rateCommand.lossOfPersonalBelongings || rateCommand.lossOfPersonalBelongings == 'no') {
            notIncludedList.add(g.message(code:'funnel.checkout.lossOfPersonalBelongings'))
        }

        if (rateCommand.emergencyMedical && rateCommand.emergencyMedical != 'no' && session[IConstant.SITE_COUNTRY] == CountryEnum.UAE) {
            if (rateCommand.emergencyMedical == 'yes') {
                includedList.add(g.message(code: 'common.emergencyMedical'))
            } else {
                includedList.add(g.message(code: 'common.emergencyMedical') + ' (' + rateCommand.currency + ' ' +  rateCommand.emergencyMedical + ')')
            }
        } else if ((!rateCommand.emergencyMedical || rateCommand.emergencyMedical == 'no') && session[IConstant.SITE_COUNTRY] == CountryEnum.UAE) {
            notIncludedList.add(g.message(code:'common.emergencyMedical'))
        }

        if (rateCommand.windScreenCover && rateCommand.windScreenCover != 'no') {
            String windscreenCover = g.message(code:'funnel.checkout.windscreenDamage')
            windscreenCover = windscreenCover
                .concat(rateCommand.windScreenCover != 'yes' ? ' (Upto ' + rateCommand.currency + ' ' + rateCommand.windScreenCover + ')' : '')
            includedList.add(windscreenCover)
        } else if (!rateCommand.windScreenCover?.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.windscreenDamage'))
        }

        if (rateCommand.replacementLocks && rateCommand.replacementLocks != 'no') {
            String replacementLocks = g.message(code:'funnel.checkout.replacementLocks')
            replacementLocks = replacementLocks
                .concat(rateCommand.replacementLocks != 'yes' ? ' (' + rateCommand.currency + ' ' + rateCommand.replacementLocks + ')' : '')
            includedList.add(replacementLocks)
        } else if (!rateCommand.replacementLocks?.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.replacementLocks'))
        }

        if (utilService.toBoolean(rateCommand.valetParkingTheft)){
            includedList.add(g.message(code:'funnel.checkout.valetParkingTheft'))
        } else if (!rateCommand.valetParkingTheft?.isNumber()) {
            notIncludedList.add(g.message(code:'funnel.checkout.valetParkingTheft'))
        }

        [includedList:includedList,notIncludedList:notIncludedList]
    }

    def addons = { attr ->
        RateCommand rateCommand = attr.rateCommand
        Model model = attr.model
        BigDecimal insuredValue = attr.insuredValue

        def coversMap = session[IConstant.SRP_COVERS_MAP] ?: [:]

        def addOns = quoteService.getAddOns(rateCommand, model, attr.quote.requestSource, coversMap, insuredValue, attr.quote)

        Set checkBoxlist = addOns.checkboxList
        Set dropDownList = addOns.dropdownList

        Product product = Product.read(rateCommand.productId)
        String countryCode = product.provider.country.code

        CountryEnum country = CountryEnum.findCountryByDfp(countryCode)

        if (checkBoxlist || dropDownList ) {
            out << g.render (template:'/car/checkout/addons',
                model:[checkBoxlist:checkBoxlist, dropDownList:dropDownList, currency:utilService.getCurrency(),
                       country:country])
        }
    }

    def applyVAT(def items) {
        items.each { item ->
            item.originalValue = item.value
            item.value = commonUtilService.addVAT(item.value)
            item.value = new BigDecimal(item.value).setScale(2, RoundingMode.CEILING)
        }

        items
    }

    def getVATAmount = { attr ->
        out << commonUtilService.getVATAmount(attr.price)
    }

    def orderSummary = { attr ->
        log.info("checkoutTagLib.orderSummary - quoteId:${attr.quote.id}")

        def quote = attr.quote
        String template
        def addons

        if (!(quote instanceof CarQuote) || quote.isUAEQuote()) {

            if (quote instanceof HomeQuote) {
                template = '/homeInsurance/checkout/order_summary'
            } else if (quote instanceof HealthQuote) {
                template = '/health/checkout/order_summary'
            } else if (quote instanceof LifeQuote) {
                template = '/life/checkout/order_summary'
            } else if (quote instanceof TravelQuote) {
                template = '/travel/checkout/order_summary'
            } else if (quote instanceof PetQuote) {
                template = '/pet/checkout/order_summary'
            } else if (quote instanceof PaLead) {
                template = '/pa/checkout/order_summary'
            } else if (quote instanceof GeneralQuote) {
                template = '/generalInsurance/checkout/order_summary'
            } else {
                template = '/car/checkout/order_summary'
            }
            log.info("quote product type:${quote.productType}, template:${template}")

            def model = checkoutService.getUAEOrderSummary(quote)

            log.info("checkoutTagLib.orderSummary - quoteId:${attr.quote.id} , model${model}")

            out << g.render(template: template, model: model)

        } else {

            template = "/car/checkout/order_summary_simple"

            def model = checkoutService.getSimpleOrderSummary(quote)


            out << g.render(template: template, model: model)
        }
    }

    def discount = { attr ->
        Integer insuranceType = ProductType.CAR

        String homeInsurance = InsuranceTypeEnum.HOMEINSURANCE.toString().toLowerCase()
        String lifeInsurance = InsuranceTypeEnum.LIFE.toString().toLowerCase()
        String healthInsurance = InsuranceTypeEnum.HEALTH.toString().toLowerCase()

        if (session[IConstant.INSURANCE_TYPE].toString().equals(homeInsurance)) {
            insuranceType = ProductType.HOME
        } else if (session[IConstant.INSURANCE_TYPE].toString().equals(healthInsurance)) {
            insuranceType = ProductType.HEALTH
        }
        else if (session[IConstant.INSURANCE_TYPE].toString().equals(lifeInsurance)) {
            insuranceType = ProductType.LIFE
        }

        out << g.render (template: '/components/discountControl',
            model: [productType: insuranceType, productId: attr.productId])
    }

    /**
     * Show knet receipt.
     * This could be complete payment receipt or error code
     */
    def knetReceipt = { attr ->
        def quote = attr.quote
        String paymentId = attr.paymentId
        log.info("creating receipt:${quote.id}, paymentId:${paymentId}")

        if (quote instanceof CarQuote) {
            KnetPaymentResponse knetResponse = KnetPaymentResponse.findByPaymentId(paymentId)
            log.info("creating receipt -> knetResponse:${knetResponse}")

            if (!knetResponse) {
                out << ""
                return
            }

            out << g.render (template: '/components/knetPaymentReceipt', model: [knetResponse:knetResponse])
        }
    }

}
