package com.cover.car

import com.safeguard.InsuranceTypeEnum
import com.cover.car.commands.RateCommand
import com.cover.util.IConstant
import com.safeguard.CountryEnum
import com.safeguard.car.CarQuote
import grails.util.Environment

class SrpTagLib {
    static namespace = "yc"

    def newLead = { attrs ->
        def html = null

        //todo handle different insuranceTypes, languages, and countries
        if (attrs.insuranceType == InsuranceTypeEnum.CAR
            && session[IConstant.NEW_USER]) {
            html = """<div id='new-user-div'></div>"""
        }
        out << html
    }

    def quoteTrackers = { attrs ->
        def html = null

        //todo handle different insuranceTypes, languages, and countries
        if (attrs.insuranceType == InsuranceTypeEnum.CAR
            && Environment.current == Environment.PRODUCTION
            && attrs.quote) {
            html = g.render(
                template: "/car/funnel/carQuoteTrackers",
                model: [
                    quote: attrs.quote
                ]
            )
        }
        out << html
    }

    def breadcrumbs = { attrs ->
        def html = null

        //todo handle different insuranceTypes, languages, and countries
        if (attrs.insuranceType == InsuranceTypeEnum.CAR) {
            html = g.render(
                template: "/car/partials/breadcrumbs_${session[IConstant.SITE_COUNTRY].toString()}",
                model: [
                    page: attrs.page,
                    country: attrs.country,
                    currentLang: attrs.lang
                ]
            )
        }
        out << html
    }

    def carQuotes = { attrs ->
        out << g.render(
            template: "/car/partials/quotes",
            model: [
                quote: attrs.quote
            ]
        )
    }

    def carQuoteV2 = { attrs ->
        String pagePostfix = "_v2"

        if (session[IConstant.SITE_COUNTRY].toString() == CountryEnum.LBN.code) pagePostfix = "_lbn"
        else if (session[IConstant.SITE_COUNTRY].toString() == CountryEnum.KWT.code) pagePostfix = "_kwt"
        else if (session[IConstant.SITE_COUNTRY].toString() == CountryEnum.EGYPT.code) pagePostfix = "_egy"

        CarQuote quote = attrs.quote
        RateCommand rateCommand = attrs.rateCommand
        Integer index = attrs.index
        Boolean isRenewal = attrs.isRenewal
        Boolean expandRenewal = attrs.expandRenewal
        Boolean isThirdParty = rateCommand.coverageTypeId == 2
        Boolean isOfflineQuotes = rateCommand.isOfflineQuotes
        List<String> thingsToConsider = []

        //Things to consider
        if (!rateCommand.agencyRepair) {
            thingsToConsider.add( g.message(code: "car.srp.static.agencyRepairParts", args: [quote.model.make.name]) as String )
        }

        if (isThirdParty) {
            thingsToConsider.add( g.message(code: 'car.srp.static.thirdParty.1') as String )
            thingsToConsider.add( g.message(code: 'car.srp.static.thirdParty.2') as String )
        } else {
            thingsToConsider.add( g.message(code: 'car.srp.static.fullyComprehensive.1') as String )
            thingsToConsider.add( g.message(code: 'car.srp.static.fullyComprehensive.2') as String )
            thingsToConsider.add( g.message(code: 'car.srp.static.fullyComprehensive.3') as String )
        }

        //YC coverage rating
//        Double ycCoverageRating = 0.5
//        if (rateCommand.thirdPartyLiabilityAmount >= 2000000) ycCoverageRating += 0.5
//        if (rateCommand.thirdPartyLiabilityAmount >= 5000000) ycCoverageRating += 0.5
//        if (rateCommand.breakdownCover in ['silver', 'gold', 'platinum']) ycCoverageRating += 0.5
//        if (!isThirdParty) ycCoverageRating += 0.5
//        if (rateCommand.agencyRepair) ycCoverageRating += 0.5
//        if (rateCommand.paCover == 'yes') ycCoverageRating += 0.5
//        if (rateCommand.personalAccidentPax == 'yes') ycCoverageRating += 0.5
//        if (rateCommand.replacementCar == 'yes' || rateCommand.carRegService == 'yes' || rateCommand.offRoadDesertRecovery == 'yes') ycCoverageRating += 0.5
//        if (rateCommand.damageToYourVehicleOman) ycCoverageRating += 0.5

        //Balloon popups
        String excessTitle
        if (isOfflineQuotes) {
            excessTitle = g.message(code: "car.srp.static.offlineQuotes.access")
        } else if (isThirdParty) {
            excessTitle = g.message(code: "car.srp.static.thirdParty.access")
        } else {
            excessTitle = g.message(code: "car.srp.static.fullyComprehensive.access", args: [quote.currency, rateCommand.excess])
        }

        String repairsTitle = ""
        if (isThirdParty) {
            repairsTitle = g.message(code: "car.srp.static.thirdParty.repairsTitle")
        } else if (!isThirdParty && rateCommand.agencyRepair) {
            repairsTitle = g.message(code: "car.srp.static.fullyComprehensive.agencyRepair.repairsTitle", args: [quote.model.make.name])
        } else if (!isThirdParty && !rateCommand.agencyRepair) {
            repairsTitle = g.message(code: "car.srp.static.fullyComprehensive.garageRepair.repairsTitle", args: [rateCommand.provider, quote.model.make.name])
        }

        String tplDamageLimitTitle = g.message(code: "car.srp.static.tplDamageLimitTitle", args: [quote.currency, (rateCommand.thirdPartyLiabilityAmount ?: 0) / 1000000 ])

        String roadsideTitle
        if (rateCommand.breakdownCover in ['silver', 'gold', 'platinum', 'bronze']) {
            roadsideTitle = g.message(code: "car.srp.static.fullyComprehensive.roadSideAs")

            rateCommand.roadsideAssistances.eachWithIndex { ra, i ->
                if (i > 0) roadsideTitle += ", "
                roadsideTitle += ra
            }
        } else if (rateCommand.breakdownCover == 'none') {
            roadsideTitle = g.message(code: "car.srp.static.fullyComprehensive.noRoadSideAs")
        } else {
            roadsideTitle = g.message(code: "car.srp.static.fullyComprehensive.roadSideAs.extra", args: [quote.currency, rateCommand.breakdownCover])

            rateCommand.roadsideAssistances.eachWithIndex { ra, i ->
                if (i > 0) roadsideTitle += ", "
                roadsideTitle += ra
            }
        }

        String driverInjuryTitle
        if (rateCommand.paCover == "yes") {
            driverInjuryTitle = g.message(code: "car.srp.static.fullyComprehensive.driverInjuryTitle.paCoverYes")
        } else if (rateCommand.paCover == "no") {
            driverInjuryTitle = g.message(code: "car.srp.static.fullyComprehensive.driverInjuryTitle.paCoverNo")
        } else {
            driverInjuryTitle = g.message(code: "car.srp.static.fullyComprehensive.driverInjuryTitle.paCover", args: [quote.currency, rateCommand.paCover])
        }

        String passengerInjuryTitle
        if (rateCommand.personalAccidentPax == "yes") {
            passengerInjuryTitle =  g.message(code: "car.srp.static.fullyComprehensive.personalAccidentPaxYes")
        } else if (rateCommand.personalAccidentPax == "no") {
            passengerInjuryTitle = g.message(code: "car.srp.static.fullyComprehensive.personalAccidentPaxNo")
        } else {
            passengerInjuryTitle = g.message(code: "car.srp.static.fullyComprehensive.personalAccidentPax", args: [quote.currency, rateCommand.personalAccidentPax])
        }

        String rentalCarTitle
        if (rateCommand.carHireCashBenefit == "yes") {
            rentalCarTitle = g.message(code: "car.srp.static.fullyComprehensive.carHireCashBenefitYes")
        } else if (rateCommand.carHireCashBenefit && rateCommand.carHireCashBenefit.isNumber()) {
            rentalCarTitle = g.message(code: "car.srp.static.fullyComprehensive.carHireCashBenefit", args: [quote.currency, rateCommand.carHireCashBenefit])
        } else if (rateCommand.replacementCar == "yes") {
            rentalCarTitle = g.message(code: "car.srp.static.fullyComprehensive.replacementCarYes")
        } else if (rateCommand.replacementCar == "no") {
            rentalCarTitle = g.message(code: "car.srp.static.fullyComprehensive.replacementCarNo")
        } else {
            rentalCarTitle = g.message(code: "car.srp.static.fullyComprehensive.replacementCar", args: [quote.currency, rateCommand.replacementCar])
        }

        String ownDamageOmanTitle
        if (rateCommand.damageToYourVehicleOman) {
            ownDamageOmanTitle = g.message(code: "car.srp.static.fullyComprehensive.damageToYourVehicleOman")
        } else {
            ownDamageOmanTitle = g.message(code: "car.srp.static.fullyComprehensive.damageToYourVehicleOmanNo")
        }

        String offroadTitle
        if (rateCommand.offRoadDesertRecovery == 'yes') {
            offroadTitle = g.message(code: "car.srp.static.fullyComprehensive.offRoadDesertRecovery")
        } else {
            offroadTitle = g.message(code: "car.srp.static.fullyComprehensive.offRoadDesertRecoveryNo")
        }

        String carRegistrationTitle
        if (rateCommand.carRegService == 'yes') {
            carRegistrationTitle = g.message(code: "car.srp.static.fullyComprehensive.carRegService", args: [rateCommand.provider])
        } else {
            carRegistrationTitle = g.message(code: "car.srp.static.fullyComprehensive.carRegServiceNo")
        }

        if (rateCommand.providerId == 63) {
            log.info("testing for insured value; providerInsuredValue: ${rateCommand.insuredValue} && insuredValue: ${quote.insuredValue}")
        }

        String offRoadText
        if (rateCommand.providerId == WathbaRateV2Service.PROVIDER_ID){
            offRoadText = g.message(code: "car.srp.static.tag.offRoadCoverWathba")
        } else {
            offRoadText = g.message(code: "car.srp.static.tag.offRoadCover")
        }

        String carRegistrationText
        if (rateCommand.providerId == WathbaRateV2Service.PROVIDER_ID){
            carRegistrationText = g.message(code: "car.srp.static.tag.carRegServiceWathba")
        } else {
            carRegistrationText = g.message(code: "car.srp.static.tag.carRegService")
        }


        out << g.render(
            template: "/car/partials/carQuote${pagePostfix}",
            model: [
                'isRenewal'                : isRenewal,
                'quoteId'                  : quote.id,
                'productId'                : rateCommand.productId,
                'provider'                 : rateCommand.provider,
                'providerEn'               : rateCommand.providerEn,
                'product'                  : rateCommand.productName,
                'productEn'                : rateCommand.productNameEn,
                'imgSrc'                   : rateCommand.providerImage,
                'currency'                 : quote.quoteCountry ? g.message(code: "currency.${CountryEnum.findCountryByIsoAlpha2Code(quote.quoteCountry.code)}") : quote.currency,
                'premium'                  : rateCommand.premium,
                'noDiscountPremium'        : rateCommand.getPremiumBeforeDiscount(),
                'additionalCharges'        : rateCommand.additionalCharges,
                'discountPercent'          : rateCommand.discountPercent,
                'specialPremium'           : null,
                'locations'                : rateCommand.damageToYourVehicle,
                'excess'                   : rateCommand.excess,
                'thirdPartyLimit'          : "${(rateCommand.thirdPartyLiabilityAmount ?: 0) / 1000000} ${g.message(code: 'common.million')}",
                'tplBodilyDamageLimit'     : rateCommand.tplBodilyDamageLimit,
                'tplMaterialDamageLimit'   : rateCommand.tplMaterialDamageLimit,
                'isTakaful'                : rateCommand.isTakaful,
                'isThirdPartyOnly'         : isThirdParty,
                'hasPremiumGarage'         : rateCommand.hasPremiumGarage,
                'hasAgencyRepair'          : rateCommand.agencyRepair,
                'personalAccidentDriver'   : rateCommand.paCover,
                'personalAccidentPassenger': rateCommand.personalAccidentPax,
                'naturalCalamity'          : rateCommand.naturalCalamity,
                'windscreenDamage'         : rateCommand.windScreenCover,
                'valetTheft'               : rateCommand.valetParkingTheft,
                'replacementLocks'         : rateCommand.replacementLocks,
                'lossOfBelongings'         : rateCommand.lossOfPersonalBelongings,
                'emergencyMedical'         : rateCommand.emergencyMedical,
                'roadsideAssistanceType'   : rateCommand.breakdownCover,
                'roadsideAssistances'      : rateCommand.roadsideAssistances,
                'specialFeatures'          : rateCommand.specialFeatures?.split(';'),
                'replacementCar'           : rateCommand.replacementCar,
                'coverLink'                : rateCommand.coverLink,
                'productDiscountAmount'    : rateCommand.productDiscountAmount,
                'productDiscountPercent'   : rateCommand.productDiscountPercent,
                'productHasPercentDiscount': rateCommand.productHasPercentDiscount,
                'carHireCashBenefit'       : rateCommand.carHireCashBenefit,
                'index'                    : index,
                'expandRenewal'            : expandRenewal,
                'carRegService'            : rateCommand.carRegService,
                'offRoadDesertRecovery'    : rateCommand.offRoadDesertRecovery,
                'damageToYourVehicleOman'  : rateCommand.damageToYourVehicleOman,
                'damageToThirdPartyOman'   : rateCommand.damageToThirdPartyOman,
//                'ycCoverageRating'         : ycCoverageRating,
                'thingsToConsider'         : thingsToConsider,
                'excessTitle'              : excessTitle,
                'repairsTitle'             : repairsTitle,
                'tplDamageLimitTitle'      : tplDamageLimitTitle,
                'roadsideTitle'            : roadsideTitle,
                'driverInjuryTitle'        : driverInjuryTitle,
                'passengerInjuryTitle'     : passengerInjuryTitle,
                'rentalCarTitle'           : rentalCarTitle,
                'ownDamageOmanTitle'       : ownDamageOmanTitle,
                'offroadTitle'             : offroadTitle,
                'carRegistrationTitle'     : carRegistrationTitle,
                'noClaimDiscountPercent'   : rateCommand.noClaimDiscountPercent,
                'requiredSelfDeclarationNumber' : rateCommand.requiredSelfDeclarationNumber,
                'bypassedDiscountForTPLMaxLimit': rateCommand.bypassedDiscountForTPLMaxLimit,
                'discountLimitedToC4meFees'     : rateCommand.discountLimitedToC4meFees,
                'discountCode'             : rateCommand.discountCode,
                'insuredValue'             : quote.insuredValue,
                'providerInsuredValue'     : rateCommand.insuredValue,
                'isOfflineQuotes'          : isOfflineQuotes,
                'carAge'                   : rateCommand.carAge,
                'omanOwnDamageTitle'       : rateCommand.omanOwnDamageTitle,
                'carHireBenefitTitle'      : rateCommand.carHireBenefitTitle,
                'offRoadText'              : offRoadText,
                'carRegistrationText'      : carRegistrationText
            ]
        )
    }
}
