package com.cover.pet



class PetInsuranceTagLib {

    static namespace = 'petInsurance'
    static returnObjectForTags = ['countryList', 'drivingExperienceList', 'ncdList']

    def petService
    def valuationService
    def utilService
    def commonUtilService



    /**
     * Renders pet Type drop down list
     *
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def petTypeDDL = { attr ->

        def petTypeList = petService.getPetTypeList().sort { it.name }

        out << g.select (name:"petType", id:"petType", from:petTypeList, optionKey:"id", optionValue: "name", class:attr.class,
                noSelection: ['-1':g.message(code: 'petInsurance.type')], value: attr.value)
    }

   /**
     * Renders pet plan drop down list
     *
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def petPlanDDL = { attr ->

        def petPlanList = petService.getPetPlanList().sort { it.name }

        out << g.select (name:"petPlan", id:"petPlan", from:pet<PERSON><PERSON><PERSON>ist, optionKey:"id", optionValue: "name", class:attr.class,
                noSelection: ['-1':g.message(code: 'petInsurance.plan')], value: attr.value)
    }

    /**
     * Renders petBreed drop down list
     *
     * @attr petType REQUIRED pet type id of pet
     * @attr class css class for drop down styling
     * @attr value default selected value of drow down
     */
    def petBreedDDL = { attr ->
        def petBreedList = []

        if (attr.petType) {
            petBreedList = petService.getPetBreedList(attr.petType)
        }

        out << g.select (name:"petBreed", id:"petBreed", from:petBreedList, optionKey: "id", optionValue: "name", class:attr.class,
            noSelection: ['-1':g.message(code: 'petInsurance.breed')], value: attr.value)
    }

    /**
     * returns firstLicenseCountry list object.
     */
    def countryList = { attr ->
        String  sortBy= attr.sortBy ?: 'nameEn'
        utilService.getCountries(sortBy)
    }

}
