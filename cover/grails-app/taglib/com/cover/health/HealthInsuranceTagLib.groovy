package com.cover.health

import com.cover.health.commands.HealthRateCommand
import com.cover.util.IConstant
import com.safeguard.Country
import com.safeguard.CountryEnum
import com.safeguard.HealthApplicationTypeEnum
import com.safeguard.Product
import com.safeguard.health.HealthQuote

class HealthInsuranceTagLib {

    static namespace = 'health'

    def healthQuoteService
    def utilService

    def quotesRows = { attr ->

        HealthQuote healthQuote = attr.healthQuote
        def currency = utilService.getCountry().currency
        def quotes = attr.quotes

        String _template =  '/health/partials/quotesRow'

        quotes.each {
            out << g.render(template: _template, model: [healthQuoteId: healthQuote.id, quote: it, currency: currency])
        }
    }

    def quotesHeadRow = { attr ->
        HealthQuote healthQuote = attr.healthQuote

        String _template =  '/health/partials/srpHeadRow'

        out << g.render(template: _template, model: [quote: healthQuote])
    }

    def quoteDetailsField = { attr ->
        String val = attr.value
        boolean isLabel = attr.label

        if (!val) {
            out << "<i class=\"insurance-icon-cross-out-mark\"></i>"
        } else if (val.isNumber() && Double.parseDouble(val)){
            def currency = utilService.getCountry().currency
            out << "${currency} ${g.formatNumber(number:val, type:'number')}"
        } else if (val.equalsIgnoreCase('yes')) {
            out << "<i class=\"insurance-icon-verification-mark\"></i>"
        } else {
            out << val
        }
    }

    def featuresTable = { attr ->
        HealthRateCommand rateCommand = attr.rateCommand
        String routineDental = rateCommand.routineDental

        def deductible = rateCommand.deductible.split('##')
        deductible = (deductible.length > 1) ? deductible.drop(1).join('. ') : rateCommand.deductible

        def coverage = rateCommand.coverage?.split('##')
        coverage = (coverage && coverage.length > 1) ? coverage.drop(1).join('. ') : rateCommand.coverage

        def pharmaceuticals = rateCommand.pharmaceuticals?.split('##')
        pharmaceuticals = (pharmaceuticals?.length > 1) ? pharmaceuticals.drop(1).join('. ') : rateCommand.pharmaceuticals

        List featuresSummaryList = [
            "<div><b>${g.message(code:'healthInsurance.deductibles')}</b></div> ${quoteDetailsField(value:deductible)}",
            "<div><b>${g.message(code:'healthInsurance.maxAnnualCoverage')}</b></div> ${quoteDetailsField(value:rateCommand.maxAnnualCoverage)}",
            "<div><b>${g.message(code:'healthInsurance.coverage')}</b></div> ${quoteDetailsField(value:coverage)}",
            "<div><b>${g.message(code:'healthInsurance.routineDental')}</b></div> ${routineDental}",
            "<div><b>${g.message(code:'healthInsurance.networks')}</b></div> ${quoteDetailsField(value:rateCommand.networks)}",
            "<div><b>${g.message(code:'healthInsurance.maternity')}</b></div> ${quoteDetailsField(value:rateCommand.maternity)}",
            "<div><b>${g.message(code:'healthInsurance.pharmaceuticals')}</b></div> ${quoteDetailsField(value:pharmaceuticals)}"

        ]
        // in case of salama
        if ( rateCommand.policyWordingsLink ) {
            featuresSummaryList.add("<div><b>Policy Wording</b></div> <a class='text-primary' target='_blank' href='$rateCommand.policyWordingsLink'>Click here to view the policy wording</a>")
        }

        out << g.render(template: '/health/checkout/features',
            model: [featuresSummaryList: featuresSummaryList, command: rateCommand])
    }

    def salaryField = { attrs ->
        if (attrs.applicationType != HealthApplicationTypeEnum.EMPLOYEES || !attrs.applicationType) {
            out << render(template: "/health/templates/salaryField")
        }
        else {
            out << """<input type="hidden" name="salaryOver4k" value="true"/>"""
        }
    }

    def insuranceForYourselfSwitch = { attrs ->
        if (attrs.applicationType == HealthApplicationTypeEnum.FAMILY) {
            out << render(template: "/health/templates/insuranceForYourselfSwitch", model: [
                checked: attrs.checked
            ])
        }
    }

    def ebpProductsSwitch = { attrs ->
            HealthApplicationTypeEnum applicationType = attrs.applicationType
            Boolean checked = false
            if (applicationType == HealthApplicationTypeEnum.WORKER) {
                checked = true
            }
            out << render(template: "/health/templates/viewEbpProducts", model: [
                    checked: checked
            ])
    }

    def companyNameField = { attrs ->
        if (attrs.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
            out << formControl.textbox([
                id         : attrs.id,
                placeholder: attrs.placeholder,
                label      : attrs.label,
                value      : attrs.value
            ])
        }
    }

    def numberOfEmployeesField = { attrs ->
        if (attrs.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
            out << formControl.numberBox([
                id         : attrs.id,
                placeholder: attrs.placeholder,
                label      : attrs.label,
                value      : attrs.value
            ])
        }
    }

    def membersSection = { attrs ->
        if (attrs.applicationType != HealthApplicationTypeEnum.EMPLOYEES) {

            if (attrs.applicationType == HealthApplicationTypeEnum.YOURSELF) {
                attrs.healthMembers = attrs.healthMembers?.getAt(0)
            }

            out << render(template: "/health/templates/membersSection", model: [
                applicationType: attrs.applicationType,
                healthMembers  : attrs.healthMembers,
                isEbp: attrs.isEbp,
                insuranceForYourself: attrs.insuranceForYourself
            ])
        }
    }

    def member = { attrs ->
//        def showMemberNumber = (attrs.applicationType != HealthApplicationTypeEnum.YOURSELF) && (!attrs.insuranceForYourself)
        def showRemovalButton = (attrs.i != 0)

        def memberLabel
        def number = attrs.i > -1 ? attrs.i + 1 : null

        if (attrs.applicationType != HealthApplicationTypeEnum.YOURSELF) {
            memberLabel = "member <span class='member__number'>$number</span>"
        }

        if (attrs.applicationType == HealthApplicationTypeEnum.FAMILY && attrs.insuranceForYourself && attrs.i == 0) {
            memberLabel = "Your Details"
        }

        out << render(template: "/health/templates/member", model: [
            i: attrs.i,
            age: attrs.age,
            gender: attrs.gender,
            dob:attrs.dob,
            salaryOver4k: attrs.salaryOver4k,
            nationality: attrs.nationality,
            relationship: attrs.relationship,
            applicationType: attrs.applicationType,
            memberLabel: memberLabel,
            showRemovalButton: showRemovalButton,
            insuranceForYourself: attrs.insuranceForYourself,
            residentType: attrs.residentType
        ])
    }

    def memberRelationshipField = { attrs ->
        switch (attrs.applicationType) {
            case HealthApplicationTypeEnum.YOURSELF:
                out << formControl.hidden([
                    id   : "members[0].relationship",
                    name : "members[0].relationship",
                    value: "5" //I am the sponsor
                ])
                break

            case HealthApplicationTypeEnum.WORKER:
                out << formControl.hidden([
                    id   : "members[${(attrs.i > -1) ? attrs.i : -1}].relationship",
                    name : "members[${(attrs.i > -1) ? attrs.i : -1}].relationship",
                    value: "6", //They are my employee
                    class: "js-memberRelationshipId"
                ])
                break

            case HealthApplicationTypeEnum.FAMILY:
                if (attrs.insuranceForYourself && attrs.i == 0) {
                    out << formControl.hidden([
                        id   : "members[${(attrs.i > -1) ? attrs.i : -1}].relationship",
                        name : "members[${(attrs.i > -1) ? attrs.i : -1}].relationship",
                        value: "5", //They are my employee
                        class: "js-memberRelationshipId"
                    ])
                    break
                }
                else {
                    out << render(template: "/health/templates/memberRelationshipField", model: [
                        i           : attrs.i,
                        relationship: attrs.relationship,
                        applicationType: attrs.applicationType,
                    ])
                    break
                }

//            case HealthApplicationTypeEnum.EMPLOYEES:
//                out << render(template: "/health/templates/memberRelationshipField", model: [
//                    i           : attrs.i,
//                    relationship: attrs.relationship,
//                    applicationType: attrs.applicationType
//                ])
//                break
        }
    }

    def residentTypeField = { attrs ->
        out << render(template: "/health/templates/memberResidentTypeField", model: [
            i               :   attrs.i,
            residentType    :   attrs.residentType
        ])
    }

    def memberAgeField = { attrs ->
        def label = "What's their age?"
        if (attrs.applicationType == HealthApplicationTypeEnum.YOURSELF) {
            label = "What's your age?"
        }

        out << render(template: "/health/templates/memberAgeField", model: [
            label: label,
            i    : attrs.i,
            dob  : attrs.dob
        ])
    }


    def nationalityField = { attrs ->
        def label = "What's their nationality?"
        if (attrs.applicationType == HealthApplicationTypeEnum.YOURSELF) {
            label = "What's your nationality?"
        }
        String sortBy = attrs.lang == 'ar' ? 'nationalityAr' : 'nationalityEn'
        List<Country> countries = utilService.getCountries(sortBy)

        out << render(template: "/health/templates/nationalityField", model: [
                label           :   label,
                countries       :   countries,
                nationality :   attrs.value,
                i               :   attrs.i
        ])

    }

    def memberGenderField = { attrs ->
        def label = "What's their gender?"
        if (attrs.applicationType == HealthApplicationTypeEnum.YOURSELF) {
            label = "What's your gender?"
        }

        out << render(template: "/health/templates/memberGenderField", model: [
            label: label,
            i    : attrs.i,
            gender  : attrs.gender
        ])
    }

    def addMemberButton = { attrs ->

        def applicationTypes = [
            HealthApplicationTypeEnum.YOURSELF,
            HealthApplicationTypeEnum.EMPLOYEES
        ]

        if (!applicationTypes.contains(attrs.applicationType)) {
            out << render(template: "/health/templates/addMemberButton")
        }
    }

    def formSubmitButton = { attrs ->

        def text = "One more step..."
        if (attrs.applicationType == HealthApplicationTypeEnum.EMPLOYEES) {
            text = g.message(code: "funnel.getQuotes")
        }

        out << render(template: "/health/templates/formSubmitButton", model: [text: text])
    }

    def addons = { attr ->
        HealthRateCommand rateCommand = attr.rateCommand
        HealthQuote healthQuote = attr.quote

        def coversMap = session[IConstant.SRP_COVERS_MAP] ?: [:]

        def addOns = healthQuoteService.getAddOns(rateCommand, coversMap, healthQuote)

        Set checkBoxlist = addOns.checkboxList
        Set dropDownList = addOns.dropdownList

        Product product = Product.read(rateCommand.productId)
        String countryCode = product.provider.country.code

        CountryEnum country = CountryEnum.findCountryByDfp(countryCode)

        if (checkBoxlist || dropDownList ) {
            out << g.render (template:'/health/checkout/addons',
                model:[checkBoxlist:checkBoxlist, dropDownList:dropDownList, currency:utilService.getCurrency(),
                       country:country])
        }
    }

}
